import { Entity, Field, Datatype } from "./Entity.mjs";
import {
  ScriptHelper,
  pname,
  blockN,
  paramName,
  ppdt,
  jsname,
  sqldt,
} from "./ScriptHelper.mjs";

type Script = (e: Entity, b: ScriptHelper, params?: Array<Field>) => string;

export enum Language {
  sql,
  plpgSql,
}

export class PgScript {
  public b: ScriptHelper;
  private _script = "";

  constructor(private e: Entity, private h: ScriptHelper) {}

  public mfunc(
    name: string,
    language: Language,
    multi: Array<Array<Field>>,
    script: Script,
    returnsSet = false,
    returnsVoid = false
  ) {
    let all = "";
    for (let fields of multi) {
      let fullname = `${name}_by_${fields.map((f) => f.name).join("_and_")}`;
      all += this.func(
        fullname,
        language,
        fields,
        script,
        returnsSet,
        returnsVoid
      );
    }
    return all;
  }

  public dfunc(
    name: string,
    language: Language,
    script: Script,
    returnsSet = false
  ) {
    return this.func(name, language, this.h.pgParams, script, returnsSet);
  }

  public func(
    name: string,
    language: Language,
    params: Array<Field>,
    script: Script,
    returnsSet = false,
    returnsVoid = false
  ) {
    let body = script(this.e, this.h, params);

    if (language === Language.plpgSql) {
      body = `
    declare
        ${!returnsSet && !returnsVoid ? `result a0."${jsname(this.e)}";` : ""}
        ${params
          .filter((f) => pname(f) !== paramName(f))
          .map((f) => `${pname(f)} ${sqldt(f)};`)
          .join("\n\t\t")}
    begin
        ${params
          .filter((f) => pname(f) !== paramName(f))
          .map((f) => {
            if (f.datatype === Datatype.binary)
              return `${pname(f)} := decode(${paramName(f)}, 'base64');`;
            else if (f.datatype === Datatype.int64) {
              return `${pname(f)} := ${paramName(f)}::int8;`;
            }
          })
          .join("\n\t\t")}
        ${blockN(this.e, `on_${name}_decoded`)}
       
        ${blockN(this.e, `before_${name}`)}
        ${body}
        ${blockN(this.e, `after_${name}`)}

        ${!returnsSet && !returnsVoid ? `return result;` : ""}
    end;`;
    }

    let returnType = `a0."${jsname(this.e)}"`;
    if (returnsSet) returnType = `setof ${returnType}`;
    if (returnsVoid) returnType = "void";

    let sql = `drop function if exists a0.${this.e.name}_${name}; 
        create function a0.${this.e.name}_${name}(
            ${params.map((f) => `${paramName(f)} ${ppdt(f)}`).join(",\n\t")}
        )
        returns ${returnType}
        as $$
        ${body}
        $$
        language ${language === Language.sql ? "sql" : "plpgsql"};
        `;
    this._script += `\n${sql}\n`;
    return sql;
  }

  public text(sql: string) {
    this._script += `\n${sql}\n`;
    return sql;
  }

  public toString() {
    return this._script;
  }
}
