#include "encode_functions.h"
#include "Field.h"
#include "helpers.h"
#include "MYStringUtil.h"

Napi::Buffer<uint8_t> encode_sync(Napi::Env env, const Napi::Object &root)
{
	// {
	MYBJsonBigRW *writer = new MYBJsonBigRW();

	struct tagState
	{
		Napi::Env isolate;
		MYBJsonBigRW *writer;
		Napi::Object recordType;
		Napi::Object bf00;
		Napi::Object b0ff;
		int recordTypeId;
	};

	tagState state = {
			env,
			writer};

	writer->StartObject();

	auto recordTypes = root.GetPropertyNames();
	for (size_t iRecordType = 0; iRecordType < recordTypes.Length(); iRecordType++)
	{
		std::string recordTypeKey = recordTypes.Get(iRecordType).ToString();
		state.recordTypeId = helpers::rt(recordTypeKey);
		state.recordType = root.Get(recordTypeKey).ToObject();
		state.writer->StartObject(state.recordTypeId);

		auto bf00s = state.recordType.GetPropertyNames();
		for (size_t if00 = 0; if00 < bf00s.Length(); if00++)
		{
			Napi::Value keyf00 = bf00s.Get(if00);
			int32_t nf00 = keyf00.ToNumber();
			state.writer->StartObject(nf00);
			state.bf00 = state.recordType.Get(keyf00).ToObject();

			auto b0ffs = state.bf00.GetPropertyNames();
			for (size_t i0ff = 0; i0ff < b0ffs.Length(); i0ff++)
			{
				Napi::Value key0ff = b0ffs.Get(i0ff);
				int32_t n0ff = key0ff.ToNumber();
				state.writer->StartObject(n0ff);
				state.b0ff = state.bf00.Get(key0ff).ToObject();

				auto operations = state.b0ff.GetPropertyNames();
				for (size_t iOp = 0; iOp < operations.Length(); iOp++)
				{
					Napi::Value keyOp = operations.Get(iOp);
					auto opId = helpers::rt(keyOp.ToString());
					state.writer->StartArray(opId);

					// wants operation
					if (opId != MYLiterals::data)
					{
						auto trevs = state.b0ff.Get(keyOp).As<Napi::Array>();
						for (size_t i = 0; i < trevs.Length(); ++i)
						{
							state.writer->MYTRev(helpers::trev(trevs.Get(i).ToString()));
						}
					}
					// data operation
					else
					{
						auto objects = state.b0ff.Get(keyOp).As<Napi::Array>();
						for (size_t i = 0; i < objects.Length(); ++i)
						{
							helpers::encodeObject(state.writer, state.recordTypeId, objects.Get(i).ToObject());
						}
					}

					// { resources: { 0: { 0: { have: [ "xx" ]
					state.writer->EndArray();
				}
				state.writer->EndObject();
			}
			state.writer->EndObject();
		}
		state.writer->EndObject();
	}
	state.writer->EndObject();

	return Napi::Buffer<uint8_t>::New(
			env,
			(uint8_t *)writer->pbegin(),
			writer->psize(),
			helpers::freeWriter,
			writer);
}

Napi::Object decode_sync(Napi::Env env, const Napi::Buffer<uint8_t> &bjson)
{
	auto data = bjson.Data();
	auto size = bjson.Length();
	auto endPtr = data + size;

	MYBJsonIterator current = MYBJsonIterator(data, endPtr);
	MYBJsonIterator end(endPtr, endPtr);

	Napi::Object root = Napi::Object::New(env);
	Napi::Object recordType;
	Napi::Object bf00;
	Napi::Object b0ff;
	Napi::Array operations;

	int opKey = -1;
	// int iOp = -1;
	int recordTypeKey = -1;
	bool insideData = false;
	const int payload_level = 1;
	const int recordType_level = 2;
	const int bf00_level = 3;
	const int b0ff_level = 4;
	const int operation_level = 5;
	// const int object_level = 6;

	int openCount = 0;
	int closedCount = 0;
	int prevOpenCount = 0;
	int prevClosedCount = 0;

	while (current != end)
	{

		if (current->isScopeEnd())
		{
			closedCount++;
		}

		if (current->isScopeBegin())
		{
			openCount++;
		}

		int level = openCount - closedCount;

		if (openCount > prevOpenCount)
		{
			if (level == payload_level)
			{
				assert(current->type() == BJsonType::object);
				root = Napi::Object::New(env);
			}
			else if (level == recordType_level)
			{
				assert(current->type() == BJsonType::object);
				recordTypeKey = current->key();
				auto recordTypeKeyText = helpers::t(recordTypeKey);
				recordType = Napi::Object::New(env);
				root.Set(recordTypeKeyText, recordType);
			}
			else if (level == bf00_level)
			{
				assert(current->type() == BJsonType::object);
				auto bf00Key = current->key();
				bf00 = Napi::Object::New(env);
				recordType.Set(bf00Key, bf00);
			}
			else if (level == b0ff_level)
			{
				assert(current->type() == BJsonType::object);
				auto b0ffKey = current->key();
				b0ff = Napi::Object::New(env);
				bf00.Set(b0ffKey, b0ff);
			}
			else if (level == operation_level)
			{
				assert(current->type() == BJsonType::array);
				opKey = current->key();
				insideData = (opKey == MYLiterals::data);
				auto opKeyText = helpers::t(opKey);
				operations = Napi::Array::New(env);
				b0ff.Set(opKeyText, operations);
				if (insideData)
				{
					while ((++current)->type() != BJsonType::end)
					{
						auto obj = helpers::decodeObject(env, current, recordTypeKey);
						operations.Set(operations.Length(), obj);
					}
				}
				else
				{
					while ((++current)->type() != BJsonType::end)
					{
						assert(current->type() == BJsonType::trev);
						operations.Set(operations.Length(), helpers::trev64(current->asTrev()));
					}
				}
				prevClosedCount = closedCount;
				prevOpenCount = openCount;
				// do not advance the iterator
				continue;
			}
		}

		prevClosedCount = closedCount;
		prevOpenCount = openCount;
		current++;

		(void)prevClosedCount;
	}
	return root;
}
