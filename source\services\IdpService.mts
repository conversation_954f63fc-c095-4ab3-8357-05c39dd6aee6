"use strict";

import { AccountService } from "./AccountService.mjs";
import { AuthenticationService } from "./AuthenticationService.mjs";
import { Context } from "../system/Context.mjs";
import { Account } from "../models/Account.model.mjs";
import { error } from "../system/error.mjs";
import { ids } from "../system/Strings.mjs";
import { Token } from "../models/Token.mjs";
import { config, getServices } from "../system/Config.mjs";
import { makeError } from "../system/error.mjs";
import { JwksClient } from "jwks-rsa";
import jsonwebtoken from "jsonwebtoken";
import moment from "moment";
import { TokenService } from "./TokenService.mjs";
import { composeResult } from "../routes/authentication.routes.mjs";
import { LicenseService } from "./LicenseService.mjs";

interface IJWTConfig {
  validationOptions: jsonwebtoken.VerifyOptions;
  getPublicKey: jsonwebtoken.GetPublicKeyOrSecret;
}

class JWTConfigWithJWKS implements IJWTConfig {
  constructor(validationOptions: jsonwebtoken.VerifyOptions, jwksUrl: string) {
    this.validationOptions = validationOptions;
    this._jwksClient = new JwksClient({
      cache: true,
      cacheMaxEntries: 5,
      cacheMaxAge: moment.duration("PT10H").asMilliseconds(),
      jwksUri: jwksUrl,
    });
  }

  public validationOptions: jsonwebtoken.VerifyOptions;

  public getPublicKey(
    header: jsonwebtoken.JwtHeader,
    callback: jsonwebtoken.SigningKeyCallback
  ) {
    this._jwksClient.getSigningKey(header.kid, (err, key: any) => {
      var signingKey = key.publicKey || key.rsaPublicKey;
      callback(err, signingKey);
    });
  }

  private _jwksClient: JwksClient;
}

let JWTConfigs: { [key: string]: IJWTConfig } = {
  apple: new JWTConfigWithJWKS(
    {
      issuer: config.apple.authority,
      audience: config.apple.clientIds,
    },
    "https://appleid.apple.com/auth/keys"
  ),

  google: new JWTConfigWithJWKS(
    {
      issuer: ["accounts.google.com", "https://accounts.google.com"],
      audience: config.google.clientID,
    },
    "https://www.googleapis.com/oauth2/v3/certs"
  ),

  microsoft: new JWTConfigWithJWKS(
    {
      audience: config.microsoft.clientID,
    },
    "https://login.microsoftonline.com/common/discovery/v2.0/keys"
  ),
};

export class IdpService {
  constructor(
    private accountService: AccountService,
    private authenticationService: AuthenticationService,
    private tokenService: TokenService,
    private subscriptionService: LicenseService
  ) { }

  public async validateJWT(
    idp: string,
    token: string
  ): Promise<{ [key: string]: any }> {
    return new Promise((resolve, reject) => {
      let config: IJWTConfig = JWTConfigs[idp];
      jsonwebtoken.verify(
        token,
        config.getPublicKey.bind(config),
        config.validationOptions,
        (err, decoded: { [key: string]: any }) => {
          if (err || !decoded.sub || !decoded.aud) {
            reject(makeError(403, ids.INVALID_DATA));
            return;
          }
          resolve(decoded);
        }
      );
    });
  }

  public async subscribe(
    context: Context,
    idp: string,
    subId: string,
    email: string
  ) {
    let account = new Account({
      role: "user",
      email,
      idp,
    });
    this._prepAccount(account, subId);
    account = await this.subscriptionService.createAccountAndLicense(
      context,
      account
    );
    let rtokenString = this.tokenService.rtoken(context, account);
    let rtokenResult = this.tokenService.tryVerifyRToken(
      context,
      rtokenString,
      account
    );
    let token = this.tokenService.atoken(context, account, rtokenResult.token);
    return composeResult(context, token, account, rtokenString);
  }

  public async token(context: Context, subId: string, idp: string) {
    let aud = context.audience;
    let sub = this._generateSub(subId, idp);
    let account = await this.accountService.bySubAndIdp(context, sub, idp);
    let rtokenString = this.tokenService.rtoken(context, account);
    let rtokenResult = this.tokenService.tryVerifyRToken(
      context,
      rtokenString,
      account
    );
    let token = this.tokenService.atoken(context, account, rtokenResult.token);
    return composeResult(context, token, account, rtokenString);
  }

  private _prepAccount(account: Account, subId: string) {
    account.sub(this._generateSub(subId, account.idp()));
  }

  private _generateSub(subId: string, idp: string) {
    return `${subId}@${idp}.mylio.com`;
  }
}
