#include <node.h>
#include "MYTrev.h"
#include "bjson.h"
#include "MYLiterals.h"
#include "base64.h"
#include "MYHash.h"
#include "CppObject.h"
#include "CppArray.h"
#include "helpers.h"
#include "MYTrev.h"
#include "MYHash.h"

using v8::Array;
using v8::Date;
using v8::FunctionCallbackInfo;
using v8::Isolate;
using v8::Local;
using v8::Number;
using v8::Object;
using v8::String;
using v8::Value;

v8::Local<v8::String> CppArray::s(const std::string &value) const
{
	return String::NewFromUtf8(_isolate, value.c_str());
}

v8::Local<v8::Number> CppArray::n(double value) const
{
	return Number::New(_isolate, value);
}

CppArray::CppArray(v8::Isolate *isolate) : _isolate(isolate) {}
CppArray::CppArray(v8::Isolate *isolate, const v8::Local<v8::Value> &v8Object) : _v8Array(v8::Local<v8::Array>::Cast(v8Object)), _isolate(isolate) {}

void CppArray::wrap(const v8::Local<v8::Value> &v8Object)
{
	_v8Array = v8::Local<v8::Array>::Cast(v8Object);
}

v8::Local<v8::Array> CppArray::unwrap()
{
	return _v8Array;
}

void CppArray::New()
{
	_v8Array = v8::Array::New(_isolate);
}

void CppArray::addObject(CppObject &obj)
{
	int size = _v8Array->Length();
	_v8Array->Set(size, obj.unwrap());
}

void CppArray::addTRev(const MYTRev &trev)
{
	int size = _v8Array->Length();
	auto value64 = helpers::trev64(trev);
	_v8Array->Set(size, s(value64));
}

void CppArray::addHash(const MYHash &value)
{
	int size = _v8Array->Length();
	auto value64 = helpers::hash64(value);
	_v8Array->Set(size, s(value64));
}

void CppArray::addBinary(uint8_t *data, size_t size)
{
	auto value64 = base64_encode((unsigned char *)data, size);
	int arraySize = _v8Array->Length();
	_v8Array->Set(arraySize, s(value64));
}

void CppArray::addString(const std::string &value)
{
	int arraySize = _v8Array->Length();
	_v8Array->Set(arraySize, s(value));
}
