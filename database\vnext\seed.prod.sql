\c account0;

insert into a0.account(account_id, idp, sub)
values (nextval('a0.account_account_id'), 'system', 'system');

insert into a0.account_metadata(account_id)
select account_id from a0.account;

insert into a0.system_property(account_id, system_property_id, name, value, deleted)
values
(1, nextval('a0.system_property_system_property_id'), 'build-1', '{"deviceType":"1","buildNumber":1000,"uri":"https://s3/binary/1000/windows/setupMylio.exe"}', false),
(1, nextval('a0.system_property_system_property_id'), 'build-2', '{"deviceType":"2","buildNumber":2000,"uri":"https://s3/binary/2000/windows/setupMylio.zip"}', false),
(1, nextval('a0.system_property_system_property_id'), 'flickr', '{"key": "x", "secret": "y"}', false),
(1, nextval('a0.system_property_system_property_id'), 'buildOs33', '{"latestBuild":4719,"uri":"http://mylio-builds.s3.amazonaws.com/osx/4719/Mylio.app.zip"}', false),
(1, nextval('a0.system_property_system_property_id'), 'buildOs34', '{"latestBuild":4719,"uri":"http://mylio-builds.s3.amazonaws.com/windows/4719/SetupMylio.exe"}', false);

update a0.system_property set t = public.new_trev4(system_property_id, 9);

select * from a0.system_property_merkle(1);
