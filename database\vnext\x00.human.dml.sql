
alter table x0.x00 drop constraint if exists t_must_be_33_bytes;
alter table x0.x00 add constraint t_must_be_33_bytes check (length(t) = 33);
alter table x0.x00 alter column t set not null;

create or replace function x0.x00_merkle(__account_id int)
returns void
as
$$
    insert into x0.b0ff_metadata(account_id, b0ff, merkle)
        select __account_id, /* ~bucket */ 0 , digest(string_agg(t, null order by t), 'sha1' )
        from x0.x00 meta
        where meta.account_id = __account_id
        on conflict(account_id, b0ff) 
        do update set merkle = EXCLUDED.merkle;
$$
language sql;

create or replace function x0.x00_sync_data(__account_id int, _data_json json)
returns table (
    op int,
    t text,
    d text
)
as
$$
    truncate table __data;
    insert into __data(_account_id, _rid, _t, _d)
    select
        __account_id,
        substring(decode(t, 'base64') from 1 for 20),
        decode(t, 'base64') as _t,
        decode(d, 'base64') as _d 
    from json_to_recordset(_data_json) 
    as t(
        "accountId" int,
        "t" text,
        "d" text
    );

    update x0.x00 cloud
        set rid = _rid,
            t = _t,
            d = _d
    from __data client
    where account_id = _account_id 
    and _rid = rid
    and _t > t;

    insert into x0.x00 (account_id, rid, t, d)
    select _account_id, _rid, _t, _d
    from __data
    where not exists (
        select * 
        from x0.x00 
        where account_id = _account_id 
        and rid = _rid
    );

    select from x0.x00_merkle(__account_id);

    select
        2 as op,
        encode(t, 'base64'),
        encode(d, 'base64')
    from x0.x00 cloud
    join __data on _account_id = account_id 
    and rid = _rid
    where cloud.account_id = __account_id
    and _t < t;
$$
language sql;

create or replace function x0.x00_sync_have(__account_id int, _have_json json)
returns table (
    op int,
    t text,
    d text
)
as
$$
truncate table __have;
insert into __have(_account_id, _rid, _t)
select __account_id, substring(decode(t#>>'{}', 'base64') from 1 for 20) as _rid, decode(t#>>'{}', 'base64') as _t 
from json_array_elements(_have_json) as t;

select
    2 as op,
    encode(t, 'base64'),
    encode(d, 'base64')
from x0.x00 cloud
left join __have 
    on _account_id = account_id 
    and _rid = rid
where account_id = __account_id
and (_t < t or _t is null)

union all

select 
    3 as op,
	encode(_t, 'base64') as t,
	null
from __have
left join x0.x00 cloud 
on account_id = _account_id
and _rid = rid
where (t is null or t < _t);  

$$
language sql;


