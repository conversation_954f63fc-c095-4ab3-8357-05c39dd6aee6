"use strict";

import moment from "moment";
import crypto from "crypto";

import { Account, merge } from "../models/Account.model.mjs";
import { error, makeError } from "../system/error.mjs";
import { chooseHasher, makeSalt } from "../system/PasswordHashers.mjs";
import { AccountService } from "../services/AccountService.mjs";
import { IToken, Token } from "../models/Token.mjs";
import { Context } from "../system/Context.mjs";
import { EmailService } from "../services/EmailService.mjs";
import { ids } from "../system/Strings.mjs";
import { TokenService } from "./TokenService.mjs";
import { config, getServices } from "../system/Config.mjs";
import { CertificateService, ICertResponse } from "./CertificateService.mjs";
import { DeviceService } from "./DeviceService.mjs";
import { Pin } from "../models/Pin.model.mjs";
import { PinRestDataService } from "../dataServices/Pin.rest.dataService.mjs";
import { dbnull } from "../system/data.mjs";
import { LicenseService } from "./LicenseService.mjs";

export class AuthenticationService {
  constructor(
    private accountService: AccountService,
    private emailService: EmailService,
    private tokenService: TokenService,
    private deviceService: DeviceService,
    private pinDataService: PinRestDataService,
    private subscriptionService: LicenseService
  ) { }

  async checkPassword(context: Context, account: Account, password: string) {
    if (!password) {
      throw makeError(401, ids.AUTHENTICATION_FAILED);
    }
    let hasher = chooseHasher(account.passwordHashVersion());
    let result = await hasher(password, account.salt());
    if (result.passwordHash !== account.passwordHash().replace(/\n/g, "")) {
      throw makeError(401, ids.AUTHENTICATION_FAILED, "Invalid password");
    }
  }

  async sendResetPasswordLink(context, email) {
    let account = await this.accountService.bySubAndIdp(
      context,
      email,
      "mylio"
    );
    return this.emailService.sendResetPasswordLink(context, account);
  }

  async impersonate(context: Context, adminId: number, accountId: number) {
    const adminAccount = await this.accountService.read(context, adminId);
    const account = await this.accountService.read(context, accountId);
    const rtoken = this.tokenService.rtoken(context, account, adminAccount);
    const atoken = await this.atoken(context, rtoken, account);
    return { atoken, account };
  }

  async rtoken(
    context: Context,
    aud: string,
    sub: string,
    idp: string,
    password: string,
    pinNumber?: string,
    codeVerifier?: string
  ) {
    let pinValid = false;
    let account: Account;
    let passwordValid = false;

    if (!((codeVerifier && pinNumber) || password))
      throw ids.MISSING_PASSWORD_AND_PIN;
    let codeChallenge: string;
    let pin: Pin;
    if (codeVerifier) {
      codeChallenge = crypto
        .createHash("sha256")
        .update(codeVerifier)
        .digest("base64");
      pin = await this.pinDataService.readByEmailAndCodeChallenge(
        context,
        sub,
        codeChallenge
      );
      if (!pin)
        throw makeError(
          400,
          ids.CODE_VERIFIER_NOT_FOUND,
          "The pin has already been used"
        );

      if (pin.email() !== sub)
        throw makeError(400, ids.EMAIL_DOES_MATCH_CODE_VERIFIER);

      if (pin.pin() !== pinNumber)
        throw makeError(400, ids.PIN_DOES_NOT_MATCH_CODE_VERIFIER);
      else if (moment.utc().isAfter(pin.expiresAt()))
        throw makeError(400, ids.LOGIN_LINK_EXPIRED);

      pinValid = true;
      await this.pinDataService.deleteByEmailAndCodeChallenge(
        context,
        sub,
        codeChallenge
      );

      if (pin.createAccount()) {
        account = await this.accountService.tryBySubAndIdp(context, sub, idp);
        if (account)
          throw makeError(
            400,
            ids.DUPLICATE_SUB_AND_IDP,
            "Create account option set, but the account already exists"
          );
        let newAccount = new Account({
          idp,
          sub,
          planId: "1000",
          email: sub,
          password,
          role: "user",
        });
        await this.subscriptionService.createAccountAndLicense(
          context,
          newAccount
        );
      }
    }

    if (!account) {
      account = await this.accountService.tryBySubAndIdp(context, sub, idp);
      if (!account) {
        account = await this.accountService.tryByEmail(context, sub);
        if (account) {
          switch (account.idp()) {
            case "facebook":
              throw makeError(401, ids.THIS_IS_A_FACEBOOK_ACCOUNT);
            case "apple":
              throw makeError(401, ids.THIS_IS_AN_APPLE_ACCOUNT);
            case "microsoft":
              throw makeError(401, ids.THIS_IS_A_MICROSOFT_ACCOUNT);
            case "google":
              throw makeError(401, ids.THIS_IS_A_GOOGLE_ACCOUNT);
          }
        } else {
          throw makeError(404, ids.ACCOUNT_NOT_FOUND);
        }
      }
    }

    if (password) {
      await this.checkPassword(context, account, password);
      passwordValid = true;
    }

    if (account.tfa()) {
      if (!(pinValid && passwordValid))
        throw makeError(
          400,
          "TFA_INVALID_PIN_AND_PASSWORD",
          "Your account is configured to use two factor authentication, you must specify both a valid pin and password"
        );
    }

    let rtoken = await this.tokenService.rtoken(context, account);
    return {
      account,
      rtoken,
    };
  }

  async atoken(context: Context, rtokenString: string, account?: Account) {
    if (!rtokenString)
      throw makeError(401, ids.NOT_AUTHENTICATED, "RTOKEN not provided");

    let rtoken = Token.tryDecode(rtokenString);
    if (!rtoken)
      throw makeError(401, ids.NOT_AUTHENTICATED, "RTOKEN not decodable");

    if (!account)
      account = await this.accountService.read(context, rtoken.aid());

    if (!account)
      throw makeError(404, ids.ACCOUNT_DOES_NOT_EXIST_OR_ACCESS_DENIED);

    let result = this.tokenService.tryVerifyRToken(
      context,
      rtokenString,
      account
    );

    if (!result.success) {
      throw makeError(
        401,
        ids.NOT_AUTHENTICATED,
        "RTOKEN verify failed: " + result.error
      );
    }

    return this.tokenService.atoken(context, account, result.token);
  }

  async signin(
    context: Context,
    aud: string,
    sub: string,
    idp: string,
    password: string,
    pinNumber?: string,
    codeVerifier?: string
  ) {
    const { rtoken, account } = await this.rtoken(
      context,
      aud,
      sub,
      idp,
      password,
      pinNumber,
      codeVerifier
    );

    const token = await this.atoken(context, rtoken, account);

    return {
      account,
      rtoken,
      token,
    };
  }

  async refreshAccessToken(context: Context, rtokenString: string) {
    const rtoken = Token.tryDecode(rtokenString);
    if (!rtoken) throw makeError(401, ids.NOT_AUTHENTICATED, "Invalid RTOKEN");
    let account = await this.accountService.read(context, rtoken.aid());
    if (rtoken.sub() !== account.sub())
      throw makeError(401, ids.NOT_AUTHENTICATED, "Sub doesn't match");
    const { success } = this.tokenService.tryVerifyRToken(
      context,
      rtokenString,
      account
    );
    if (!success) throw makeError(401, ids.NOT_AUTHENTICATED, "Invalid RTOKEN");
    let token = await this.atoken(context, rtokenString, account);
    return { token, account };
  }

  sendResetEmail(context: Context, sub: string, idp: string) {
    return this.accountService.tryByEmail(context, sub).then((account) => {
      //sendResetPasswordLink will handle null account
      //do NOT return errors here to prevent email guessing
      return this.emailService.sendResetPasswordLink(context, account);
    });
  }

  resetPassword(context: Context, account: Account) {
    return this.accountService
      .read(context, account.accountId())
      .then((result) => {
        return this.accountService.update(context, account);
      });
  }

  async makeMagicLink(
    context: Context,
    email: string,
    target: string,
    query: string,
    protocol: string,
    account: Account
  ) {
    if (!account) {
      throw makeError(400, ids.ACCOUNT_NOT_FOUND, "Email not found");
    }
    let stoken = this.tokenService.shortLivedToken(
      context,
      account,
      config.magic_link_token_lifespan
    );
    let state = {
      email,
      target,
      query,
      protocol,
      stoken,
    };
    let link = `${config.cloud}/authorize-magic-link?s=${btoa(
      JSON.stringify(state)
    )}`;
    return link;
  }

  public async sendMagicLink(
    context: Context,
    email: string,
    target: string,
    query: string,
    protocol: string
  ) {
    let account = await this.accountService.byEmail(context, email);
    let link = await this.makeMagicLink(
      context,
      email,
      target,
      query,
      protocol,
      account
    );
    return this.emailService.sendMagicLink(context, account, link);
  }

  async makeCreateLink(
    context: Context,
    email: string,
    target: string,
    protocol: string,
    account: Account
  ) {
    if (account) {
      throw makeError(400, ids.DUPLICATE_SUB_AND_IDP);
    }
    let dummy = new Account({
      idp: "mylio",
      role: "user",
      sub: email,
      email,
      accountId: 0,
      planId: "1000",
    });
    let ctoken = this.tokenService.shortLivedToken(
      context,
      dummy,
      config.create_link_token_lifespan
    );
    let state = {
      email,
      target,
      protocol,
      ctoken,
    };

    let link = `${config.cloud}/authorize-create-link?s=${btoa(
      JSON.stringify(state)
    )}`;

    return link;
  }

  generatePin() {
    // Generate a random number between 0 and 999999
    const pin = crypto.randomInt(0, 999999).toString().padStart(6, "0");

    return pin;
  }

  async createPin(
    context: Context,
    email: string,
    target: string,
    query: string,
    protocol: string,
    codeChallenge: string,
    createAccount: boolean,
    password: string,
    account: Account
  ) {
    if (!account && !createAccount) {
      throw makeError(400, ids.ACCOUNT_NOT_FOUND, "Email not found");
    }
    if (account && createAccount) {
      throw makeError(
        400,
        "ACCOUNT_ALREADY_EXISTS",
        "the create account option was specified but the account already exists"
      );
    }
    let pin = new Pin({
      codeChallenge,
      email: email,
      pin: this.generatePin(),
      expiresAt: moment
        .utc()
        .add(moment.duration(config.login_link_token_lifespan))
        .toDate(),
    });
    pin.createAccount(createAccount);
    return this.pinDataService.create(context, pin);
  }

  public async sendPin(
    context: Context,
    email: string,
    target: string,
    query: string,
    protocol: string,
    codeChallenge: string,
    createAccount: boolean,
    password: string,
    website: string
  ) {
    let account = await this.accountService.tryByEmail(context, email);
    let pin = await this.createPin(
      context,
      email,
      target,
      query,
      protocol,
      codeChallenge,
      createAccount,
      password,
      account
    );

    let state = {
      email,
      target,
      protocol,
      pin: pin.pin(),
    };

    let link = `${website || config.website}/launch?${query ? `${query}&` : ``}s=${btoa(
      JSON.stringify(state)
    )}`;

    return this.emailService.sendPin(context, email, link, pin.pin());
  }

  async ptoken(context: Context, aud: string, account: Account) {
    let rtoken = this.tokenService.rtoken(context, account);
    let atoken = await this.atoken(context, rtoken);
    return {
      account,
      rtoken,
      token: atoken,
    };
  }

  public async sendCreateLink(
    context: Context,
    email: string,
    target: string,
    protocol: string
  ) {
    let account = await this.accountService.tryByEmail(context, email);
    let link = await this.makeCreateLink(
      context,
      email,
      target,
      protocol,
      account
    );
    return this.emailService.sendCreateAccountLink(context, email, link);
  }

  async authenticate(context: Context, account: Account, password: string) {
    if (!password) {
      throw makeError(401, ids.AUTHENTICATION_FAILED);
    }
    let hasher = chooseHasher(account.passwordHashVersion());
    let result = await hasher(password, account.salt());
    if (result.passwordHash !== account.passwordHash().replace(/\n/g, "")) {
      throw makeError(401, ids.AUTHENTICATION_FAILED, "Invalid password");
    }
  }

  async changePassword(context: Context, aid: number, changePassword: any) {
    let account = await this.accountService.read(context, aid);
    if (account.passwordHash() && !context.hasAdminRights()) {
      await this.authenticate(context, account, changePassword.password);
    }
    let pem = CertificateService.CreateSelfSignedRootForAccount(
      context,
      account
    );
    account.rsaPrivateKey(pem.keyPEM);
    account.x509Cert(pem.certificatePEM);
    if (changePassword.newPassword)
      account.password(changePassword.newPassword);
    else account.passwordHash(dbnull);
    account.salt(await makeSalt(undefined));
    return this.accountService.update(context, account);
  }

  async shortLivedTokenToRToken(context: Context, token: Token) {
    let account = await this.accountService.read(context, token.aid());
    return this.tokenService.rtoken(context, account);
  }

  async rtoken4stoken(context: Context, stoken: Token) {
    let account = await this.accountService.read(context, stoken.aid());
    let rtoken = this.tokenService.rtoken(context, account);
    let atoken = await this.atoken(context, rtoken, account);
    return {
      account,
      rtoken,
      token: atoken,
    };
  }

  async stoken4ctoken(context: Context, ctoken: Token) {
    let account = await this.accountService.tryByEmail(
      context,
      ctoken.sub().trim()
    );
    if (!account) {
      throw makeError(400, ids.ACCOUNT_NOT_FOUND);
    }
    let stoken = this.tokenService.shortLivedToken(
      context,
      account,
      config.magic_link_token_lifespan
    );
    return stoken;
  }

  async sendSsoToPinLink(context: Context, email: string) {
    const account = await this.accountService.read(
      context,
      context.token.aid()
    );
    const emailToken = this.tokenService.shortLivedToken(
      context,
      account,
      config.reset_password_link_lifespan
    );
    const link = `${config.cloud
      }/accounts/${account.accountId()}/sso-to-pin/${emailToken}?email=${encodeURIComponent(email)}`;
    this.emailService.sendSsoToPinLink(context, email, link, "mylio");
  }

  async ssoToPin(context: Context, email: string) {
    let account = await this.accountService.read(context, context.token.aid());
    if (!account) throw makeError(400, ids.ACCOUNT_NOT_FOUND);
    account.email(email);
    account.sub(email);
    account.idp("mylio");
    account.passwordHash(dbnull);
    account.salt(crypto.randomBytes(256).toString("base64"));
    this.accountService.update(context, account);
  }
}
