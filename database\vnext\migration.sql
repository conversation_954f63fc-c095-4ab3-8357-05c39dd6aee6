-- Enable the postgres_fdw extension in the current database
CREATE EXTENSION IF NOT EXISTS postgres_fdw;

-- Create a foreign server object that points to the remote database "a0"
CREATE SERVER old
  FOREIGN DATA WRAPPER postgres_fdw
  OPTIONS (host 'account-production-0.c04zdcgu39tj.us-west-2.rds.amazonaws.com', port '5432', dbname 'account0');

-- Create a user mapping so that the local role "superuser" can connect to the foreign server
CREATE USER MAPPING FOR superuser
  SERVER old
  OPTIONS (user 'superuser', password 'dFnrG8pe2B03iY6sVGqJ');

-- Create a local schema "remote" to hold the imported foreign tables
CREATE SCHEMA IF NOT EXISTS p0_a0;

-- Import the foreign schema "a0" from the remote database into the local schema "remote"
IMPORT FOREIGN SCHEMA a0
  FROM SERVER old
  INTO p0_a0;

  
 -- Create a foreign server object that points to the remote database "a0"
CREATE SERVER dw
  FOREIGN DATA WRAPPER postgres_fdw
  OPTIONS (host '***********', port '5432', dbname 'datawarehouse');

-- Create a user mapping so that the local role "superuser" can connect to the foreign server
CREATE USER MAPPING FOR superuser
  SERVER dw
  OPTIONS (user 'superuser', password 'jidz3Zktt93Bnw693o3qBRZG');

-- Create a local schema "remote" to hold the imported foreign tables
CREATE SCHEMA IF NOT EXISTS fast_spring;

-- Import the foreign schema "a0" from the remote database into the local schema "remote"
IMPORT FOREIGN SCHEMA fast_spring
  FROM SERVER dw
  INTO fast_spring;
  
truncate table a0.account;
INSERT INTO a0.account(
	flags, modified_time, created_time, account_id, t, cipher, min_build, peer_to_peer_key, rsa_private_key, x509_cert, tfa, idp, sub, email, password_hash, password_hash_version, salt, password_set_time, plan_id, role, device_limit, photo_limit, cloud_storage_limit, features, next_plan_date, affiliate_id)
select
	flags, modified_time, created_time, account_id, t, cipher, min_build, peer_to_peer_key, rsa_private_key, x509_cert, tfa, idp, sub, email, password_hash, password_hash_version, salt, password_set_time, plan_id, role, device_limit, photo_limit, cloud_storage_limit, features, next_plan_date, affiliate_id
from p0_a0.account;

truncate table a0.account_metadata;
insert into a0.account_metadata
	(flags, modified_time, created_time, account_id, account_merkle, device_merkle, message_merkle, system_property_merkle, user_property_merkle, device_data_merkle, next_device_id, next_message_id, bootstrap_device_id, next_system_property_id)
select 	flags, modified_time, created_time, account_id, account_merkle, device_merkle, message_merkle, system_property_merkle, user_property_merkle, device_data_merkle, next_device_id, next_message_id, bootstrap_device_id, next_system_property_id
from p0_a0.account_metadata;

truncate table a0.backblaze_info;
INSERT INTO a0.backblaze_info(
	flags, account_id, region, service, info, expires)
select 
	flags, account_id, region, service, info, expires
from p0_a0.backblaze_info;

truncate table a0.device;
INSERT INTO a0.device(
	flags, modified_time, created_time, account_id, deleted, t, d, device_id, name, device_type, nickname, encrypt, creation_time, long_id, support_ticket)
select
	flags, modified_time, created_time, account_id, deleted, t, d, device_id, name, device_type, nickname, encrypt, creation_time, long_id, support_ticket
from p0_a0.device;

truncate table a0.device_data;
INSERT INTO a0.device_data(
	flags, modified_time, created_time, account_id, deleted, t, d, device_id, build, last_access_time, os, media_count, protocol_version, version, last_startup_time, last_hid_time, last_import_time, original_size, local_original_size)
select 
	flags, modified_time, created_time, account_id, deleted, t, d, device_id, build, last_access_time, os, media_count, protocol_version, version, last_startup_time, last_hid_time, last_import_time, original_size, local_original_size 
from p0_a0.device_data;

truncate table a0.lock;
INSERT INTO a0.lock(
	account_id, key, ticket, expire)
select account_id, key, ticket, expire
from p0_a0.lock;

truncate table a0.message;
INSERT INTO a0.message(
	flags, modified_time, created_time, account_id, deleted, t, d, device_id, message_id, seconds_to_display, displayed, message, link)
select flags, modified_time, created_time, account_id, deleted, t, d, device_id, message_id, seconds_to_display, displayed, message, link
from p0_a0.message;

truncate table a0.pin;
INSERT INTO a0.pin(
	flags, modified_time, created_time, code_challenge, email, pin, expires_at)
select flags, modified_time, created_time, code_challenge, email, pin, expires_at
from p0_a0.pin;

truncate table a0.refresh_token;
INSERT INTO a0.refresh_token(
	flags, modified_time, created_time, account_id, idp, task, sub, token, email)
select flags, modified_time, created_time, account_id, idp, task, sub, token, email
from p0_a0.refresh_token;
	
truncate table a0.system_property;
INSERT INTO a0.system_property(
	flags, modified_time, created_time, account_id, deleted, t, d, system_property_id, name, value)
select flags, modified_time, created_time, account_id, deleted, t, d, system_property_id, name, value
	from p0_a0.system_property;
	
truncate table a0.user_property;
INSERT INTO a0.user_property(
	flags, modified_time, created_time, account_id, deleted, t, d, user_property_id, name, value)
select 	flags, modified_time, created_time, account_id, deleted, t, d, user_property_id, name, value
from p0_a0.user_property;

DROP INDEX IF EXISTS idx_license_first24; 
CREATE INDEX idx_license_first24
ON a0.license ((left(license_id, 24)));

truncate table a0.license;
INSERT INTO a0.license(
	modified_time, created_time, license_id, account_id, manager, end_date, status, activation_key, template_id)
select
	modified_time, 
	created_time, 
	license_id, 
	account_id, 
	manager, 
	end_date,
	case 
		when deleted IS TRUE then 2
		when canceled IS TRUE then 3
		when trial IS TRUE then 4
		else 1
	end, 
	activation_key,
	case template_id
		when '1000' then 'free'
		when 'create_trial' then 'personal'
		when 'storage-2tb' then 'storage-2tb'
		when 'storage-5tb' then 'storage-5tb'
		when 'trial' then 'personal'
		when 'vip' then 'personal-group'
		when 'trial_extension' then 'personal'
		else 
			'personal'
	end
from p0_a0.license
where template_id not in ('auto', 'test', 'trial_1d', 'stripe-plus', 'stripe-create') and manager not in ('P', 'S');

drop table if exists fastsubs;
create temp table fastsubs(license_id text primary key, email text, real_sku text, state int, end_date timestamptz);
insert into fastsubs(license_id, email, real_sku, state, end_date)
select 
	'F.' || s.subscription_id, 
	email, 
	case  
		when s.real_sku in('business-group', 'personal-group', 'storage-5tb', 'storage-2tb')  then s.real_sku
		else 'personal'
	end,
	case s.state 
			when 'trial' then 4
			when 'active' then 1
			when 'canceled' then 3
			when 'deactivated' then 2
			else 1
		end, 
	coalesce(s.end_time, s.next_charge_time, now())
from fast_spring.subscription s
join fast_spring.account a on a.account_id = s.account_id
where live = true;

update a0.license l
	set status = f.state,
		end_date = f.end_date	
from fastsubs f
where f.license_id = left(l.license_id, 24);

select a0.recompute_licenses_for_all_accounts();

SELECT setval('a0.account_account_id', (SELECT MAX(account_id) FROM a0.account));
SELECT setval('a0.message_message_id', (SELECT MAX(message_id) FROM a0.message));
