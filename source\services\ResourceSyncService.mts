import { Bucket } from "../system/merkle.mjs";
import { query, cs, squery } from "../system/Postgres.mjs";
import { Context } from "../system/Context.mjs";
import { Bitset } from "../system/Bitset.mjs";
import _ = require("lodash");
import crypto = require("crypto");
import { error } from "../system/error.mjs";

interface ISyncResult {
  op: number;
  t: string;
  d: string;
}

const HASH_SIZE = 20;
const BLOB_SIZE = 5120;
let emptyBuffer = Buffer.alloc(BLOB_SIZE, 0);

let pingBuckets: Bucket[] = [];
for (let i = 0; i < 16; i++) {
  pingBuckets.push(new Bucket(i, 0));
}

interface IDeviceLock {
  bf00?: number;
  b16Hash?: number;
  bdlHash?: number;
  ticket?: string;
  timeout?: string;
  mustCheckHash?: boolean;
}

export class ResourceSyncService {
  public lockDevice(
    context: Context,
    accountId: number,
    deviceId: number,
    bf00: number,
    lock: IDeviceLock
  ): Promise<any> {
    if (lock.mustCheckHash) {
      if (!(lock.b16Hash || lock.bdlHash))
        return error<{ ticket: string }>(400, "MUST_PROVIDE_HASH");
    }

    if (lock.ticket && lock.ticket.length > 50)
      return error<{ ticket: string }>(400, "TICKET_EXCEEDS_50_CHARS");

    if (isNaN(bf00))
      return error<{ ticket: string }>(400, "BFOO_MUST_BE_A_NUMBER");
    let bucket = new Bucket(bf00, 0);

    if (!lock.ticket) lock.ticket = crypto.randomBytes(20).toString("base64");
    return query<any>(
      context,
      `select * from ${bucket.schema()}.lock_device($1,$2,$3,$4,$5,$6) as ticket`,
      [
        accountId,
        deviceId,
        lock.b16Hash,
        lock.bdlHash,
        lock.ticket,
        lock.timeout,
      ],
      cs(context, bucket.schema())
    )
      .then((rows) => {
        return { success: true, ticket: lock.ticket };
      })
      .catch((err) => {
        if (err && err.code === "P0001")
          return { success: false, message: err.message };
        else return error(400, err.message);
      });
  }

  public unlockDevice(
    context: Context,
    accountId: number,
    deviceId: number,
    bf00: number,
    lock: IDeviceLock
  ): Promise<any> {
    if (isNaN(bf00)) return error<any[]>(400, "BFOO_MUST_BE_A_NUMBER");
    let bucket = new Bucket(bf00, 0);

    return query<any>(
      context,
      `select ${bucket.schema()}.unlock_device($1,$2,$3,$4,$5) as success`,
      [accountId, deviceId, lock.b16Hash, lock.bdlHash, lock.ticket],
      cs(context, bucket.schema())
    )
      .then(() => {
        return { success: true, message: "Success" };
      })
      .catch((err) => {
        if (err && err.code === "P0001")
          return { success: false, message: err.message };
        else return error(400, err.message);
      });
  }

  public ping(context: Context, aid: number, ping: { resources: string[] }) {
    type R = { nf00: number; merkle: string };
    let sql =
      "select <nf00> nf00, encode(merkle, 'base64') merkle from <schema>.bf00_metadata where account_id = $1";
    return squery<R>("ping", context, sql, [aid]).then((rows) => {
      let bitset = new Bitset(16);
      for (let i = 0; i < ping.resources.length; i++) {
        let row = rows.find((r) => r.nf00 === i);
        if (
          ping.resources[i] !==
          ((row && row.merkle) || "7I2NsHrOIa4BTE19vkIpff5hl2o=")
        ) {
          bitset.set(i);
        }
      }
      return { resources: bitset.toInt16() };
    });
  }

  public ping256(context: Context, aid: number, ping256In: { resources: any }) {
    type R = { nf00: number; blob: string };
    let nf00s = Object.keys(ping256In.resources).map((sf00) =>
      parseInt(sf00, 10)
    );
    let resources = {} as any;
    return squery<R>(
      "",
      context,
      "select <nf00> nf00, blob from <schema>.ping256($1)",
      [aid],
      nf00s
    ).then((rows) => {
      for (let sf00 of Object.keys(ping256In.resources)) {
        let nf00 = parseInt(sf00, 10);
        let row = rows.find((r) => r.nf00 === nf00);
        let cloudBlob =
          (row && row.blob && Buffer.from(row.blob, "base64")) || emptyBuffer;
        let clientBlob = Buffer.from(ping256In.resources[sf00], "base64");
        let ibit = 0;
        let bitset = new Bitset(256);
        for (let i0ff = 0; i0ff < BLOB_SIZE; i0ff += HASH_SIZE) {
          if (
            clientBlob
              .slice(i0ff, i0ff + HASH_SIZE)
              .compare(cloudBlob.slice(i0ff, i0ff + HASH_SIZE)) !== 0
          )
            bitset.set(ibit);
          ibit++;
        }
        resources[sf00] = bitset.toString("base64");
      }
      return { resources };
    });
  }

  public sync(context: Context, aid: number, input: any) {
    let queries = [];
    let resources = input.resources;
    let bf00s = Object.keys(resources)
      .map((sf00) => parseInt(sf00, 10))
      .map((nf00) => new Bucket(nf00, 0));

    let output = {} as any;

    for (let bf00 of bf00s) {
      let b0ffs = Object.keys(resources[bf00.sf00()])
        .map((s0ff) => parseInt(s0ff, 10))
        .map((n0ff) => new Bucket(bf00.nf00(), n0ff));

      let commands = b0ffs.map((b0ff) => {
        let obj0ff = resources[bf00.sf00()][b0ff.s0ff()];
        return {
          n0ff: b0ff.n0ff(),
          table: b0ff.fqT(),
          data: obj0ff.data,
          have: obj0ff.have,
        };
      });

      let connectionString = cs(context, bf00.schema());

      queries.push(
        query(
          context,
          `select * from sync($1,$2,$3)`,
          [aid, bf00.schema(), JSON.stringify(commands)],
          connectionString
        )
          .then(
            (results: { n0ff: number; op: number; t: string; d: string }[]) => {
              if (results.length === 0) return;
              let objf00 = {} as any;
              output[bf00.sf00()] = objf00;
              let groups0ff = _.groupBy(results, "n0ff");
              for (let k0ff of Object.keys(groups0ff)) {
                let g0ff = groups0ff[k0ff];
                let obj0ff = {} as any;
                objf00[k0ff] = obj0ff;
                let want = g0ff.filter((r) => r.op === 3).map((r) => r.t);
                let data = g0ff
                  .filter((r) => r.op === 2)
                  .map((r) => {
                    return { t: r.t, d: r.d };
                  });
                if (want && want.length > 0) obj0ff.want = want;
                if (data && data.length > 0) obj0ff.data = data;
              }
            }
          )
          .catch((err) => {
            if (
              err &&
              err.code === "55P03" &&
              err.message ===
                'could not obtain lock on row in relation "bf00_metadata"'
            )
              return error(
                429,
                "B16_LOCKED",
                "Another device is already syncing with this shard"
              );
            else return Promise.reject(err);
          })
      );
    }

    return Promise.all(queries).then(() => {
      return { resources: output };
    });
  }
}
