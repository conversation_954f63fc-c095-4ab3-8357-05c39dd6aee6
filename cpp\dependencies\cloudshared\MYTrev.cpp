#if defined(WIN32)
#pragma comment(lib, "ws2_32.lib")
#endif

#include <cassert>
#include <chrono>
#include <thread>
#include <atomic>
#include <cstring>
#include <cstddef>

#include "util/date.h"
#include "MYHash.h"
#include "MYTrev.h"
#include "MYStringUtil.h"

#if defined(MYLIO_CLIENT)
#include "MYResourceTypes.h"
#else
#if defined(_MSC_VER)
#include <winsock2.h>
#else
#include <arpa/inet.h>
#endif
#endif

#if defined(__ANDROID__)
#include <arpa/inet.h>
#endif

#include <limits>

using namespace date;

auto unix_epoch = sys_days{1_d / 1 / 1970};
auto mylio_epoch = sys_days{1_d / 1 / 2012};
auto mylio_cutoff = sys_days{31_d / 12 / 2069};

MYTRev::MYTRev()
{
    static_assert(sizeof(MYTRev) == 33, "TRev size unexpected - check your compiler settings");
    clear();
}

MYTRev::MYTRev(no_init_t)
{
    // Special use garbage memory constructor
#if defined(DEBUG) || defined(_DEBUG)
    memset(this, 0xcd, sizeof(MYTRev));
#endif
}

MYTRev::MYTRev(const MYHashStorage &rid, const MYTRevKindAndVersion &version)
{
    this->setHashId(rid, version.kindCode);
    setTRevKindAndVersion(version);

    assert(isValid());
}

MYTRev::MYTRev(const MYHash &rid, const MYTRevKindAndVersion &version)
{
    this->setHashId(rid.raw, version.kindCode);
    setTRevKindAndVersion(version);

    assert(isValid());
}

MYTRev::MYTRev(const MYHash &rid, KindCode_t kindCode, MYDeviceId deviceId)
{
    this->setHashId(rid.raw, kindCode);
    this->setOrev(1);
    this->setMylioTime(getNewTimeStamp());
    this->setDeviceId(deviceId);

    assert(isValid());
}

MYTRev::MYTRev(uint32_t id, KindCode_t kindCode, MYDeviceId deviceId)
{
    this->setUInt32Id(id, kindCode);
    this->setOrev(1);
    this->setMylioTime(getNewTimeStamp());
    this->setDeviceId(deviceId);

    assert(isValid());
}

MYTRev::MYTRev(unsigned char *blob)
{
    memcpy(this, blob, sizeof(MYTRev));
    assert(isValid());
}

MYTRev::MYTRev(const std::string &s, SerializationFlags flags) : MYTRev(s, (flags & SerializationFlags::Base64Hash))
{
}

MYTRev::MYTRev(const std::string &s, bool base64 /* = false*/)
{
    if (s.empty())
    {
        clear();
    }

    if (base64)
    {
        size_t trevSize = sizeof(MYTRev);
        bool success = base64_decode(s.data(), s.length(), this, &trevSize);
        if (success && trevSize == sizeof(MYTRev))
        {
            assert(isValid());
            return;
        }

        assert(false);
        clear();
    }

    unsigned char *raw = (unsigned char *)this;
    const char *chars = s.c_str();
    assert(s.size() == sizeof(MYTRev) * 2);
    for (size_t i = 0; i < sizeof(MYTRev); i++)
    {
        raw[i] = MYString::hex2byte(chars + i * 2);
    }

    assert(isValid());
}

MYTRev MYTRev::next(MYDeviceId deviceId, uint32_t incrementOrevBy /*= 1*/) const
{
    MYTRev newTrev(no_init); // Garbage initialized
    newTrev._id_storage = this->_id_storage;

    auto orev = this->getOrev();
    if (incrementOrevBy && (orev < 0xFFFFFFFF))
    {
        newTrev.setOrev(orev + incrementOrevBy);
    }
    else
    {
        newTrev._orev_big_endian = this->_orev_big_endian;
    }

    auto updatedTime = getUpdatedTimeStamp(getMylioTime());
    newTrev.setMylioTime(updatedTime);
    newTrev.setDeviceId(deviceId);

    assert(newTrev.isValid());

    return newTrev;
}

void MYTRev::clear()
{
    memset(this, 0, sizeof(MYTRev));
}

bool MYTRev::empty() const
{
    return *this == g_emptyTRev;
}

void MYTRev::setHashId(const MYHashStorage &hashId, uint8_t kindCode)
{
    static_assert(sizeof(MYHashStorage) == sizeof(_hashId), "Doesn't fit");

    memcpy(&_hashId[0], &hashId[0], sizeof(_hashId));
    _kindCode = kindCode;
}

MYHash MYTRev::getHashId() const
{
    MYHash hash(no_init);
    memcpy(&hash.raw[0], &_hashId[0], sizeof(_hashId));

    hash.resetsetbit();
    return hash;
}

void MYTRev::setUInt32Id(uint32_t newId, uint8_t newKindCode)
{
    _uint32id_little_endian = newId;
    _padding1 = 0;
    _padding2 = 0;
    _padding3 = 0;
    _kindCode = newKindCode;
}

uint32_t MYTRev::getUInt32Id() const
{
    assert(_padding1 == 0);
    assert(_padding2 == 0);
    assert(_padding3 == 0);
    return _uint32id_little_endian;
}

std::array<uint8_t, 20> MYTRev::getRawId() const
{
    return _id_storage;
}

void MYTRev::setTRevKindAndVersion(const MYTRevKindAndVersion &newVersion)
{
    static_assert(sizeof(MYTRev) - offsetof(MYTRev, _orev_big_endian) == sizeof(MYTRevKindAndVersion), "MYTRevKindAndVersion won't fit!");
    memcpy(&_orev_big_endian, &newVersion, sizeof(MYTRevKindAndVersion));
}

const MYTRevKindAndVersion &MYTRev::getTRevKindAndVersion() const
{
    static_assert(sizeof(MYTRev) - offsetof(MYTRev, _orev_big_endian) == sizeof(MYTRevKindAndVersion), "MYTRevKindAndVersion won't fit!");
    return *(MYTRevKindAndVersion *)&_orev_big_endian;
}

KindCode_t MYTRev::getKindCode() const
{
    return (KindCode_t)_kindCode;
}

void MYTRev::setKindCode(KindCode_t kindCode)
{
    _kindCode = (uint8_t)kindCode;
}

void MYTRev::setMylioTime(uint32_t timeValue)
{
    assert(timeValue < 0x10000000);
    _mylioTime_big_endian = htonl(timeValue);
}

uint32_t MYTRev::getMylioTime() const
{
    return ntohl(_mylioTime_big_endian);
}

uint32_t MYTRev::getUnixTime() const
{
    auto mylio_unix_diff = mylio_epoch - unix_epoch;

    auto mylioTime = getMylioTime() + std::chrono::duration_cast<std::chrono::seconds>(mylio_unix_diff).count();
    return (uint32_t)mylioTime;
}

void MYTRev::setOrev(uint32_t orev)
{
    _orev_big_endian = htonl(orev);
}

uint32_t MYTRev::getOrev() const
{
    return ntohl(_orev_big_endian);
}

void MYTRev::setDeviceId(MYDeviceId deviceId)
{
    _deviceId_big_endian = htonl(deviceId.toInt());
}

MYDeviceId MYTRev::getDeviceId() const
{
    return MYDeviceId::fromInt(ntohl(_deviceId_big_endian));
}

signed int MYTRev::quickRevisionCompare(const MYTRev &other) const
{
    assert(other._hashId == _hashId);

    const uint8_t *startMine = (const uint8_t *)&this->_orev_big_endian;
    const uint8_t *startTheirs = (const uint8_t *)&other._orev_big_endian;

    static_assert(sizeof(MYTRev) - offsetof(MYTRev, _orev_big_endian) == sizeof(MYTRev) - sizeof(_hashId), "Compare doesn't fit!");

    assert(startMine == (sizeof(_hashId) + (uint8_t *)this));
    static_assert(sizeof(MYTRev) - sizeof(_hashId) == 13, "unexpected size");

    return memcmp(startMine, startTheirs, sizeof(MYTRev) - sizeof(_hashId));
}

bool MYTRev::operator<(const MYTRev &other) const
{
    return memcmp(this, &other, sizeof(MYTRev)) < 0;
}

bool MYTRev::operator==(const MYTRev &other) const
{
    return memcmp(this, &other, sizeof(MYTRev)) == 0;
}

bool MYTRev::operator!=(const MYTRev &other) const
{
    return !(*this == other);
}

MYTRev MYTRev::fromString(const std::string &s, SerializationFlags flags)
{
    return fromString(s, (flags & SerializationFlags::Base64Hash) != 0);
}

MYTRev MYTRev::fromString(const std::string &s, bool base64 /* = false*/)
{
    MYTRev trev(s, base64);
    assert(trev.isValid());
    return trev;
}

std::string MYTRev::toSQLString() const
{
    return "x'" + toString() + "'";
}

std::string MYTRev::toString(SerializationFlags flags) const
{
    return toString((flags & SerializationFlags::Base64Hash) != 0);
}

std::string MYTRev::toString(bool base64 /* = false*/) const
{
    assert(this->getOrev() != 0xFFFFFFFF);
    if (base64)
    {
        return base64_encode((unsigned char *)this, sizeof(MYTRev));
    }

    return MYString::blobToHexString((unsigned char *)this, sizeof(MYTRev));
}

std::string MYTRev::toTraceString() const
{
    assert(this->getOrev() != 0xFFFFFFFF);
    std::stringstream ss;

    ss << MYString::blobToHexString((unsigned char *)this, sizeof(MYHash));
    ss << "-" << MYString::blobToHexString((unsigned char *)&this->_orev_big_endian, sizeof(this->_orev_big_endian));
    ss << "-" << MYString::blobToHexString((unsigned char *)&this->_mylioTime_big_endian, sizeof(this->_mylioTime_big_endian));
    ss << "-" << MYString::blobToHexString((unsigned char *)&this->_deviceId_big_endian, sizeof(this->_deviceId_big_endian));
    ss << "-" << MYString::blobToHexString((unsigned char *)&this->_kindCode, sizeof(this->_kindCode));

    return ss.str();
}

bool MYTRev::isValid() const
{
    if (getOrev() > 0x01000000)
        return false;

    if (_padding1 == 0xcdcdcdcd)
    {
        return false;
    }

    return true;
}

static uint32_t millisecond_since_midnight()
{
    uint64_t millisecondsSinceEpoch = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now() - mylio_epoch).count();
    uint32_t millisecondsSinceMidnight = (uint32_t)(millisecondsSinceEpoch / 86400000);
    return millisecondsSinceMidnight;
}

static uint32_t millisecond_since_yesterday_midnight()
{
    uint64_t millisecondsSinceEpoch = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now() - mylio_epoch).count();
    uint32_t millisecondsSinceMidnight = ((uint32_t)(millisecondsSinceEpoch / 86400000)) + 86400000;
    return millisecondsSinceMidnight;
}

uint32_t MYTRev::getNewTimeStamp()
{
    auto now = std::chrono::system_clock::now();
#ifdef MYLIO_CLIENT
    if (now < mylio_epoch)
    {
        return millisecond_since_midnight();
    }
    else if (now > mylio_cutoff)
    {
        return millisecond_since_yesterday_midnight();
    }
#endif // MYLIO_CLIENT
    return (uint32_t)std::chrono::duration_cast<std::chrono::seconds>(now - mylio_epoch).count();
}

uint32_t MYTRev::getUpdatedTimeStamp(uint32_t currentTimeStamp)
{
    uint32_t previousTimeStamp = currentTimeStamp;
    uint32_t newTimeStamp = getNewTimeStamp();

    if (newTimeStamp > previousTimeStamp)
    {
        return newTimeStamp;
    }

    return previousTimeStamp + 1;
}

bool MYTRevKindAndVersion::empty() const
{
    return memcmp(this, &g_emptyKindAndVersion, sizeof(MYTRevKindAndVersion)) == 0;
}

void MYTRevKindAndVersion::clear()
{
    *this = g_emptyKindAndVersion;
}

std::string MYTRevKindAndVersion::toSQLString() const
{
    auto hexString = MYString::blobToHexString((unsigned char *)this, sizeof(MYTRevKindAndVersion));
    return "x'" + hexString + "'";
}

MYTRevLookupRange::MYTRevLookupRange(const MYHash hash, KindCode_t kindCode) : greaterThanOrEqual(no_init), smallerThanOrEqual(no_init)
{
    bucket = hash.getBucket();

    memset(&smallerThanOrEqual, 0, sizeof(MYTRev));
    smallerThanOrEqual.setHashId(hash.raw, kindCode);

    memset(&greaterThanOrEqual, 0xFF, sizeof(MYTRev));
    greaterThanOrEqual.setHashId(hash.raw, kindCode);
}
