# Cloud v3

Mylio backend services

## Setup:

### Clone the repository

```sh
$ <NAME_EMAIL>:mylollc/cloudv3.git
$ cd cloudv3
```

### Install and run Postgres

#### OSX:

1. [Install homebrew](https://brew.sh/)
2. Install Postgres:

```sh
$ brew install postgres
$ brew services start postgresql
```

#### Windows:

https://www.postgresql.org/download/windows/

### Create postgres user

```sh
$ createuser -s postgres
```

OR use something like [PGAdmin](https://www.pgadmin.org/)

### Test your Postgres connection (note: version may differ)

```sh
$ psql -U postgres

psql (9.5.4)
Type "help" for help.

postgres=#
```

OR use something like [PGAdmin](https://www.pgadmin.org/)

### Install Python 2.7.x

Get Python 2 version 2.7+ from https://www.python.org/downloads/.

### Install Node.js

Get the latest LTS version of Node from https://nodejs.org/en/ OR
use something like [nvm](https://github.com/creationix/nvm).

### Install and build the cloud services

```sh
npm run init
```

## Set up your local config

To take full advantage of the cloud on a local machine, you will need a local config file. This file contains a number of secret keys and customizations and for obvious security reasons we do not publish it to GitHub.

The file can be found on the **MyloBigNAS** at `shared/Cloud/local.json`. Place this file in your **cloudv3** repo under `config/local.json`

## Run the cloud services:

If you'd like to run all three cloud services, open three separate terminal windows and run:

### Account

```sh
$ npm run start:account:localhost
```

### Telemetry

```sh
$ npm run start:telemetry:localhost
```

### Resource

```sh
$ npm run start:resource:localhost
```

## Run the tests

Once the services are up and running, you can run the tests with `npm test`.

## Deployment

### Services

Service deployment is a two part process. Step one is to package the service for delivery to a specific environment with a `publish` step. Step two is to deploy the published package to the environment with a `deploy` step.

#### Dependencies

In addition to all of the server dependencies, You will need the following software installed in order to deploy the cloud services:

- [Python 2 version 2.6.5+ or Python 3 version 3.3+](https://www.python.org/downloads/)
- [aws cli](https://docs.aws.amazon.com/cli/latest/userguide/cli-chap-install.html)
- [elastic beanstalk cli](https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/eb-cli3-install.html)

> NOTE: If you need to manage multiple Python installations, I would recommend using [pyenv](https://github.com/pyenv/pyenv)

#### Publishing

To publish a given service on a specific environment you will run `npm run publish:[service]:ebs:<env>`. This will create a folder under `../publications/cloudv3/ebs` for the corresponding `service` and `env`. The following are all the possible commands:

| Service   | Environment | Command                              | Out Dir (relative to `cloudv3` repo)        |
| --------- | ----------- | ------------------------------------ | ------------------------------------------- |
| Account   | Test        | `npm run publish:account:ebs:test`   | `../publications/cloudv3/ebs/accounttest`   |
| Account   | Prod        | `npm run publish:account:ebs`        | `../publications/cloudv3/ebs/account`       |
| Telemetry | Test        | `npm run publish:telemetry:ebs:test` | `../publications/cloudv3/ebs/telemetrytest` |
| Telemetry | Prod        | `npm run publish:telemetry:ebs`      | `../publications/cloudv3/ebs/telemetry`     |
| Resource  | Test        | `npm run publish:resource:ebs:test`  | `../publications/cloudv3/ebs/resourcetest`  |
| Resource  | Prod        | `npm run publish:resource:ebs`       | `../publications/cloudv3/ebs/resource`      |

#### Deploying

After you have run a `publish` task from above, head to the proper `Out Dir` and run `eb deploy`. This will pushed the packaged service up to the proper AWS Elastic Beankstalk environment.

That's it!

### Lambdas

Similar to service deployment, aws lambda deployment also follows a two-step `publish` then `deploy` process.

#### Publishing

To publish a given lambda you will run `npm run publish:[lambda]:lambda`. This will create folder under `../publications/cloudv3/lambda` for the corresponding
`lambda`. The following are all the possible commands:

| Lambda        | Command                                | Out Dir (relative to `cloudv3` repo)           |
| ------------- | -------------------------------------- | ---------------------------------------------- |
| HealthChecker | `npm run publish:healthchecker:lamdba` | `../publications/cloudv3/lambda/healthchecker` |
| LogScraper    | `npm run publish:logscraper:lambda`    | `../publications/cloudv3/lambda/logscraper`    |
| PGRunner      | `npm run publish:pgrunner:lambda`      | `../publications/cloudv3/lambda/pgrunner`      |
| EventScraper  | `npm run publish:eventscraper:lambda`  | `../publications/cloudv3/lambda/eventscraper`  |
| EventHandler  | `npm run publish:eventhandler:lambda`  | `../publicatoins/cloudv3/lambda/eventhandler`  |
| LogStreamer   | `npm run publish:logstreamer:lambda`   | `../publications/cloudv3/lambda/logstreamer`   |

#### Deploying

After you have run a `publish` task from above, head to the proper `Out Dir` and run `./push.lambda.s3.sh`.
This will upload the source code for the lambda to the `S3` bucket `mylio-lambda/<lambda-name>`.

Once the lambda has been pushed to `S3`, head to the [Lambda Console](https://us-west-2.console.aws.amazon.com/lambda/home?region=us-west-2#/functions)
and create or update the corresponding lamba function.

That's it!
