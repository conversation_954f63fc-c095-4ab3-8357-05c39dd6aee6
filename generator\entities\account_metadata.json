{"flow": "web<>cloud<>postgres<>disk", "mixin": ["all"], "fields": {"account_id": {"datatype": "int32"}, "account_merkle": {"datatype": "binary"}, "device_merkle": {"datatype": "binary"}, "message_merkle": {"datatype": "binary"}, "system_property_merkle": {"datatype": "binary"}, "user_property_merkle": {"datatype": "binary"}, "device_data_merkle": {"datatype": "binary"}, "next_device_id": {"datatype": "int32"}, "next_message_id": {"datatype": "int32"}, "bootstrap_device_id": {"datatype": "binary"}, "next_system_property_id": {"datatype": "int32"}}, "directives": {"identify": [["account_id"]]}}