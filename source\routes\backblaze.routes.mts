import * as express from "express";
import { microservice as g } from "../microservices/account.microservice.mjs";
import { safeAny, secure } from "../system/safe.mjs";
import { Context } from "../system/Context.mjs";
import crypto from "crypto";
import querystring from "querystring";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import {
  SecretsManagerClient,
  GetSecretValueCommand,
} from "@aws-sdk/client-secrets-manager";
import { log } from "console";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const regionBuckets = {
  "eu-central": {
    drive: {
      bucket: "a4c722f3c9475ad48aa90b1e",
      bucketName: "myliodrive-eu-central",
    },
    share: {
      bucket: "a4176203c9475ad48aa90b1e",
      bucketName: "myliosharing-eu-central",
      sourceRoot: "eu-central",
    },
    secrets: {
      secretsgroup: "eu-central",
    },
  },
  "us-east": {
    drive: {
      bucket: "d8aa82a3b63458dc8da20719",
      bucketName: "myliodrive-us-east",
    },
    share: {
      bucket: "b8ea8283b64458dc8da20719",
      bucketName: "myliosharing-us-east",
      sourceRoot: "us-east",
    },
    secrets: {
      secretsgroup: "us-east",
    },
  },
  "us-west": {
    drive: {
      bucket: "423572fa27955dfa87a90418",
      bucketName: "myliodrive-us-west-2",
    },
    share: {
      bucket: "e245b2ca27955dfa87a90418",
      bucketName: "myliosharing-us-west-2",
      sourceRoot: "us-west",
    },
    secrets: {
      secretsgroup: "us-west",
    },
  },
  "us-cdn": {
    share: {
      bucket: "88faa283462458fc8da20719",
      bucketName: "myliosharing-cdn",
      url: "https://sharecdn.mylio.com/",
      shareurl: "https://share.mylio.com/",
    },
    secrets: {
      secretsgroup: "us-cdn",
    },
  },
  "us-cdn-test": {
    share: {
      bucket: "545bee24413b51f382ac0819",
      bucketName: "myliosharing-cdn-test",
      url: "https://sharecdntest.mylio.com/",
      shareurl: "https://sharetest.mylio.com/",
    },
    secrets: {
      secretsgroup: "us-test",
    },
  },
  "us-test": {
    drive: {
      bucket: "f41b7eb4d12b51d382ac0819",
      bucketName: "myliodrive-test",
    },
    share: {
      bucket: "a4ebcea4d12b51d382ac0819",
      bucketName: "myliosharing-test",
      sourceRoot: "us-test",
    },
    secrets: {
      secretsgroup: "us-test",
    },
  },
};

async function getLoginKeys(context, secretsgroup) {
  context.debug(`USE_SECRETS_MANAGER: ${process.env.USE_SECRETS_MANAGER}`);

  const keyId = secretsgroup + "-keyid";
  const key = secretsgroup + "-key";
  const bunnyKey = secretsgroup + "-bunny-key";
  const accountPrefix = secretsgroup + "-account-prefix";

  if (process.env.USE_SECRETS_MANAGER === "1") {
    const client = new SecretsManagerClient({ region: "us-west-2" });
    const command = new GetSecretValueCommand({
      SecretId: "production/mylio/backblaze",
    });
    const response = await client.send(command);
    const result = JSON.parse(response.SecretString);

    const keyResult = {
      keyId: result[keyId],
      key: result[key],
      bunnyKey: result[bunnyKey],
      accountPrefix: result[accountPrefix],
    };

    return keyResult;
  } else {
    const fileName = path.join(
      __dirname,
      "../../../../../secrets/secrets.json"
    );
    context.debug(fileName);
    const file = fs.readFileSync(fileName, "utf8");
    const result = JSON.parse(file);

    const keyResult = {
      keyId: result[keyId],
      key: result[key],
      bunnyKey: result[bunnyKey],
      accountPrefix: result[accountPrefix],
    };

    if (result[keyId] === undefined) {
      context.debug("No login key for :" + keyId);
    }

    return keyResult;
  }
}

async function backblazeLogin(context: Context, keys) {
  let basicAuth = keys.keyId + ":" + keys.key;
  let base64 = Buffer.from(basicAuth, "utf-8").toString("base64");

  let options = {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: "Basic " + base64,
    },
  };

  context.debug("B2LOGIN");
  context.debug(basicAuth);
  context.debug("B2LOGIN");
  context.debug(JSON.stringify(options));

  const response = await fetch(
    "https://api.backblazeb2.com/b2api/v3/b2_authorize_account",
    options
  );
  const result = await response.json();

  context.debug("RESULT:");
  context.debug(result);

  return {
    authorizationToken: result.authorizationToken,
    accountId: result.accountId,
    apiUrl: result.apiInfo.storageApi.apiUrl,
    s3ApiUrl: result.apiInfo.storageApi.s3ApiUrl,
    downloadUrl: result.apiInfo.storageApi.downloadUrl,
  };
}

async function backblazeGenerateKey(
  context: Context,
  authorization,
  request,
  duration
) {
  let options = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: authorization.authorizationToken,
    },
    body: JSON.stringify({
      accountId: authorization.accountId,
      bucketId: request.bucketId,
      namePrefix: request.pathPrefix,
      capabilities: request.capabilities, // the capabilities you want to grant to the new application key
      keyName: request.keyPrefix,
      validDurationInSeconds: duration, // the number of seconds before the key expires
    }),
  };

  context.debug("OPTIONS:");
  context.debug(JSON.stringify(options));

  const response = await fetch(
    authorization.apiUrl + "/b2api/v2/b2_create_key",
    options
  );
  const result = await response.json();

  context.debug("GENERATEKEY_RESULT:");
  context.debug(result);

  if (response.status == 200) {
    return {
      keyId: result.applicationKeyId,
      key: result.applicationKey,
      validDurationInSeconds: duration,
      bucketId: result.bucketId,
    };
  } else {
    context.debug("B2 generate key error");
    context.debug(result);
    return null;
  }
}

function clamp(value, min, max, def) {
  if (typeof value !== "number" || isNaN(value)) {
    return def;
  }

  value = Math.round(value); // Convert to nearest integer
  return Math.max(min, Math.min(value, max));
}

function bunnyAddCountries(url, a, b) {
  var tempUrl = url;
  if (a != null) {
    var tempUrlOne = new URL(tempUrl);
    tempUrl += (tempUrlOne.search == "" ? "?" : "&") + "token_countries=" + a;
  }
  if (b != null) {
    var tempUrlTwo = new URL(tempUrl);
    tempUrl +=
      (tempUrlTwo.search == "" ? "?" : "&") + "token_countries_blocked=" + b;
  }
  return tempUrl;
}
function bunnySignUrl(
  url,
  securityKey,
  expirationTime = 3600,
  userIp,
  isDirectory = false,
  pathAllowed,
  countriesAllowed,
  countriesBlocked
) {
  /*
        url: CDN URL w/o the trailing '/' - exp. http://test.b-cdn.net/file.png
        securityKey: Security token found in your pull zone
        expirationTime: Authentication validity (default. 86400 sec/24 hrs)
        userIp: Optional parameter if you have the User IP feature enabled
        isDirectory: Optional parameter - "true" returns a URL separated by forward slashes (exp. (domain)/bcdn_token=...)
        pathAllowed: Directory to authenticate (exp. /path/to/images)
        countriesAllowed: List of countries allowed (exp. CA, US, TH)
        countriesBlocked: List of countries blocked (exp. CA, US, TH)
    */
  var parameterData = "",
    parameterDataUrl = "",
    signaturePath = "",
    hashableBase = "",
    token = "";
  var expires = Math.floor(new Date().valueOf() / 1000) + expirationTime;
  var url = bunnyAddCountries(url, countriesAllowed, countriesBlocked);
  var parsedUrl = new URL(url);
  var parameters = new URL(url).searchParams;
  if (pathAllowed != "") {
    signaturePath = pathAllowed;
    parameters.set("token_path", signaturePath);
  } else {
    signaturePath = decodeURIComponent(parsedUrl.pathname);
  }
  parameters.sort();
  if (Array.from(parameters).length > 0) {
    parameters.forEach(function (value, key) {
      if (value == "") {
        return;
      }
      if (parameterData.length > 0) {
        parameterData += "&";
      }
      parameterData += key + "=" + value;
      parameterDataUrl += "&" + key + "=" + querystring.escape(value);
    });
  }
  hashableBase =
    securityKey +
    signaturePath +
    expires +
    (userIp != null ? userIp : "") +
    parameterData;

  token = Buffer.from(
    crypto.createHash("sha256").update(hashableBase).digest()
  ).toString("base64");
  token = token
    .replace(/\n/g, "")
    .replace(/\+/g, "-")
    .replace(/\//g, "_")
    .replace(/=/g, "");
  if (isDirectory) {
    return (
      parsedUrl.protocol +
      "//" +
      parsedUrl.host +
      "/bcdn_token=" +
      token +
      parameterDataUrl +
      "&expires=" +
      expires +
      parsedUrl.pathname
    );
  } else {
    return (
      parsedUrl.protocol +
      "//" +
      parsedUrl.host +
      parsedUrl.pathname +
      "?token=" +
      token +
      parameterDataUrl +
      "&expires=" +
      expires
    );
  }
}

function hasRemainingTime(timeRemaining, info) {
  if (timeRemaining < 60) {
    return false;
  }

  if (info.upload) {
    if (Math.min(info.upload.validDurationInSeconds, timeRemaining) < 60) {
      return false;
    }
  }
  if (info.download) {
    if (Math.min(info.download.validDurationInSeconds, timeRemaining) < 60) {
      return false;
    }
  }
  if (info.share) {
    if (Math.min(info.share.validDurationInSeconds, timeRemaining) < 60) {
      return false;
    }
  }

  return true;
}

export function addBackblazeRoutes(router: express.Router) {
  router.post(
    "/accessPaths/:aid/region/:regionid",
    safeAny,
    secure,
    async (req, res, next) => {
      const context: Context = req.context;
      const data = context.any;

      context.debug("DATA");
      context.debug(data);

      if (!data) {
        context.debug(context.aid + " - No request json provided");
        context.dumpLog();
        res.status(400).send("No request json provided");
        return;
      }

      const regionId = req.params.regionid;
      if (!regionId) {
        context.debug(context.aid + " - No region id specifed");
        context.dumpLog();
        res.status(400).send("No region id specifed");
        return;
      }

      const response2 = await g.backblazeService.getBackblazeInfo(
        context,
        context.aid,
        regionId,
        data.accessType
      );
      if (response2) {
        context.debug(JSON.stringify(response2));
        if (hasRemainingTime(response2.timeRemaining, response2.info)) {
          response2.info.cachedResponse = true;
          if (response2.info.upload) {
            response2.info.upload.validDurationInSeconds = Math.min(
              response2.info.upload.validDurationInSeconds,
              response2.timeRemaining
            );
          }
          if (response2.info.download) {
            response2.info.download.validDurationInSeconds = Math.min(
              response2.info.download.validDurationInSeconds,
              response2.timeRemaining
            );
          }
          if (response2.info.share) {
            response2.info.share.validDurationInSeconds = Math.min(
              response2.info.share.validDurationInSeconds,
              response2.timeRemaining
            );
          }
          context.dumpLog();
          res.status(200).json(response2.info);
          return;
        }
      }

      const regionBucket = regionBuckets[regionId];
      if (!regionBucket) {
        context.debug(context.aid + " - Region not found");
        context.dumpLog();
        res.status(400).send("Region not found");
        return;
      }

      context.debug("REGION:");
      context.debug(regionId);
      context.debug(regionBucket);

      let keyRequestDuration = clamp(
        data.duration,
        60,
        60 * 60 * 24 * 180,
        60 * 60 * 24 * 7
      );

      const loginKeys = await getLoginKeys(
        context,
        regionBucket.secrets.secretsgroup
      );
      if (loginKeys === undefined || loginKeys.key === undefined) {
        context.debug(
          context.aid +
          " - No login keys for " +
          regionBucket.secrets.secretsgroup
        );
        context.dumpLog();
        res
          .status(400)
          .send("No login keys for " + regionBucket.secrets.secretsgroup);
        return;
      }

      let backblazeRequest: any = {
        needKey: false,
        capabilities: [],
      };

      let bunnyRequest: any = {
        needKey: false,
      };

      const deviceid = data.deviceid || "0";
      const buildnumber = data.buildnumber || "0";
      const timestamp = Math.floor(Date.now() / 1000);

      if (buildnumber == 0) {
        context.dumpLog();
        res.status(403).send("did not specify build number");
        return;
      }

      if (data.accessType == "drive") {
        if (data.upload == 1) {
          backblazeRequest.needKey = true;
          backblazeRequest.accountId = context.aid;
          backblazeRequest.capabilities = backblazeRequest.capabilities.concat([
            "writeFiles",
            "listFiles",
            "deleteFiles",
            "readFiles",
          ]);
          backblazeRequest.keyPrefix =
            "z-drive-" +
            context.aid +
            "-" +
            deviceid +
            "-" +
            buildnumber +
            "-" +
            timestamp;
          backblazeRequest.bucketId = regionBucket.drive.bucket;
          backblazeRequest.bucketName = regionBucket.drive.bucketName;
        }

        if (data.download == 1) {
          bunnyRequest.needKey = true;
          bunnyRequest.root = "https://myliostorage.b-cdn.net/";
        }
      } else if (data.accessType == "share") {
        if (data.upload == 1) {
          backblazeRequest.needKey = true;
          backblazeRequest.accountId = context.aid;
          backblazeRequest.capabilities = backblazeRequest.capabilities.concat([
            "writeFiles",
            "listFiles",
            "deleteFiles",
            "readFiles",
          ]);
          backblazeRequest.keyPrefix =
            "z-share-" +
            context.aid +
            "-" +
            deviceid +
            "-" +
            buildnumber +
            "-" +
            timestamp;
          backblazeRequest.bucketId = regionBucket.share.bucket;
          backblazeRequest.bucketName = regionBucket.share.bucketName;
        }
      }

      context.debug("DATA");
      context.debug(data);
      context.debug("----");

      let accountIdToHash =
        loginKeys.accountPrefix + "_" + data.accessType + "_" + context.aid;

      context.debug(accountIdToHash);

      let prefix = Buffer.from(
        crypto.createHash("sha1").update(accountIdToHash).digest()
      ).toString("hex");
      let pathPrefix = prefix + "/";

      context.debug(prefix);

      let authorization = null;
      let backblazeKey = null;
      if (backblazeRequest.needKey) {
        // Remove dups
        backblazeRequest.capabilities = backblazeRequest.capabilities.filter(
          (value, index, self) => {
            return self.indexOf(value) === index;
          }
        );

        backblazeRequest.pathPrefix = pathPrefix;

        authorization = await backblazeLogin(context, loginKeys);
        if (!authorization) {
          context.debug("Login failure - could not log into backblaze");
          context.dumpLog();
          res.status(403).send("login failure");
          return;
        }

        backblazeKey = await backblazeGenerateKey(
          context,
          authorization,
          backblazeRequest,
          keyRequestDuration
        );
        context.debug(backblazeKey);
      }

      let bunnySignedUrl = null;
      if (bunnyRequest.needKey) {
        bunnySignedUrl =
          bunnySignUrl(
            bunnyRequest.root + prefix,
            loginKeys.bunnyKey,
            keyRequestDuration,
            "",
            true,
            "/" + prefix,
            "CA,US",
            "JP"
          ) + "/";
      }

      if (backblazeRequest.needKey && !backblazeKey) {
        context.debug("Login failure - no backblaze key");
        context.dumpLog();
        res.status(403).send("login failure");
        return;
      }

      if (bunnyRequest.needKey && !bunnySignedUrl) {
        context.debug("Login failure - no bunny singature");
        context.dumpLog();
        res.status(403).send("login failure");
        return;
      }

      context.debug("USER LOGIN");
      let userAuthorization = {};
      if (data.share === 1 && data.upload === 1 && false) {
        let userKeys = {
          keyId: backblazeKey.keyId,
          key: backblazeKey.keyId,
        };

        userAuthorization = await backblazeLogin(context, userKeys);
      }
      context.debug("----------");

      let response: any = {};
      if (data.upload == 1) {
        response.upload = {
          validDurationInSeconds: keyRequestDuration,
          prefix: pathPrefix,
        };

        if (data.accessType == "drive") {
          response.upload.url =
            authorization.s3ApiUrl +
            "/" +
            backblazeRequest.bucketName +
            "/" +
            pathPrefix;
          response.upload.keyId = backblazeKey.keyId;
          response.upload.key = backblazeKey.key;
          response.upload.auth = "s3v4";
          response.upload.apiUrl = authorization.apiUrl;
        }

        if (data.accessType == "share") {
          response.upload.url =
            authorization.s3ApiUrl +
            "/" +
            backblazeRequest.bucketName +
            "/" +
            pathPrefix;
          response.upload.keyId = backblazeKey.keyId;
          response.upload.key = backblazeKey.key;
          response.upload.auth = "s3v4";
          response.upload.apiUrl = authorization.apiUrl;
          response.upload.bucketId = regionBucket.share.bucket;
        }
      }

      if (data.download == 1) {
        response.download = {
          validDurationInSeconds: keyRequestDuration,
          prefix: pathPrefix,
        };

        //if (data.accessType == 'drive') {
        //    response.download.url = bunnySignedUrl;
        //    response.download.auth = 'url';
        //}

        if (data.accessType == "drive") {
          response.download.url =
            authorization.s3ApiUrl +
            "/" +
            backblazeRequest.bucketName +
            "/" +
            pathPrefix;
          response.download.keyId = backblazeKey.keyId;
          response.download.key = backblazeKey.key;
          response.download.auth = "s3v4";
        }

        if (data.accessType == "share") {
          response.download.url =
            authorization.s3ApiUrl +
            "/" +
            backblazeRequest.bucketName +
            "/" +
            pathPrefix;
          response.download.keyId = backblazeKey.keyId;
          response.download.key = backblazeKey.key;
          response.download.auth = "s3v4";
        }
      }

      if (data.share == 1) {
        response.share = {
          validDurationInSeconds: keyRequestDuration,
          prefix: pathPrefix,
        };

        if (data.accessType == "share") {
          response.share.url =
            regionBucket.share?.url ??
            authorization.downloadUrl +
            "/file/" +
            backblazeRequest.bucketName +
            "/";
          response.share.shareurl =
            regionBucket.share?.shareurl ??
            authorization.downloadUrl +
            "/file/" +
            backblazeRequest.bucketName +
            "/";
          response.share.sourceRoot = regionBucket.share?.sourceRoot;
          response.share.auth = "none";
        }
      }

      context.debug(response);
      context.debug(authorization);

      await g.backblazeService.setBackblazeInfo(
        context,
        context.aid,
        regionId,
        data.accessType,
        response,
        keyRequestDuration + " seconds"
      );
      response.cachedResponse = false;
      context.dumpLog();
      res.status(200).json(response);
    }
  );
}
