#include "encode_functions.h"
#include "decode_functions.h"
#include "helpers.h"

Napi::Buffer<uint8_t> encode_support_ticket(Napi::Env env, const Napi::Object &jsObj)
{
    // {
    MYBJsonBigRW *writer = new MYBJsonBigRW();
    writer->StartObject(MYLiterals::Device::supportTicket);

    writer->String(MYLiterals::supportTicket_subject, jsObj.Get("subject").ToString());
    writer->String(MYLiterals::supportTicket_commentsToSend, jsObj.Get("comments").ToString());
    writer-><PERSON>ol(MYLiterals::supportTicket_takeScreenShot, jsObj.Get("sendScreenshot").ToBoolean());
    writer-><PERSON><PERSON>(MYLiterals::supportTicket_includeCatalog, jsObj.Get("includeCatalog").ToBoolean());
    MYHash hash = MYHash::fromHashString(jsObj.Get("requestId").ToString(), true);
    writer->MYHash(MYLiterals::supportTicket_requestId, hash);
    writer->Int32(MYLiterals::supportTicket_logs, jsObj.Get("logLevelFlags").ToNumber());
    writer->Bool(MYLiterals::supportTicket_isFromWebsite, jsObj.Get("isFromWebsite").ToBoolean());
    writer->Int32(MYLiterals::supportTicket_validUntilTime, jsObj.Get("ttl").ToNumber());
    if (jsObj.Has("consoleCommands"))
    {
        writer->StartArray(MYLiterals::supportTicket_consoleCommands);
        auto consoleCommands = jsObj.Get("consoleCommands").As<Napi::Array>();
        for (size_t i = 0; i < consoleCommands.Length(); ++i)
        {
            writer->String(consoleCommands.Get(i).ToString());
        }
        writer->EndArray();
    }

    writer->EndObject();

    return Napi::Buffer<uint8_t>::New(
        env,
        (uint8_t *)writer->pbegin(),
        writer->psize(),
        helpers::freeWriter,
        writer);
}

Napi::Object decode_support_ticket(Napi::Env env, const Napi::Buffer<uint8_t> &bjson)
{
    auto data = bjson.Data();
    auto size = bjson.Length();
    auto endPtr = data + size;

    MYBJsonIterator current = MYBJsonIterator(data, endPtr);
    MYBJsonIterator end(endPtr, endPtr);

    auto root = Napi::Object::New(env);

    while ((++current)->type() != BJsonType::end)
    {

        switch (current->key())
        {
        case MYLiterals::supportTicket_subject:
            root.Set("subject", current->asString());
            break;
        case MYLiterals::supportTicket_commentsToSend:
            root.Set("comments", current->asString());
            break;
        case MYLiterals::supportTicket_takeScreenShot:
            root.Set("sendScreenshot", current->asBool());
            break;
        case MYLiterals::supportTicket_includeCatalog:
            root.Set("includeCatalog", current->asBool());
            break;
        case MYLiterals::supportTicket_requestId:
            root.Set("requestId", current->asString());
            break;
        case MYLiterals::supportTicket_logs:
            root.Set("logLevelFlags", current->asInt32());
            break;
        case MYLiterals::supportTicket_isFromWebsite:
            root.Set("isFromWebsite", current->asBool());
            break;
        case MYLiterals::supportTicket_consoleCommands:
            auto consoleCommands = Napi::Array::New(env);
            size_t i = 0;
            while ((++current)->type() != BJsonType::end)
            {
                consoleCommands.Set(i++, current->asString());
            }
            root.Set("consoleCommands", consoleCommands);
            break;
        }
    }

    return root;
}
