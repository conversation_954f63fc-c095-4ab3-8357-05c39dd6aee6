\c telemetry;

create or replace function t0.save_client_events(_device_long_id text, _events json)
returns void AS
$$
    insert into t0.client_event(
        device_long_id,
        time_on_client_clock,
        event_type,
        data,
        uptime,
        session_id,
        modified_time
    )
    select
        decode(_device_long_id, 'hex'),
        TIMESTAMP WITH TIME ZONE 'epoch' + "time" * INTERVAL '1 ms',
        name,
        data,
        uptime,
        session_id,
        now()
    from json_to_recordset(_events)
    as raw_json ("time" bigint, name char(2), data text, uptime bigint, session_id int)
    on conflict do nothing;
$$
language sql;

create or replace function t0.save_counters(_account_id int, _device_id int, _json json)
returns void
as
$$
    insert into t0.counters (
        account_id,
        device_id,
        modified_time,
        data
    )
    select _account_id, _device_id, now(), _json
    on conflict(account_id, device_id)
    do update set data = excluded.data, modified_time = excluded.modified_time;
$$
language sql;

create or replace function t0.save_exit_survey(_account_id int, _data json)
returns void as $$
    insert into t0.exit_survey(
        account_id,
        creation_time,
        data
    )
    select _account_id, now(), _data
    on conflict do nothing;
$$ language sql;

create or replace function t0.save_cloud_event(
    _account_id int,
    _time_on_cloud_clock timestamptz,
    _device_id int,
    _event_type int,
     _data json)
returns void as $$
    insert into t0.cloud_event(
        account_id,
        time_on_cloud_clock,
        device_id,
        event_type ,
        data)
    values (_account_id, _time_on_cloud_clock, _device_id, _event_type, _data)
    on conflict do nothing;
$$ language sql;
