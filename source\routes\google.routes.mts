"use strict";

import * as express from "express";
import { config, getServices } from "../system/Config.mjs";
import {
  safeAny,
  safeAccount,
  secure,
  safeNone,
  b,
  s,
} from "../system/safe.mjs";
import { microservice } from "../microservices/account.microservice.mjs";
import { Context } from "../system/Context.mjs";
import { sanitizeOutput } from "../models/Account.model.mjs";
import { ids } from "../system/Strings.mjs";
import { makeError } from "../system/error.mjs";
import { binaryEncoder, sendResponse } from "../system/bjson.cjs";

export interface IGoogleRequest extends express.Request {
  accessToken: string;
  refreshToken: string;
}

function getExpectedRedirect(req): string | null {
  const userAgent = req.header("User-Agent");
  const isMobile =
    userAgent &&
    (userAgent.startsWith("Mylio iOS") ||
      userAgent.startsWith("Mylio Android"));

  if (b(req.query.redirect)) {
    return req.query.redirect;
  }

  if (isMobile) {
    return "com.mylio:";
  }

  return null;
}

function validateGoogleJWT(
  req: express.Request,
  res: express.Response,
  next: express.NextFunction
) {
  let token = s(req.query.access_token);
  if (!token) {
    throw makeError(403, ids.INVALID_DATA);
  }

  return microservice.idpService
    .validateJWT("google", token)
    .then((jwt) => {
      req.user = {
        id: jwt.sub,
        email: jwt.email,
        email_verified: jwt.email_verified,
      };
      req.context.audience = jwt.aud;
    })
    .then(next, next);
}

export function addGoogleRoutes(router: express.Router) {
  async function getToken(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    const context: Context = req.context;
    const result = await microservice.idpService.token(
      context,
      req.user.id,
      "google"
    );
    context.dumpLog();
    return sendResponse(req, res, result, (r) =>
      binaryEncoder.encode_token_responseV2(r)
    );
  }

  async function subscribe(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    const context: Context = req.context;
    const email = req.user && req.user.email_verified && req.user.email;
    if (!email) throw makeError(403, ids.INVALID_DATA);
    context.account.email(email);
    let result = await microservice.idpService.subscribe(
      context,
      "google",
      req.user.id,
      email
    );
    context.dumpLog();
    return sendResponse(req, res, result, (r) =>
      binaryEncoder.encode_rtoken_response(r)
    );
  }

  router.get("/google/token", safeNone, validateGoogleJWT, getToken);
  router.get("/v2/google/token", safeNone, validateGoogleJWT, getToken);
  router.post("/google/subscribe", safeAccount, validateGoogleJWT, subscribe);

  router.get("/google/authenticate", (req, res, next) => {
    res.status(302).set("location", `com.mylio:${req.url}`).end();
    return next();
  });

  router.get("/google/authenticate/win", (req, res, next) => {
    res
      .status(302)
      .set("location", `mylio-app://account.mylio.com/oauth${req.url}`)
      .end();
    return next();
  });

  router.get(
    "/accounts/:aid/google/drive/access-token",
    safeNone,
    secure,
    (req, res, next) => {
      return microservice.googleService
        .getDriveAccessToken(req.context, req.context.aid)
        .then((token) => {
          return res.status(200).json({ token });
        })
        .catch(next);
    }
  );

  router.get("/google/authorize", (req, res, next) => {
    let state = JSON.parse(
      Buffer.from(s(req.query.state), "base64").toString("ascii")
    );

    let redirect = config.cloud;
    if (state.redirect == null || state.redirect == "embedded") {
      redirect = config.cloud;
    } else if (state.isMobile === true) {
      redirect = "com.mylio:";
    } else {
      redirect = state.redirect;
    }

    return microservice.googleService
      .addRefreshToken(s(req.query.code), state)
      .then(() => {
        res.setHeader("location", `${redirect}/google/loggedin?success=true`);
        res.status(302).end();
        return res;
      })
      .catch((err) => {
        let reason = new Buffer(JSON.stringify(err.error)).toString("hex");
        res.setHeader(
          "location",
          `${redirect}/google/loggedin?success=false&reason=${reason}`
        );
        res.status(302).end();
        return res;
      })
      .catch(next);
  });

  router.get(
    "/accounts/:aid/google/drive/url",
    safeAny,
    secure,
    (req, res, next) => {
      try {
        let redirect = getExpectedRedirect(req);
        return res.status(200).json({
          url: microservice.googleService.getDriveOAuthUrl(
            req.context,
            req.context.aid,
            b(req.query.encrypt),
            redirect
          ),
        });
      } catch (err) {
        next(err);
      }
    }
  );

  router.post(
    "/accounts/:aid/google/drive/reservation",
    safeAny,
    secure,
    (req, res, next) => {
      try {
        return microservice.googleService
          .reserveDrive(req.context, req.context.aid)
          .then((reservation) => {
            return res.status(200).json(reservation);
          })
          .catch(next);
      } catch (err) {
        next(err);
      }
    }
  );

  router.get(
    "/accounts/:aid/google/import-images/access-token",
    safeAccount,
    secure,
    (req, res, next) => {
      return microservice.googleService
        .getImportImagesAccessToken(
          req.context,
          req.context.account.accountId()
        )
        .then((token) => {
          return res.status(200).json({ token });
        })
        .catch(next);
    }
  );

  router.put(
    "/accounts/:aid/google/:task/revoke",
    safeNone,
    secure,
    (req, res, next) => {
      const context: Context = req.context;
      const aid = context.aid;
      const task = req.params.task;

      return microservice.googleService
        .revokeToken(context, aid, task)
        .then(() => {
          context.dumpLog();
          return res.sendStatus(200);
        })
        .catch(next);
    }
  );

  router.get(
    "/accounts/:aid/google/import-images/url",
    safeAny,
    secure,
    (req, res, next) => {
      try {
        let redirect = getExpectedRedirect(req);
        return res.status(200).json({
          url: microservice.googleService.getImportImagesOAuthUrl(
            req.context,
            req.context.aid,
            b(req.query.encrypt),
            redirect
          ),
        });
      } catch (err) {
        next(err);
      }
    }
  );

  router.get("/google/loggedin", (req, res, next) => {
    res
      .status(200)
      .contentType("text/html")
      /* tslint:disable */
      .write(
        `<html>
          <body>
            <h1 id="warning" style="display:none;color:red">SECURITY WARNING: Please treat the URL above as you would your password and do not share it with anyone<h1>
            <script>
              (function() {
                setTimeout(function() {
                  document.getElementById("warning").style.display = "inline-block";
                  window.close();
                }, 2000);
              })();
            </script>
          </body>
        </html>`
      );
    /* tslint:enable */

    res.end();
  });
}
