#!/bin/bash

echo "started $0"

PLATFORM=$1
SERVICE=$2
ENV=$3
VER=$4


echo "BRANCH: $BRANCH"
echo "SERVICE: $SERVICE"
echo "ENV: $ENV"
echo "VER: $VER"


# OUTDIR="../publications/cloudv3/$PLATFORM/$SERVICE-$ENV-$VER"
OUTDIR="dist"

echo "publishing to $OUTDIR"

# Delete the old folder to ensure that if we are on the wrong
# branch we know that the process failed
rm -rf $OUTDIR


if ! [[ $ENV ]]; then
    echo 'You must specify an environment (test/production)'
    exit 1
fi

if ! [[ $SERVICE ]]; then
    echo 'You must specify an environment (account/resource/telemetry)'
    exit 1
fi

if ! [[ $VER ]]; then
    echo 'You must specify a version (0)'
    exit 1
fi

# We must deploy test from the "master" branch and 
# proction from the "gold" branch
BRANCH=$(git rev-parse --abbrev-ref HEAD)
if [[ "$BRANCH" = "master" && "$ENV" != "test" ]]; then
   echo '!!! ERROR !!!'
   echo 'Test environments must be deployed from the master git brach';
   exit 1;
 fi

if [[ "$BRANCH" = "gold" && "$ENV" != "production" ]]; then
   echo "$BRANCH"
   echo "$ENV"
   echo '!!! ERROR !!!'
   echo 'Production environments must be deployed from the gold git branch';
   exit 1;
fi

rm -rf $OUTDIR
mkdir -p $OUTDIR
cp -R ./cpp $OUTDIR/cpp
cp -R ./debug/source $OUTDIR/microservices
cp ./package.json $OUTDIR
cp -R ./elasticbeanstalk $OUTDIR/.elasticbeanstalk
cp ./binding.gyp $OUTDIR
cp -R ./config $OUTDIR/config
cp  ./docker/* $OUTDIR
cp ./scripts/eb-deploy.sh $OUTDIR/..
cp -R ./node_modules $OUTDIR/node_modules
cp -R ./debug/ $OUTDIR/debug
cp -R ./build $OUTDIR/build

if [ -f "$OUTDIR/config/local.json" ]
then
    rm $OUTDIR/config/local.json
fi

cd $OUTDIR

if [[ $(uname -s) == "Darwin" ]]; then # OSX
    sed -i "" "s/<service>.microservice.express.mjs/$SERVICE.microservice.express.mjs/g" package.json
    sed -i "" "s/<service>/$SERVICE/g" docker-compose.yml
    sed -i "" "s/<type>/$ENV/g" docker-compose.yml
    sed -i "" "s/<type>/$ENV/g" Dockerfile
    sed -i "" "s/<version>/$VER/g" docker-compose.yml
else
    sed -i "s/<service>.microservice.express.mjs/$SERVICE.microservice.express.mjs/g" package.json
    sed -i "s/<service>/$SERVICE/g" docker-compose.yml 
    sed -i "s/<type>/$ENV/g" docker-compose.yml
    sed -i "s/<type>/$ENV/g" Dockerfile
    sed -i "s/<version>/$VER/g" docker-compose.yml
fi


#node ./update_changelog.mjs $SERVICE $ENV $VER

echo "completed $0"
