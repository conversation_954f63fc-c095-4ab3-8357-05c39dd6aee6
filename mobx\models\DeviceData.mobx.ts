
import { makeAutoObservable } from "mobx"
    


    




export interface IDeviceData {
    flags?: number;
	modifiedTime?: Date;
	createdTime?: Date;
	accountId?: number;
	deleted?: boolean;
	t?: string;
	d?: string;
	deviceId?: number;
	build?: string;
	lastAccessTime?: Date;
	os?: string;
	mediaCount?: number;
	protocolVersion?: number;
	version?: string;
	lastStartupTime?: Date;
	lastHidTime?: Date;
	lastImportTime?: Date;
	originalSize?: string;
	localOriginalSize?: string;
}

export interface IWireDeviceData {
    flags?: number;
	modifiedTime?: string;
	createdTime?: string;
	accountId?: number;
	deleted?: boolean;
	t?: string;
	d?: string;
	deviceId?: number;
	build?: string;
	lastAccessTime?: string;
	os?: string;
	mediaCount?: number;
	protocolVersion?: number;
	version?: string;
	lastStartupTime?: string;
	lastHidTime?: string;
	lastImportTime?: string;
	originalSize?: string;
	localOriginalSize?: string;
}

export class DeviceData implements IDeviceData {
    flags?: number;
	modifiedTime?: Date;
	createdTime?: Date;
	accountId?: number;
	deleted?: boolean;
	t?: string;
	d?: string;
	deviceId?: number;
	build?: string;
	lastAccessTime?: Date;
	os?: string;
	mediaCount?: number;
	protocolVersion?: number;
	version?: string;
	lastStartupTime?: Date;
	lastHidTime?: Date;
	lastImportTime?: Date;
	originalSize?: string;
	localOriginalSize?: string;
    setFlags(value: number) { this.changed = true; this.flags = value; }
	setModifiedTime(value: Date) { this.changed = true; this.modifiedTime = value; }
	setCreatedTime(value: Date) { this.changed = true; this.createdTime = value; }
	setAccountId(value: number) { this.changed = true; this.accountId = value; }
	setDeleted(value: boolean) { this.changed = true; this.deleted = value; }
	setT(value: string) { this.changed = true; this.t = value; }
	setD(value: string) { this.changed = true; this.d = value; }
	setDeviceId(value: number) { this.changed = true; this.deviceId = value; }
	setBuild(value: string) { this.changed = true; this.build = value; }
	setLastAccessTime(value: Date) { this.changed = true; this.lastAccessTime = value; }
	setOs(value: string) { this.changed = true; this.os = value; }
	setMediaCount(value: number) { this.changed = true; this.mediaCount = value; }
	setProtocolVersion(value: number) { this.changed = true; this.protocolVersion = value; }
	setVersion(value: string) { this.changed = true; this.version = value; }
	setLastStartupTime(value: Date) { this.changed = true; this.lastStartupTime = value; }
	setLastHidTime(value: Date) { this.changed = true; this.lastHidTime = value; }
	setLastImportTime(value: Date) { this.changed = true; this.lastImportTime = value; }
	setOriginalSize(value: string) { this.changed = true; this.originalSize = value; }
	setLocalOriginalSize(value: string) { this.changed = true; this.localOriginalSize = value; }
    changed = false;
    setChanged() {
        this.changed = true;
    }

    clearChanged() {
        this.changed = false;
    }

    constructor(state? : IWireDeviceData | IDeviceData) {
        if (!state)
            throw "An DeviceData must have a valid start state";
        this.flags = state.flags;;
	if (typeof(state.modifiedTime) === "string")
            this.modifiedTime = new Date(state.modifiedTime);
         else
            this.modifiedTime = state.modifiedTime;
	if (typeof(state.createdTime) === "string")
            this.createdTime = new Date(state.createdTime);
         else
            this.createdTime = state.createdTime;
	this.accountId = state.accountId;;
	this.deleted = state.deleted;;
	this.t = state.t;;
	this.d = state.d;;
	this.deviceId = state.deviceId;;
	this.build = state.build;;
	if (typeof(state.lastAccessTime) === "string")
            this.lastAccessTime = new Date(state.lastAccessTime);
         else
            this.lastAccessTime = state.lastAccessTime;
	this.os = state.os;;
	this.mediaCount = state.mediaCount;;
	this.protocolVersion = state.protocolVersion;;
	this.version = state.version;;
	if (typeof(state.lastStartupTime) === "string")
            this.lastStartupTime = new Date(state.lastStartupTime);
         else
            this.lastStartupTime = state.lastStartupTime;
	if (typeof(state.lastHidTime) === "string")
            this.lastHidTime = new Date(state.lastHidTime);
         else
            this.lastHidTime = state.lastHidTime;
	if (typeof(state.lastImportTime) === "string")
            this.lastImportTime = new Date(state.lastImportTime);
         else
            this.lastImportTime = state.lastImportTime;
	this.originalSize = state.originalSize;;
	this.localOriginalSize = state.localOriginalSize;
        makeAutoObservable(this, {
            flags: true,
			modifiedTime: true,
			createdTime: true,
			accountId: true,
			deleted: true,
			t: true,
			d: true,
			deviceId: true,
			build: true,
			lastAccessTime: true,
			os: true,
			mediaCount: true,
			protocolVersion: true,
			version: true,
			lastStartupTime: true,
			lastHidTime: true,
			lastImportTime: true,
			originalSize: true,
			localOriginalSize: true
        });

    }

    state() : IDeviceData {
        return {
            flags : this.flags,
		modifiedTime : this.modifiedTime,
		createdTime : this.createdTime,
		accountId : this.accountId,
		deleted : this.deleted,
		t : this.t,
		d : this.d,
		deviceId : this.deviceId,
		build : this.build,
		lastAccessTime : this.lastAccessTime,
		os : this.os,
		mediaCount : this.mediaCount,
		protocolVersion : this.protocolVersion,
		version : this.version,
		lastStartupTime : this.lastStartupTime,
		lastHidTime : this.lastHidTime,
		lastImportTime : this.lastImportTime,
		originalSize : this.originalSize,
		localOriginalSize : this.localOriginalSize
        };
    }

    asWire() : IWireDeviceData {
        return {
            flags : this.flags,
		modifiedTime : this.modifiedTime ? this.modifiedTime.toISOString() : undefined,
		createdTime : this.createdTime ? this.createdTime.toISOString() : undefined,
		accountId : this.accountId,
		deleted : this.deleted,
		t : this.t,
		d : this.d,
		deviceId : this.deviceId,
		build : this.build,
		lastAccessTime : this.lastAccessTime ? this.lastAccessTime.toISOString() : undefined,
		os : this.os,
		mediaCount : this.mediaCount,
		protocolVersion : this.protocolVersion,
		version : this.version,
		lastStartupTime : this.lastStartupTime ? this.lastStartupTime.toISOString() : undefined,
		lastHidTime : this.lastHidTime ? this.lastHidTime.toISOString() : undefined,
		lastImportTime : this.lastImportTime ? this.lastImportTime.toISOString() : undefined,
		originalSize : this.originalSize,
		localOriginalSize : this.localOriginalSize
        };
    }

    



    


}


