#pragma once

#include "./util/liboauthcpp/src/base64.h"
#include "./short_alloc.h"
#include "MYHash.h"
#include "MYTRev.h"
#include <vector>
#include <deque>

enum class BJsonType
{
    // Max value for this structure is 0xF - must fit into 4 bits as part of id

    null = 0x0,       // no data. Value is implied 0 or 'false'
    true1 = 0x1,      // no data. Value is implied 1 or 'true'
    uint8 = 0x2,      // data is uint8_t
    varint = 0x3,     // data is 7-bit packed integer
    varint_neg = 0x4, // data is ones' complement of 7-bit packed integer. -0 means std::numeric_limits<int64_t>::min().
    string = 0x5,     // data is string - varint prefixed length, followed by char array. Embedded nulls ok.
    float32 = 0x6,    // data is varint up to 4 bytes (IEEE 754 float in little endian)
    double64 = 0x7,   // data is varint up to 8 bytes (IEEE 754 double in little endian)
    array = 0x8,      // data is start of array
    object = 0x9,     // data is start of object
    end = 0xA,        // data is close of last object/array
    hash = 0xB,       // data is MYHash (20 bytes)
    trev = 0xC,       // data is MYTRev (20 bytes for now)
    binary = 0xD,     // data is binary - varint prefixed length, followed by uint8_t array. Embedded nulls ok.
    binary1 = 0xE,    // forward compatible marshalling structure (treat as binary)
    binary2 = 0xF     // forward compatible marshalling structure (treat as binary)
};

// Key is of the form
// 0: marker bit (0 is 8-bit key, 1 is 16-bit key)
// For 8 bit key
//   0: marker bit
//   1-3: serialized id - sid (0 to 7)
//   4-7: type (BJsonType)
// For 16 bit key
//   0: marker bit
//   1-12:  serialized id - sid (8 to 2048). 0 to 7 is treated the same, but unused to avoid confusion.
//   13-16: type (BJsonType)
class MYBJsonElement
{
    uint8_t sidAndType;

private:
    void bugcheck() const
    {
        assert(false);
        int *nullaccess = nullptr;
        *nullaccess = 42;
    }

    uint8_t varIntLength(const uint8_t *varIntValue) const
    {
        int bytes = 1;

        const uint8_t *pos = varIntValue;
        for (int iteration = 0; iteration < 10; iteration++)
        {
            if (*pos < 0b10000000)
            {
                // No more bits
                return bytes;
            }

            pos++;
            bytes++;
        }

        // More than 10 bytes in the integer
        bugcheck();
        return 0;
    }

    uint64_t readVarInt(const uint8_t *varIntValue, uint8_t *varIntLength) const
    {
        const uint8_t *pos = varIntValue;
        uint64_t value = 0;
        for (int iteration = 0; iteration < 10; iteration++)
        {
            uint64_t real7bitValue = (*pos) & 0b01111111;
            if (real7bitValue == 0)
            {
                // not math needed for this one
            }
            else
            {
                short shiftLeftBy = 7 * iteration;
                uint64_t correctlyPositionedValue = real7bitValue << shiftLeftBy;
                value |= correctlyPositionedValue;
            }

            if (*pos < 0b10000000)
            {
                *varIntLength = iteration + 1;
                // No more bits
                return value;
            }

            pos++;
        }

        // More than 10 bytes in the integer
        bugcheck();
        *varIntLength = 10;
        return value;
    }

    uint64_t readVarInt(const uint8_t *varIntValue) const
    {
        const uint8_t *pos = varIntValue;
        uint64_t value = 0;
        for (int iteration = 0; iteration < 10; iteration++)
        {
            uint64_t real7bitValue = (*pos) & 0b01111111;
            if (real7bitValue == 0)
            {
                // not math needed for this one
            }
            else
            {
                short shiftLeftBy = 7 * iteration;
                uint64_t correctlyPositionedValue = real7bitValue << shiftLeftBy;
                value |= correctlyPositionedValue;
            }

            if (*pos < 0b10000000)
            {
                // No more bits
                return value;
            }

            pos++;
        }

        // More than 10 bytes in the integer
        bugcheck();
        return value;
    }

    bool hasShortKey() const
    {
        return (sidAndType & 0x80) == 0;
    }

    const uint8_t *offset(size_t n) const
    {
        return &sidAndType + n;
    }

    const uint8_t *data() const
    {
        if (hasShortKey())
        {
            return offset(1);
        }

        return offset(2);
    }

public:
    MYBJsonElement(const MYBJsonElement &other) = delete;
    MYBJsonElement &operator==(const MYBJsonElement &other) = delete;

    bool isScopeEnd() const
    {
        auto typ = type();
        if (typ == BJsonType::end)
        {
            return true;
        }
        return false;
    }

    bool isScopeBegin() const
    {
        auto typ = type();
        if ((typ == BJsonType::array) || (typ == BJsonType::object))
        {
            return true;
        }
        return false;
    }

    int scopeAdjust() const
    {
        auto typ = type();
        if ((typ == BJsonType::array) || (typ == BJsonType::object))
        {
            return +1;
        }
        else if (typ == BJsonType::end)
        {
            return -1;
        }
        return 0;
    }

    BJsonType type() const
    {
        return (BJsonType)(sidAndType & 0x0F);
    }

    int key() const
    {
        if (hasShortKey())
        {
            return (sidAndType & 0x70) >> 4;
        }

        int key = sidAndType & 0x70;
        key <<= 4;

        uint8_t sidAndType2 = *offset(1);
        key |= sidAndType2;
        return key;
    }

    template <typename T>
    T asType() const;

    bool asBool() const
    {
        switch (type())
        {
        case BJsonType::null:
            return false;
        case BJsonType::true1:
            return true;
        default:
            assert(false);
            return false;
        }
    }

    uint8_t asUint8() const
    {
        switch (type())
        {
        case BJsonType::null:
            return 0;
        case BJsonType::true1:
            return 1;
        case BJsonType::uint8:
            return uint8();
        case BJsonType::varint:
        {
            uint64_t varint = uint64();
            assert(varint <= std::numeric_limits<uint8_t>::max());
            return (uint8_t)varint;
        }
        default:
            assert(false);
            return 0;
        }
    }

    uint32_t asUint32() const
    {
        switch (type())
        {
        case BJsonType::null:
            return 0;
        case BJsonType::true1:
            return 1;
        case BJsonType::uint8:
            return uint8();
        case BJsonType::varint:
        {
            uint64_t varint = uint64();
            assert(varint <= std::numeric_limits<uint32_t>::max());
            return (uint32_t)varint;
        }
        default:
            assert(false);
            return 0;
        }
    }

    int32_t asInt32() const
    {
        switch (type())
        {
        case BJsonType::null:
            return 0;
        case BJsonType::true1:
            return 1;
        case BJsonType::uint8:
            return uint8();
        case BJsonType::varint:
        {
            uint64_t varint = uint64();
            assert(varint <= std::numeric_limits<int32_t>::max());
            return (int32_t)varint;
        }
        case BJsonType::varint_neg:
        {
            int64_t varint = int64_t();
            assert((varint >= std::numeric_limits<int32_t>::min()) &&
                   (varint <= std::numeric_limits<int32_t>::max()));
            return (int32_t)varint;
        }
        default:
            assert(false);
            return 0;
        }
    }

    uint64_t asUint64() const
    {
        switch (type())
        {
        case BJsonType::null:
            return 0;
        case BJsonType::true1:
            return 1;
        case BJsonType::uint8:
            return uint8();
        case BJsonType::varint:
        {
            return uint64();
        }
        default:
            assert(false);
            return 0;
        }
    }

    bool isInteger() const
    {
        switch (type())
        {
        case BJsonType::null:
            return true;
        case BJsonType::true1:
            return true;
        case BJsonType::uint8:
            return true;
        case BJsonType::varint:
            return true;
        case BJsonType::varint_neg:
            return true;
        default:
            return false;
        }
    }

    int64_t asInt64() const
    {
        switch (type())
        {
        case BJsonType::null:
            return 0;
        case BJsonType::true1:
            return 1;
        case BJsonType::uint8:
            return uint8();
        case BJsonType::varint:
        {
            uint64_t varint = uint64();
            assert(varint <= (uint64_t)std::numeric_limits<int64_t>::max());
            return varint;
        }
        case BJsonType::varint_neg:
        {
            int64_t varint = int64_t();
            assert((varint >= std::numeric_limits<int64_t>::min()) &&
                   (varint <= std::numeric_limits<int64_t>::max()));
            return varint;
        }
        default:
            assert(false);
            return 0;
        }
    }

    float asFloat() const
    {
        switch (type())
        {
        case BJsonType::null:
            return 0.0f;
        case BJsonType::true1:
            return 1.0f;
        case BJsonType::uint8:
            return uint8();
        case BJsonType::varint:
        {
            uint64_t varint = uint64();
            assert(varint <= (uint64_t)std::numeric_limits<float>::max());
            return (float)varint;
        }
        case BJsonType::varint_neg:
        {
            int64_t varint = int64_t();
            assert((varint >= std::numeric_limits<float>::min()) &&
                   (varint <= std::numeric_limits<float>::max()));
            return (float)varint;
        }
        case BJsonType::float32:
        {
            return float32();
        }
        case BJsonType::double64:
        {
            double d = double64();
            assert((d >= std::numeric_limits<float>::min()) &&
                   (d <= std::numeric_limits<float>::max()));
            return (float)d;
        }
        default:
            assert(false);
            return 0;
        }
    }

    double asDouble() const
    {
        switch (type())
        {
        case BJsonType::null:
            return 0.0f;
        case BJsonType::true1:
            return 1.0f;
        case BJsonType::uint8:
            return uint8();
        case BJsonType::varint:
        {
            uint64_t varint = uint64();
            assert(varint <= (uint64_t)std::numeric_limits<double>::max());
            return (double)varint;
        }
        case BJsonType::varint_neg:
        {
            int64_t varint = int64_t();
            assert((varint >= std::numeric_limits<double>::min()) &&
                   (varint <= std::numeric_limits<double>::max()));
            return (double)varint;
        }
        case BJsonType::float32:
        {
            return float32();
        }
        case BJsonType::double64:
        {
            return double64();
        }
        default:
            assert(false);
            return 0;
        }
    }

    MYHash asHash() const
    {
        switch (type())
        {
        case BJsonType::null:
            return ::MYHash();
        case BJsonType::hash:
        {
            return hash();
        }
        default:
            assert(false);
            return ::MYHash();
        }
    }

    MYTRev asTrev() const
    {
        switch (type())
        {
        case BJsonType::null:
            return ::MYTRev();
        case BJsonType::trev:
        {
            return trev();
        }
        default:
            assert(false);
            return MYTRev();
        }
    }

    std::string asString() const
    {
        switch (type())
        {
        case BJsonType::null:
            return std::string();
        case BJsonType::string:
        {
            return string_copy();
        }
        default:
            assert(false);
            return std::string();
        }
    }

    std::string asBinaryBase64() const
    {
        switch (type())
        {
        case BJsonType::null:
            return std::string();
        case BJsonType::binary:
        case BJsonType::binary1:
        case BJsonType::binary2:
        {
            return binary_base64();
        }
        default:
            assert(false);
            return std::string();
        }
    }

    std::vector<uint8_t> asBinary() const
    {
        switch (type())
        {
        case BJsonType::null:
            return std::vector<uint8_t>();
        case BJsonType::binary:
        case BJsonType::binary1:
        case BJsonType::binary2:
        {
            return binary_copy();
        }
        default:
            assert(false);
            return std::vector<uint8_t>();
        }
    }

    uint8_t uint8() const
    {
        assert(type() == BJsonType::uint8);
        return *data();
    }

    uint64_t uint64() const
    {
        assert(type() == BJsonType::varint);
        return readVarInt(data());
    }

    int64_t int64() const
    {
        assert(type() == BJsonType::varint || type() == BJsonType::varint_neg);

        uint64_t n = readVarInt(data());

        if (type() == BJsonType::varint)
            return n;

        return (int64_t)~n;
    }

    std::pair<const uint8_t *, size_t> binary() const
    {
        assert(type() == BJsonType::binary);

        uint8_t prefixLen;
        size_t blobLength = (size_t)readVarInt(data(), &prefixLen);
        return std::make_pair((data() + prefixLen), blobLength);
    }

    std::vector<uint8_t> binary_copy() const
    {
        assert(type() == BJsonType::binary);

        uint8_t prefixLen;
        size_t blobLength = (size_t)readVarInt(data(), &prefixLen);

        std::vector<uint8_t> vec((data() + prefixLen), (data() + prefixLen) + blobLength);
        return vec;
    }

    std::string binary_base64() const
    {
        assert(type() == BJsonType::binary);

        uint8_t prefixLen;
        size_t blobLength = (size_t)readVarInt(data(), &prefixLen);

        std::string ret = base64_encode(data() + prefixLen, blobLength);
        return ret;
    }

    std::pair<const char *, size_t> string() const
    {
        assert(type() == BJsonType::string);

        uint8_t prefixLen;
        size_t strlen = (size_t)readVarInt(data(), &prefixLen);
        return std::make_pair((const char *)(data() + prefixLen), strlen);
    }

    std::string string_copy() const
    {
        assert(type() == BJsonType::string);

        auto string_buffer = string();
        return std::string(string_buffer.first, string_buffer.second);
    }

    MYHash hash() const
    {
        assert(type() == BJsonType::hash);
        MYHash hash(data());
        return hash;
    }

    MYTRev trev() const
    {
        assert(type() == BJsonType::trev);
        MYTRev trev;
        memcpy(&trev, data(), sizeof(MYTRev));
        return trev;
    }

    double double64() const
    {
        uint64_t varInt = readVarInt(data());

        double d = *(double *)&varInt;
        return d;
    }

    float float32() const
    {
        uint64_t varInt = readVarInt(data());
        uint32_t varInt32 = (uint32_t)varInt;
        float f = *(float *)&varInt32;
        return f;
    }

    int length() const
    {
        auto myType = type();
        switch (myType)
        {
        case BJsonType::null:
        case BJsonType::true1:
        case BJsonType::array:
        case BJsonType::object:
        case BJsonType::end:
            return hasShortKey() ? 1 : 2;

        case BJsonType::uint8:
            return hasShortKey() ? 2 : 3;

        case BJsonType::varint:
        case BJsonType::varint_neg:
        case BJsonType::float32:
        case BJsonType::double64:
            return (hasShortKey() ? 1 : 2) + varIntLength(data());

        case BJsonType::hash:
        case BJsonType::trev:
            return hasShortKey() ? 21 : 22;

        case BJsonType::string:
        case BJsonType::binary:
        case BJsonType::binary1:
        case BJsonType::binary2:
        {
            uint8_t prefixLen;
            uint64_t bloblen = readVarInt(data(), &prefixLen);
            int length = (hasShortKey() ? 1 : 2) + prefixLen + (int)bloblen;
            if (bloblen >= (std::numeric_limits<int>::max() - length))
            {
                bugcheck();
            }

            return length;
        }

        default:
            bugcheck();
            return std::numeric_limits<int>::max();
        }
    }
};

// Key is of the form
// 0: marker bit (0 is 8-bit key, 1 is 16-bit key)
// For 8 bit key
//   0: marker bit
//   1-3: serialized id - sid (0 to 7)
//   4-7: type (BJsonType)
// For 16 bit key
//   0: marker bit
//   1-12:  serialized id - sid (8 to 2048). 0 to 7 is treated the same, but unused to avoid confusion.
//   13-16: type (BJsonType)
class MYBJsonElementRef
{
public:
    const uint8_t *sidAndType;
    size_t _length;
    uint16_t _key;
    uint8_t _keyLen;
    BJsonType _type;

    MYBJsonElementRef(const uint8_t *ptr, const uint8_t *end)
    {
        if (ptr != end)
        {
            init(ptr);
        }
        else
        {
            sidAndType = end;
            _length = (size_t)-1;
        }
    }

    void init(const MYBJsonElementRef &other)
    {
        sidAndType = other.sidAndType;
        _length = other._length;
        _key = other._key;
        _keyLen = other._keyLen;
        _type = other._type;
    }

    void init(const uint8_t *ptr)
    {
        sidAndType = ptr;
        register uint8_t ref = *ptr;

        _type = (BJsonType)(ref & 0x0F);

        if (ref < 0x80)
        {
            _key = (ref & 0x70) >> 4;
            _keyLen = 1;
        }
        else
        {
            _key = ref & 0x70;
            _key <<= 4;

            ++ptr;
            _key |= *ptr;
            _keyLen = 2;
        }

        auto data = ptr + _keyLen;

        switch (_type)
        {
        case BJsonType::null:
        case BJsonType::true1:
        case BJsonType::array:
        case BJsonType::object:
        case BJsonType::end:
            _length = _keyLen;
            break;

        case BJsonType::uint8:
            _length = _keyLen + 1;
            break;

        case BJsonType::varint:
        case BJsonType::varint_neg:
        case BJsonType::float32:
        case BJsonType::double64:
            _length = _keyLen + varIntLength(data);
            break;

        case BJsonType::hash:
        case BJsonType::trev:
            _length = _keyLen + 20;
            break;

        case BJsonType::string:
        case BJsonType::binary:
        case BJsonType::binary1:
        case BJsonType::binary2:
        {
            uint8_t prefixLen;
            size_t bloblen = (size_t)readVarInt(data, &prefixLen);
            _length = _keyLen + prefixLen + bloblen;
            if (bloblen >= (std::numeric_limits<int>::max() - _length))
            {
                bugcheck();
            }

            break;
        }

        default:
            bugcheck();
            _length = std::numeric_limits<int>::max();
        }
    }

    // MYBJsonElementRef(const MYBJsonElementRef& ptr) : sidAndType(ptr.sidAndType)
    //{}

private:
    void bugcheck() const
    {
        assert(false);
        int *nullaccess = nullptr;
        *nullaccess = 42;
    }

    uint8_t varIntLength(const uint8_t *varIntValue) const
    {
        int bytes = 1;

        const uint8_t *pos = varIntValue;
        for (int iteration = 0; iteration < 10; iteration++)
        {
            if (*pos < 0b10000000)
            {
                // No more bits
                return bytes;
            }

            pos++;
            bytes++;
        }

        // More than 10 bytes in the integer
        bugcheck();
        return 0;
    }

    uint64_t readVarInt(const uint8_t *varIntValue, uint8_t *varIntLength) const
    {
        const uint8_t *pos = varIntValue;
        uint64_t value = 0;
        for (int iteration = 0; iteration < 10; iteration++)
        {
            uint64_t real7bitValue = (*pos) & 0b01111111;
            if (real7bitValue == 0)
            {
                // not math needed for this one
            }
            else
            {
                short shiftLeftBy = 7 * iteration;
                uint64_t correctlyPositionedValue = real7bitValue << shiftLeftBy;
                value |= correctlyPositionedValue;
            }

            if (*pos < 0b10000000)
            {
                *varIntLength = iteration + 1;
                // No more bits
                return value;
            }

            pos++;
        }

        // More than 10 bytes in the integer
        bugcheck();
        *varIntLength = 10;
        return value;
    }

    uint64_t readVarInt(const uint8_t *varIntValue) const
    {
        const uint8_t *pos = varIntValue;
        uint64_t value = 0;
        for (int iteration = 0; iteration < 10; iteration++)
        {
            uint64_t real7bitValue = (*pos) & 0b01111111;
            if (real7bitValue == 0)
            {
                // not math needed for this one
            }
            else
            {
                short shiftLeftBy = 7 * iteration;
                uint64_t correctlyPositionedValue = real7bitValue << shiftLeftBy;
                value |= correctlyPositionedValue;
            }

            if (*pos < 0b10000000)
            {
                // No more bits
                return value;
            }

            pos++;
        }

        // More than 10 bytes in the integer
        bugcheck();
        return value;
    }

    const uint8_t *offset(size_t n) const
    {
        return sidAndType + n;
    }

    const uint8_t *data() const
    {
        return offset(_keyLen);
    }

public:
    MYBJsonElementRef(const MYBJsonElementRef &other) = delete;
    MYBJsonElementRef &operator==(const MYBJsonElementRef &other) = delete;

    bool isScopeEnd() const
    {
        auto typ = type();
        if (typ == BJsonType::end)
        {
            return true;
        }
        return false;
    }

    bool isScopeBegin() const
    {
        auto typ = type();
        if ((typ == BJsonType::array) || (typ == BJsonType::object))
        {
            return true;
        }
        return false;
    }

    int scopeAdjust() const
    {
        auto typ = type();
        if ((typ == BJsonType::array) || (typ == BJsonType::object))
        {
            return +1;
        }
        else if (typ == BJsonType::end)
        {
            return -1;
        }
        return 0;
    }

    BJsonType type() const
    {
        return _type;
    }

    int key() const
    {
        return _key;
    }

    template <typename T>
    T asType() const;

    bool asBool() const
    {
        switch (type())
        {
        case BJsonType::null:
            return false;
        case BJsonType::true1:
            return true;
        default:
            assert(false);
            return false;
        }
    }

    uint8_t asUint8() const
    {
        switch (type())
        {
        case BJsonType::null:
            return 0;
        case BJsonType::true1:
            return 1;
        case BJsonType::uint8:
            return uint8();
        case BJsonType::varint:
        {
            uint64_t varint = uint64();
            assert(varint <= std::numeric_limits<uint8_t>::max());
            return (uint8_t)varint;
        }
        default:
            assert(false);
            return 0;
        }
    }

    uint32_t asUint32() const
    {
        switch (type())
        {
        case BJsonType::null:
            return 0;
        case BJsonType::true1:
            return 1;
        case BJsonType::uint8:
            return uint8();
        case BJsonType::varint:
        {
            uint64_t varint = uint64();
            assert(varint <= std::numeric_limits<uint32_t>::max());
            return (uint32_t)varint;
        }
        default:
            assert(false);
            return 0;
        }
    }

    int32_t asInt32() const
    {
        switch (type())
        {
        case BJsonType::null:
            return 0;
        case BJsonType::true1:
            return 1;
        case BJsonType::uint8:
            return uint8();
        case BJsonType::varint:
        {
            uint64_t varint = uint64();
            assert(varint <= std::numeric_limits<int32_t>::max());
            return (int32_t)varint;
        }
        case BJsonType::varint_neg:
        {
            int64_t varint = int64_t();
            assert((varint >= std::numeric_limits<int32_t>::min()) &&
                   (varint <= std::numeric_limits<int32_t>::max()));
            return (int32_t)varint;
        }
        default:
            assert(false);
            return 0;
        }
    }

    uint64_t asUint64() const
    {
        switch (type())
        {
        case BJsonType::null:
            return 0;
        case BJsonType::true1:
            return 1;
        case BJsonType::uint8:
            return uint8();
        case BJsonType::varint:
        {
            return uint64();
        }
        default:
            assert(false);
            return 0;
        }
    }

    bool isInteger() const
    {
        switch (type())
        {
        case BJsonType::null:
            return true;
        case BJsonType::true1:
            return true;
        case BJsonType::uint8:
            return true;
        case BJsonType::varint:
            return true;
        case BJsonType::varint_neg:
            return true;
        default:
            return false;
        }
    }

    int64_t asInt64() const
    {
        switch (type())
        {
        case BJsonType::null:
            return 0;
        case BJsonType::true1:
            return 1;
        case BJsonType::uint8:
            return uint8();
        case BJsonType::varint:
        {
            uint64_t varint = uint64();
            assert(varint <= (uint64_t)std::numeric_limits<int64_t>::max());
            return varint;
        }
        case BJsonType::varint_neg:
        {
            int64_t varint = int64_t();
            assert((varint >= std::numeric_limits<int64_t>::min()) &&
                   (varint <= std::numeric_limits<int64_t>::max()));
            return varint;
        }
        default:
            assert(false);
            return 0;
        }
    }

    float asFloat() const
    {
        switch (type())
        {
        case BJsonType::null:
            return 0.0f;
        case BJsonType::true1:
            return 1.0f;
        case BJsonType::uint8:
            return uint8();
        case BJsonType::varint:
        {
            uint64_t varint = uint64();
            assert(varint <= (uint64_t)std::numeric_limits<float>::max());
            return (float)varint;
        }
        case BJsonType::varint_neg:
        {
            int64_t varint = int64_t();
            assert((varint >= std::numeric_limits<float>::min()) &&
                   (varint <= std::numeric_limits<float>::max()));
            return (float)varint;
        }
        case BJsonType::float32:
        {
            return float32();
        }
        case BJsonType::double64:
        {
            double d = double64();
            assert((d >= std::numeric_limits<float>::min()) &&
                   (d <= std::numeric_limits<float>::max()));
            return (float)d;
        }
        default:
            assert(false);
            return 0;
        }
    }

    double asDouble() const
    {
        switch (type())
        {
        case BJsonType::null:
            return 0.0f;
        case BJsonType::true1:
            return 1.0f;
        case BJsonType::uint8:
            return uint8();
        case BJsonType::varint:
        {
            uint64_t varint = uint64();
            assert(varint <= (uint64_t)std::numeric_limits<double>::max());
            return (double)varint;
        }
        case BJsonType::varint_neg:
        {
            int64_t varint = int64_t();
            assert((varint >= std::numeric_limits<double>::min()) &&
                   (varint <= std::numeric_limits<double>::max()));
            return (double)varint;
        }
        case BJsonType::float32:
        {
            return float32();
        }
        case BJsonType::double64:
        {
            return double64();
        }
        default:
            assert(false);
            return 0;
        }
    }

    MYHash asHash() const
    {
        switch (type())
        {
        case BJsonType::null:
            return ::MYHash();
        case BJsonType::hash:
        {
            return hash();
        }
        default:
            assert(false);
            return ::MYHash();
        }
    }

    MYTRev asTrev() const
    {
        switch (type())
        {
        case BJsonType::null:
            return ::MYTRev();
        case BJsonType::trev:
        {
            return trev();
        }
        default:
            assert(false);
            return MYTRev();
        }
    }

    std::string asString() const
    {
        switch (type())
        {
        case BJsonType::null:
            return std::string();
        case BJsonType::string:
        {
            return string_copy();
        }
        default:
            assert(false);
            return std::string();
        }
    }

    std::string asBinaryBase64() const
    {
        switch (type())
        {
        case BJsonType::null:
            return std::string();
        case BJsonType::binary:
        case BJsonType::binary1:
        case BJsonType::binary2:
        {
            return binary_base64();
        }
        default:
            assert(false);
            return std::string();
        }
    }

    std::vector<uint8_t> asBinary() const
    {
        switch (type())
        {
        case BJsonType::null:
            return std::vector<uint8_t>();
        case BJsonType::binary:
        case BJsonType::binary1:
        case BJsonType::binary2:
        {
            return binary_copy();
        }
        default:
            assert(false);
            return std::vector<uint8_t>();
        }
    }

    uint8_t uint8() const
    {
        assert(type() == BJsonType::uint8);
        return *data();
    }

    uint64_t uint64() const
    {
        assert(type() == BJsonType::varint);
        return readVarInt(data());
    }

    int64_t int64() const
    {
        assert(type() == BJsonType::varint || type() == BJsonType::varint_neg);

        uint64_t n = readVarInt(data());

        if (type() == BJsonType::varint)
            return n;

        return (int64_t)~n;
    }

    std::pair<const uint8_t *, size_t> binary() const
    {
        assert(type() == BJsonType::binary);

        uint8_t prefixLen;
        size_t blobLength = (size_t)readVarInt(data(), &prefixLen);
        return std::make_pair((data() + prefixLen), blobLength);
    }

    std::vector<uint8_t> binary_copy() const
    {
        assert(type() == BJsonType::binary);

        uint8_t prefixLen;
        size_t blobLength = (size_t)readVarInt(data(), &prefixLen);

        std::vector<uint8_t> vec((data() + prefixLen), (data() + prefixLen) + blobLength);
        return vec;
    }

    std::string binary_base64() const
    {
        assert(type() == BJsonType::binary);

        uint8_t prefixLen;
        size_t blobLength = (size_t)readVarInt(data(), &prefixLen);

        std::string ret = base64_encode(data() + prefixLen, blobLength);
        return ret;
    }

    std::pair<const char *, size_t> string() const
    {
        assert(type() == BJsonType::string);

        uint8_t prefixLen;
        size_t strlen = (size_t)readVarInt(data(), &prefixLen);
        return std::make_pair((const char *)(data() + prefixLen), strlen);
    }

    std::string string_copy() const
    {
        assert(type() == BJsonType::string);

        auto string_buffer = string();
        return std::string(string_buffer.first, string_buffer.second);
    }

    MYHash hash() const
    {
        assert(type() == BJsonType::hash);
        MYHash hash(data());
        return hash;
    }

    MYTRev trev() const
    {
        assert(type() == BJsonType::trev);
        MYTRev trev;
        memcpy(&trev, data(), sizeof(MYTRev));
        return trev;
    }

    double double64() const
    {
        uint64_t varInt = readVarInt(data());

        double d = *(double *)&varInt;
        return d;
    }

    float float32() const
    {
        uint64_t varInt = readVarInt(data());
        uint32_t varInt32 = (uint32_t)varInt;
        float f = *(float *)&varInt32;
        return f;
    }

    int length() const
    {
        return _length;
    }
};

class MYBJsonIterator
    : public std::iterator<std::forward_iterator_tag, const MYBJsonElementRef>
{
private:
    void bugcheck() const
    {
        assert(false);
        int *nullaccess = nullptr;
        *nullaccess = 42;
    }
    typedef const MYBJsonIterator iterator;
    MYBJsonElementRef element_;
    const uint8_t *end_;
    bool scoped_ = false;

public:
    uint8_t *ptr() const
    {
        return (uint8_t *)element_.sidAndType;
    }

    MYBJsonIterator() : element_(nullptr, nullptr), end_(nullptr) {}
    MYBJsonIterator(const MYBJsonIterator &iter) : element_(iter.element_.sidAndType, iter.end_), end_(iter.end_), scoped_(iter.scoped_) {}
    MYBJsonIterator(const MYBJsonIterator &iter, bool scoped) : element_(iter.element_.sidAndType, iter.end_), end_(iter.end_), scoped_(scoped) {}
    // MYBJsonIterator(pointer begin, pointer end, bool scoped = false) : element_(begin), end_(end), scoped_(scoped) {}
    MYBJsonIterator(const uint8_t *begin, const uint8_t *end, bool scoped = false) : element_(begin, end), end_(end), scoped_(scoped) {}
    ~MYBJsonIterator() {}

    MYBJsonIterator extent() const
    {
        if (element_.sidAndType == end_)
        {
            assert(false);
            return MYBJsonIterator(end_, end_);
        }

        const uint8_t *nextPtr = element_.sidAndType;
        MYBJsonElementRef next(nextPtr, end_);
        assert((next.type() == BJsonType::array) || (next.type() == BJsonType::object));

        int level = 1;
        while (level != 0)
        {
            nextPtr += next.length();
            if (nextPtr >= (uint8_t *)end_)
            {
                bugcheck();
            }

            next.init(nextPtr);
            if (next.sidAndType == end_)
            {
                assert(false);
                return MYBJsonIterator(end_, end_);
            }

            if (next.type() == BJsonType::end)
            {
                level--;
                if (level == 0)
                {
                    return MYBJsonIterator(nextPtr, end_);
                }
            }
            else if (next.type() == BJsonType::object)
            {
                level++;
            }
            else if (next.type() == BJsonType::array)
            {
                level++;
            }
        }

        assert(false);
        return MYBJsonIterator(end_, end_);
    }

    iterator operator++(int) /* postfix */
    {
        assert(false);
        iterator pos(element_.sidAndType, end_);
        ++(*this);
        return pos;
    }
    iterator &operator++() /* prefix */
    {
        // static_assert(sizeof(*pos_) == sizeof(uint8_t), "unexpected length");

        if (scoped_ && ((element_.type() == BJsonType::object) || (element_.type() == BJsonType::array)))
        {
            element_.init(extent().ptr());
            if (element_.type() == BJsonType::end)
            {
                assert(element_.sidAndType != end_);
                element_.init(element_.sidAndType + 1);
            }
            else
            {
                assert(false);
                element_.init(end_);
            }
        }
        else
        {
            size_t len = element_.length();
            if (element_.sidAndType + len > end_)
            {
                bugcheck();
            }

            element_.init(element_.sidAndType + len);
#if defined(DEBUG) || defined(_DEBUG)
            if (element_.sidAndType != end_)
            {
                size_t len = element_.length();
                if (element_.sidAndType + len > end_)
                {
                    bugcheck();
                }
            }
#endif
        }

        if (scoped_ && (element_.sidAndType != end_) && (element_.type() == BJsonType::end))
        {
            element_.init(end_);
        }

        return *this;
    }
    reference operator*() const
    {
        return element_;
    }

    pointer operator->() const
    {
        return &element_;
    }

    bool operator==(const iterator &rhs) const
    {
        return element_.sidAndType == rhs.element_.sidAndType;
    }
    bool operator!=(const iterator &rhs) const { return element_.sidAndType != rhs.element_.sidAndType; }
};

class MYBJson
{
protected:
    MYBJson(){};

public:
    virtual ~MYBJson() {}

    MYBJson(const MYBJson &other) = delete;
    MYBJson &operator=(const MYBJson &other) = delete;

    virtual MYBJsonIterator begin() const = 0;
    virtual MYBJsonIterator end() const = 0;

    MYBJsonIterator null_iterator() const;

    uint8_t *pbegin() const
    {
        return begin().ptr();
    }

    uint8_t *pend() const
    {
        return end().ptr();
    }

    size_t psize() const
    {
        return (pend() - pbegin());
    }

    class MYBJsonView unscoped() const;
    class MYBJsonView unscoped(MYBJsonIterator scopedIter) const;
    class MYBJsonView scoped() const;
    class MYBJsonView scoped(const MYBJsonElement &scopedIterElement) const;
    class MYBJsonView scoped(MYBJsonIterator scopedIter) const;
    virtual bool isScoped()
    {
        return false;
    }

    MYBJsonIterator findKey(uint32_t key, bool nullOnMissing = false) const
    {
        for (auto iter = begin(); iter != end(); ++iter)
        {
            if (iter->key() == key)
            {
                return iter;
            }
        }

        return nullOnMissing ? null_iterator() : end();
    }

    MYBJsonIterator findKey(uint32_t key, BJsonType bjsonType, bool nullOnMissing = false) const
    {
        for (auto iter = begin(); iter != end(); ++iter)
        {
            if ((iter->key() == key) && (iter->type() == bjsonType))
            {
                return iter;
            }
        }

        return nullOnMissing ? null_iterator() : end();
    }

    bool empty() const
    {
        return begin() == end();
    }

    bool operator==(const MYBJson &other) const
    {
        if (psize() != other.psize())
            return false;

        return memcmp(begin().ptr(), other.begin().ptr(), psize()) == 0;
    }

    std::string toJsonString() const;
};

class MYBJsonView final : public MYBJson
{
private:
    MYBJsonIterator begin_;
    MYBJsonIterator end_;
    bool scoped_ = false;

public:
    MYBJsonView &operator=(const MYBJsonView &other)
    {
        begin_ = other.begin();
        end_ = other.end();
        scoped_ = other.scoped_;
        return *this;
    }

    MYBJsonView(MYBJsonView &&other);

    MYBJsonView(const MYBJson &other) : begin_(other.begin()), end_(other.end()) {}
    MYBJsonView(const MYBJsonView &other) : begin_(other.begin(), other.scoped_), end_(other.end()), scoped_(other.scoped_) {}
    MYBJsonView(MYBJsonIterator begin, MYBJsonIterator end, bool scoped = false) : begin_(MYBJsonIterator(begin.ptr(), end.ptr(), scoped)), end_(MYBJsonIterator(end.ptr(), end.ptr())), scoped_(scoped) {}

    MYBJsonView(const uint8_t *begin, const uint8_t *end) : begin_(begin, end), end_(end, end)
    {
    }

    virtual bool isScoped() override
    {
        return scoped_;
    }

    virtual MYBJsonIterator begin() const override
    {
        return begin_;
    }

    virtual MYBJsonIterator end() const override
    {
        return end_;
    }

    static std::shared_ptr<class MYBJson> emptyObjectPtr();
    static MYBJsonView emptyObject();
};

class MYBJsonOwnedView final : public MYBJson
{
private:
    MYBJsonIterator begin_;
    MYBJsonIterator end_;

public:
    MYBJsonOwnedView(const MYBJsonOwnedView &other) = delete;
    MYBJsonOwnedView &operator=(const MYBJsonOwnedView &other) = delete;

    MYBJsonOwnedView &operator=(MYBJsonOwnedView &&other)
    {
        std::swap(begin_, other.begin_);
        std::swap(end_, other.end_);
        return *this;
    }

    MYBJsonOwnedView(MYBJsonOwnedView &&other) : begin_(other.begin_), end_(other.end_)
    {
        other.begin_ = MYBJsonIterator();
        other.end_ = MYBJsonIterator();
    }

    MYBJsonOwnedView(const uint8_t *begin, const uint8_t *end) : begin_(begin, end), end_(end, end)
    {
    }

    ~MYBJsonOwnedView()
    {
        auto ptr = begin().ptr();
        if (ptr)
        {
            delete[] ptr;
        }
    }

    virtual MYBJsonIterator begin() const override
    {
        return begin_;
    }

    virtual MYBJsonIterator end() const override
    {
        return end_;
    }
};

class MYBJsonStringView final : public MYBJson
{
private:
    MYBJsonIterator begin_;
    MYBJsonIterator end_;
    std::string data_;

public:
    MYBJsonStringView(const MYBJsonOwnedView &other) = delete;
    MYBJsonStringView &operator=(const MYBJsonOwnedView &other) = delete;

    MYBJsonStringView &operator=(MYBJsonStringView &&other)
    {
        std::swap(begin_, other.begin_);
        std::swap(end_, other.end_);
        std::swap(data_, other.data_);
        return *this;
    }

    MYBJsonStringView(MYBJsonStringView &&other) : begin_(other.begin_), end_(other.end_)
    {
        other.begin_ = MYBJsonIterator();
        other.end_ = MYBJsonIterator();
        data_ = std::move(other.data_);
    }

    MYBJsonStringView(std::string &&s)
    {
        if (!s.empty())
        {
            data_ = std::move(s);
            const uint8_t *begin = (const uint8_t *)data_.c_str();
            const uint8_t *end = (const uint8_t *)data_.c_str() + data_.length();
            begin_ = MYBJsonIterator(begin, end);
            end_ = MYBJsonIterator(end, end);
        }
        else
        {
            begin_ = MYBJson::null_iterator();
            end_ = MYBJson::null_iterator();
        }
    }

    ~MYBJsonStringView()
    {
    }

    virtual MYBJsonIterator begin() const override
    {
        return begin_;
    }

    virtual MYBJsonIterator end() const override
    {
        return end_;
    }
};

template <typename TBuffer>
class MYBJsonRWBase : public MYBJson, public TBuffer
{
private:
#if defined(DEBUG) || defined(_DEBUG)
    std::deque<bool> modes; // true is object, false is array
#endif
    uint16_t _shortId;

public:
    MYBJsonRWBase(const uint8_t *begin, const uint8_t *end) : TBuffer(begin, end) {}
    MYBJsonRWBase(const MYBJson &other) : TBuffer(other.pbegin(), other.pend()) {}
    MYBJsonRWBase(const MYBJsonRWBase &other) : TBuffer(other.pbegin(), other.pend()),
#if defined(DEBUG) || defined(_DEBUG)
                                                modes(other.modes),
#endif
                                                _shortId(other._shortId)
    {
    }

    MYBJsonRWBase(MYBJsonRWBase &&other) : TBuffer(std::move(other)),
#if defined(DEBUG) || defined(_DEBUG)
                                           modes(std::move(other.modes)),
#endif
                                           _shortId(other._shortId)
    {
    }

    MYBJsonRWBase() : _shortId(0xFFFF)
    {
    }

    std::string toString(SerializationFlags flags)
    {
        if (flags & SerializationFlags::Base64Hash)
        {
            return base64_encode(begin().ptr(), psize());
        }

        assert(false);
        return "";
    }

    MYBJsonRWBase &operator=(const MYBJsonRWBase &other)
    {
        TBuffer::_buffer.clear();
        TBuffer::_buffer.insert(TBuffer::_buffer.end(), other.TBuffer::_buffer.begin(), other.TBuffer::_buffer.end());
        return *this;
    }

    virtual MYBJsonIterator begin() const override
    {
        if (TBuffer::_buffer.empty())
        {
            return MYBJsonIterator();
        }
        uint8_t *begin = (uint8_t *)&(*TBuffer::_buffer.begin());
        uint8_t *end = begin + TBuffer::_buffer.size();
        return MYBJsonIterator(begin, end);
    }

    virtual MYBJsonIterator end() const override
    {
        if (TBuffer::_buffer.empty())
        {
            return MYBJsonIterator();
        }

        uint8_t *begin = (uint8_t *)&(*TBuffer::_buffer.begin());
        uint8_t *end = begin + TBuffer::_buffer.size();
        return MYBJsonIterator(end, end);
    }

    bool Key(const char *str, size_t length, bool copy = false)
    {
        int key = atoi(str);
        return Key(key);
    }

    bool Key(uint16_t shortId)
    {
        // assert(modes.size() > 0);
        // assert(modes.back() == true);
#if defined(DEBUG) || defined(_DEBUG)
        assert(_shortId == 0xFFFF); // Can't specify more than one key
        assert(shortId < 2048);     // Keys have to be smaller than 2048
#endif

        _shortId = shortId;
        return true;
    }

    void Type(BJsonType type)
    {
#if defined(DEBUG) || defined(_DEBUG)
        if ((modes.size() == 0) || (type == BJsonType::end))
        {
            assert((type == BJsonType::object) || (type == BJsonType::array) || (type == BJsonType::end));
        }
        else if (modes.back() == false) // We have an array, so each element is preceded by a type
        {
            // assert means you haven't specified a key. For array elements, insert them all with key == 0;
            assert(_shortId == 0);
        }
        assert(_shortId != 0xFFFF);
#endif

        if (_shortId < 8)
        {
            // 1 byte format:
            // HIII TTTT
            // H -> Hi bit - 0 if set to 1 byte format
            // I -> ID bits (0b000 to 0b111)
            // T -> Type bits
            uint8_t sidAndType = (uint8_t)(_shortId << 4) | (uint8_t)type;
            assert((sidAndType & 0x80) == 0);
            TBuffer::_buffer.push_back(sidAndType);
        }
        else if (_shortId <= 0x7FF)
        {
            // 2 byte format:
            // HIII TTTT  LLLLLLLL
            // H -> Hi bit - 1 if set to 2 byte format
            // I -> ID bits (high 3 bits)
            // T -> Type bits
            // L -> ID bits (lower 256 bits)
            uint8_t keyHi = (uint8_t)((_shortId >> 4) & 0x70);
            assert((keyHi & 0b10001111) == 0);
            uint8_t sidAndType1 = 0x80 | keyHi | (uint8_t)type;
            uint8_t sidAndType2 = (uint8_t)(_shortId & 0xFF);

            TBuffer::_buffer.push_back(sidAndType1);
            TBuffer::_buffer.push_back(sidAndType2);
        }
        else
        {
            assert(false);
        }

        _shortId = 0xFFFF;
    }

    bool addMember(uint16_t key, std::nullptr_t) { return Null(key); }
    bool Null()
    {
        Type(BJsonType::null);
        return true;
    }
    bool Null(uint16_t key)
    {
        Key(key);
        return Null();
    }

    bool addMember(uint16_t key, bool v) { return Bool(key, v); }
    bool Bool(bool b)
    {
        if (b)
        {
            Type(BJsonType::true1);
        }
        else
        {
            Type(BJsonType::null);
        }

        return true;
    }

    bool Bool(uint16_t key, bool b)
    {
        Key(key);
        return Bool(b);
    }

    bool writeVarIntVal(uint64_t value)
    {
        while (true)
        {
            if (value < 0x80) // fits in 7 bits - just write it
            {
                TBuffer::_buffer.push_back((uint8_t)value);
                break;
            }
            else // take 7 bits, mark high bit to indicate there is more
            {
                uint8_t charValue = (uint8_t)(value | 0x80);

                TBuffer::_buffer.push_back(charValue);

                value = value >> 7;
            }
        }
        return true;
    }

    bool writeInteger(uint64_t i)
    {
        if (i <= 0xFF)
        {
            if (i == 0)
            {
                Type(BJsonType::null);
            }
            else if (i == 1)
            {
                Type(BJsonType::true1);
            }
            else
            {
                Type(BJsonType::uint8);
                TBuffer::_buffer.push_back((uint8_t)i);
            }
        }
        else
        {
            Type(BJsonType::varint);
            writeVarIntVal(i);
        }

        return true;
    }

    bool writeNegativeInteger(uint64_t i) // Caller needs to pass in the ones' complement of the number, this only special-cases the type
    {
        Type(BJsonType::varint_neg);
        writeVarIntVal(i);

        return true;
    }

    bool addMember(uint16_t key, int32_t v) { return Int(key, v); }
    bool Int(int32_t i)
    {
        if (i < 0)
        {
            return writeNegativeInteger((uint32_t)~i);
        }
        return writeInteger(i);
    }

    bool Int(uint16_t key, int i)
    {
        Key(key);
        return Int(i);
    }

    bool addMember(MYBJsonIterator &iter)
    {
        const auto &element = *iter;
        switch (element.type())
        {
        case BJsonType::null:
            return Null(element.key());
        case BJsonType::true1:
            return addMember(element.key(), true);
        case BJsonType::array:
        {
            auto begin = iter;
            auto next = iter.extent();

            auto end = next;
            end++;
            MYBJsonView view(begin, end);

            iter = next;
            return Array(begin->key(), view);
        }

        case BJsonType::object:
        {
            auto begin = iter;
            auto next = iter.extent();

            auto end = next;
            end++;
            MYBJsonView view(begin, end);

            iter = next;
            return Object(begin->key(), view);
        }

        case BJsonType::end:
            assert(false);
            return true;

        case BJsonType::uint8:
            return addMember(element.key(), element.uint8());

        case BJsonType::varint:
            return addMember(element.key(), element.asUint64());

        case BJsonType::varint_neg:
            return addMember(element.key(), element.asInt64());

        case BJsonType::float32:
            return addMember(element.key(), element.asFloat());

        case BJsonType::double64:
            return addMember(element.key(), element.asDouble());

        case BJsonType::hash:
            return addMember(element.key(), element.asHash());

        case BJsonType::trev:
            return addMember(element.key(), element.asTrev());

        case BJsonType::binary:
        case BJsonType::binary1:
        case BJsonType::binary2:
            return addMember(element.key(), element.asBinary());

        case BJsonType::string:
            return addMember(element.key(), element.string_copy());

        default:
            assert(false);
        }
        return false;
    }

    bool addMember(uint16_t key, uint32_t v) { return Uint(key, v); }
    bool Uint(uint32_t u) { return writeInteger(u); }
    bool Uint(uint16_t key, uint32_t u)
    {
        Key(key);
        return Uint(u);
    }

    bool addMember(uint16_t key, int64_t v) { return Int64(key, v); }
    bool Int64(int64_t i64)
    {
        if (i64 < 0)
        {
            return writeNegativeInteger(~(uint64_t)i64);
        }

        return writeInteger((uint64_t)i64);
    }

    bool Int64(uint16_t key, int64_t i64)
    {
        Key(key);
        return Int64(i64);
    }

    bool addMember(uint16_t key, uint64_t v) { return Uint64(key, v); }
    bool Uint64(uint64_t u64) { return writeInteger(u64); }
    bool Uint64(uint16_t key, uint64_t u64)
    {
        Key(key);
        return Uint64(u64);
    }

    bool addMember(uint16_t key, float v) { return Double(key, v); }
    bool Double(float d)
    {
        static_assert(sizeof(d) == 4, "float on this platform not 4 bytes");
        uint32_t n = *(uint32_t *)&d;
        if (n == 0)
        {
            Type(BJsonType::null);
        }
        else
        {
            Type(BJsonType::float32);
            writeVarIntVal(n);
        }

        return true;
    }
    bool Double(uint16_t key, float d)
    {
        Key(key);
        return Double(d);
    }

    bool addMember(uint16_t key, double v) { return Double(key, v); }
    bool Double(double d)
    {
        static_assert(sizeof(d) == 8, "double on this platform not 8 bytes");

        float f = (float)d;
        if (f == d)
        {
            return Double(f); // Store as a float32 instead
        }

        uint64_t n = *(uint64_t *)&d;
        if (n == 0)
        {
            Type(BJsonType::null);
        }
        else
        {
            Type(BJsonType::double64);
            writeVarIntVal(n);
        }

        return true;
    }
    bool Double(uint16_t key, double d)
    {
        Key(key);
        return Double(d);
    }

    bool Raw(uint8_t *begin, uint8_t *end)
    {
        TBuffer::_buffer.reserve(TBuffer::_buffer.size() + (end - begin));
        TBuffer::_buffer.insert(TBuffer::_buffer.end(), begin, end);
        return true;
    }

    bool Raw(uint8_t *data, size_t length)
    {
        TBuffer::_buffer.reserve(TBuffer::_buffer.size() + length);
        TBuffer::_buffer.insert(TBuffer::_buffer.end(), data, data + length);
        return true;
    }

    bool addMember(uint16_t key, const MYHash &hash) { return MYHash(key, hash); }
    bool MYHash(const MYHash &hash)
    {
        if (hash.empty())
        {
            return Null();
        }

        Type(BJsonType::hash);
        TBuffer::_buffer.insert(TBuffer::_buffer.end(), hash.raw.begin(), hash.raw.end());

        return true;
    }
    bool MYHash(uint16_t key, const ::MYHash &hash)
    {
        Key(key);
        return MYHash(hash);
    }

    bool addMember(uint16_t key, const MYTRev &hash) { return MYTRev(key, hash); }
    bool MYTRev(const MYTRev &trev)
    {
        if (trev.empty())
        {
            return Null();
        }

        Type(BJsonType::trev);
        TBuffer::_buffer.insert(TBuffer::_buffer.end(), (uint8_t *)&trev, ((uint8_t *)&trev) + sizeof(::MYTRev));

        return true;
    }
    bool MYTRev(uint16_t key, const ::MYTRev &hash)
    {
        Key(key);
        return MYTRev(hash);
    }

    bool String(const char *str, size_t length, bool copy = false)
    {
        if (length == 0)
        {
            return Null();
        }

        Type(BJsonType::string);
        writeVarIntVal(length);

        TBuffer::_buffer.insert(TBuffer::_buffer.end(), str, str + length);

        return true;
    }
    bool String(uint16_t key, const char *str, size_t length, bool copy = false)
    {
        Key(key);
        return String(str, length, copy);
    }

    bool addMember(uint16_t key, const char *ch) { return String(key, ch); }
    bool addMember(uint16_t key, const std::string &v) { return String(key, v); }

    bool String(const char *str) { return String(str, strlen(str)); }
    bool String(const std::string &str) { return String(str.c_str(), (size_t)str.length()); }

    bool String(uint16_t key, const char *str)
    {
        Key(key);
        return String(str);
    }

    bool String(uint16_t key, const std::string &str)
    {
        Key(key);
        return String(str);
    }

    bool addMember(uint16_t key, const std::vector<uint8_t> &vec) { return Binary(key, &vec[0], vec.size()); }
    bool Binary(const uint8_t *raw, size_t length)
    {
        Type(BJsonType::binary);
        writeVarIntVal(length);

        TBuffer::_buffer.insert(TBuffer::_buffer.end(), raw, raw + length);

        return true;
    }
    bool Binary(uint16_t key, const uint8_t *raw, size_t length)
    {
        Key(key);
        return Binary(raw, length);
    }

    bool BinaryBase64(const char *base64, size_t length)
    {
        Type(BJsonType::binary);
        auto binaryLength = binary_length_from_base64(base64, length);
        writeVarIntVal(binaryLength);
        base64_decode(base64, length, TBuffer::_buffer);
        return true;
    }

    bool BinaryBase64(uint16_t key, const char *base64, size_t length)
    {
        Key(key);
        return BinaryBase64(base64, length);
    }

    bool addMember(uint16_t key, const MYBJson &other)
    {
        return Object(key, other);
    }

    bool Object(const MYBJson &other)
    {
        MYBJsonView view(other.pbegin(), other.pend());

        auto iter = view.pbegin();
        if (iter == view.pend())
        {
            return Null();
        }

        if (view.begin()->type() == BJsonType::object)
        {
            Type(BJsonType::object);
            ++iter;
            if (iter == view.pend())
            {
                EndObject();
            }
            else
            {
                TBuffer::_buffer.insert(TBuffer::_buffer.end(), iter, view.pend());
            }
            return true;
        }

        assert(false);
        return Null();
    }

    bool Object(uint16_t key, const MYBJson &other)
    {
        Key(key);
        return Object(other);
    }

    bool StartObject()
    {
        if (_shortId == 0xFFFF)
        {
            _shortId = 0;
        }
        Type(BJsonType::object);
#if defined(DEBUG) || defined(_DEBUG)
        modes.push_back(true);
#endif
        return true;
    }
    bool StartObject(uint16_t key)
    {
        Key(key);
        return StartObject();
    }

    bool RewindEndObject()
    {
        assert(*pend() == (uint8_t)BJsonType::end);
        assert(TBuffer::_buffer.size() > 1);
        TBuffer::_buffer.resize(TBuffer::_buffer.size() - 1);
        return true;
    }

    bool EndObject(size_t memberCount = 0)
    {
#if defined(DEBUG) || defined(_DEBUG)
        if (modes.size() > 0)
        {
            assert(modes.back() == true);
            modes.pop_back();
        }
#endif

        _shortId = 0;
        Type(BJsonType::end);
        return true;
    }

    bool Array(const MYBJson &other)
    {
        MYBJsonView view(other.pbegin(), other.pend());

        auto iter = view.pbegin();
        if (iter == view.pend())
        {
            return Null();
        }

        if (view.begin()->type() == BJsonType::array)
        {
            Type(BJsonType::array);
            ++iter;
            if (iter == view.pend())
            {
                EndArray();
            }
            else
            {
                TBuffer::_buffer.insert(TBuffer::_buffer.end(), iter, view.pend());
            }
            return true;
        }
        assert(false);
        return Null();
    }

    bool Array(uint16_t key, const MYBJson &other)
    {
        Key(key);
        return Array(other);
    }

    bool StartArray()
    {
        if (_shortId == 0xFFFF)
        {
            _shortId = 0;
        }

        Type(BJsonType::array);
#if defined(DEBUG) || defined(_DEBUG)
        modes.push_back(false);
#endif
        return true;
    }
    bool StartArray(uint16_t key)
    {
        Key(key);
        return StartArray();
    }
    bool EndArray(size_t memberCount = 0)
    {
#if defined(DEBUG) || defined(_DEBUG)
        if (modes.size() > 0)
        {
            assert(modes.back() == false);
            modes.pop_back();
        }
#endif

        _shortId = 0;
        Type(BJsonType::end);
        return true;
    }
};

template <std::size_t bufferSize>
class MYBJsonStackBuffer
{
protected:
    typedef std::vector<uint8_t, short_alloc<uint8_t, bufferSize, alignof(size_t)>> smallvec;

    typename smallvec::allocator_type::arena_type _buffer_embed;
    smallvec _buffer;

    MYBJsonStackBuffer() : _buffer(_buffer_embed) { _buffer.reserve(bufferSize); }
    MYBJsonStackBuffer(const uint8_t *begin, const uint8_t *end) : _buffer(begin, end, _buffer_embed) {}
    MYBJsonStackBuffer(MYBJsonStackBuffer &&other) : _buffer(other._buffer.begin(), other._buffer.end(), _buffer_embed) {}

public:
    void clear()
    {
        _buffer.clear();
    }
};
typedef MYBJsonRWBase<MYBJsonStackBuffer<128>> MYBJsonRW;

template <std::size_t bufferSize>
class MYBJsonVectorBuffer
{
protected:
    std::vector<uint8_t> _buffer;

    MYBJsonVectorBuffer() { _buffer.reserve(bufferSize); }
    MYBJsonVectorBuffer(const uint8_t *begin, const uint8_t *end)
    {
        _buffer.reserve(bufferSize);
        _buffer.insert(_buffer.end(), begin, end);
    }
    MYBJsonVectorBuffer(MYBJsonVectorBuffer &&other) : _buffer(std::move(other._buffer)) {}

public:
    void clear()
    {
        _buffer.clear();
    }
};
typedef MYBJsonRWBase<MYBJsonVectorBuffer<4 * 1024 * 1024>> MYBJsonBigRW;
typedef MYBJsonRWBase<MYBJsonVectorBuffer<0>> MYBJsonSmallRW;

class MYBJsonReader
{
public:
    static const size_t kDefaultStackCapacity = 256; //!< Default stack capacity in bytes for storing a single decoded string.

    inline bool next(const uint8_t *&sidAndType, const uint8_t *isEnd)
    {
        sidAndType++;
        if (sidAndType == isEnd)
        {
            assert(false);
            return false;
        }
        return true;
    }

    BJsonType getType(const uint8_t *&sidAndType)
    {
        return (BJsonType)((*sidAndType) & 0x0F);
    }

    template <typename Handler>
    bool Type(const uint8_t *&sidAndType, const uint8_t *isEnd, Handler &handler, BJsonType &type, bool setKey)
    {
        type = (BJsonType)((*sidAndType) & 0x0F);
        if (setKey && (type != BJsonType::end))
        {
            int key;
            if ((*sidAndType & 0x80) == 0)
            {
                key = (*sidAndType) >> 4;
            }
            else
            {
                key = (*sidAndType) & 0x70;
                key <<= 4;

                if (!next(sidAndType, isEnd))
                {
                    return false;
                }

                key |= *sidAndType;
            }

            handler.Key(key);
        }
        return true;
    }

    bool readVarInt(const uint8_t *&sidAndType, const uint8_t *isEnd, uint64_t &value)
    {
        value = 0;
        for (int iteration = 0; iteration < 10; iteration++)
        {
            uint64_t real7bitValue = (*sidAndType) & 0b01111111;
            if (real7bitValue == 0)
            {
                // not math needed for this one
            }
            else
            {
                short shiftLeftBy = 7 * iteration;
                uint64_t correctlyPositionedValue = real7bitValue << shiftLeftBy;
                value |= correctlyPositionedValue;
            }

            if (*sidAndType < 0b10000000)
            {
                // No more bits
                return true;
            }

            if (!next(sidAndType, isEnd))
            {
                // expected bit not found
                return false;
            }
        }

        // More than 10 bytes in the integer
        assert(false);
        return false;
    }

    const MYBJson *_is = nullptr;
    const uint8_t *_ptrType = nullptr;
    MYBJsonIterator getTypeParsePosition()
    {
        return MYBJsonIterator(_ptrType, _is->pend());
    }

    const MYBJson *getInputStream()
    {
        return _is;
    }

    template <typename Handler>
    bool Parse(const MYBJson &is, Handler &handler, bool copy = true)
    {
        _is = &is;

        std::deque<bool> modes; // true for object, false for array

        int currentMemberCount = 0;
        std::deque<int> memberCount;

        for (const uint8_t *_ptr = is.pbegin(); _ptr != is.pend(); _ptr++)
        {
            _ptrType = _ptr;
            BJsonType type;
            if (!Type(_ptr, is.pend(), handler, type, modes.size() > 0 ? modes.back() : 0))
                return false;

            if (type == BJsonType::null)
            {
                currentMemberCount++;
                if (!handler.Null())
                    return false;
            }
            else if (type == BJsonType::true1)
            {
                currentMemberCount++;
                if (!handler.Int(1))
                    return false;
            }
            else if (type == BJsonType::array)
            {
                currentMemberCount++;
                memberCount.push_back(currentMemberCount);
                currentMemberCount = 0;

                modes.push_back(false);
                if (!handler.StartArray())
                    return false;
            }
            else if (type == BJsonType::object)
            {
                currentMemberCount++;
                memberCount.push_back(currentMemberCount);
                currentMemberCount = 0;

                modes.push_back(true);
                if (!handler.StartObject())
                    return false;
            }
            else if (type == BJsonType::end)
            {
                if ((modes.size() > 0) && !modes.back())
                {
                    if (!handler.EndArray(currentMemberCount))
                        return false;

                    if (memberCount.size() > 0)
                    {
                        currentMemberCount = memberCount.back();
                        memberCount.pop_back();
                    }
                }
                else
                {
                    if (!handler.EndObject(currentMemberCount))
                        return false;

                    if (memberCount.size() > 0)
                    {
                        currentMemberCount = memberCount.back();
                        memberCount.pop_back();
                    }
                }

                if (modes.size() > 0)
                    modes.pop_back();
            }
            else if (type == BJsonType::uint8)
            {
                currentMemberCount++;

                if (!next(_ptr, is.pend()))
                    return false;

                if (!handler.Uint(*_ptr))
                    return false;
            }
            else if (type == BJsonType::varint)
            {
                currentMemberCount++;

                if (!next(_ptr, is.pend()))
                    return false;

                uint64_t varInt;
                if (!readVarInt(_ptr, is.pend(), varInt))
                    return false;

                if (!handler.Int64(varInt))
                    return false;
            }
            else if (type == BJsonType::varint_neg)
            {
                currentMemberCount++;

                if (!next(_ptr, is.pend()))
                    return false;

                uint64_t varInt;
                if (!readVarInt(_ptr, is.pend(), varInt))
                    return false;

                varInt = ~varInt;
                if (!handler.Int64(varInt))
                    return false;
            }
            else if (type == BJsonType::string)
            {
                currentMemberCount++;

                if (!next(_ptr, is.pend()))
                    return false;

                uint64_t strlength;
                if (!readVarInt(_ptr, is.pend(), strlength))
                    return false;

                assert(strlength < std::numeric_limits<size_t>::max());
                if (strlength == 0)
                {
                    if (!handler.String("", 0, copy))
                        return false;
                }
                else
                {
                    if (!next(_ptr, is.pend()))
                        return false;

                    if (_ptr + strlength >= is.pend())
                        return false;

                    if (!handler.String((const char *)_ptr, (size_t)strlength, copy))
                        return false;

                    if (strlength > 1)
                    {
                        _ptr += (strlength - 1);
                    }
                }
            }
            else if (type == BJsonType::binary)
            {
                currentMemberCount++;

                if (!next(_ptr, is.pend()))
                    return false;

                uint64_t strlength;
                if (!readVarInt(_ptr, is.pend(), strlength))
                    return false;

                if (!next(_ptr, is.pend()))
                    return false;

                if (_ptr + strlength >= is.pend())
                    return false;

                assert(strlength < std::numeric_limits<size_t>::max());
                if (!handler.Binary(_ptr, (size_t)strlength, copy))
                    return false;

                if (strlength > 1)
                {
                    _ptr += (strlength - 1);
                }
            }
            else if (type == BJsonType::hash)
            {
                currentMemberCount++;

                if (!next(_ptr, is.pend()))
                    return false;

                if (_ptr + 20 >= is.pend())
                    return false;

                MYHash hash;
                memcpy(&hash.raw[0], _ptr, 20);
                hash.resetsetbit();

                if (!handler.MYHash(hash))
                    return false;

                _ptr += 19;
            }
            else if (type == BJsonType::trev)
            {
                currentMemberCount++;

                if (!next(_ptr, is.pend()))
                    return false;

                if (_ptr + 20 >= is.pend())
                    return false;

                MYTRev trev;
                memcpy(&trev, _ptr, sizeof(MYTRev));

                if (!handler.MYTRev(trev))
                    return false;

                _ptr += 19;
            }
            else if (type == BJsonType::float32)
            {
                currentMemberCount++;

                if (!next(_ptr, is.pend()))
                    return false;

                uint64_t varInt;
                if (!readVarInt(_ptr, is.pend(), varInt))
                    return false;

                assert(varInt <= std::numeric_limits<uint32_t>::max());

                uint32_t varInt32 = (uint32_t)varInt;
                float f = *(float *)&varInt32;
                if (!handler.Double(f))
                    return false;
            }
            else if (type == BJsonType::double64)
            {
                currentMemberCount++;

                if (!next(_ptr, is.pend()))
                    return false;

                uint64_t varInt;
                if (!readVarInt(_ptr, is.pend(), varInt))
                    return false;

                double d = *(double *)&varInt;
                if (!handler.Double(d))
                    return false;
            }
        }

        return true;
    }

    template <typename Handler>
    MYBJsonIterator ParseWithIter(const MYBJson &is, Handler &handler, bool copy = true)
    {
        _is = &is;

        std::deque<bool> modes; // true for object, false for array

        for (const uint8_t *_ptr = is.pbegin(); _ptr != is.pend(); _ptr++)
        {
            _ptrType = _ptr;
            const uint8_t *ptrToType = _ptr;

            BJsonType type;
            if (!Type(_ptr, is.pend(), handler, type, modes.size() > 0 ? modes.back() : 0))
                return MYBJsonIterator(is.pend(), is.pend());

            if (type == BJsonType::null)
            {
                if (!handler.Null())
                    return MYBJsonIterator(ptrToType, is.pend());
            }
            else if (type == BJsonType::true1)
            {
                if (!handler.Int(1))
                    return MYBJsonIterator(ptrToType, is.pend());
            }
            else if (type == BJsonType::array)
            {
                modes.push_back(false);
                if (!handler.StartArray())
                    return MYBJsonIterator(ptrToType, is.pend());
            }
            else if (type == BJsonType::object)
            {
                modes.push_back(true);
                if (!handler.StartObject())
                    return MYBJsonIterator(ptrToType, is.pend());
            }
            else if (type == BJsonType::end)
            {
                if (modes.size() == 0)
                {
                    return MYBJsonIterator(is.pend(), is.pend());
                }

                if (!modes.back())
                {
                    if (!handler.EndArray(0))
                        return MYBJsonIterator(ptrToType, is.pend());
                }
                else
                {
                    if (!handler.EndObject(0))
                        return MYBJsonIterator(ptrToType, is.pend());
                }
                modes.pop_back();
            }
            else if (type == BJsonType::uint8)
            {
                if (!next(_ptr, is.pend()))
                    return MYBJsonIterator(is.pend(), is.pend());

                if (!handler.Uint(*_ptr))
                    return MYBJsonIterator(ptrToType, is.pend());
            }
            else if (type == BJsonType::varint)
            {
                if (!next(_ptr, is.pend()))
                    return MYBJsonIterator(is.pend(), is.pend());

                uint64_t varInt;
                if (!readVarInt(_ptr, is.pend(), varInt))
                    return MYBJsonIterator(is.pend(), is.pend());

                if (!handler.Int64(varInt))
                    return MYBJsonIterator(ptrToType, is.pend());
            }
            else if (type == BJsonType::varint_neg)
            {
                if (!next(_ptr, is.pend()))
                    return MYBJsonIterator(is.pend(), is.pend());

                uint64_t varInt;
                if (!readVarInt(_ptr, is.pend(), varInt))
                    return MYBJsonIterator(is.pend(), is.pend());

                varInt = ~varInt;
                if (!handler.Int64((int64_t)varInt))
                    return MYBJsonIterator(ptrToType, is.pend());
            }
            else if (type == BJsonType::string)
            {
                if (!next(_ptr, is.pend()))
                    return MYBJsonIterator(is.pend(), is.pend());

                uint64_t strlength;
                if (!readVarInt(_ptr, is.pend(), strlength))
                    return MYBJsonIterator(is.pend(), is.pend());

                if (!next(_ptr, is.pend()))
                    return MYBJsonIterator(is.pend(), is.pend());

                if (_ptr + strlength >= is.pend())
                    return MYBJsonIterator(is.pend(), is.pend());

                assert(strlength < std::numeric_limits<size_t>::max());
                if (!handler.String((const char *)_ptr, (size_t)strlength, copy))
                    return MYBJsonIterator(ptrToType, is.pend());

                if (strlength > 1)
                {
                    _ptr += (strlength - 1);
                }
            }
            else if (type == BJsonType::binary)
            {
                if (!next(_ptr, is.pend()))
                    return MYBJsonIterator(is.pend(), is.pend());

                uint64_t strlength;
                if (!readVarInt(_ptr, is.pend(), strlength))
                    return MYBJsonIterator(is.pend(), is.pend());

                if (!next(_ptr, is.pend()))
                    return MYBJsonIterator(is.pend(), is.pend());

                if (_ptr + strlength >= is.pend())
                    return MYBJsonIterator(is.pend(), is.pend());

                assert(strlength < std::numeric_limits<size_t>::max());
                if (!handler.Binary(_ptr, (size_t)strlength, copy))
                    return MYBJsonIterator(ptrToType, is.pend());

                if (strlength > 1)
                {
                    _ptr += (strlength - 1);
                }
            }
            else if (type == BJsonType::hash)
            {
                if (!next(_ptr, is.pend()))
                    return MYBJsonIterator(is.pend(), is.pend());

                if (_ptr + 20 >= is.pend())
                    return MYBJsonIterator(is.pend(), is.pend());

                MYHash hash;
                memcpy(&hash.raw[0], _ptr, 20);
                hash.resetsetbit();

                if (!handler.MYHash(hash))
                    return MYBJsonIterator(ptrToType, is.pend());

                _ptr += 19;
            }
            else if (type == BJsonType::trev)
            {
                if (!next(_ptr, is.pend()))
                    return MYBJsonIterator(is.pend(), is.pend());

                if (_ptr + 20 >= is.pend())
                    return MYBJsonIterator(is.pend(), is.pend());

                MYTRev trev;
                memcpy(&trev, _ptr, sizeof(MYTRev));

                if (!handler.MYTRev(trev))
                    return MYBJsonIterator(ptrToType, is.pend());

                _ptr += 19;
            }
            else if (type == BJsonType::float32)
            {
                if (!next(_ptr, is.pend()))
                    return MYBJsonIterator(is.pend(), is.pend());

                uint64_t varInt;
                if (!readVarInt(_ptr, is.pend(), varInt))
                    return MYBJsonIterator(is.pend(), is.pend());

                assert(varInt <= std::numeric_limits<uint32_t>::max());

                uint32_t varInt32 = (uint32_t)varInt;
                float f = *(float *)&varInt32;
                if (!handler.Double(f))
                    return MYBJsonIterator(ptrToType, is.pend());
            }
            else if (type == BJsonType::double64)
            {
                if (!next(_ptr, is.pend()))
                    return MYBJsonIterator(is.pend(), is.pend());

                uint64_t varInt;
                if (!readVarInt(_ptr, is.pend(), varInt))
                    return MYBJsonIterator(is.pend(), is.pend());

                double d = *(double *)&varInt;
                if (!handler.Double(d))
                    return MYBJsonIterator(ptrToType, is.pend());
            }
        }

        return MYBJsonIterator(is.pend(), is.pend());
    }
};
