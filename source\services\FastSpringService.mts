import { Manager, License, LicenseStatus } from "../models/License.model.mjs";
import { config } from "../system/Config.mjs";
import { Context } from "../system/Context.mjs";
import { LicenseService } from "./LicenseService.mjs";
import { makeError } from "../system/error.mjs";
import { AccountService } from "./AccountService.mjs";
import moment from "moment";
import crypto from "crypto";
import { Account } from "../models/Account.model.mjs";
import { dbnull } from "../system/data.mjs";
import FastSpringCloudClient from "./FastSpringCloudClient.mjs";
import { Subscription } from "./FastSpring.interfaces.mjs";
import { result } from "lodash";
import { Templates } from "../models/LicenseTemplate.model.mjs";




interface ILicenseBreakdown {
  licenses: License[];
}

class SkuAndQuantity {
  sku: string;
  quantity: number;
}

class FastSpringLicenseBreakdown implements ILicenseBreakdown {
  licenses: License[] = [];
  private skusAndQuantities: SkuAndQuantity[] = [];


  constructor(
    private licenseService: LicenseService,
    private fsub: any,
    private accounts: Map<string, Account>,
    private keys: string[]
  ) {

  }

  private addLicense(context: Context, sku: string, index: number, activationKey = dbnull, account: Account = undefined) {


    if (["MYLIO-PHOTOS-PLUS",
      "MYLIO-SUB-STANDARD-MONTH",
      "MYLIO-SUB-STANDARD-YEAR",
      "plus"].includes(sku))
      sku = Templates.personal;


    let license = new License({
      licenseId: this.licenseService.makeLicenseId(Manager.FastSpring, this.fsub.id, activationKey || "NOKEY", sku, index),
      manager: Manager.FastSpring,
      templateId: sku,
      endDate: moment.unix(this.fsub.nextChargeDateInSeconds).toDate(),
      status: LicenseStatus.Active,
      accountId: account?.accountId(),
      activationKey: activationKey
    });
    this.licenses.push(license);
  }

  async load(context: Context) {
    this.skusAndQuantities.push({ sku: this.fsub.sku.toLocaleLowerCase(), quantity: this.fsub.quantity });
    if (this.fsub.addons) {
      for (let addon of this.fsub?.addons) {
        this.skusAndQuantities.push({ sku: addon.sku.toLocaleLowerCase(), quantity: addon.quantity });
      }
    }

    for (let key of this.keys) {
      for (let SnQ of this.skusAndQuantities) {
        for (let i = 0; i < SnQ.quantity; i++) {
          this.addLicense(context, SnQ.sku, i, key, this.accounts.get(key));
        }
      }
    }

    if (!(this.keys && this.keys.length > 0)) {
      for (let SnQ of this.skusAndQuantities) {
        for (let i = 0; i < SnQ.quantity; i++) {
          this.addLicense(context, SnQ.sku, i, dbnull, this.accounts.get("NOKEY"));
        }
      }
    }
  }
}

class CurrentLicenseBreakdown implements ILicenseBreakdown {
  constructor(
    public licenses: License[] = []
  ) {
  }
}

function MakeWork(newBreakDown: ILicenseBreakdown, oldBreakDown: ILicenseBreakdown) {
  let work = {
    licenses: {
      create: newBreakDown.licenses.filter(
        l => !oldBreakDown.licenses.find(
          ol => ol.licenseId() === l.licenseId()
        )
      ),
      update: oldBreakDown.licenses.filter(
        l => newBreakDown.licenses.find(nl => nl.licenseId() === l.licenseId())
      ),
      delete: oldBreakDown.licenses.filter(
        l => !newBreakDown.licenses.find(nl => nl.licenseId() === l.licenseId())
      )
    }
  };
  return work;

}

enum FastSpringState {
  active = "active",
  overdue = "overdue",
  canceled = "canceled",
  deactivated = "deactivated",
  trial = "trial",
}

function escapeRegex(string) {
  return string.replace(/[/\-\\^$*+?.()|[\]{}]/g, '\\$&');
}

export default class FastSpringService {
  constructor(
    private accountService: AccountService,
    private licenseService: LicenseService,
    private fsCloud: FastSpringCloudClient
  ) { }

  async handleEvents(context: Context, events: any) {

    for (let event of events.events) {
      switch (event.type) {
        case "subscription.activated":
        case "subscription.deactivated":
        case "subscription.canceled":
        case "subscription.uncanceled":
        case "subscription.updated":
        case "subscription.charge.completed":
        case "subscription.charge.failed":
          await this.reconcile(context, event.data);
          break;
        default:
          throw makeError(
            400,
            "UNEXPECTED_FASTSPRING_EVENT",
            `Event: ${event.type} is handled`
          );
      }
    }
  }


  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }



  async reconcile(context: Context, fsubid: string | Subscription) {


    let fsub: Subscription;
    if (typeof fsubid == "string") {
      fsub = await this.fsCloud.getSubscription(fsubid);
    }
    else {
      if (fsubid.id && fsubid.sku)
        fsub = fsubid;
      else if (fsubid.subscription && typeof fsubid.subscription === "string")
        fsub = await this.fsCloud.getSubscription(fsubid.subscription);
      else if (fsubid.subscription && typeof fsubid.subscription === "object")
        fsub = fsubid.subscription;
      else
        throw makeError(400, "INVALID_SUBSCRIPTION", "Unable to locate subscription in the payload");
      fsubid = fsub.id;
    }

    let faccount: any = fsub.account;
    if (!faccount || typeof faccount === "string")
      faccount = await this.fsCloud.getCustomer(fsub.account);
    let buyerEmail = faccount.contact.email;

    let blacklist = config.fastspring.email_blacklist;
    let whilelist = config.fastspring.email_whitelist;
    if (blacklist && new RegExp(blacklist).test(buyerEmail))
      if (!(whilelist && new RegExp(whilelist).test(buyerEmail)))
        return;

    let keys: string[] = [];

    if (fsub.initialOrderId) {
      let order = await this.fsCloud.getOrder(fsub.initialOrderId);
      for (let item of order.items) {
        if (item.fulfillments) {
          for (let attribute of Object.keys(item.fulfillments)) {
            let fulfillment = item.fulfillments[attribute];
            if (typeof fulfillment === "string")
              continue;
            for (let license of fulfillment) {
              let key = license.license;
              if (key && !keys.includes(key)) {
                keys.push(key);
              }
            }
          }
        }
      }
    }

    let licenses = await this.licenseService.findByPartialId(context, `${Manager.FastSpring}.${fsubid}`);
    let existingKeys = licenses.map(function (l) { return { key: l.activationKey() || "NOKEY", aid: l.accountId() }; }).filter(k => !!k.aid);
    let accounts = new Map<string, Account>();
    for (let k of existingKeys) {
      if (!accounts.has(k.key)) {
        let a = await this.accountService.read(context, k.aid);
        accounts.set(k.key, a);
      }
    }

    if (licenses.length === 0 && keys.length === 0) {
      let account = await this.accountService.tryByEmail(context, buyerEmail);
      if (!account)
        throw makeError(400, "ACCOUNT_NOT_FOUND", `No account was found for email ${faccount.contact.email} for non license key subscription ${fsubid}`);
      accounts.set("NOKEY", account);
    }

    let oldBreakdown = new CurrentLicenseBreakdown(licenses);
    let newBreakdown = new FastSpringLicenseBreakdown(this.licenseService, fsub, accounts, keys);
    await newBreakdown.load(context);
    let status: LicenseStatus = LicenseStatus.Active;
    if (fsub.state === FastSpringState.trial)
      status = LicenseStatus.Trial;
    if (fsub.state === FastSpringState.canceled)
      status = LicenseStatus.Canceled;
    if (fsub.state === FastSpringState.deactivated)
      status = LicenseStatus.Deleted;
    let endDate = new Date(fsub.nextChargeDate);
    if (status === LicenseStatus.Deleted || status === LicenseStatus.Canceled)
      endDate = new Date(fsub.deactivationDateValue);
    let work = MakeWork(newBreakdown, oldBreakdown);
    for (let l of work.licenses.create) {
      l.endDate(endDate);
      l.status(status);
      await this.licenseService.create(context, l);
    }
    for (let l of work.licenses.update) {
      l.endDate(endDate);
      l.status(status);
      await this.licenseService.update(context, l);
    }
    for (let l of work.licenses.delete) {
      l.endDate(new Date());
      l.status(LicenseStatus.Deleted);
      await this.licenseService.update(context, l);
    }
  }

  async enryptContact(
    context: Context,
    aid: number,
    firstName: string,
    lastName: string
  ) {
    let account = await this.accountService.read(context, aid);
    const payload = {
      contact: {
        firstName,
        lastName,
        email: account.email(),
      },
    };
    let fsKey = config.fastspring.api_secret;
    const privateKey = Buffer.from(fsKey, "base64");
    const aesKey = crypto.randomBytes(16);
    const iv = Buffer.from("");
    const cipher = crypto.createCipheriv("aes-128-ecb", aesKey, iv);
    const encryptedPayload = cipher.update(
      JSON.stringify(payload),
      "utf8",
      "base64"
    );
    const securePayload = encryptedPayload + cipher.final("base64");
    const secureKey = crypto
      .privateEncrypt(privateKey, aesKey)
      .toString("base64");
    return {
      payload: securePayload,
      key: secureKey,
    };
  }

  async redeemLicenseKey(context: Context, aid: number, key: string) {
    let licenses = await this.licenseService.findByPartialActivationKey(
      context,
      key.substring(0, 12)
    );

    if (licenses.length === 0)
      throw makeError(400, "KEY_NOT_FOUND", "Key not found");

    for (let license of licenses) {
      if (license.accountId() && license.accountId() !== aid)
        throw makeError(400, "KEY_ALREADY_USED", "Key already used");
      license.accountId(aid);
      await this.licenseService.update(context, license);
    }
  }

  async listSubscriptions(context: Context, aid: number) {
    let subscriptions = [];
    for (let subId of (await this.getSubscriptionIds(context, aid)).flat()) {
      let subscription = await this.fsCloud.getSubscription(subId);
      subscriptions.push(subscription);
    }
    return subscriptions
  }

  async getFastSpringCustomerData(context: Context, aid: number) {
    const account = await this.accountService.read(context, aid);
    const licenses = await this.licenseService.findByAccountId(context, aid);
    const fsLicenses = licenses.filter(l => l.manager() === Manager.FastSpring && !l.deleted());
    let result = [];
    let customer: any;
    if (fsLicenses.length > 0) {
      for (const fsLicense of fsLicenses) {
        const fsSub = await this.fsCloud.getSubscription(fsLicense.licenseId().split(".")[1]);
        if (!customer || customer.id !== fsSub.account) {
          customer = await this.fsCloud.getCustomer(fsSub.account);
        }
        const isOwner = customer.contact.email === account.email();
        let info = {
          sku: fsLicense.templateId(),
          product: fsSub.product,
          quantity: fsSub.quantity,
          state: fsSub.state,
          endDate: moment.unix(fsSub.nextChargeDateInSeconds).toDate(),
          ownerEmail: customer.contact.email,
          ownerName: customer.contact.first + " " + customer.contact.last,
          isOwner,
          subscription: isOwner ? fsSub : undefined,
          customer: isOwner ? customer : undefined,
        }
        result.push(info);
      }
    }
    return result;
  }

  computeStorageUnits(tbs: number) {
    if (tbs < 2)
      throw makeError(400, "QUANTITY_TOO_SMALL", "Quantity too small");
    let result = {
      twoTbUnits: 0,
      fiveTbUnits: 0,
    };
    if (tbs > 2) {
      result.fiveTbUnits = tbs / 5;
    }
    if (tbs % 5 > 0) {
      result.twoTbUnits = 1;
    }
    return result;
  }

  async getSubscriptions(context: Context, aid: number) {
    let subscriptions = await this.listSubscriptions(context, aid);
    return subscriptions;
  }

  private async getSubscriptionIds(context: Context, aid: number) {
    let allLicenses = await this.licenseService.findByAccountId(context, aid);
    let fsLicenses = allLicenses.filter(l => l.manager() === Manager.FastSpring);
    let subIdsWithDups = fsLicenses.map(l => l.licenseId().split(".")[1]);
    let subIds = [...new Set(subIdsWithDups)];
    return subIds;
  }

  makeUpdate(context: Context, fsub: Subscription, tbs: number, preview: boolean) {
    let type = "sub";
    let twoTbProductId = `storage-${type}-2tb-${fsub.intervalLength}-${fsub.intervalUnit}`;
    let fiveTbProductId = `storage-${type}-5tb-${fsub.intervalLength}-${fsub.intervalUnit}`;
    let units = this.computeStorageUnits(tbs);

    let update = {
      subscription: fsub.id,
      product: units.twoTbUnits > 0 ? twoTbProductId : fiveTbProductId,
      quantity: units.twoTbUnits > 0 ? units.twoTbUnits : units.fiveTbUnits,
      prorate: true,
    };
    return update;
  }


  async buyCloudStorage(context: Context, aid: number, tbs: number, preview = false) {
    let subscriptions = await this.listSubscriptions(context, aid);
    let fsub = subscriptions.find(s => s.state === FastSpringState.active && s.sku.startsWith("storage"));
    if (!fsub)
      throw makeError(400, "NO_ACTIVE_STORAGE_SUBSCRIPTION", "No active storage subscription found");
    let update = this.makeUpdate(context, fsub, tbs, preview);
    if (preview) {
      let response = await this.fsCloud.previewSubscription(fsub.id, update);
      return response;
    }
    let response = await this.fsCloud.updateSubscription(fsub.id, { subscriptions: [update] });
    return response;
  }

  async changeProduct(
    context: Context,
    aid: number,
    fromProduct: string,
    toProduct: string,
    prorate: boolean,
    preview: boolean) {
    let fssubs = await this.listSubscriptions(context, aid);
    for (let fsub of fssubs) {
      if (fsub.product === fromProduct) {
        let update = {
          subscription: fsub.id,
          product: toProduct,
          quantity: fsub.quantity,
          prorate
        };
        if (preview)
          return await this.fsCloud.previewSubscription(fsub.id, update);
        else
          return await this.fsCloud.updateSubscription(fsub.id, { subscriptions: [update] });
      }
    }
    throw makeError(404, "SUBSCRIPTION_NOT_FOUND", "No subscription matches the from product");
  }


  async getProduct(
    context: Context,
    productId: string) {

    let product = await this.fsCloud.getProduct(productId);
    if (!product)
      throw makeError(404, "PRODUCT_NOT_FOUND", "Product not found");
    return product;
  }


  async getProductPrice(context: Context, id: string) {
    let response = await this.fsCloud.getProductPrice(id);
    return response;
  }

  async getProductPriceByCountry(context: Context, id: string, country: string) {
    let response = await this.fsCloud.getProductPriceByCountry(id, country);
    return response;
  }

  async cancelSubscription(context: Context, aid: number, subscriptionId: string, immediate: boolean) {
    let subscription = await this.fsCloud.getSubscription(subscriptionId);
    if (subscription.state === FastSpringState.trial && immediate)
      throw makeError(400, "NON_TRIAL_SUBSCRIPTION", "Can not immediately cancel a paid subscription");
    await this.fsCloud.cancelSubscription(subscriptionId, immediate);
  }
}
