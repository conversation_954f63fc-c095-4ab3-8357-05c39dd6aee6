

import Validation from "../system/Validation.mjs";
import { IModel } from "./IModel.mjs";







export interface IRefreshToken {
    flags?: number;
	modifiedTime?: Date;
	createdTime?: Date;
	accountId?: number;
	idp?: string;
	task?: string;
	sub?: string;
	token?: string;
	email?: string;
}


export class RefreshToken 
implements IModel {
    private _state: IRefreshToken;

    


    
    changed = false;

    constructor(state: IRefreshToken) {
        this.state(state);
    }

    isUndefined() {
        return this._state !== void 0;
    }

    async validate(skipUndefined?: boolean) {
        let v = new Validation(this, skipUndefined);
        


        return v;
    }

    rtt() {
        return "RefreshToken"; 
    }

    state (value?: IRefreshToken) {
        if (value !== undefined) { 
            this._state = value;
            
            


        }
        return this._state;
    }

    flags(value?: number) {
                if (value !== void 0) {
                    if (this.state().flags !== value) {
                        this.state().flags = value;
                        this.changed = true;
                    }
                }
                return this.state().flags;
            };

		modifiedTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().modifiedTime !== value) {
                        this.state().modifiedTime = value;
                        this.changed = true;
                    }
                }
                return this.state().modifiedTime;
            };

		createdTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().createdTime !== value) {
                        this.state().createdTime = value;
                        this.changed = true;
                    }
                }
                return this.state().createdTime;
            };

		accountId(value?: number) {
                if (value !== void 0) {
                    if (this.state().accountId !== value) {
                        this.state().accountId = value;
                        this.changed = true;
                    }
                }
                return this.state().accountId;
            };

		idp(value?: string) {
                if (value !== void 0) {
                    if (this.state().idp !== value) {
                        this.state().idp = value;
                        this.changed = true;
                    }
                }
                return this.state().idp;
            };

		task(value?: string) {
                if (value !== void 0) {
                    if (this.state().task !== value) {
                        this.state().task = value;
                        this.changed = true;
                    }
                }
                return this.state().task;
            };

		sub(value?: string) {
                if (value !== void 0) {
                    if (this.state().sub !== value) {
                        this.state().sub = value;
                        this.changed = true;
                    }
                }
                return this.state().sub;
            };

		token(value?: string) {
                if (value !== void 0) {
                    if (this.state().token !== value) {
                        this.state().token = value;
                        this.changed = true;
                    }
                }
                return this.state().token;
            };

		email(value?: string) {
                if (value !== void 0) {
                    if (this.state().email !== value) {
                        this.state().email = value;
                        this.changed = true;
                    }
                }
                return this.state().email;
            };

    differs(original: RefreshToken) {
        return (
            (this.flags() !== void 0 && this.flags() !== original.flags())
		 || (this.modifiedTime() !== void 0 && this.modifiedTime() !== original.modifiedTime())
		 || (this.createdTime() !== void 0 && this.createdTime() !== original.createdTime())
		 || (this.accountId() !== void 0 && this.accountId() !== original.accountId())
		 || (this.idp() !== void 0 && this.idp() !== original.idp())
		 || (this.task() !== void 0 && this.task() !== original.task())
		 || (this.sub() !== void 0 && this.sub() !== original.sub())
		 || (this.token() !== void 0 && this.token() !== original.token())
		 || (this.email() !== void 0 && this.email() !== original.email())
        );
    }







}



export function sanitizeInput(source: RefreshToken, amdin: boolean, mode: string) : IRefreshToken;
export function sanitizeInput(source: IRefreshToken, admin: boolean, mode: string) : IRefreshToken;
export function sanitizeInput(source: RefreshToken | IRefreshToken, admin = false, mode="default"): IRefreshToken {
    let s: IRefreshToken;
    if (source instanceof RefreshToken)
        s = source.state();
    else
        s = source;        
    let t = {} as IRefreshToken;
    
    
        
            switch(mode) {
                
                
                
            }
            t.flags = s.flags;
		t.accountId = s.accountId;
		t.idp = s.idp;
		t.task = s.task;
		t.sub = s.sub;
		t.token = s.token;
		t.email = s.email;
        
    return t;
}

export function sanitizeOutput(source: RefreshToken, amdin: boolean) : IRefreshToken;
export function sanitizeOutput(source: IRefreshToken, admin: boolean) : IRefreshToken;
export function sanitizeOutput(source: RefreshToken | IRefreshToken, admin = false): IRefreshToken {
    let s: IRefreshToken;
    if (source instanceof RefreshToken)
        s = source.state();
    else
        s = source;        
    let t = {} as IRefreshToken;
    
    
    
    t.flags = s.flags;	
t.modifiedTime = s.modifiedTime;	
t.createdTime = s.createdTime;	
t.accountId = s.accountId;	
t.idp = s.idp;	
t.task = s.task;	
t.sub = s.sub;	
t.token = s.token;	
t.email = s.email;
    return t;
}

export function mergeState(dbVersion: IRefreshToken, newVersion: IRefreshToken) {
    let targetState: IRefreshToken = {};
    targetState.flags = newVersion.flags === undefined ? dbVersion.flags : newVersion.flags;
	targetState.modifiedTime = newVersion.modifiedTime === undefined ? dbVersion.modifiedTime : newVersion.modifiedTime;
	targetState.createdTime = newVersion.createdTime === undefined ? dbVersion.createdTime : newVersion.createdTime;
	targetState.accountId = newVersion.accountId === undefined ? dbVersion.accountId : newVersion.accountId;
	targetState.idp = newVersion.idp === undefined ? dbVersion.idp : newVersion.idp;
	targetState.task = newVersion.task === undefined ? dbVersion.task : newVersion.task;
	targetState.sub = newVersion.sub === undefined ? dbVersion.sub : newVersion.sub;
	targetState.token = newVersion.token === undefined ? dbVersion.token : newVersion.token;
	targetState.email = newVersion.email === undefined ? dbVersion.email : newVersion.email;
    return targetState;
}

export function merge(dbVersion: RefreshToken, newVersion: RefreshToken) {
    return new RefreshToken(mergeState(dbVersion.state(), newVersion.state()));
}
