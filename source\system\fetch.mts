import { ids } from "./Strings.mjs";
import { makeError } from "./error.mjs";

export async function post<T>(url: string, body: unknown, headers: unknown = undefined) {
  let r = await request<T>(url, "POST", body, headers);
  return r;
}

export function parameterizeUrl(urlString: string, qsObject: any) {
  let url = new URL(urlString);
  for (let param of Object.keys(qsObject)) {
    url.searchParams.append(param, qsObject[param]);
  }
  return url.toString();
}

export async function put<T>(url: string, body: unknown) {
  let r = await request<T>(url, "PUT", body);
  return r;
}

export async function get<T>(url: string, headers?: any) {
  let r = await request<T>(url, "GET", undefined, headers);
  return r;
}

export async function del<T>(url: string, headers?: any) {
  let r = await request<T>(url, "DELETE", undefined, headers);
  return r;
}

export async function request<T>(
  url: string,
  method: string,
  body: unknown | undefined = undefined,
  headers: any | undefined = undefined
) {
  headers = headers || {};
  headers["Content-Type"] = "application/json";
  headers["Accept"] = "application/json";

  const response = await fetch(url, {
    method,
    headers,
    body: body ? JSON.stringify(body) : undefined,
  });

  let json: any;
  try {
    let text = await response.text();
    if (text?.length) {
      json = JSON.parse(text);
    }
  } catch (error) { }
  if (!response.ok) {
    let error = makeError(response.status, ids.SERVER_ERROR);
    if (json) {
      error.code = json.code || error.code;
      error.message = json.message || error.message;
    }
    throw error;
  }

  return json;
}

export async function postForm(url: string, form: any) {
  const formData = new FormData();
  for (let key of Object.keys(form)) {
    formData.append(key, form[key]);
  }

  return fetch(url, {
    method: "post",
    body: formData,
  });
}
