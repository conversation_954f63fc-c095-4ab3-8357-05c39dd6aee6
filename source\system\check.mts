import _ = require("lodash");
import { makeError } from "./error.mjs";
import { ids } from "../system/Strings.mjs";

export interface IModel {
  validate(skipUndefined?: boolean): Promise<any>;
}

export async function check<T extends IModel>(
  model: T,
  warn = false,
  pick?: Array<string>
) {
  let v = await model.validate();
  let errors = [] as string[];
  for (const f of Object.keys(v.errors)) {
    errors.push(`${f}: ${v.errors[f]}`);
  }
  if (errors.length)
    throw makeError(400, ids.VALIDATION_ERRORS, "Validation Errors", errors);
}
