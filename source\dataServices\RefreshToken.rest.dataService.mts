
import { query } from "../system/Postgres.mjs";
import { RefreshToken, IRefreshToken} from "../models/RefreshToken.model.mjs";
import { Context } from "../system/Context.mjs";
import { makeError } from "../system/error.mjs";


export class RefreshTokenRestDataService {

    
/* b::rest_public_members */

/* end */


  public query(context: Context, sql: string, params: any[]) {
    return query < IRefreshToken> (context, sql, params)
      .then(results => {
        if (results.length === 1) {
          if (!results[0].accountId && !results[0].idp && !results[0].task) {
      return [];
    }
  }
  return results.filter(o => !!o).map(o => new RefreshToken(o));
});
    }

		public create (context: Context, entity: RefreshToken) {
          


  let params = [
    entity.flags(),
				entity.modifiedTime(),
				entity.createdTime(),
				entity.accountId(),
				entity.idp(),
				entity.task(),
				entity.sub(),
				entity.token(),
				entity.email()
  ];
  return this
    .query(context, "select * from a0.refresh_token_create ($1,$2,$3,$4,$5,$6,$7,$8,$9) ", params)
  .then(r => r[0]);
        }

		public readByAccountIdAndIdpAndTask (context: Context, accountId: number, idp: string, task: string) {
  let params = [
    accountId,
				idp,
				task
  ];
  return this
    .query(context, "select * from a0.refresh_token_read_by_account_id_and_idp_and_task  ($1,$2,$3) ", params).then(r => r[0]); 
                
        }

		public readByAccountId (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.refresh_token_read_by_account_id  ($1) ", params).then(r => r[0]); 
                
        }

		public update (context: Context, entity: RefreshToken) {
          


  let params = [
    entity.flags(),
				entity.modifiedTime(),
				entity.createdTime(),
				entity.accountId(),
				entity.idp(),
				entity.task(),
				entity.sub(),
				entity.token(),
				entity.email()
  ];
  return this
    .query(context, "select * from a0.refresh_token_update ($1,$2,$3,$4,$5,$6,$7,$8,$9) ", params)
  .then(r => r[0]);
        }

		public deleteByAccountIdAndIdpAndTask (context: Context, accountId: number, idp: string, task: string) {
  let params = [
    accountId,
				idp,
				task
  ];
  return this
    .query(context, "select * from a0.refresh_token_delete_by_account_id_and_idp_and_task  ($1,$2,$3) ", params).then(r => r[0]); 
                
        }

		public deleteByAccountId (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.refresh_token_delete_by_account_id  ($1) ", params).then(r => r[0]); 
                
        }

		public findByAccountId (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.refresh_token_find_by_account_id  ($1) ", params); 
                
        }

}
