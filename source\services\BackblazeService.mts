import { query } from "../system/Postgres.mjs";
import { Context } from "../system/Context.mjs";
import { error } from "../system/error.mjs";
import crypto = require("crypto");
import { ids } from "../system/Strings.mjs";
import c from "config";

export class BackblazeService {
  async getBackblazeInfo(
    context: Context,
    aid: number,
    region: string,
    service: string
  ) {
    let rows = await query<{ info: any; time_remaining: any }>(
      context,
      `select info, round(extract('epoch' from expires - now())) as time_remaining
            from a0.backblaze_info 
            where account_id = $1 
            and region=$2 
            and service=$3 
            and expires > now()`,
      [aid, region, service]
    );

    if (rows && rows.length > 0) {
      context.telemetry("CACHE_HIT", {
        key: `${aid}-${region}-${service}`,
      });
      return { info: rows[0].info, timeRemaining: rows[0].time_remaining };
    } else {
      context.telemetry("CACHE_MISS", {
        key: `${aid}-${region}-${service}`,
      });
      return undefined;
    }
  }

  async setBackblazeInfo(
    context: Context,
    aid: number,
    region: string,
    service: string,
    info: any,
    timeout: string
  ) {
    await query<{ info: any }>(
      context,
      `insert into a0.backblaze_info(account_id, region, service, info, expires)
            values($1, $2, $3, $4, now() + $5::interval)
            on conflict (account_id, region, service) do update
              set info = EXCLUDED.info, expires = EXCLUDED.expires
            `,
      [aid, region, service, info, timeout]
    );
    context.telemetry("CACHE_ADD", { key: `${aid}-${region}-${service}` });
  }
}
