
import { query } from "../system/Postgres.mjs";
import { Pin, IPin} from "../models/Pin.model.mjs";
import { Context } from "../system/Context.mjs";
import { makeError } from "../system/error.mjs";


export class PinRestDataService {

    



  public query(context: Context, sql: string, params: any[]) {
    return query < IPin> (context, sql, params)
      .then(results => {
        if (results.length === 1) {
          if (!results[0].email && !results[0].codeChallenge) {
      return [];
    }
  }
  return results.filter(o => !!o).map(o => new Pin(o));
});
    }

		public create (context: Context, entity: Pin) {
          


  let params = [
    entity.flags(),
				entity.modifiedTime(),
				entity.createdTime(),
				entity.codeChallenge(),
				entity.email(),
				entity.pin(),
				entity.expiresAt()
  ];
  return this
    .query(context, "select * from a0.pin_create ($1,$2,$3,$4,$5,$6,$7) ", params)
  .then(r => r[0]);
        }

		public readByEmailAndCodeChallenge (context: Context, email: string, codeChallenge: string) {
  let params = [
    email,
				codeChallenge
  ];
  return this
    .query(context, "select * from a0.pin_read_by_email_and_code_challenge  ($1,$2) ", params).then(r => r[0]); 
                
        }

		public readByEmail (context: Context, email: string) {
  let params = [
    email
  ];
  return this
    .query(context, "select * from a0.pin_read_by_email  ($1) ", params).then(r => r[0]); 
                
        }

		public update (context: Context, entity: Pin) {
          


  let params = [
    entity.flags(),
				entity.modifiedTime(),
				entity.createdTime(),
				entity.codeChallenge(),
				entity.email(),
				entity.pin(),
				entity.expiresAt()
  ];
  return this
    .query(context, "select * from a0.pin_update ($1,$2,$3,$4,$5,$6,$7) ", params)
  .then(r => r[0]);
        }

		public deleteByEmailAndCodeChallenge (context: Context, email: string, codeChallenge: string) {
  let params = [
    email,
				codeChallenge
  ];
  return this
    .query(context, "select * from a0.pin_delete_by_email_and_code_challenge  ($1,$2) ", params).then(r => r[0]); 
                
        }

		public deleteByEmail (context: Context, email: string) {
  let params = [
    email
  ];
  return this
    .query(context, "select * from a0.pin_delete_by_email  ($1) ", params).then(r => r[0]); 
                
        }

		public findByEmail (context: Context, email: string) {
  let params = [
    email
  ];
  return this
    .query(context, "select * from a0.pin_find_by_email  ($1) ", params); 
                
        }

}
