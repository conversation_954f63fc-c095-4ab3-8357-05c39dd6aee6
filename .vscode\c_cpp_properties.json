{"configurations": [{"name": "<PERSON>", "includePath": ["/usr/include", "/usr/local/include", "${workspaceRoot}"], "defines": [], "intelliSenseMode": "clang-x64", "browse": {"path": ["/usr/include", "/usr/local/include", "${workspaceRoot}"], "limitSymbolsToIncludedHeaders": true, "databaseFilename": ""}, "macFrameworkPath": ["/System/Library/Frameworks", "/Library/Frameworks"], "cStandard": "c11", "cppStandard": "c++17"}, {"name": "Linux", "includePath": ["/usr/lib/gcc/x86_64-pc-linux-gnu/7.3.0/../../../../include/c++/7.3.0", "/usr/lib/gcc/x86_64-pc-linux-gnu/7.3.0/../../../../include/c++/7.3.0/x86_64-pc-linux-gnu", "/usr/lib/gcc/x86_64-pc-linux-gnu/7.3.0/../../../../include/c++/7.3.0/backward", "/usr/lib/gcc/x86_64-pc-linux-gnu/7.3.0/include", "/usr/local/include", "/usr/lib/gcc/x86_64-pc-linux-gnu/7.3.0/include-fixed", "/usr/include", "${workspaceRoot}", "${workspaceRoot}/cpp/dependencies/cloudshared", "/usr/include/node"], "defines": [], "intelliSenseMode": "clang-x64", "browse": {"path": ["/usr/lib/gcc/x86_64-pc-linux-gnu/7.3.0/../../../../include/c++/7.3.0", "/usr/lib/gcc/x86_64-pc-linux-gnu/7.3.0/../../../../include/c++/7.3.0/x86_64-pc-linux-gnu", "/usr/lib/gcc/x86_64-pc-linux-gnu/7.3.0/../../../../include/c++/7.3.0/backward", "/usr/lib/gcc/x86_64-pc-linux-gnu/7.3.0/include", "/usr/local/include", "/usr/lib/gcc/x86_64-pc-linux-gnu/7.3.0/include-fixed", "/usr/include", "${workspaceRoot}"], "limitSymbolsToIncludedHeaders": true, "databaseFilename": ""}, "cStandard": "c11", "cppStandard": "c++17"}, {"name": "Win32", "includePath": ["C:/Program Files (x86)/Microsoft Visual Studio 14.0/VC/include", "${workspaceRoot}"], "defines": ["_DEBUG", "UNICODE", "_UNICODE"], "intelliSenseMode": "msvc-x64", "browse": {"path": ["C:/Program Files (x86)/Microsoft Visual Studio 14.0/VC/include/*", "${workspaceRoot}"], "limitSymbolsToIncludedHeaders": true, "databaseFilename": ""}, "cStandard": "c11", "cppStandard": "c++17"}], "version": 4}