// MyLO Development LLC. Confidential - All Rights Reserved
// Generated file. Do not edit by hand.

#ifndef DEFINE_LITERALS
struct Account
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(accountId, 1);
    DECLARE_LITERAL(account<PERSON><PERSON>, 2);
    DECLARE_LITERAL(sub, 3);
    DECLARE_LITERAL(flags, 4);
    DECLARE_LITERAL(peerToPeerKey, 5);
    DECLARE_LITERAL(email, 6);
    DECLARE_LITERAL(cipher, 8);
    DECLARE_LITERAL(idp, 9);
    DECLARE_LITERAL(planId, 10);
    DECLARE_LITERAL(role, 11);
    DECLARE_LITERAL(min<PERSON>uild, 12);
    DECLARE_LITERAL(rsaPrivateKey, 13);
    DECLARE_LITERAL(x509Cert, 14);
    DECLARE_LITERAL(deviceLimit, 15);
    DECLARE_LITERAL(photoLimit, 16);
    DECLAR<PERSON>_LITERAL(features, 17);
    DECLARE_LITERAL(featureSet1, 18);
    DECLARE_LITERAL(nextPlanDate, 19);
    DECLARE_LITERAL(cloudStorageLimit, 20);
    DECLARE_LITERAL(clientCipher, 21);
    DECLARE_LITERAL(clientPeerToPeerKey, 22);
    DECLARE_LITERAL(clientCipherVersion, 23);
    DECLARE_LITERAL(clientPeerToPeerKeyVersion, 24);
    DECLARE_LITERAL(availableUpgrades, 25);
    DECLARE_LITERAL(licenseTemplateId, 26);
    DECLARE_LITERAL(licenseDisplayName, 27);
    DECLARE_LITERAL(licenseFlags, 28);
    DECLARE_LITERAL(licenseManager, 29);
    DECLARE_LITERAL(availableUpgradesFeatures, 30);

#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct Album
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(SF, 6);
    DECLARE_LITERAL(PAL, 7);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct Appointment
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(IsAllDay, 22);
    DECLARE_LITERAL(IsRecurring, 23);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct AuditEntry
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(ALAT, 2);
    DECLARE_LITERAL(ALDL, 3);
    DECLARE_LITERAL(ALDS, 0);
    DECLARE_LITERAL(ALD, 4);
    DECLARE_LITERAL(ALC, 5);
    DECLARE_LITERAL(ALM, 6);
    DECLARE_LITERAL(ALR, 7);
    DECLARE_LITERAL(ALDI, 8);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct BucketDeviceLink
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(LocalFiles, 2);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct Channel
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(KindCode, 0);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct DeletedResource
{
#endif // DEFINE_LITERALS
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct Device
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(deviceId, 1);
    DECLARE_LITERAL(name, 2);
    DECLARE_LITERAL(deviceType, 3);
    DECLARE_LITERAL(creationTime, 4);
    DECLARE_LITERAL(nickname, 5);
    DECLARE_LITERAL(longId, 7);
    DECLARE_LITERAL(deleted, 16);
    DECLARE_LITERAL(deletedVersion, 17);
    DECLARE_LITERAL(IsThisDevice, 0);
    DECLARE_LITERAL(NASHash, 14);
    DECLARE_LITERAL(InitialBuild, 6);
    DECLARE_LITERAL(DeviceLocation, 12);
    DECLARE_LITERAL(FDLs, 21);
    DECLARE_LITERAL(Shuttle, 23);
    DECLARE_LITERAL(IsEncrypted, 25);
    DECLARE_LITERAL(ByocRootFolderId, 26);
    DECLARE_LITERAL(supportTicket, 27);
    DECLARE_LITERAL(PrioritizeLocalReplication, 28);
    DECLARE_LITERAL(flags, 29);
    DECLARE_LITERAL(DiskFullAlertLevel, 30);
    DECLARE_LITERAL(LinkedFolderSettingDisabled, 31);
    DECLARE_LITERAL(ReservedSpace, 32);
    DECLARE_LITERAL(DeviceRole, 33);
    DECLARE_LITERAL(AutoReservedSpace, 34);
    DECLARE_LITERAL(ReservedSpaceLocal, 0);
    DECLARE_LITERAL(KeepAllDocuments, 35);
    DECLARE_LITERAL(ProtectedVault, 36);
    DECLARE_LITERAL(CategorySchema, 37);
    DECLARE_LITERAL(ByocRootFolderIdV2, 38);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct DeviceData
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(deviceId, 1);
    DECLARE_LITERAL(build, 3);
    DECLARE_LITERAL(lastAccessTime, 4);
    DECLARE_LITERAL(Paths, 5);
    DECLARE_LITERAL(os, 6);
    DECLARE_LITERAL(NNO, 7);
    DECLARE_LITERAL(Schema, 8);
    DECLARE_LITERAL(ResourceCount, 9);
    DECLARE_LITERAL(mediaCount, 10);
    DECLARE_LITERAL(Address, 11);
    DECLARE_LITERAL(ThumbnailSize, 12);
    DECLARE_LITERAL(PreviewSize, 13);
    DECLARE_LITERAL(originalSize, 14);
    DECLARE_LITERAL(DisabledDevices, 15);
    DECLARE_LITERAL(LastSupportTicketProcessed, 16);
    DECLARE_LITERAL(LastFilesInSyncTime, 0);
    DECLARE_LITERAL(ResourceKinds, 17);
    DECLARE_LITERAL(protocolVersion, 18);
    DECLARE_LITERAL(LocalThumbnailSize, 19);
    DECLARE_LITERAL(LocalPreviewSize, 20);
    DECLARE_LITERAL(LocalOriginalSize, 21);
    DECLARE_LITERAL(version, 22);
    DECLARE_LITERAL(lastStartupTime, 23);
    DECLARE_LITERAL(lastHidTime, 24);
    DECLARE_LITERAL(lastImportTime, 25);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct Email
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(EmailAlias, 2);
    DECLARE_LITERAL(IsGroup, 3);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct Event
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(SRC, 6);
    DECLARE_LITERAL(SID, 7);
    DECLARE_LITERAL(LAT, 8);
    DECLARE_LITERAL(LON, 9);
    DECLARE_LITERAL(CAP, 10);
    DECLARE_LITERAL(MBU, 11);
    DECLARE_LITERAL(SDT, 12);
    DECLARE_LITERAL(EDT, 13);
    DECLARE_LITERAL(SCOPE, 14);
    DECLARE_LITERAL(DO, 15);
    DECLARE_LITERAL(RAT, 16);
    DECLARE_LITERAL(CATE, 17);
    DECLARE_LITERAL(FT, 18);
    DECLARE_LITERAL(SC, 19);
    DECLARE_LITERAL(ParentEvent, 20);
    DECLARE_LITERAL(Location, 21);
    DECLARE_LITERAL(KindCode, 0);
    DECLARE_LITERAL(ImportedLocally, 0);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct FaceRectangle
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(TopLeftX, 2);
    DECLARE_LITERAL(TopLeftY, 3);
    DECLARE_LITERAL(Width, 4);
    DECLARE_LITERAL(Height, 5);
    DECLARE_LITERAL(SID, 6);
    DECLARE_LITERAL(PER, 7);
    DECLARE_LITERAL(MD, 8);
    DECLARE_LITERAL(CLID, 0);
    DECLARE_LITERAL(FDSC, 9);
    DECLARE_LITERAL(FINF, 10);
    DECLARE_LITERAL(FIGN, 11);
    DECLARE_LITERAL(PPHS, 0);
    DECLARE_LITERAL(CONF, 0);
    DECLARE_LITERAL(RPHS, 14);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct FacebookCheckin
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(FBMessage, 22);
    DECLARE_LITERAL(FBApplicationName, 23);
    DECLARE_LITERAL(FBCaption, 24);
    DECLARE_LITERAL(FBIsPhotoPost, 25);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct FacebookEvent
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(FBEventDescription, 22);
    DECLARE_LITERAL(RsvpStatus, 23);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct FilterHistoryItem
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(TimeStamp, 2);
    DECLARE_LITERAL(FilterString, 3);
    DECLARE_LITERAL(IsPinned, 4);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct Folder
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(LocalRootOrTemporaryPath, 0);
    DECLARE_LITERAL(IsMissing, 0);
    DECLARE_LITERAL(IsPrivate, 6);
    DECLARE_LITERAL(RDL, 9);
    DECLARE_LITERAL(HasUnhandledFiles, 0);
    DECLARE_LITERAL(ALURL, 0);
    DECLARE_LITERAL(LocalName, 0);
    DECLARE_LITERAL(ReadOnlyDeviceHash, 8);
    DECLARE_LITERAL(PFO, 7);
    DECLARE_LITERAL(FE, 61);
    DECLARE_LITERAL(FER, 62);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct FolderDeviceLink
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(SmallIntention, 4);
    DECLARE_LITERAL(SmallKeywords, 6);
    DECLARE_LITERAL(SmallRatingFilter, 7);
    DECLARE_LITERAL(SmallStarFilter, 8);
    DECLARE_LITERAL(LargeIntention, 10);
    DECLARE_LITERAL(LargeKeywords, 11);
    DECLARE_LITERAL(LargeRatingFilter, 12);
    DECLARE_LITERAL(LargeStarFilter, 13);
    DECLARE_LITERAL(MediumIntention, 5);
    DECLARE_LITERAL(MediumKeywords, 15);
    DECLARE_LITERAL(MediumRatingFilter, 16);
    DECLARE_LITERAL(MediumStarFilter, 17);
    DECLARE_LITERAL(OverrideChildMDLs, 22);
    DECLARE_LITERAL(FR, 2);
    DECLARE_LITERAL(DV, 3);
    DECLARE_LITERAL(LastOverwriteTime, 23);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct ForgottenResource
{
#endif // DEFINE_LITERALS
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct GhostedResource
{
#endif // DEFINE_LITERALS
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct ImportSession
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(SDT, 2);
    DECLARE_LITERAL(EDT, 3);
    DECLARE_LITERAL(SRC, 4);
    DECLARE_LITERAL(DID, 5);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct ImportableResource
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(IS, 2);
    DECLARE_LITERAL(Categories, 60);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct IngestIgnoreDeviceLink
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(DeviceId, 1);
    DECLARE_LITERAL(Url, 2);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct Internet
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(IPAddress, 2);
    DECLARE_LITERAL(URL, 3);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct Link
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(FDT, 4);
    DECLARE_LITERAL(TDT, 5);
    DECLARE_LITERAL(SRS, 2);
    DECLARE_LITERAL(TRS, 3);
    DECLARE_LITERAL(SourceResourceKind, 6);
    DECLARE_LITERAL(TargetResourceKind, 7);
    DECLARE_LITERAL(KindCode, 0);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct Media
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(DC, 8);
    DECLARE_LITERAL(SR, 9);
    DECLARE_LITERAL(KW, 10);
    DECLARE_LITERAL(FSPathChanged, 0);
    DECLARE_LITERAL(MN, 11);
    DECLARE_LITERAL(YN, 12);
    DECLARE_LITERAL(CAP, 13);
    DECLARE_LITERAL(DN, 14);
    DECLARE_LITERAL(OT, 16);
    DECLARE_LITERAL(SID, 17);
    DECLARE_LITERAL(TL, 18);
    DECLARE_LITERAL(LB, 19);
    DECLARE_LITERAL(CY, 20);
    DECLARE_LITERAL(CMK, 21);
    DECLARE_LITERAL(CMO, 22);
    DECLARE_LITERAL(CSN, 23);
    DECLARE_LITERAL(GLA, 24);
    DECLARE_LITERAL(GLO, 25);
    DECLARE_LITERAL(FN, 3);
    DECLARE_LITERAL(AP, 27);
    DECLARE_LITERAL(CR, 28);
    DECLARE_LITERAL(FL, 29);
    DECLARE_LITERAL(FO, 30);
    DECLARE_LITERAL(ISO, 31);
    DECLARE_LITERAL(LE, 32);
    DECLARE_LITERAL(RV, 33);
    DECLARE_LITERAL(SS, 34);
    DECLARE_LITERAL(AU, 35);
    DECLARE_LITERAL(IF, 36);
    DECLARE_LITERAL(IE, 37);
    DECLARE_LITERAL(OES, 38);
    DECLARE_LITERAL(EM, 39);
    DECLARE_LITERAL(WTE, 40);
    DECLARE_LITERAL(WTI, 41);
    DECLARE_LITERAL(DCC, 42);
    DECLARE_LITERAL(NFN, 43);
    DECLARE_LITERAL(CZF, 44);
    DECLARE_LITERAL(EB, 45);
    DECLARE_LITERAL(MM, 46);
    DECLARE_LITERAL(WM, 47);
    DECLARE_LITERAL(LocalFileNameNoExt, 0);
    DECLARE_LITERAL(ScannerFiles, 0);
    DECLARE_LITERAL(LocalTempContainingFolderURL, 0);
    DECLARE_LITERAL(II, 48);
    DECLARE_LITERAL(IID, 49);
    DECLARE_LITERAL(AddToCacheTime, 0);
    DECLARE_LITERAL(SearchAttributes, 0);
    DECLARE_LITERAL(SearchPartitions, 0);
    DECLARE_LITERAL(AddToCacheTimeRemovable, 0);
    DECLARE_LITERAL(PCS, 50);
    DECLARE_LITERAL(CF, 4);
    DECLARE_LITERAL(PL, 52);
    DECLARE_LITERAL(PEI, 53);
    DECLARE_LITERAL(RF, 5);
    DECLARE_LITERAL(ContainedInAlbum, 0);
    DECLARE_LITERAL(BA, 55);
    DECLARE_LITERAL(Files, 6);
    DECLARE_LITERAL(LocalFiles, 0);
    DECLARE_LITERAL(NeedsId, 0);
    DECLARE_LITERAL(SerializedNeeds, 57);
    DECLARE_LITERAL(MDLs, 58);
    DECLARE_LITERAL(BG, 56);
    DECLARE_LITERAL(GPR, 61);
    DECLARE_LITERAL(DRS, 62);
    DECLARE_LITERAL(DRE, 63);
    DECLARE_LITERAL(SCOPE, 64);
    DECLARE_LITERAL(UND, 65);
    DECLARE_LITERAL(BundleManifest, 66);
    DECLARE_LITERAL(Address, 67);
    DECLARE_LITERAL(City, 68);
    DECLARE_LITERAL(StateProvince, 69);
    DECLARE_LITERAL(PostalCode, 70);
    DECLARE_LITERAL(Country, 71);
    DECLARE_LITERAL(Phone, 72);
    DECLARE_LITERAL(Credit, 73);
    DECLARE_LITERAL(Email, 74);
    DECLARE_LITERAL(CopyrightInfoUrl, 75);
    DECLARE_LITERAL(SpecialInstructions, 76);
    DECLARE_LITERAL(ReuploadBYOA, 0);
    DECLARE_LITERAL(SoftFetchPreview, 0);
    DECLARE_LITERAL(SoftFetchOriginal, 0);
    DECLARE_LITERAL(LastViewMinute, 0);
    DECLARE_LITERAL(AutoRanking, 0);
    DECLARE_LITERAL(AutoOriginalPriority, 0);
    DECLARE_LITERAL(PeopleCount, 0);
    DECLARE_LITERAL(FaceRectangleCount, 0);
    DECLARE_LITERAL(Purgeable, 0);
    DECLARE_LITERAL(EffectiveCategories, 0);
    DECLARE_LITERAL(LastFileChangeWasLocal, 0);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct MediaAlbumLink
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(DO, 4);
    DECLARE_LITERAL(SRS, 2);
    DECLARE_LITERAL(TRS, 3);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct MediaContainer
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(DirectChildMedia, 0);
    DECLARE_LITERAL(NAM, 3);
    DECLARE_LITERAL(RecursiveChildMedia, 0);
    DECLARE_LITERAL(RecursiveChildMediaStartDateTime, 0);
    DECLARE_LITERAL(RecursiveChildMediaEndDateTime, 0);
    DECLARE_LITERAL(FSPathChanged, 0);
    DECLARE_LITERAL(DirectMediaEndDate, 0);
    DECLARE_LITERAL(DirectMediaStartDate, 0);
    DECLARE_LITERAL(NNM, 4);
    DECLARE_LITERAL(DirectChildContainers, 0);
    DECLARE_LITERAL(GeneratedCoverDirty, 0);
    DECLARE_LITERAL(CICM, 0);
    DECLARE_LITERAL(CM, 5);
    DECLARE_LITERAL(GeneratedCoverMedia, 0);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct MediaMLHelper
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(FD, 2);
    DECLARE_LITERAL(OD, 3);
    DECLARE_LITERAL(OK, 4);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct Message
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(messageId, 1);
    DECLARE_LITERAL(message, 2);
    DECLARE_LITERAL(link, 3);
    DECLARE_LITERAL(deviceId, 4);
    DECLARE_LITERAL(secondsToDisplay, 5);
    DECLARE_LITERAL(displayed, 6);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct NAS
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(NASC, 0);
    DECLARE_LITERAL(NetworkPath, 3);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct Party
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(SRC, 3);
    DECLARE_LITERAL(SID, 4);
    DECLARE_LITERAL(NNM, 5);
    DECLARE_LITERAL(SecondaryHomeLocation, 6);
    DECLARE_LITERAL(PrimaryHomeLocation, 7);
    DECLARE_LITERAL(WorkLocation, 8);
    DECLARE_LITERAL(WorkPhone, 9);
    DECLARE_LITERAL(HomePhone, 10);
    DECLARE_LITERAL(MobilePhone, 11);
    DECLARE_LITERAL(WorkEmail, 12);
    DECLARE_LITERAL(PrimaryEmail, 13);
    DECLARE_LITERAL(SecondaryEmail, 14);
    DECLARE_LITERAL(PrimaryWebsite, 15);
    DECLARE_LITERAL(SecondaryWebsite, 16);
    DECLARE_LITERAL(OtherLocation, 17);
    DECLARE_LITERAL(PMD, 18);
    DECLARE_LITERAL(OPM, 19);
    DECLARE_LITERAL(KindCode, 0);
    DECLARE_LITERAL(Centroid, 0);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct Person
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(Gender, 20);
    DECLARE_LITERAL(BirthDateYear, 21);
    DECLARE_LITERAL(FirstName, 22);
    DECLARE_LITERAL(LastName, 23);
    DECLARE_LITERAL(BirthDateMonth, 24);
    DECLARE_LITERAL(BirthDateDayOfMonth, 25);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct Place
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(LocationName, 3);
    DECLARE_LITERAL(SRC, 4);
    DECLARE_LITERAL(CM, 5);
    DECLARE_LITERAL(City, 7);
    DECLARE_LITERAL(Country, 8);
    DECLARE_LITERAL(State, 9);
    DECLARE_LITERAL(County, 10);
    DECLARE_LITERAL(LAT, 13);
    DECLARE_LITERAL(LON, 14);
    DECLARE_LITERAL(BoundsNELatitude, 15);
    DECLARE_LITERAL(BoundsNELongitude, 16);
    DECLARE_LITERAL(BoundsSWLatitude, 17);
    DECLARE_LITERAL(BoundsSWLongitude, 18);
    DECLARE_LITERAL(TypeName, 19);
    DECLARE_LITERAL(TypeValue, 20);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct Resource
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(r, 0);
    DECLARE_LITERAL(Id, 0);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct ResourceDeviceAlert
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(RH, 2);
    DECLARE_LITERAL(DV, 3);
    DECLARE_LITERAL(NormalizedGlobalPath, 6);
    DECLARE_LITERAL(Alert, 4);
    DECLARE_LITERAL(Description, 5);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct SharedConfiguration
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(Key, 2);
    DECLARE_LITERAL(Value, 3);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct System
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(systemPropertyId, 1);
    DECLARE_LITERAL(deleted, 4);
    DECLARE_LITERAL(deletedVersion, 5);
    DECLARE_LITERAL(name, 2);
    DECLARE_LITERAL(value, 3);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct Telephone
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(TelephoneNumber, 2);
    DECLARE_LITERAL(Extension, 3);
    DECLARE_LITERAL(CountryCode, 4);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct UnknownResource
{
#endif // DEFINE_LITERALS
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#ifndef DEFINE_LITERALS
struct User
{
#endif // DEFINE_LITERALS
    DECLARE_LITERAL(userPropertyId, 1);
    DECLARE_LITERAL(deleted, 4);
    DECLARE_LITERAL(name, 2);
    DECLARE_LITERAL(value, 3);
#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS
