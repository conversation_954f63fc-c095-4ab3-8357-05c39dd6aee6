import express = require("express");
import { microservice as g } from "../microservices/account.microservice.mjs";
import { sanitizeInput, sanitizeOutput } from "../models/Account.model.mjs";
import {
  safeAccount,
  safeNone,
  safeAny,
  secure,
  admin,
  s,
  secureURLToken,
} from "../system/safe.mjs";
import { config, getServices } from "../system/Config.mjs";
import { binaryEncoder, sendResponse } from "../system/bjson.cjs";
import { EventType } from "../system/Strings.mjs";
import { telemetry } from "../system/telemetry.mjs";
import { Context } from "../system/Context.mjs";
import { Account, IAccount } from "../models/Account.model.mjs";
import { makeError } from "../system/error.mjs";
import { ILicense, License, Manager } from "../models/License.model.mjs";
import { Mode } from "../models/IModel.mjs";
import { recaptcha } from "../system/recaptcha.mjs";

export function addAccountRoutes(router: express.Router) {
  router.get("/accounts/:aid", safeNone, secure, (req, res, next) => {
    let context = req.context as Context;
    return g.accountService
      .read(context, context.aid)
      .then((account) => {
        context.dumpLog();
        return res
          .status(200)
          .json(sanitizeOutput(account, context.hasAdminRights()));
      })
      .catch(next);
  });

  router.put("/accounts/:aid", safeAccount, secure, async (req, res, next) => {
    let context = req.context;
    let oldAccount = await g.accountService.read(context, context.aid);
    if (
      context.account.planId() &&
      oldAccount.planId() !== context.account.planId()
    ) {
      throw makeError(
        400,
        "PLAN_FIELD_NOT_UPDATABLE",
        "You have to create a subscription to change the plan"
      );
    } else {
      let account = await g.accountService.update(
        context,
        new Account(
          sanitizeInput(context.account, context.hasAdminRights(), Mode.Update)
        )
      );
      context.dumpLog();
      return res
        .status(200)
        .json(sanitizeOutput(account, context.hasAdminRights()));
    }
  });

  router.get("/tests", async (req, res, next) => {
    return res.status(200).json({ hello: "world" });
  });

  router.post("/accounts", safeAccount, recaptcha, async (req, res, next) => {
    const context: Context = req.context;
    let inAccount = new Account(
      sanitizeInput(context.account, context.hasAdminRights(), Mode.Create)
    );
    inAccount.role("user");
    inAccount.idp("mylio");
    let account = await g.licenseService.createAccountAndLicense(
      context,
      inAccount
    );
    account = await g.accountService.read(context, account.accountId());
    const rtokenResult = await g.authenticationService.ptoken(
      context,
      context.audience,
      account
    );

    telemetry(context, account.accountId(), EventType.ACCOUNT_CREATED, {
      aid: account.accountId(),
    });

    context.dumpLog();

    const services = getServices(context);
    const clean: any = {
      account: sanitizeOutput(account, context.hasAdminRights()),
      rtoken: rtokenResult.rtoken,
      token: rtokenResult.token,
      services,
    };

    return sendResponse(req, res, clean, (r) =>
      binaryEncoder.encode_account(clean)
    );
  });

  router.delete("/accounts/:aid", safeNone, secure, async (req, res, next) => {
    let context = req.context;
    await g.accountService
      .delete(context, context.aid);
    telemetry(context, context.aid, EventType.ACCOUNT_DELETED);
    context.dumpLog();
    return res.json({ "status": "ok" });
  });


  router.put(
    "/accounts/:aid/password",
    safeAny,
    secure,
    async (req, res, next) => {
      let context = req.context;
      let account = await g.authenticationService.changePassword(
        context,
        context.aid,
        context.any
      );
      telemetry(context, context.aid, EventType.PASSWORD_CHANGED, {
        aid: context.aid,
      });
      context.dumpLog();
      return res
        .status(200)
        .json(sanitizeOutput(account, context.hasAdminRights()));
    }
  );

  router.get("/keys", safeNone, secure, (req, res, next) => {
    return res.status(200).json({
      flickrAppKey: config.flickr_app_key,
      flickrApiSecret: config.flickr_api_secret,
    });
  });

  router.get("/media/getSupportPolicy", safeNone, secure, (req, res, next) => {
    let context = req.context;
    let issue = context.query.issue;
    let ext = context.query.ext;

    return g.accountService
      .getSupportPolicy(context, issue, ext)
      .then((policy) => {
        context.dumpLog();
        return res.status(200).json(policy);
      })
      .catch(next);
  });

  router.get("/groove/auth", safeNone, secure, (req, res, next) => {
    let context = req.context;
    context.dumpLog();

    return res.status(200).json({ token: config.groove_private_key });
  });

  router.post(
    "/accounts/:aid/emails/:emailTemplateId",
    safeAny,
    async (req, res, next) => {
      let context = req.context;
      let emailTemplateId = req.params.emailTemplateId;
      let params = context.any.params;
      let to = context.any.email;

      return g.emailService
        .sendEmail(context, to, emailTemplateId, params || {})
        .then(() => {
          context.dumpLog();
          return res.status(200).json({});
        })
        .catch(next);
    }
  );

  router.post(
    "/accounts/:aid/send-sso-to-pin-link",
    safeAny,
    secure,
    async (req, res, next) => {
      try {
        let context = req.context;
        let email = context.any.email as string;
        await g.authenticationService.sendSsoToPinLink(context, email);
        return res.status(200).send();
      } catch (err) {
        next(err);
      }
    }
  );

  router.get(
    "/accounts/:aid/sso-to-pin/:token",
    safeAny,
    secureURLToken,
    async (req, res, next) => {
      let context = req.context;
      let email = req.query.email as string;
      await g.authenticationService.ssoToPin(context, email);
      return res.status(200).send(`html><body><h3>Account will now use login links</h3></body></html>`);
    }
  );

  router.delete("/accounts/:aid/resources", safeNone, secure, async (req, res, next) => {
    req.setTimeout(600000);
    let context = req.context;
    await g.resourceService.deleteResources(context, context.aid);
    res.status(200).send();
  });


}
