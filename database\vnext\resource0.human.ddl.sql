\set ON_ERROR_STOP on

SELECT pg_terminate_backend(pg_stat_activity.pid)
FROM pg_stat_activity
WHERE pg_stat_activity.datname = '/* r::database */ resource0' -- ← change this to your DB
  AND pid <> pg_backend_pid();

drop database if exists /* r::database */ resource0;
create database /* r::database */ resource0; -- with owner = mylio;

\c /* r::database */ resource0; 

SET client_min_messages=WARNING;

create extension pgcrypto;

/* repeat for each schema */

\i x0.human.ddl.sql

/* end schemas */


