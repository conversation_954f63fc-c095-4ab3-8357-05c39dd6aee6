"use strict";

import * as path from "path";
import * as fs from "fs";
import * as yargs from "yargs";
import * as os from "os";
import * as rimraf from "rimraf";

interface IShard {
  root_script: string;
  database: string;
  host: string;
  port: number;
  password: string;
  foreign_server: string;
  encoding: string;
}

let eol = "\n";

if (os.platform() === "darwin") {
  // fix eol for osx
  eol = "\n";
}

let argv: {
  f: string;
  c: string;
  t: "account" | "resource" | "telemetry" | "datawarehouse";
} = yargs.usage("Usage: $0 -c[.json]").option("c", {
  alias: "config",
  description: "The config (.json file) to build against",
  type: "string",
  demand: true,
}).argv;

// create a "setup" folder to put the generated scripts into
let setupFolder = path.join(__dirname, "setup");
rimraf.sync(setupFolder);

try {
  fs.statSync(setupFolder);
} catch (err) {
  console.log("creating setup folder");
  fs.mkdirSync(setupFolder);
}

let p = path.parse(argv.c);
let globalConfig = JSON.parse(
  fs.readFileSync(path.join(p.dir, p.name + ".json"), "utf8")
);
let globalAShard = globalConfig.account.shards[0];
let globalRShards = globalConfig.resource.shards;
let globalTShard = globalConfig.telemetry.shards[0];
let globalDShard = globalConfig.datawarehouse.shards[0];
let globalSystem = globalConfig.system;

// used for 0-padding of table names e.g. x00 or x0F
function lpad(str: string, padString: string, length: number) {
  while (str.length < length) str = padString + str;
  return str;
}

// injects the scripts referenced by psql \i directives into the root script
// the replace will occur repeatedly until all nested \i directives
// have been resolved
function inject(root: string) {
  let result = root;
  let found = false;
  do {
    found = false;
    result = result.replace(/\\i ((?:\w|\.)+\.sql)/gm, (a, filename) => {
      console.log(`injecting file: ${filename}`);
      found = true;
      return fs.readFileSync(path.join(__dirname, filename), "utf8");
    });
  } while (found);
  return result;
}

// replaces comment encoded variables e.g. /* database */ resource0 with the
// config value from the shard e.g. { database: "resource1"}
function replace(root: string, shard: IShard, ashard: IShard) {
  return root
    .replace(
      /\/\*\s*r::account-(\w+)\s*\*\/\s*(')?\w+(')?/gm,
      (full, key, lq = "", rq = "") => `${lq}${ashard[key]}${rq}`
    )
    .replace(
      /\/\*\s*r::(\w+)\s*\*\/\s*(')?\w+(')?/gm,
      (full, key, lq = "", rq = "") => `${lq}${shard[key]}${rq}`
    )
    .replace(/base64/gm, globalSystem.encoding);
}

// read the root script for resource shards from the config and resolve all \i psql directives
let globalRDDLScript = inject(
  fs.readFileSync(
    path.join(__dirname, globalSystem.resource_script + ".ddl.sql"),
    "utf8"
  )
);
let globalRDMLScript = inject(
  fs.readFileSync(
    path.join(__dirname, globalSystem.resource_script + ".dml.sql"),
    "utf8"
  )
);
let globalRSecureScript = inject(
  fs.readFileSync(
    path.join(__dirname, globalSystem.resource_script + ".secure.sql"),
    "utf8"
  )
);

// read the root script for account shards from the config and resolve all \i psql directives
// then replace script variables with values from the config
let globalADDLScript = replace(
  inject(
    fs.readFileSync(
      path.join(__dirname, globalSystem.account_script + ".ddl.sql"),
      "utf8"
    )
  ),
  globalAShard,
  globalAShard
);
let globalADMLScript = replace(
  inject(
    fs.readFileSync(
      path.join(__dirname, globalSystem.account_script + ".dml.sql"),
      "utf8"
    )
  ),
  globalAShard,
  globalAShard
);
let globalASecureScript = replace(
  inject(
    fs.readFileSync(
      path.join(__dirname, globalSystem.account_script + ".secure.sql"),
      "utf8"
    )
  ),
  globalAShard,
  globalAShard
);

// read the root script for telemetry shards from the config and resolve all \i psql directives
// then replace script variables with values from the config
let globalTDDLScript = inject(
  fs.readFileSync(
    path.join(__dirname, globalSystem.telemetry_script + ".ddl.sql"),
    "utf8"
  )
);
let globalTDMLScript = inject(
  fs.readFileSync(
    path.join(__dirname, globalSystem.telemetry_script + ".dml.sql"),
    "utf8"
  )
);
let globalTSecureScript = inject(
  fs.readFileSync(
    path.join(__dirname, globalSystem.telemetry_script + ".secure.sql"),
    "utf8"
  )
);

let globalDDDLScript = inject(
  fs.readFileSync(
    path.join(__dirname, globalSystem.datawarehouse_script + ".ddl.sql"),
    "utf8"
  )
);
let globalDDMLScript = inject(
  fs.readFileSync(
    path.join(__dirname, globalSystem.datawarehouse_script + ".dml.sql"),
    "utf8"
  )
);

// write out the psql script for the account database
fs.writeFileSync(
  path.join(__dirname, "setup", `${globalAShard.database}.ddl.sql`),
  globalADDLScript,
  { encoding: "utf8" }
);
fs.writeFileSync(
  path.join(__dirname, "setup", `${globalAShard.database}.dml.sql`),
  globalADMLScript,
  { encoding: "utf8" }
);
fs.writeFileSync(
  path.join(__dirname, "setup", `${globalAShard.database}.secure.sql`),
  globalASecureScript,
  { encoding: "utf8" }
);

// write out the psql script for the telemetry database
fs.writeFileSync(
  path.join(__dirname, "setup", `${globalTShard.database}.ddl.sql`),
  globalTDDLScript,
  { encoding: "utf8" }
);
fs.writeFileSync(
  path.join(__dirname, "setup", `${globalTShard.database}.dml.sql`),
  globalTDMLScript,
  { encoding: "utf8" }
);
fs.writeFileSync(
  path.join(__dirname, "setup", `${globalTShard.database}.secure.sql`),
  globalTSecureScript,
  { encoding: "utf8" }
);

// write out the psql script for the datawarehouse database
fs.writeFileSync(
  path.join(__dirname, "setup", `${globalDShard.database}.ddl.sql`),
  globalDDDLScript,
  { encoding: "utf8" }
);
fs.writeFileSync(
  path.join(__dirname, "setup", `${globalDShard.database}.dml.sql`),
  globalDDMLScript,
  { encoding: "utf8" }
);

for (let shard of globalRShards) {
  // replace script variables with values from the config
  for (let config of [
    { ext: "ddl", script: globalRDDLScript },
    { ext: "dml", script: globalRDMLScript },
    { ext: "secure", script: globalRSecureScript },
  ]) {
    let shardScript = replace(config.script, shard, globalAShard);

    let schemaBlock = "";
    let tableBlock = "";
    let lines = shardScript.split(eol);
    let script = "";
    for (let line of lines) {
      if (/\/\*\s*repeat for each table\s*\*\//g.test(line)) {
        if (!schemaBlock) {
          throw Error("Can't start a table block outside of a schema block");
        }
        if (tableBlock) {
          throw Error("Can't start a table block inside a table block");
        }
        tableBlock = `/* ========================================================${eol}`;
        tableBlock += `    table x00${eol}`;
        tableBlock += `========================================================*/${eol}`;
      } else if (/\/\*\s*repeat for each schema\s*\*\//g.test(line)) {
        if (schemaBlock) {
          throw Error("Can't start a schema block inside a schema block");
        }
        if (tableBlock) {
          throw Error("Can't start a schema block inside a table block");
        }
        schemaBlock = `/* ========================================================${eol}`;
        schemaBlock += `    schema x0${eol}`;
        schemaBlock += `========================================================*/${eol}`;
      } else if (/\/\*\s*end tables\s*\*\//g.test(line)) {
        let allTables = "";
        for (let iTable = 0; iTable < 256; iTable++) {
          allTables +=
            eol +
            tableBlock
              .replace(/x00/gm, `x${lpad(iTable.toString(16), "0", 2)}`)
              .replace(/\/\*\s*~bucket\s*\*\/\s*\d/gm, iTable.toString());
        }
        schemaBlock += allTables;
        tableBlock = "";
      } else if (/\/\*\s*end schemas\s*\*\//g.test(line)) {
        let allSchemas = "";
        for (
          let iSchema = shard.firstBucket;
          iSchema <= shard.lastBucket;
          iSchema++
        ) {
          allSchemas += `\r\n${schemaBlock.replace(
            /x0(\b|_)/gm,
            `x${iSchema.toString(16)}$1`
          )} `;
        }
        script += allSchemas;
        schemaBlock = "";
      } else {
        if (tableBlock) tableBlock += eol + line;
        else if (schemaBlock) schemaBlock += eol + line;
        else script += eol + line;
      }
    }

    // write out the psql script for this resource shard
    fs.writeFileSync(
      path.join(__dirname, "setup", `${shard.database}.${config.ext}.sql`),
      script,
      { encoding: "utf8" }
    );
  }
}
