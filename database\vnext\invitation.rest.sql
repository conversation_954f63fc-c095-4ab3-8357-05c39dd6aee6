

    drop view if exists a0."Invitation" cascade;

    create or replace view a0."Invitation" as
    select
    flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		email,
		pin,
		expires_at as "expiresAt",
		device_config as "deviceConfig"
    from a0.invitation;
    

drop function if exists a0.invitation_create; 
        create function a0.invitation_create(
            _flags int,
	_modified_time timestamptz,
	_created_time timestamptz,
	_account_id int,
	_email text,
	_pin text,
	_expires_at timestamptz,
	_device_config text
        )
        returns a0."Invitation"
        as $$
        
    declare
        result a0."Invitation";
        
    begin
        
        


       
        


        
        
        
        
        _modified_time := coalesce(_modified_time, now());
        _created_time := coalesce(_created_time, now());
        insert into a0.invitation (
            flags,
	modified_time,
	created_time,
	account_id,
	email,
	pin,
	expires_at,
	device_config
        )
        values(
            _flags,
			_modified_time,
			_created_time,
			_account_id,
			_email,
			_pin,
			_expires_at,
			_device_config
        )
        returning
            flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		email,
		pin,
		expires_at as "expiresAt",
		device_config as "deviceConfig"
        into result;

        



        

    
        



        return result;
    end;
        $$
        language plpgsql;
        

drop function if exists a0.invitation_update; 
        create function a0.invitation_update(
            _flags int,
	_modified_time timestamptz,
	_created_time timestamptz,
	_account_id int,
	_email text,
	_pin text,
	_expires_at timestamptz,
	_device_config text
        )
        returns a0."Invitation"
        as $$
        
    declare
        result a0."Invitation";
        
    begin
        
        


       
        


        
        
        _modified_time := now();
        update a0.invitation
        set
            flags = _flags,
			modified_time = _modified_time,
			created_time = _created_time,
			expires_at = _expires_at,
			device_config = _device_config
        where account_id = _account_id and email = _email and pin = _pin
        returning
            flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		email,
		pin,
		expires_at as "expiresAt",
		device_config as "deviceConfig"
        into result;

        

    
        



        return result;
    end;
        $$
        language plpgsql;
        

drop function if exists a0.invitation_read_by_account_id_and_email_and_pin; 
        create function a0.invitation_read_by_account_id_and_email_and_pin(
            _account_id int,
	_email text,
	_pin text
        )
        returns a0."Invitation"
        as $$
        select
        flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		email,
		pin,
		expires_at as "expiresAt",
		device_config as "deviceConfig"
        from a0.invitation
        where account_id = _account_id and email = _email and pin = _pin;
        $$
        language sql;
        

drop function if exists a0.invitation_read_by_pin; 
        create function a0.invitation_read_by_pin(
            _pin text
        )
        returns a0."Invitation"
        as $$
        select
        flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		email,
		pin,
		expires_at as "expiresAt",
		device_config as "deviceConfig"
        from a0.invitation
        where pin = _pin;
        $$
        language sql;
        

drop function if exists a0.invitation_delete_by_account_id_and_email_and_pin; 
        create function a0.invitation_delete_by_account_id_and_email_and_pin(
            _account_id int,
	_email text,
	_pin text
        )
        returns void
        as $$
        
    declare
        
        
    begin
        
        


       
        


        

    delete from a0.invitation
    where account_id = _account_id and email = _email and pin = _pin;

    
    
        



        
    end;
        $$
        language plpgsql;
        

drop function if exists a0.invitation_delete_by_pin; 
        create function a0.invitation_delete_by_pin(
            _pin text
        )
        returns void
        as $$
        
    declare
        
        
    begin
        
        


       
        


        

    delete from a0.invitation
    where pin = _pin;

    
    
        



        
    end;
        $$
        language plpgsql;
        

drop function if exists a0.invitation_delete_by_account_id; 
        create function a0.invitation_delete_by_account_id(
            _account_id int
        )
        returns void
        as $$
        
    declare
        
        
    begin
        
        


       
        


        

    delete from a0.invitation
    where account_id = _account_id;

    
    
        



        
    end;
        $$
        language plpgsql;
        
