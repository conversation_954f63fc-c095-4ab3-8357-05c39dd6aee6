DO
$$
BEGIN
   IF NOT EXISTS (
      SELECT
      FROM   pg_catalog.pg_user
      WHERE  usename = 'datawarehouse') THEN
        CREATE USER datawarehouse NOSUPERUSER PASSWORD 'password';
   END IF;
   IF NOT EXISTS (
      SELECT
      FROM   pg_catalog.pg_user
      WHERE  usename = 'cloud') THEN
        CREATE USER cloud NOSUPERUSER PASSWORD 'password';
   END IF;
   IF NOT EXISTS (
      SELECT
      FROM   pg_catalog.pg_user
      WHERE  usename = 'reports') THEN
        CREATE USER reports NOSUPERUSER PASSWORD 'password';
   END IF;
END
$$;

REVOKE ALL ON DATABASE account0 FROM PUBLIC;
GRANT CONNECT ON DATABASE /* r::database */ account0 TO datawarehouse;
GRANT CONNECT ON DATABASE /* r::database */ account0 TO cloud;
GRANT CONNECT ON DATABASE /* r::database */ account0 TO reports;

\c account0;

-- remove all permissions for everyone except postgres
REVOKE USAGE ON SCHEMA public FROM PUBLIC;
R<PERSON><PERSON><PERSON> ALL ON ALL TABLES IN SCHEMA public FROM PUBLIC;
<PERSON><PERSON><PERSON><PERSON> ALL ON ALL SEQUENCES IN SCHEMA public FROM PUBLIC;
REVOKE ALL ON ALL FUNCTIONS IN SCHEMA public FROM PUBLIC;
REVOKE USAGE ON SCHEMA a0 FROM PUBLIC;
REVOKE ALL ON ALL TABLES IN SCHEMA a0 FROM PUBLIC;
REVOKE ALL ON ALL SEQUENCES IN SCHEMA a0 FROM PUBLIC;
REVOKE ALL ON ALL FUNCTIONS IN SCHEMA a0 FROM PUBLIC;

--datawarehouse user
grant usage on schema public to datawarehouse;
grant usage on schema a0 to datawarehouse;
grant execute on function a0.get_modified_accounts(timestamptz) to datawarehouse;
grant execute on function a0.get_modified_devices(timestamptz) to datawarehouse;

-- cloud user
grant usage on schema public to cloud;
grant usage on schema a0 to cloud;
grant temp on database account0 to cloud;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO cloud;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO cloud;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO cloud;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA a0 TO cloud;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA a0 TO cloud;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA a0 TO cloud;

-- reports user
GRANT USAGE ON SCHEMA a0 TO reports;
GRANT SELECT ON ALL TABLES IN SCHEMA a0 TO reports;

grant execute on function digest(bytea, text) to public;
grant execute on function digest(text, text) to public;
grant execute on function gen_random_bytes(int) to public;

