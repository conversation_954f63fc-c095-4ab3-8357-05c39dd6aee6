{"plural": "messages", "flow": "web<>cloud<>postgres<>disk", "k": 33, "mixin": ["all", "syncable"], "fields": {"device_id": {"datatype": "int32"}, "message_id": {"autoNumber": true, "datatype": "int32"}, "seconds_to_display": {"datatype": "int32"}, "displayed": {"datatype": "int32"}, "message": {"datatype": "string"}, "link": {"datatype": "string"}}, "directives": {"identify": [["account_id", "message_id"]], "find": [["account_id"]]}}