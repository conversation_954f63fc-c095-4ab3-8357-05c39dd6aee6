\c datawarehouse

delete from d0.device d
       using a0.device a0d
       where d.device_id = a0d.device_id
       and d.account_id = a0d.account_id
       and d.device_type not in (18, 19)
       and not exists (
           select * from a0.device_data dd
                  where d.device_id = dd.device_id
                  and d.account_id = dd.account_id
       );

delete from d0.device_history dh
       using a0.device a0d
       where dh.device_id = a0d.device_id
       and dh.account_id = a0d.account_id
       and dh.device_type not in (18, 19)
       and not exists (
           select * from a0.device_data dd
                  where dh.device_id = dd.device_id
                  and dh.account_id = dd.account_id
       );
