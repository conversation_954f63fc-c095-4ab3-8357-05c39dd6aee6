import express = require("express");
import cors = require("cors");
import bodyParser = require("body-parser");
import { bootstrap } from "./system/bootstrap.mjs";
import { microservice } from "./microservices/account.microservice.mjs";
import { addAccountRoutes } from "./routes/account.routes.mjs";
import { addDeviceRoutes } from "./routes/device.routes.mjs";
import { addHealthRoutes } from "./routes/account.health.routes.mjs";
import { addAnonymousRoutes } from "./routes/anonymous.routes.mjs";
import { addAdminRoutes } from "./routes/admin.routes.mjs";
import { config, testSecrets } from "./system/Config.mjs";
import { errorHandler } from "./system/errorHandler.mjs";
import { addSyncRoutes } from "./routes/sync.routes.mjs";
import { addBinaryRoutes } from "./routes/account.binary.routes.mjs";
import { addLockRoutes } from "./routes/lock.routes.mjs";
import { addBacktraceRoutes } from "./routes/backtrace.routes.mjs";
import { addAmazonRoutes } from "./routes/amazon.routes.mjs";
import { addGoogleRoutes } from "./routes/google.routes.mjs";
import { addFacebookRoutes } from "./routes/facebook.routes.mjs";
import { addInstagramRoutes } from "./routes/instagram.routes.mjs";
import { addAppleRoutes } from "./routes/apple.routes.mjs";
import { addMicrosoftRoutes } from "./routes/microsoft.routes.mjs";
import { addMessageRoutes } from "./routes/message.routes.mjs";
import { makeError, logError } from "./system/error.mjs";
import { ids } from "./system/Strings.mjs";
import { Token } from "./models/Token.mjs";
import { addHtmlRoutes } from "./routes/html.routes.mjs";
import { addMagicLinkRoutes } from "./routes/magic-link.routes.mjs";
import { addLicenseRoutes } from "./routes/license.routes.mjs";
import { addAuthenticationRoutes } from "./routes/authentication.routes.mjs";
import addFastSpringWebhook from "./routes/fastSpring.webhook.mjs";
import addFastSpringRoutes from "./routes/fastSpring.routes.mjs";
import addAppStoreWebhook from "./routes/appstore.webhook.mjs";
import { addPinRoutes } from "./routes/pin.routes.mjs";
import { addSsoRoutes } from "./routes/sso.routes.mjs";
import https from "https";
import fs from "fs";
import path from "path";
let app = express();

import * as url from "url";
import { addBackblazeRoutes } from "./routes/backblaze.routes.mjs";
import { addMultiUserRoutes } from "./routes/multiuser.routes.mjs";
import { parseUserAgent } from "./system/UserAgent.mjs";
const __filename = url.fileURLToPath(import.meta.url);
const __dirname = url.fileURLToPath(new URL(".", import.meta.url));



async function update() {
  app.get("/health", (req, res, next) => {
    return res.status(200).json({ status: "ok" });
  });

  app.listen(config.port, () => {
    console.log(
      `Cloud running on port ${config.port
      } started at ${new Date().toUTCString()}`
    );
    console.log(process.pid);
  });

}

async function run() {
  bootstrap()
    .then(() => microservice.start())
    .then(() => {
      app.set("etag", false);
      app.set("x-powered-by", false);
      app.set("lastModified", false);

      // must be added first
      addFastSpringWebhook(app);
      addAppStoreWebhook(app);
      app.use(parseUserAgent);
      app.use(cors({ maxAge: Number.MAX_SAFE_INTEGER }));
      app.use(express.json({ limit: "50mb" }));
      app.use(express.raw({ inflate: false, limit: "50mb" }));

      //if (process.env.HOST !== "localhost") {
      app.use(function (req, res, next) {
        res.setHeader("Strict-Transport-Security", "60000");
        next();
      });
      //}

      app.use((req, res, next) => {
        if (microservice.maintenanceMode) {
          let url = req.url;

          if (url === "/accounts/x/rtoken") {
            return next();
          }

          let authHeader = req.header("Authorization");
          let tokenString = "";

          if (authHeader) {
            let parts = authHeader.split(" ");
            if (parts.length > 1) {
              tokenString = parts[1];
            }
          }

          if (tokenString) {
            let result = Token.tryVerify(tokenString, config.access_token_secret);
            if (result.success && result.token.hasAdminRights) return;
          }

          return next(makeError(503, ids.MAINTENANCE_MODE));
        }

        return next();
      });

      // app.use("*", (req, res, next) => {
      //     if (req.header("Content-Type") === "application/octet-stream") {
      //         console.log(`binary - path: ${req.originalUrl}`);
      //     }

      //     next();
      // });

      addBinaryRoutes(app);
      addHealthRoutes(app);
      addAnonymousRoutes(app);
      addAccountRoutes(app);
      addDeviceRoutes(app);
      addSyncRoutes(app);
      addAdminRoutes(app);
      addFacebookRoutes(app);
      addInstagramRoutes(app);
      addAppleRoutes(app);
      addMicrosoftRoutes(app);

      // lock shared resources such as google drive
      addLockRoutes(app);

      addBacktraceRoutes(app);
      addAmazonRoutes(app);
      addGoogleRoutes(app);
      addMessageRoutes(app);

      addHtmlRoutes(app);
      addMagicLinkRoutes(app);

      addLicenseRoutes(app);
      addAuthenticationRoutes(app);
      addFastSpringRoutes(app);
      addPinRoutes(app);
      addSsoRoutes(app);
      addBackblazeRoutes(app);
      addMultiUserRoutes(app);
      app.get("/testzzz", async (req, res, next) => {
        await testSecrets();
        return res.status(200).json({ status: "ok" });
      });

      // not found catch all
      app.use(async (req, res, next) => {
        throw makeError(
          400,
          "THIS_ROUTE_DOES_NOT_EXIST",
          `route ${req.route} does not exist`
        );
      });

      // global error handler
      app.use(errorHandler);

      if (process.env.IS_WILLEM === "1")
        https
          .createServer(
            // Provide the private and public key to the server by reading each
            // file's content with the readFileSync() method.
            {
              key: fs.readFileSync(
                "C:/certbot/live/willem.mylio.com/privkey.pem"
              ),
              cert: fs.readFileSync(
                "C:/certbot/live/willem.mylio.com/fullchain.pem"
              ),
            },
            app
          )
          .listen(443, "192.168.0.160", () => {
            console.log("serever is runing at port " + process.env.PORT);
          });
      else
        app.listen(config.port, () => {
          console.log(
            `Cloud running on port ${config.port
            } started at ${new Date().toUTCString()}`
          );
          console.log(config);
        });
    })
    .catch((err) => {
      logError(err);
      process.exit(666);
    });

}

await run();
//await update();
