#pragma once

#include <string>
#include <cassert>

std::string base64_encode(unsigned char const *, size_t len);
std::string base64_decode(std::string const &s);
std::string base64_decodeSkipNewLines(const std::string &in);

extern const std::string base64_chars;
extern signed char base64_chars_reverse[256];

inline bool is_base64(unsigned char c)
{
    return base64_chars_reverse[c] >= 0;
}

inline size_t base64_length_from_binary(size_t binaryLength)
{
    if (binaryLength == 0)
        return 0;

    size_t length = ((4 * binaryLength / 3) + 3) & ~3;
    return length;
}

inline size_t binary_length_from_base64(const char *in, size_t length)
{
    if (length == 0)
        return 0;

    size_t ret = length * 6;
    if (length > 0 && in[length - 1] == '=')
    {
        assert(ret > 6);
        ret -= 6;
    }
    if (length > 1 && in[length - 2] == '=')
    {
        assert(ret > 6);
        ret -= 6;
    }

    ret = ret >> 3;
    return ret;
}

#define B64_WHITESPACE 64
#define B64_EQUALS 65
#define B64_INVALID 66

static const unsigned char decode_table_base64[] = {
    66, 66, 66, 66, 66, 66, 66, 66, 66, 64, 64, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66,
    66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 62, 66, 66, 66, 63, 52, 53,
    54, 55, 56, 57, 58, 59, 60, 61, 66, 66, 66, 65, 66, 66, 66, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9,
    10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 66, 66, 66, 66, 66, 66, 26, 27, 28,
    29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 66, 66,
    66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66,
    66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66,
    66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66,
    66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66,
    66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66, 66,
    66, 66, 66, 66, 66, 66};

template <typename TVec>
bool base64_decode(const char *in, size_t inLen, TVec &vec)
{
    size_t originalVectorSize = vec.size();
    size_t outLen = binary_length_from_base64(in, inLen);
    size_t newVectorSize = originalVectorSize + outLen;

    vec.resize(newVectorSize);

    unsigned char *out = &(*(vec.begin() + originalVectorSize));

    const char *end = in + inLen;
    char iter = 0;
    uint32_t buf = 0;
    size_t len = 0;

    while (in < end)
    {
        unsigned char c = decode_table_base64[(uint8_t)*in++];

        switch (c)
        {
        case B64_WHITESPACE: //
            continue;        /* skip whitespace */
        case B64_INVALID:
            assert(false);
            return false; /* invalid input, return error */
        case B64_EQUALS:  /* pad character, end of data */
            in = end;
            continue;
        default:
            buf = buf << 6 | c;
            iter++; // increment the number of iteration
                    /* If the buffer is full, split it into bytes */
            if (iter == 4)
            {
                if ((len += 3) > outLen)
                    return false; /* buffer overflow */
                *(out++) = (buf >> 16) & 255;
                *(out++) = (buf >> 8) & 255;
                *(out++) = buf & 255;
                buf = 0;
                iter = 0;
            }
        }
    }

    if (iter == 3)
    {
        if ((len += 2) > outLen)
            return false; /* buffer overflow */
        *(out++) = (buf >> 10) & 255;
        *(out++) = (buf >> 2) & 255;
    }
    else if (iter == 2)
    {
        if (++len > outLen)
            return false; /* buffer overflow */
        *(out++) = (buf >> 4) & 255;
    }

    assert(len <= outLen);
    vec.resize(len);
    return true;
}

bool base64_decode(const char *in, size_t inLen, void *buffer, size_t *bufferSizeInOut);