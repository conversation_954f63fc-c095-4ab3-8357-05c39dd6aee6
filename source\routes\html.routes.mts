import express = require("express");
import { microservice as g } from "../microservices/account.microservice.mjs";
import { safeNone, secureURLToken } from "../system/safe.mjs";
import { config } from "../system/Config.mjs";

// the job service may execute long running jobs
// so to avoid http timeouts the response must be set
// to success before actually running the logic
// if the job fails the error will be logged

export function addHtmlRoutes(router: express.Router) {
  router.get("/html/launch", safeNone, async (req, res, next) => {
    let context = (req as any).context;

    let html = `<html>
                <body onload="spawnMylio()">
                    <a id="link" />
                    <script>
async function spawnMylio() {

  let params = (new URL(document.location)).searchParams;
  let state = JSON.parse(atob(params.get("s")));
  let link = state.protocol + "://magic-link/" + state.stoken;
  if (state.target === "website")
    link = "${config.website}";
  document.querySelector("#link").href = link;
  if (state.target === "app")
    document.querySelector("#link").text = "Launch Mylio";
  document.location.href = link;
}
</script>
                </body>
            <html>
            
            `;

    res.contentType(".html");
    res.status(200).send(html);
  });
}
