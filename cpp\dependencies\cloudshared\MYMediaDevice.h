#pragma once

#include "MYMediaDeviceField.h"
#include "MYMediaFileType.h"
#include "MYEnums.h"
#include "MYHash.h"

// #define CHECKFOR_MDF_COPIES

// Don't go too nuts in this class. Every bit added makes the search query exponentially more complex
enum class MDLSearchAttributes : unsigned int
{
    None = 0,
    GeneratePreviewsOrThumbs = 1,
    DetectFaces = 2,
    TotalSearchAttributeBits = 2,
};
ENUM_FLAGS(MDLSearchAttributes);

// Don't go too nuts in this class. Every bit added makes the search query exponentially more complex
enum class MDLStampAttributes : unsigned int
{
    None = 0,
    LocalOriginalsInAssetLibrary = 1,
};
ENUM_FLAGS(MDLStampAttributes);

// Don't go too nuts in this class. Every bit added makes the search query exponentially more complex
enum class MDLSearchPartitions : unsigned int
{
    All = 0,     // Return all Media Public or Private
    Public = 1,  // Return only PUBLIC media
    Private = 2, // return onlhy PRIVATE media
};
ENUM_FLAGS(MDLSearchPartitions);

#ifdef MYLIO_CLIENT
// class MYMediaDeviceDynamicField final
//{
//     int  needsHas;
//     int  exNeedsHas;
//     int  inNeedsHas;
//
//     int  nrev;
//     int  exNrev;
//     int  inNrev;
//
//     int  hrev;
//     int  exHrev;
//     int  inHrev;
//
//     int  mediaColumn;
//
//     bool needsHasModified;
//     bool nrevModified;
//     bool hrevModified;
//     bool needsHasModifiedReplicableBits;
//     bool anyNeedsModified;
//
//     // This is kept alive by the _initialDevices on the media record. Once the media record goes out of scope, this
//     // is no longer valid.
//     const class MYNetworkNode* device;
//
//     friend class MYMedia;
//     friend class MYVirtualMediaNetworkNodeLink;
//
// public:
//     MYMediaDeviceDynamicField() = delete;
//     MYMediaDeviceDynamicField(const class MYNetworkNode* device_);
//
//     //MYMediaDeviceDynamicField(MYMediaDeviceDynamicField&& that) = delete;
//     MYMediaDeviceDynamicField& operator =(MYMediaDeviceDynamicField&& that) = delete;
//
//     // Unfortunately there is one case where it's legal for this type to be copied, but it isn't used. The only "correct" case is for
//     // the copy constructor of:
//     //    std::vector<MYMediaDeviceDynamicField> _dynamicFields;
//     // which is called by MYMedia::Clone().
//     // Unfortunately means we cannot always just compile with the deleted function.
//     // Periodically put a
//     //   #define CHECKFOR_MDF_COPIES
//     // at the top of the header to look for other accidental copies.
// #if defined(CHECKFOR_MDF_COPIES)
//     MYMediaDeviceDynamicField(const MYMediaDeviceDynamicField& that) = delete;
//     MYMediaDeviceDynamicField& operator =(const MYMediaDeviceDynamicField& that) = delete;
// #else
//     MYMediaDeviceDynamicField(const MYMediaDeviceDynamicField& that);
//     MYMediaDeviceDynamicField& operator =(const MYMediaDeviceDynamicField& that);
// #endif
//
//     void clearDynamicContent()
//     {
//         needsHas = 0;
//         hrev = 0;
//         nrev = 0;
//
//         needsHasModified = false;
//         needsHasModifiedReplicableBits = false;
//         hrevModified = false;
//         nrevModified = false;
//         anyNeedsModified = false;
//
//         inNeedsHas = 0;
//         inNrev = 0;
//         inHrev = 0;
//
//         exNeedsHas = 0;
//         exNrev = 0;
//         exHrev = 0;
//     }
//
//     bool setNRev(unsigned int newNRev);
//     bool setHRev(unsigned int newHRev);
//     bool setNeedsHasBits(int newNeedsHas, bool overwriteWithoutBumpingRevs);
//
//     int getMediaColumn() const
//     {
//         return mediaColumn;
//     }
//
//     int getNeedsHas() const
//     {
//         return needsHas;
//     }
//
//     int getOriginalNeedsHas() const
//     {
//         return exNeedsHas;
//     }
//
//     MYDeviceId getDeviceId() const;
//     bool isCloud() const;
//     bool isThisDevice() const;
//
//     const MYNetworkNode* getDevice() const
//     {
//         return device;
//     }
//
//     bool empty() const
//     {
//         return (device == nullptr);
//     }
//
// private:
//     void copyNonMovableMembers(const MYMediaDeviceDynamicField& from);
// };
#endif
//
// enum class MDLNeeds : unsigned int
//{
//    InheritedNo = 0,                    // 0b000
//    ForcedNo = 1,                       // 0b001
//    InheritedYes = 2,                   // 0b010
//    ForcedYes = 3,                      // 0b011
//    // NotPossible = 4,                    // 0b100
//    InheritedYesOverridingForcedNo = 6, // 0b110
//    // Field 6 historically meant 'ProtectedYes' in v1.0, and 'InheritedYesOverridingForcedNo' was 5 in v1.1 & v1.2
//    // Enough time has elapsed that we're changing InheritedYesOverridingForcedNo to 6 in NoMDL. It will still
//    // be replicated out as ForcedNo (so no change on replication), and restamped on upgrade from 5 to 6.
//    // This allows a much cheaper check for 'doeswant', which is just & the second bit.
//
//    AnyYesMask = 2,                    // 0b010
//    AllNeedsMask = 7                   // 0b111
//};
// ENUM_FLAGS(MDLNeeds)
//
// enum class MDLHas : unsigned int
//{
//    DoesntHave = 0,     // 0b00
//    Has = 1,            // 0b01
//    CanGenerate = 2,    // 0b10
//    HasOrCanGenerate = 3// 0b11
//};
// ENUM_FLAGS(MDLHas)

enum class NeedsCalculationReason
{
    // NotPossible = 0x1,
    FDL = 0x2,
    Travel = 0x4,
    Shuttle = 0x8,

    // NotPossibleButElsewhere = 0x10,
    Heuristic = 0x20,
    Forced = 0x40,
    TravelCounted = 0x80,

    CanGenerate = 0x100,
    UpgradeProtection = 0x200,
    GeneratePreviewsOrThumbs = 0x400,
    HasLastOriginal = 0x800,

    NotLocallyProtected = 0x1000,
    NotRemotelyProtected = 0x2000,
    AllKnownOriginals = 0x4000,
    NoMediaFileOfThisType = 0x8000,

    ReplicableNonRaw = 0x00010000,
    ReplicableRaw = 0x00020000,
    ReplicableXMP = 0x00040000,
    ReplicableVideo = 0x00080000,

    ReplicableDisplayImage = 0x00100000,
    DataHashMismatch = 0x00200000,
    NoLocalDataHash = 0x00400000,

    PreservedUserSetting = 0x00800000,

    ODNotHasAndWant = 0x01000000,
    ODSkipDueToLocalCanGenerate = 0x02000000,
    ODAddedBytes = 0x04000000,
    ODDeletedResource = 0x08000000,

    CropZoomFactorMisMatch = 0x10000000,
    IsDraft = 0x20000000,
    VisualEditHashMisMatch = 0x40000000
};
ENUM_FLAGS(NeedsCalculationReason)
//
// struct NeedsCalculation
//{
//    MYDeviceId deviceId;
//
//    bool newNeedsThumbnail = false;
//    bool newNeedsPreview = false;
//    bool newNeedsOriginal = false;
//    bool overrideForced = false;
//    bool hasAllKnownOriginals = false;
//
//    int  needsHasBefore;
//    int  needsHasAfter = 0;
//
//    MDLSearchAttributes localSearchAttributes = MDLSearchAttributes::None;
//
//    int needsReasonThumbnail = 0;
//    int needsReasonPreview = 0;
//    int needsReasonOriginal = 0;
//    int needsReasonRaw = 0;
//    int needsReasonNonRaw = 0;
//    int needsReasonDisplayImage = 0;
//    int needsReasonXMP = 0;
//    int needsReasonVideo = 0;
//
//    bool slotUsed = 0;
//};
//
// typedef std::vector<NeedsCalculation> NeedsCalculationMap;
//
// static bool MDL_doesNeed(MDLNeeds needs)
//{
//    static_assert((int)MDLNeeds::AnyYesMask == ((int)MDLNeeds::InheritedYes & (int)MDLNeeds::ForcedYes & (int)MDLNeeds::InheritedYesOverridingForcedNo), "Test for 2 no longer valid");
//    return ((int)needs & (int)MDLNeeds::AnyYesMask) > 0;
//}
//
// static bool MDL_isInherited(MDLNeeds needs)
//{
//    return (needs == MDLNeeds::InheritedYes || needs == MDLNeeds::InheritedNo || needs == MDLNeeds::InheritedYesOverridingForcedNo);
//}
//
// enum class MDLNeedsBits
//{
//    // 3 bits each - bit 0 to 8
//    NeedsThumbnail = 0,
//    NeedsPreview = 3,
//    NeedsOriginals = 6,
//
//    AnyNeedsMask = (7 << NeedsThumbnail) | (7 << NeedsPreview) | (7 << NeedsOriginals),
//};

// enum class MDLHasBits
//{
//     // 2 bits each - bit 9 to 22
//     HasThumbnail = 9,
//     HasPreview = 11,
//     HasXMP = 13,
//     HasNonRAW = 15, // bit 16 (CanGenerate of HasNowRaw) starts the new word.
//     HasRAW = 17,
//     HasVideo = 19,
//     HasDisplayImage = 21,
//     HasAllOriginals = 23,
//
//     AnyHasMask = (3 << HasDisplayImage) | (3 << HasNonRAW) | (3 << HasPreview) | (3 << HasRAW) | (3 << HasThumbnail) | (3 << HasVideo) | (3 << HasXMP),
//     LocalOnlyHasBits = (3 << HasAllOriginals)
// };
//
// struct MDLBitMask
//{
//     static MDLHas GetHasBitsForValue(MDLHasBits bit, int val);
//     static MDLNeeds GetNeedsBitsForValue(MDLNeedsBits bit, int val);
//     static void SetHasBitsForValue(int& current, MDLHasBits bit, MDLHas val);
//     static void SetNeedsBitsForValue(int& current, MDLNeedsBits bit, MDLNeeds val);
//
//     static bool isValidMDLNeeds(MDLNeeds needs);
//     static bool isValidMDLHas(MDLHas has);
//     static bool isValidNeedsHas(int needsHas);
// };

// Helper methods:
// MDLNeeds getReplicableNeeds(const MDLNeeds& needs);
// int getReplicableBitsFromNeedsHas(bool isCloud, int needsHas);
//
// class MYVirtualMediaNetworkNodeLink;
// typedef std::shared_ptr<MYVirtualMediaNetworkNodeLink> MYVirtualMediaNetworkNodeLinkPtr;
//
void appendString(std::string &str, const char *s, bool &appended, std::string prefix = "");
// std::string NeedsCalculationDebugToString(int flags);
// std::string NeedsToString(MDLNeeds needs);
// std::string HasToString(std::string type, MDLHas has);
// std::string NeedsHasToString(int flags);
// std::string NeedsHasSpecificToString(int flags, MDLNeedsBits needs);
std::string SearchAttributesToString(MDLSearchAttributes flags);
//
//
//// Type for use by debugger visualization - do not use directly
// enum class MDLNeedsDbg : unsigned int
//{
//     No = 0,
//     FNo = 1,
//     Yes = 2,
//     FYes = 3,
//     na = 4
// };
//
//// Type for use by debugger visualization - do not use directly
// enum class MDLHasDbg : unsigned int
//{
//     No = 0,
//     Yes = 1,
//     Gen = 2
// };
//
//// Type for use by debugger visualization - do not use directly
// struct MDLNeedsHasBitsDbg
//{
//     MDLNeedsDbg NT : 3;
//     MDLNeedsDbg NP : 3;
//     MDLNeedsDbg NOR : 3;
//
//     unsigned int HT : 2;
//     unsigned int HP : 2;
//     unsigned int HX : 2;
//     unsigned int HN : 2;
//     unsigned int HR : 2;
//     unsigned int HV : 2;
//     unsigned int HD : 2;
// };
//
//
