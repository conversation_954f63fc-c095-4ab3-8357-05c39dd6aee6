import type { FromSchema } from 'json-schema-to-ts';
import * as schemas from './schemas';

export type AddcouponcodestoacouponBodyParam = FromSchema<typeof schemas.Addcouponcodestoacoupon.body>;
export type AddcouponcodestoacouponMetadataParam = FromSchema<typeof schemas.Addcouponcodestoacoupon.metadata>;
export type AddcouponcodestoacouponResponse200 = FromSchema<typeof schemas.Addcouponcodestoacoupon.response['200']>;
export type CancelQuoteMetadataParam = FromSchema<typeof schemas.CancelQuote.metadata>;
export type CancelQuoteResponse200 = FromSchema<typeof schemas.CancelQuote.response['200']>;
export type CancelsubscriptioninstancesMetadataParam = FromSchema<typeof schemas.Cancelsubscriptioninstances.metadata>;
export type CancelsubscriptioninstancesResponse200 = FromSchema<typeof schemas.Cancelsubscriptioninstances.response['200']>;
export type CancelsubscriptioninstancesResponse500 = FromSchema<typeof schemas.Cancelsubscriptioninstances.response['500']>;
export type ChangetheproductforanactivesubscriptionBodyParam = FromSchema<typeof schemas.Changetheproductforanactivesubscription.body>;
export type ChangetheproductforanactivesubscriptionResponse200 = FromSchema<typeof schemas.Changetheproductforanactivesubscription.response['200']>;
export type ChangetheproductforanactivesubscriptionResponse500 = FromSchema<typeof schemas.Changetheproductforanactivesubscription.response['500']>;
export type ConvertExpiredTrialWithoutPaymentMethodMetadataParam = FromSchema<typeof schemas.ConvertExpiredTrialWithoutPaymentMethod.metadata>;
export type ConvertExpiredTrialWithoutPaymentMethodResponse200 = FromSchema<typeof schemas.ConvertExpiredTrialWithoutPaymentMethod.response['200']>;
export type ConvertExpiredTrialWithoutPaymentMethodResponse400 = FromSchema<typeof schemas.ConvertExpiredTrialWithoutPaymentMethod.response['400']>;
export type CreateQuoteBodyParam = FromSchema<typeof schemas.CreateQuote.body>;
export type CreateQuoteResponse200 = FromSchema<typeof schemas.CreateQuote.response['200']>;
export type CreateanaccountBodyParam = FromSchema<typeof schemas.Createanaccount.body>;
export type CreateanaccountResponse200 = FromSchema<typeof schemas.Createanaccount.response['200']>;
export type CreateanaccountResponse500 = FromSchema<typeof schemas.Createanaccount.response['500']>;
export type CreateanewcouponBodyParam = FromSchema<typeof schemas.Createanewcoupon.body>;
export type CreateanewcouponResponse200 = FromSchema<typeof schemas.Createanewcoupon.response['200']>;
export type CreateasessionwithoutoverridinganydefaultvaluesBodyParam = FromSchema<typeof schemas.Createasessionwithoutoverridinganydefaultvalues.body>;
export type CreateasessionwithoutoverridinganydefaultvaluesResponse200 = FromSchema<typeof schemas.Createasessionwithoutoverridinganydefaultvalues.response['200']>;
export type CreateasessionwithoutoverridinganydefaultvaluesResponse400 = FromSchema<typeof schemas.Createasessionwithoutoverridinganydefaultvalues.response['400']>;
export type CreateoneormorenewproductsBodyParam = FromSchema<typeof schemas.Createoneormorenewproducts.body>;
export type CreateoneormorenewproductsResponse200 = FromSchema<typeof schemas.Createoneormorenewproducts.response['200']>;
export type CreateoneormorenewproductsResponse201 = FromSchema<typeof schemas.Createoneormorenewproducts.response['201']>;
export type CreateoneormorenewproductsResponse500 = FromSchema<typeof schemas.Createoneormorenewproducts.response['500']>;
export type CreateorupdateproductoffersBodyParam = FromSchema<typeof schemas.Createorupdateproductoffers.body>;
export type CreateorupdateproductoffersMetadataParam = FromSchema<typeof schemas.Createorupdateproductoffers.metadata>;
export type CreateorupdateproductoffersResponse200 = FromSchema<typeof schemas.Createorupdateproductoffers.response['200']>;
export type CreateorupdateproductoffersResponse500 = FromSchema<typeof schemas.Createorupdateproductoffers.response['500']>;
export type DeleteProductsMetadataParam = FromSchema<typeof schemas.DeleteProducts.metadata>;
export type DeleteProductsResponse200 = FromSchema<typeof schemas.DeleteProducts.response['200']>;
export type DeleteProductsResponse500 = FromSchema<typeof schemas.DeleteProducts.response['500']>;
export type DeleteQuoteMetadataParam = FromSchema<typeof schemas.DeleteQuote.metadata>;
export type DeleteQuoteResponse200 = FromSchema<typeof schemas.DeleteQuote.response['200']>;
export type DeleteallcouponcodesfromacouponMetadataParam = FromSchema<typeof schemas.Deleteallcouponcodesfromacoupon.metadata>;
export type DeleteallcouponcodesfromacouponResponse200 = FromSchema<typeof schemas.Deleteallcouponcodesfromacoupon.response['200']>;
export type DownloadReportMetadataParam = FromSchema<typeof schemas.DownloadReport.metadata>;
export type DownloadReportResponse200 = FromSchema<typeof schemas.DownloadReport.response['200']>;
export type DownloadReportResponse400 = FromSchema<typeof schemas.DownloadReport.response['400']>;
export type GenerateRevenueReportBodyParam = FromSchema<typeof schemas.GenerateRevenueReport.body>;
export type GenerateRevenueReportResponse200 = FromSchema<typeof schemas.GenerateRevenueReport.response['200']>;
export type GenerateRevenueReportResponse400 = FromSchema<typeof schemas.GenerateRevenueReport.response['400']>;
export type GenerateSubscriptionReportBodyParam = FromSchema<typeof schemas.GenerateSubscriptionReport.body>;
export type GenerateSubscriptionReportResponse200 = FromSchema<typeof schemas.GenerateSubscriptionReport.response['200']>;
export type GenerateSubscriptionReportResponse400 = FromSchema<typeof schemas.GenerateSubscriptionReport.response['400']>;
export type GetAllQuotesMetadataParam = FromSchema<typeof schemas.GetAllQuotes.metadata>;
export type GetAllQuotesResponse200 = FromSchema<typeof schemas.GetAllQuotes.response['200']>;
export type GetJobByIdMetadataParam = FromSchema<typeof schemas.GetJobById.metadata>;
export type GetJobByIdResponse200 = FromSchema<typeof schemas.GetJobById.response['200']>;
export type GetJobByIdResponse400 = FromSchema<typeof schemas.GetJobById.response['400']>;
export type GetJobsResponse200 = FromSchema<typeof schemas.GetJobs.response['200']>;
export type GetQuoteByIdMetadataParam = FromSchema<typeof schemas.GetQuoteById.metadata>;
export type GetQuoteByIdResponse200 = FromSchema<typeof schemas.GetQuoteById.response['200']>;
export type GetalloffersforproductbyoffertypeMetadataParam = FromSchema<typeof schemas.Getalloffersforproductbyoffertype.metadata>;
export type GetalloffersforproductbyoffertypeResponse200 = FromSchema<typeof schemas.Getalloffersforproductbyoffertype.response['200']>;
export type GetallproductspriceResponse200 = FromSchema<typeof schemas.Getallproductsprice.response['200']>;
export type GetallproductspricewithcountryMetadataParam = FromSchema<typeof schemas.Getallproductspricewithcountry.metadata>;
export type GetallproductspricewithcountryResponse200 = FromSchema<typeof schemas.Getallproductspricewithcountry.response['200']>;
export type GetallproductspricewithcountryandcurrencyMetadataParam = FromSchema<typeof schemas.Getallproductspricewithcountryandcurrency.metadata>;
export type GetallproductspricewithcountryandcurrencyResponse200 = FromSchema<typeof schemas.Getallproductspricewithcountryandcurrency.response['200']>;
export type GetallsubscriptioninstancesResponse200 = FromSchema<typeof schemas.Getallsubscriptioninstances.response['200']>;
export type GetallsubscriptioninstancesResponse500 = FromSchema<typeof schemas.Getallsubscriptioninstances.response['500']>;
export type GetauthenticatedaccountmanagementUrlMetadataParam = FromSchema<typeof schemas.GetauthenticatedaccountmanagementUrl.metadata>;
export type GetauthenticatedaccountmanagementUrlResponse200 = FromSchema<typeof schemas.GetauthenticatedaccountmanagementUrl.response['200']>;
export type GetauthenticatedaccountmanagementUrlResponse500 = FromSchema<typeof schemas.GetauthenticatedaccountmanagementUrl.response['500']>;
export type GetcouponcodesassignedtoacouponBodyParam = FromSchema<typeof schemas.Getcouponcodesassignedtoacoupon.body>;
export type GetcouponcodesassignedtoacouponMetadataParam = FromSchema<typeof schemas.Getcouponcodesassignedtoacoupon.metadata>;
export type GetcouponcodesassignedtoacouponResponse200 = FromSchema<typeof schemas.Getcouponcodesassignedtoacoupon.response['200']>;
export type GetlistofallproductidsResponse200 = FromSchema<typeof schemas.Getlistofallproductids.response['200']>;
export type GetoneaccountMetadataParam = FromSchema<typeof schemas.Getoneaccount.metadata>;
export type GetoneaccountResponse200 = FromSchema<typeof schemas.Getoneaccount.response['200']>;
export type GetoneaccountResponse500 = FromSchema<typeof schemas.Getoneaccount.response['500']>;
export type GetoneormoresubscriptioninstancesMetadataParam = FromSchema<typeof schemas.Getoneormoresubscriptioninstances.metadata>;
export type GetoneormoresubscriptioninstancesResponse200 = FromSchema<typeof schemas.Getoneormoresubscriptioninstances.response['200']>;
export type GetoneormoresubscriptioninstancesResponse500 = FromSchema<typeof schemas.Getoneormoresubscriptioninstances.response['500']>;
export type GetoneormultiplereturnsMetadataParam = FromSchema<typeof schemas.Getoneormultiplereturns.metadata>;
export type GetoneormultiplereturnsResponse200 = FromSchema<typeof schemas.Getoneormultiplereturns.response['200']>;
export type GetordersbyIdMetadataParam = FromSchema<typeof schemas.GetordersbyId.metadata>;
export type GetordersbyIdResponse200 = FromSchema<typeof schemas.GetordersbyId.response['200']>;
export type GetordersbyIdResponse400 = FromSchema<typeof schemas.GetordersbyId.response['400']>;
export type GetordersbydaterangeMetadataParam = FromSchema<typeof schemas.Getordersbydaterange.metadata>;
export type GetordersbydaterangeResponse200 = FromSchema<typeof schemas.Getordersbydaterange.response['200']>;
export type GetordersbydaterangeResponse400 = FromSchema<typeof schemas.Getordersbydaterange.response['400']>;
export type GetordersbyenddateMetadataParam = FromSchema<typeof schemas.Getordersbyenddate.metadata>;
export type GetordersbyenddateResponse200 = FromSchema<typeof schemas.Getordersbyenddate.response['200']>;
export type GetordersbyproductdaterangeMetadataParam = FromSchema<typeof schemas.Getordersbyproductdaterange.metadata>;
export type GetordersbyproductdaterangeResponse200 = FromSchema<typeof schemas.Getordersbyproductdaterange.response['200']>;
export type GetordersbyproductpathMetadataParam = FromSchema<typeof schemas.Getordersbyproductpath.metadata>;
export type GetordersbyproductpathResponse200 = FromSchema<typeof schemas.Getordersbyproductpath.response['200']>;
export type GetordersbyreturnMetadataParam = FromSchema<typeof schemas.Getordersbyreturn.metadata>;
export type GetordersbyreturnResponse200 = FromSchema<typeof schemas.Getordersbyreturn.response['200']>;
export type GetprocessedeventsMetadataParam = FromSchema<typeof schemas.Getprocessedevents.metadata>;
export type GetprocessedeventsResponse200 = FromSchema<typeof schemas.Getprocessedevents.response['200']>;
export type GetprocessedeventsResponse500 = FromSchema<typeof schemas.Getprocessedevents.response['500']>;
export type GetproductsbyidMetadataParam = FromSchema<typeof schemas.Getproductsbyid.metadata>;
export type GetproductsbyidResponse200 = FromSchema<typeof schemas.Getproductsbyid.response['200']>;
export type GetspecificproductpriceMetadataParam = FromSchema<typeof schemas.Getspecificproductprice.metadata>;
export type GetspecificproductpriceResponse200 = FromSchema<typeof schemas.Getspecificproductprice.response['200']>;
export type GetspecificproductpricecountryMetadataParam = FromSchema<typeof schemas.Getspecificproductpricecountry.metadata>;
export type GetspecificproductpricecountryResponse200 = FromSchema<typeof schemas.Getspecificproductpricecountry.response['200']>;
export type GetspecificproductpricecountrycurrencyMetadataParam = FromSchema<typeof schemas.Getspecificproductpricecountrycurrency.metadata>;
export type GetspecificproductpricecountrycurrencyResponse200 = FromSchema<typeof schemas.Getspecificproductpricecountrycurrency.response['200']>;
export type GetsubscriptioninstanceentriesMetadataParam = FromSchema<typeof schemas.Getsubscriptioninstanceentries.metadata>;
export type GetsubscriptioninstanceentriesResponse200 = FromSchema<typeof schemas.Getsubscriptioninstanceentries.response['200']>;
export type GetsubscriptioninstanceentriesResponse500 = FromSchema<typeof schemas.Getsubscriptioninstanceentries.response['500']>;
export type GetsubscriptionplanCChangehistoryMetadataParam = FromSchema<typeof schemas.GetsubscriptionplanCChangehistory.metadata>;
export type GetsubscriptionplanCChangehistoryResponse200 = FromSchema<typeof schemas.GetsubscriptionplanCChangehistory.response['200']>;
export type GetsubscriptionplanCChangehistoryResponse500 = FromSchema<typeof schemas.GetsubscriptionplanCChangehistory.response['500']>;
export type GetunprocessedeventsMetadataParam = FromSchema<typeof schemas.Getunprocessedevents.metadata>;
export type GetunprocessedeventsResponse200 = FromSchema<typeof schemas.Getunprocessedevents.response['200']>;
export type GetunprocessedeventsResponse500 = FromSchema<typeof schemas.Getunprocessedevents.response['500']>;
export type LookUpAccountsbyParametersMetadataParam = FromSchema<typeof schemas.LookUpAccountsbyParameters.metadata>;
export type LookUpAccountsbyParametersResponse200 = FromSchema<typeof schemas.LookUpAccountsbyParameters.response['200']>;
export type LookUpAccountsbyParametersResponse500 = FromSchema<typeof schemas.LookUpAccountsbyParameters.response['500']>;
export type PauseasubscriptionBodyParam = FromSchema<typeof schemas.Pauseasubscription.body>;
export type PauseasubscriptionMetadataParam = FromSchema<typeof schemas.Pauseasubscription.metadata>;
export type PauseasubscriptionResponse200 = FromSchema<typeof schemas.Pauseasubscription.response['200']>;
export type PauseasubscriptionResponse500 = FromSchema<typeof schemas.Pauseasubscription.response['500']>;
export type PostOneMoreOrdersReturnsBodyParam = FromSchema<typeof schemas.PostOneMoreOrdersReturns.body>;
export type PostOneMoreOrdersReturnsResponse200 = FromSchema<typeof schemas.PostOneMoreOrdersReturns.response['200']>;
export type RebillmanagedsubscriptioninstanceBodyParam = FromSchema<typeof schemas.Rebillmanagedsubscriptioninstance.body>;
export type RebillmanagedsubscriptioninstanceResponse200 = FromSchema<typeof schemas.Rebillmanagedsubscriptioninstance.response['200']>;
export type RebillmanagedsubscriptioninstanceResponse500 = FromSchema<typeof schemas.Rebillmanagedsubscriptioninstance.response['500']>;
export type ResetCacheResponse200 = FromSchema<typeof schemas.ResetCache.response['200']>;
export type ResumeapausedsubscriptionMetadataParam = FromSchema<typeof schemas.Resumeapausedsubscription.metadata>;
export type ResumeapausedsubscriptionResponse200 = FromSchema<typeof schemas.Resumeapausedsubscription.response['200']>;
export type ResumeapausedsubscriptionResponse500 = FromSchema<typeof schemas.Resumeapausedsubscription.response['500']>;
export type RetrievecoupondetailsMetadataParam = FromSchema<typeof schemas.Retrievecoupondetails.metadata>;
export type RetrievecoupondetailsResponse200 = FromSchema<typeof schemas.Retrievecoupondetails.response['200']>;
export type RotateWebhookKeyBodyParam = FromSchema<typeof schemas.RotateWebhookKey.body>;
export type RotateWebhookKeyResponse200 = FromSchema<typeof schemas.RotateWebhookKey.response['200']>;
export type RotateWebhookKeyResponse400 = FromSchema<typeof schemas.RotateWebhookKey.response['400']>;
export type SubscriptoinProratePreviewEstimateBodyParam = FromSchema<typeof schemas.SubscriptoinProratePreviewEstimate.body>;
export type SubscriptoinProratePreviewEstimateResponse200 = FromSchema<typeof schemas.SubscriptoinProratePreviewEstimate.response['200']>;
export type SubscriptoinProratePreviewEstimateResponse500 = FromSchema<typeof schemas.SubscriptoinProratePreviewEstimate.response['500']>;
export type UncancelasubscriptionpriortodeactivationBodyParam = FromSchema<typeof schemas.Uncancelasubscriptionpriortodeactivation.body>;
export type UncancelasubscriptionpriortodeactivationMetadataParam = FromSchema<typeof schemas.Uncancelasubscriptionpriortodeactivation.metadata>;
export type UncancelasubscriptionpriortodeactivationResponse200 = FromSchema<typeof schemas.Uncancelasubscriptionpriortodeactivation.response['200']>;
export type UncancelasubscriptionpriortodeactivationResponse500 = FromSchema<typeof schemas.Uncancelasubscriptionpriortodeactivation.response['500']>;
export type UpdateQuoteBodyParam = FromSchema<typeof schemas.UpdateQuote.body>;
export type UpdateQuoteMetadataParam = FromSchema<typeof schemas.UpdateQuote.metadata>;
export type UpdateQuoteResponse200 = FromSchema<typeof schemas.UpdateQuote.response['200']>;
export type UpdateasingleeventBodyParam = FromSchema<typeof schemas.Updateasingleevent.body>;
export type UpdateasingleeventMetadataParam = FromSchema<typeof schemas.Updateasingleevent.metadata>;
export type UpdateasingleeventResponse200 = FromSchema<typeof schemas.Updateasingleevent.response['200']>;
export type UpdateexistingaccountBodyParam = FromSchema<typeof schemas.Updateexistingaccount.body>;
export type UpdateexistingaccountMetadataParam = FromSchema<typeof schemas.Updateexistingaccount.metadata>;
export type UpdateexistingaccountResponse200 = FromSchema<typeof schemas.Updateexistingaccount.response['200']>;
export type UpdateexistingaccountResponse500 = FromSchema<typeof schemas.Updateexistingaccount.response['500']>;
export type UpdateordertagsandattributesBodyParam = FromSchema<typeof schemas.Updateordertagsandattributes.body>;
export type UpdateordertagsandattributesResponse200 = FromSchema<typeof schemas.Updateordertagsandattributes.response['200']>;
