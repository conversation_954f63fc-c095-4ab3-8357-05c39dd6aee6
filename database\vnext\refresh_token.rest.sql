

    drop view if exists a0."RefreshToken" cascade;

    create or replace view a0."RefreshToken" as
    select
    flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		idp,
		task,
		sub,
		token,
		email
    from a0.refresh_token;
    

drop function if exists a0.refresh_token_create; 
        create function a0.refresh_token_create(
            _flags int,
	_modified_time timestamptz,
	_created_time timestamptz,
	_account_id int,
	_idp text,
	_task text,
	_sub text,
	_token text,
	_email text
        )
        returns a0."RefreshToken"
        as $$
        
    declare
        result a0."RefreshToken";
        
    begin
        
        


       
        


        
        
        
        
        _modified_time := coalesce(_modified_time, now());
        _created_time := coalesce(_created_time, now());
        insert into a0.refresh_token (
            flags,
	modified_time,
	created_time,
	account_id,
	idp,
	task,
	sub,
	token,
	email
        )
        values(
            _flags,
			_modified_time,
			_created_time,
			_account_id,
			_idp,
			_task,
			_sub,
			_token,
			_email
        )
        returning
            flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		idp,
		task,
		sub,
		token,
		email
        into result;

        



        

    
        



        return result;
    end;
        $$
        language plpgsql;
        

drop function if exists a0.refresh_token_update; 
        create function a0.refresh_token_update(
            _flags int,
	_modified_time timestamptz,
	_created_time timestamptz,
	_account_id int,
	_idp text,
	_task text,
	_sub text,
	_token text,
	_email text
        )
        returns a0."RefreshToken"
        as $$
        
    declare
        result a0."RefreshToken";
        
    begin
        
        


       
        


        
        
        _modified_time := now();
        update a0.refresh_token
        set
            flags = _flags,
			modified_time = _modified_time,
			created_time = _created_time,
			sub = _sub,
			token = _token,
			email = _email
        where account_id = _account_id and idp = _idp and task = _task
        returning
            flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		idp,
		task,
		sub,
		token,
		email
        into result;

        

    
        



        return result;
    end;
        $$
        language plpgsql;
        

drop function if exists a0.refresh_token_read_by_account_id_and_idp_and_task; 
        create function a0.refresh_token_read_by_account_id_and_idp_and_task(
            _account_id int,
	_idp text,
	_task text
        )
        returns a0."RefreshToken"
        as $$
        select
        flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		idp,
		task,
		sub,
		token,
		email
        from a0.refresh_token
        where account_id = _account_id and idp = _idp and task = _task;
        $$
        language sql;
        

drop function if exists a0.refresh_token_delete_by_account_id_and_idp_and_task; 
        create function a0.refresh_token_delete_by_account_id_and_idp_and_task(
            _account_id int,
	_idp text,
	_task text
        )
        returns void
        as $$
        
    declare
        
        
    begin
        
        


       
        


        

    delete from a0.refresh_token
    where account_id = _account_id and idp = _idp and task = _task;

    
    
        



        
    end;
        $$
        language plpgsql;
        

drop function if exists a0.refresh_token_delete_by_account_id; 
        create function a0.refresh_token_delete_by_account_id(
            _account_id int
        )
        returns void
        as $$
        
    declare
        
        
    begin
        
        


       
        


        

    delete from a0.refresh_token
    where account_id = _account_id;

    
    
        



        
    end;
        $$
        language plpgsql;
        

drop function if exists a0.refresh_token_find_by_account_id; 
        create function a0.refresh_token_find_by_account_id(
            _account_id int
        )
        returns setof a0."RefreshToken"
        as $$
        select
        flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		idp,
		task,
		sub,
		token,
		email
        from a0.refresh_token
        where account_id = _account_id;
        $$
        language sql;
        
