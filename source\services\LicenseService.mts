import { LicenseRestDataService } from "../dataServices/License.rest.dataService.mjs";
import { Context } from "../system/Context.mjs";
import { License, LicenseStatus, Manager, merge } from "../models/License.model.mjs";
import { ids } from "../system/Strings.mjs";
import moment = require("moment");
import { AccountService } from "./AccountService.mjs";
import { makeError } from "../system/error.mjs";
import { Account } from "../models/Account.model.mjs";
import _ = require("lodash");
import { LicenseTemplateRestDataService } from "../dataServices/LicenseTemplate.rest.dataService.mjs";
import { tx } from "../system/Postgres.mjs";
import crypto from "crypto";
import { query } from "../system/Postgres.mjs";
import { ILicense } from "../models/License.model.mjs";
import { dbnull } from "../system/data.mjs";
import { Templates } from "../models/LicenseTemplate.model.mjs";


class Errors {
  static BOOST_NOT_AUTHORIZED = "BOOST_NOT_AUTHORIZED";
}





const theEndOfTime = new Date(****************);
export class LicenseService {

  makeLicenseId(manager: string, subscriptionId: string, key: string, sku: string, index: number) {
    return `${manager}.${subscriptionId}.${key}.${sku}.${index}`;
  }

  async findByPartialId(context: Context, partialKey: string) {
    return this.dataService.findByPartialId(context, partialKey);
  }

  async findByPartialActivationKey(context: Context, partialKey: string) {
    return this.dataService.findByPartialActivationKey(context, partialKey);
  }

  async revokeLicense(context: Context, licenseId: string) {
    return tx<License>(context, async () => {
      let license = await this.dataService.readByLicenseId(context, licenseId);
      if (!license) throw makeError(404, "LICENSE_NOT_FOUND", `License with id: ${licenseId} not found`);
      license.accountId(dbnull);
      return this.dataService.update(context, license);
    });
  }

  async readTemplate(context: Context, templateId: string) {
    return this.tmplDataService.readByTemplateId(context, templateId);
  }

  async listTemplates(context: any, aid: number) {
    let queryText = `select t.* 
            from a0."LicenseTemplate" t
            left join (
                select account_id, template_id, count(*) uses
                from a0.license s
                where s.account_id = $1
                group by 1, 2
            ) u on u.template_id = t."templateId"
            where (t.uses > u.uses or t.uses is null or u.uses is null)`;

    let templates = await this.tmplDataService.query(context, queryText, [aid]);
    return templates;
  }

  async createMylioLicense(context: Context, account: Account, license: License) {
    license.licenseId(License.makeId(context, license));
    license.accountId(account.accountId());
    license = await this.create(context, license);
    return license;
  }

  async createAccountAndLicense(context: Context, account: Account) {
    return tx<Account>(context, async () => {
      account = await this.accountService.create(context, account);
      await this.createMylioLicense(context, account, new License({ status: LicenseStatus.Active, flags: 0, manager: Manager.Mylio, templateId: Templates.free }));
      return this.accountService.read(context, account.accountId());
    });
  }
  constructor(
    private dataService: LicenseRestDataService,
    private accountService: AccountService,
    private tmplDataService: LicenseTemplateRestDataService
  ) { }

  async setLimits(context: Context, aid: number) {
    return query<void>(context, `select a0.recompute_licenses_for_one_account($1)`, [aid]);
  }

  async create(context: Context, thisLicense: License) {
    return tx<License>(context, async () => {

      thisLicense.templateId(thisLicense.templateId().toLocaleLowerCase());

      thisLicense.licenseFlags().trial(thisLicense.status() === LicenseStatus.Trial);

      if (!thisLicense.templateId())
        throw makeError(400, "TEMPLATE_REQUIRED", "Template is required");

      const template = await this.tmplDataService.readByTemplateId(
        context,
        thisLicense.templateId()
      );

      if (!template)
        throw makeError(
          400,
          "INVALID_TEMPLATE",
          `template ${thisLicense.templateId()} does not exist`
        );

      let mangerIsValid = false;
      switch (thisLicense.manager()) {
        case Manager.Mylio:
        case Manager.FastSpring:
        case Manager.Apple:
        case Manager.TestPaid:
          break;
        default:
          throw makeError(
            400,
            "INVALID_MANAGER",
            `Manager ${thisLicense.manager()} is invalid`
          );
      }

      let licenses = await this.list(context, thisLicense.accountId());
      let count = 0;
      for (let license of licenses) {
        if (!license.deleted()) {
          if (license.templateId() === thisLicense.templateId()) count++;
        }
      }

      if (template.uses() && template.uses() < count + 1)
        throw makeError(
          400,
          `Template ${template.templateId()} can only be used ${template.uses()} times`
        );

      if (!thisLicense.endDate()) {
        thisLicense.endDate(
          moment.utc().add(moment.duration(template.duration())).toDate()
        );
      }
      thisLicense = await this.dataService.create(context, thisLicense);
      if (thisLicense.accountId()) {
        licenses.push(thisLicense);
        await this.setLimits(context, thisLicense.accountId());
      }
      return thisLicense;
    });
  }

  async update(context: Context, thisLicense: License) {
    return tx<License>(context, async () => {
      thisLicense.templateId(thisLicense.templateId().toLocaleLowerCase());
      let dbVersion = await this.dataService.readByLicenseId(context, thisLicense.licenseId());
      thisLicense = merge(dbVersion, thisLicense);
      thisLicense.licenseFlags().trial(thisLicense.status() === LicenseStatus.Trial);
      thisLicense = await this.dataService.update(context, thisLicense);

      if (thisLicense.accountId()) {
        await this.setLimits(context, thisLicense.accountId());
      }
      if (!thisLicense.manager())
        thisLicense.manager(Manager.Mylio);

      if (thisLicense.accountId()) {
        this.setLimits(context, thisLicense.accountId());
      }
      return thisLicense;
    });
  }
  async list(context: Context, aid: number) {
    return this.dataService.findByAccountId(context, aid);
  }

  async findByActivationKey(context: Context, activationKey: string) {
    return this.dataService.findByActivationKey(context, activationKey);
  }

  async findByAccountId(context: Context, accountId: number) {
    return this.dataService.findByAccountId(context, accountId);
  }

  async read(context: Context, licenseId: string) {
    return this.dataService.readByLicenseId(context, licenseId);
  }


  async findAndDeleteExpiredLicenses(context: Context) {
    await tx(context, async () => {
      const accounts = await query<{ account_id: number }>(
        context,
        `select a0.recompute_licenses()`,
        []
      );

    });
  }

  async computeAvailableUpgrades(context: Context, aid: number, feature: number) {
    return query<{ sku: string }>(
      context,
      `select * from a0.compute_available_upgrades($1, $2)`,
      [aid, feature]
    );
  }
}
