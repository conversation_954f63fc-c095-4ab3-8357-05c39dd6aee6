



create table a0.device(
    flags int NULL,
	modified_time timestamptz NULL,
	created_time timestamptz NULL,
	account_id int NOT NULL,
	deleted boolean NULL,
	t bytea NULL,
	d bytea NULL,
	device_id int NOT NULL,
	name text NULL,
	device_type int NOT NULL,
	nickname text NULL,
	encrypt boolean NULL,
	creation_time timestamptz NULL,
	long_id bytea NULL,
	support_ticket bytea NULL
);

alter table a0.device
add primary key (account_id,device_id);

 

 create index ix_device_by_account_id on a0.device(account_id);
create index ix_device_by_account_id_and_device_type on a0.device(account_id,device_type);

