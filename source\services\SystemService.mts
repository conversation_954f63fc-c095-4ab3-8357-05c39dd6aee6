import { SystemProperty, merge } from "../models/SystemProperty.model.mjs";
import { error } from "../system/error.mjs";
import { SystemPropertyRestDataService } from "../dataServices/SystemProperty.rest.dataService.mjs";
import { Context } from "../system/Context.mjs";
import { Itx, dbnull } from "../system/data.mjs";
import { check } from "../system/check.mjs";
import { AccountMetadataRestDataService } from "../dataServices/AccountMetadata.rest.dataService.mjs";
// import { ResourceDataService } from "../data services/ResourceDataService.mjs";

export interface ISyncService {
  sync(context: Context, aid: number, input: any, output?: any);
}

export class SystemService {
  constructor(
    private propsDataService: SystemPropertyRestDataService,
    private metaDataService: AccountMetadataRestDataService,
    private tx: Itx<SystemProperty>,
    private syncServices: ISyncService[]
  ) { }

  public async createProperty(
    context: Context,
    aid: number,
    systemProperty: SystemProperty
  ) {
    systemProperty.accountId(aid);
    await check(systemProperty);
    return this.propsDataService.create(context, systemProperty);
  }

  public listProperties(context: Context, aid: number) {
    return this.propsDataService.findByAccountId(context, aid);
  }

  public readProperty(context: Context, aid: number, did: number) {
    return this.propsDataService
      .readByAccountIdAndSystemPropertyId(context, aid, did)
      .then((systemProperty) => {
        if (!systemProperty) {
          return error(404, "SYSTEM_PROPERTY_NOT_VALID_FOR_ACCOUNT");
        }
        return systemProperty;
      }) as Promise<SystemProperty>;
  }

  public async updateProperty(
    context: Context,
    aid: number,
    systemProperty: SystemProperty
  ) {
    return this.tx(context, async () => {
      const dbVersion =
        await this.propsDataService.readByAccountIdAndSystemPropertyId(
          context,
          systemProperty.accountId(),
          systemProperty.systemPropertyId()
        );
      systemProperty = merge(dbVersion, systemProperty);
      await check(systemProperty);
      return this.propsDataService.update(context, systemProperty);
    });
  }

  public deleteProperty(context: Context, aid: number, did: number) {
    return this.propsDataService.deleteByAccountIdAndSystemPropertyId(
      context,
      aid,
      did
    );
  }

  public ping(context: Context, aid: number, ping: any) {
    return Promise.all([
      this.metaDataService.readByAccountId(context, aid),
      this.metaDataService.readByAccountId(context, 1),
    ]).then((metas) => {
      let metadata = metas[0];
      let systemAccountMetadata = metas[1];

      let accountMerkle = ping.accounts || dbnull;
      let deviceMerkle = ping.devices || dbnull;
      let systemMerkle = ping.systemProperties || dbnull;
      let messageMerkle = ping.messages || dbnull;
      let deviceDataMerkle = ping.devicedata || dbnull;
      let userPropertiesMerkle = ping.userProperties || dbnull;

      let output = {} as any;

      output.accounts = 0;
      output.devices = 0;
      output.systemProperties = 0;
      output.messages = 0;
      output.devicedata = 0;
      output.resources = "";
      output.userProperties = 0;

      if (metadata.accountMerkle() !== accountMerkle) output.accounts = 1;
      if (metadata.deviceMerkle() !== deviceMerkle) output.devices = 1;
      if (systemAccountMetadata.systemPropertyMerkle() !== systemMerkle)
        output.systemProperties = 1;
      if (metadata.userPropertyMerkle() !== userPropertiesMerkle)
        output.userProperties = 1;
      if (metadata.messageMerkle() !== messageMerkle) output.messages = 1;
      if (metadata.deviceDataMerkle() !== deviceDataMerkle)
        output.devicedata = 1;

      return output;
    });
  }

  public async sync(context: Context, aid: number, sync: any) {
    let output = {} as any;
    for (let ss of this.syncServices) {
      await ss.sync(context, aid, sync, output);
    }

    return output;
  }
}
