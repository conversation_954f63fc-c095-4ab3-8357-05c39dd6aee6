import express = require("express");
import { microservice as g } from "../microservices/resource.microservice.mjs";
import {
  safeAny,
  validateClientBuild,
  secure,
  admin,
  s,
  b,
  n,
} from "../system/safe.mjs";
import { Context } from "../system/Context.mjs";
import { makeError } from "../system/error.mjs";
import { ids } from "../system/Strings.mjs";

/* tslint:disable */
import { binaryEncoder, sendResponse } from "../system/bjson.cjs";
/* tslint:enable */

export function checkBadAccounts(context: Context, badAids: number[]) {
  for (let badAid of badAids) {
    if (badAid === context.aid) {
      throw makeError(401, ids.BAD_ACCOUNT);
    }
  }
}

export function addResourceRoutes(router: express.Router) {
  router.post(
    "/accounts/:aid/ping",

    safeAny,
    validateClientBuild,
    secure,
    (req, res, next) => {
      let context = req.context;

      checkBadAccounts(context, g.badAccounts);

      g.syncService
        .ping(context, context.aid, context.any)
        .then((result) => {
          context.dumpLog();
          sendResponse(req, res, result, (r) =>
            binaryEncoder.encode_resource_ping_response(r)
          );
        })
        .catch(next);
    }
  );

  router.post(
    "/accounts/:aid/sync",
    safeAny,
    validateClientBuild,
    secure,
    (req, res, next) => {
      let context = req.context;
      /*
        if (context.ua_aid === 105 && context.ua_did !== 1)
            return res.status(500).json({ code: "DEBUGGING", message: "Debugging in progress, sync not allowed" });
*/
      checkBadAccounts(context, g.badAccounts);

      g.syncService
        .sync(context, context.aid, context.any)
        .then((result) => {
          context.dumpLog();
          sendResponse(req, res, result, (r) => binaryEncoder.encode_sync(r));
        })
        .catch(next);
    }
  );

  router.post(
    "/accounts/:aid/ping256",
    safeAny,
    validateClientBuild,
    secure,
    (req, res, next) => {
      let context = req.context;

      checkBadAccounts(context, g.badAccounts);

      g.syncService
        .ping256(context, context.aid, context.any)
        .then((result) => {
          context.dumpLog();
          sendResponse(req, res, result, (r) =>
            binaryEncoder.encode_ping256(r)
          );
        })
        .catch(next);
    }
  );

  router.post(
    "/accounts/:aid/devices/:did/locks/:bf00",
    safeAny,
    validateClientBuild,
    secure,
    (req, res, next) => {
      let context = req.context;
      g.syncService
        .lockDevice(
          context,
          context.aid,
          context.did,
          parseInt(req.params.bf00, 10),
          context.any
        )
        .then((result) => {
          context.dumpLog();
          res.json(result);
        })
        .catch(next);
    }
  );

  router.delete(
    "/accounts/:aid/devices/:did/locks/:bf00",
    safeAny,
    validateClientBuild,
    secure,
    (req, res, next) => {
      let context = req.context;
      g.syncService
        .unlockDevice(
          context,
          context.aid,
          context.did,
          parseInt(req.params.bf00, 10),
          {
            ticket: s(req.query.ticket),
            b16Hash: n(req.query.b16Hash),
            bdlHash: n(req.query.bdlHash),
            mustCheckHash: b(req.query.mustCheckHash),
          }
        )
        .then((result) => {
          context.dumpLog();
          res.json(result);
        })
        .catch(next);
    }
  );

  router.delete(
    "/accounts/:aid",
    safeAny,
    secure,
    admin,
    async (req, res, next) => {
      let context = req.context;
      await g.resourceService.deleteResources(context, context.aid);
      res.status(200).send();
    }
  );
}
