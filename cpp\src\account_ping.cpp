#include "encode_functions.h"
#include <napi.h>
#include "helpers.h"

Napi::Buffer<uint8_t> encode_account_ping_request(Napi::Env env, const Napi::Object &jsObj)
{
	// {
	MYBJsonBigRW *writer = new MYBJsonBigRW();
	writer->StartObject();

	writer->MYHash(MYLiterals::accounts, helpers::hash(jsObj.Get("accounts").ToString()));
	writer->MYHash(MYLiterals::devices, helpers::hash(jsObj.Get("devices").ToString()));
	writer->MYHash(MYLiterals::devicedata, helpers::hash(jsObj.Get("devicedata").ToString()));
	writer->MYHash(MYLiterals::messages, helpers::hash(jsObj.Get("messages").ToString()));
	writer->MYHash(MYLiterals::systemProperties, helpers::hash(jsObj.Get("systemProperties").ToString()));
	writer->MYHash(MYLiterals::userProperties, helpers::hash(jsObj.Get("userProperties").ToString()));

	if (jsObj.HasOwnProperty("resources"))
	{
		writer->StartArray(MYLiterals::resources);
		auto resources = jsObj.Get("resources").As<Napi::Array>();
		for (size_t i = 0; i < resources.Length(); i++)
		{
			writer->MYHash(helpers::hash(resources.Get(i).ToString()));
		}
		writer->EndArray();
	}

	writer->EndObject();

	return Napi::Buffer<uint8_t>::New(
			env,
			(uint8_t *)writer->pbegin(),
			writer->psize(),
			helpers::freeWriter,
			writer);
}

Napi::Buffer<uint8_t> encode_account_ping_response(Napi::Env env, const Napi::Object &jsObj)
{
	// {
	MYBJsonBigRW *writer = new MYBJsonBigRW();
	writer->StartObject();

	writer->Int32(MYLiterals::accounts, jsObj.Get("accounts").ToNumber());
	writer->Int32(MYLiterals::devices, jsObj.Get("devices").ToNumber());
	writer->Int32(MYLiterals::devicedata, jsObj.Get("devicedata").ToNumber());
	writer->Int32(MYLiterals::messages, jsObj.Get("messages").ToNumber());
	writer->Int32(MYLiterals::systemProperties, jsObj.Get("systemProperties").ToNumber());
	writer->Int32(MYLiterals::userProperties, jsObj.Get("userProperties").ToNumber());

	writer->EndObject();

	return Napi::Buffer<uint8_t>::New(
			env,
			(uint8_t *)writer->pbegin(),
			writer->psize(),
			helpers::freeWriter,
			writer);
}

Napi::Object decode_account_ping_request(Napi::Env env, const Napi::Buffer<uint8_t> &bjson)
{
	auto data = (uint8_t *)bjson.Data();
	auto size = bjson.Length();
	auto endPtr = data + size;

	MYBJsonIterator current = MYBJsonIterator(data, endPtr);
	MYBJsonIterator end(endPtr, endPtr);

	auto root = Napi::Object::New(env);

	while (++current != end)
	{
		if (current->type() == BJsonType::hash)
		{
			auto sectionName = helpers::t(current->key());
			root.Set(sectionName, helpers::hash64(current->asHash()));
		}
		else if (current->type() == BJsonType::array && current->key() == MYLiterals::resources)
		{
			auto resources = Napi::Array::New(env);
			root.Set("resources", resources);
			size_t i = 0;
			while ((++current)->type() != BJsonType::end)
			{
				resources.Set(i++, helpers::hash64(current->asHash()));
			}
		}
	}

	return root;
}

Napi::Object decode_account_ping_response(Napi::Env env, const Napi::Buffer<uint8_t> &bjson)
{
	auto data = bjson.Data();
	auto size = bjson.Length();
	auto endPtr = data + size;

	MYBJsonIterator current = MYBJsonIterator(data, endPtr);
	MYBJsonIterator end(endPtr, endPtr);

	auto root = Napi::Object::New(env);

	while ((++current)->type() != BJsonType::end)
	{
		auto sectionName = helpers::t(current->key());
		root.Set(sectionName, current->asInt32());
	}

	return root;
}
