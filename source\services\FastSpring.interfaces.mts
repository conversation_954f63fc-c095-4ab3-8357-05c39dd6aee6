export interface Subscription {
    prorate?: boolean;
    id?: string;
    quote?: null;
    subscription?: string;
    active?: boolean;
    state?: string;
    isSubscriptionEligibleForPauseByBuyer?: boolean;
    isPauseScheduled?: boolean;
    changed?: number;
    changedValue?: number;
    changedInSeconds?: number;
    changedDisplay?: string;
    changedDisplayISO8601?: Date;
    changedDisplayEmailEnhancements?: string;
    changedDisplayEmailEnhancementsWithTime?: string;
    live?: boolean;
    currency?: string;
    account?: string;
    product?: any;
    sku?: string;
    display?: string;
    quantity?: number;
    adhoc?: boolean;
    autoRenew?: boolean;
    price?: number;
    priceDisplay?: string;
    priceInPayoutCurrency?: number;
    priceInPayoutCurrencyDisplay?: string;
    discount?: number;
    discountDisplay?: string;
    discountInPayoutCurrency?: number;
    discountInPayoutCurrencyDisplay?: string;
    subtotal?: number;
    subtotalDisplay?: string;
    subtotalInPayoutCurrency?: number;
    subtotalInPayoutCurrencyDisplay?: string;
    attributes?: Attributes;
    next?: number;
    nextValue?: number;
    nextInSeconds?: number;
    nextDisplay?: string;
    nextDisplayISO8601?: Date;
    end?: null;
    endValue?: null;
    endInSeconds?: null;
    endDisplay?: null;
    endDisplayISO8601?: null;
    canceledDate?: null;
    canceledDateValue?: null;
    canceledDateInSeconds?: null;
    canceledDateDisplay?: null;
    canceledDateDisplayISO8601?: null;
    deactivationDate?: null;
    deactivationDateValue?: null;
    deactivationDateInSeconds?: null;
    deactivationDateDisplay?: null;
    deactivationDateDisplayISO8601?: null;
    sequence?: number;
    periods?: number;
    remainingPeriods?: number;
    begin?: number;
    beginValue?: number;
    beginInSeconds?: number;
    beginDisplay?: string;
    beginDisplayISO8601?: Date;
    beginDisplayEmailEnhancements?: string;
    beginDisplayEmailEnhancementsWithTime?: string;
    nextDisplayEmailEnhancements?: string;
    nextDisplayEmailEnhancementsWithTime?: string;
    intervalUnit?: string;
    intervalUnitAbbreviation?: string;
    intervalLength?: number;
    intervalLengthGtOne?: boolean;
    nextChargeCurrency?: string;
    nextChargeDate?: number;
    nextChargeDateValue?: number;
    nextChargeDateInSeconds?: number;
    nextChargeDateDisplay?: string;
    nextChargeDateDisplayISO8601?: Date;
    nextChargePreTax?: number;
    nextChargePreTaxDisplay?: string;
    nextChargePreTaxInPayoutCurrency?: number;
    nextChargePreTaxInPayoutCurrencyDisplay?: string;
    nextChargeTotal?: number;
    nextChargeTotalDisplay?: string;
    nextChargeTotalInPayoutCurrency?: number;
    nextChargeTotalInPayoutCurrencyDisplay?: string;
    nextNotificationType?: string;
    nextNotificationDate?: number;
    nextNotificationDateValue?: number;
    nextNotificationDateInSeconds?: number;
    nextNotificationDateDisplay?: string;
    nextNotificationDateDisplayISO8601?: Date;
    trialReminder?: TrialReminder;
    paymentReminder?: PaymentReminder;
    paymentOverdue?: PaymentOverdue;
    cancellationSetting?: CancellationSetting;
    schedule?: Schedule;
    fulfillments?: Fulfillments;
    addons?: any[];
    initialOrderId: string;
}

export interface Attributes {
    upcomingProduct?: string;
}

export interface CancellationSetting {
    cancellation?: string;
    intervalUnit?: string;
    intervalLength?: number;
}

export interface Fulfillments {
    "mylio-lic-individual-trial-1wk-1year_license_0": MylioLicIndividualTrial1Wk1YearLicense0[];
    instructions?: string;
}

export interface MylioLicIndividualTrial1Wk1YearLicense0 {
    license?: string;
    display?: string;
    type?: string;
}

export interface PaymentOverdue {
    intervalUnit?: string;
    intervalLength?: number;
    total?: number;
    sent?: number;
}

export interface PaymentReminder {
    intervalUnit?: string;
    intervalLength?: number;
}

export interface Schedule {
    repeat?: boolean;
    currentEntry?: string;
    currentEntryEndDate?: number;
    currentEntryEndDateValue?: number;
    currentEntryEndDateInSeconds?: number;
    currentEntryEndDateDisplay?: string;
    currentEntryEndDateDisplayISO8601?: Date;
    entries?: string[];
}

export interface TrialReminder {
    intervalUnit?: string;
    intervalLength?: number;
    trialType?: string;
    daysBeforeFirstPayment?: number;
    endOfTrial?: string;
}
