#pragma once
#include "bjson.h"
#include <vector>
#include <map>
#include "MYHash.h"
#include "MYTRev.h"
#include "vector_map.h"
#include "MYMediaFileType.h"
#include "MYNeeds.h"

#define WITH_FIXED_32
#ifdef WITH_FIXED_32
#define HashRefWrite Fixed32
#define HashRefRead fixed32
#else
#define HashRefWrite Uint32
#define HashRefRead asUint32
#endif

class MYSmallLocalFile;
class MYSmallLocalFiles;
class MYSmallBucketLocalFiles;

typedef uint32_t MYSmallHashRef;
typedef uint16_t MYStringRef;

extern const MYSmallHashRef MYSmallHashRefEmpty;
extern const MYStringRef MYStringRefEmpty;
extern std::string jpgFormat;

// For one media, one media type
class MYSmallLocalFile
{
public:
    MYSmallLocalFile()
    {
    }
    MYSmallLocalFile(const MYSmallLocalFile &);
    MYSmallLocalFile(MYSmallLocalFile &&other);

    MYSmallLocalFile &operator=(MYSmallLocalFile &&);
    MYSmallLocalFile &operator=(const MYSmallLocalFile &);

    MYSmallLocalFile(MYSmallBucketLocalFiles *MYSmallLocalFiles, MYMediaFileType::Enum mediaType) : _myLocalFiles(MYSmallLocalFiles), _mediaType(mediaType)
    {
        prepare();
    }
    MYSmallLocalFile(MYSmallBucketLocalFiles *MYSmallLocalFiles, MYMediaFileType::Enum mediaType, MYBJsonIterator &begin, const MYBJsonIterator &end);
    void deserializeFromBJson(MYBJsonIterator &begin, const MYBJsonIterator &end);

    const std::string &getFormat() const;
    bool setFormat(const std::string &newFormat);
    bool setFormat(std::string &&newFormat);

    MYMediaFileType::Enum getMediaType() const { return _mediaType; }

    const std::string &getRenamedFileNameNoExt() const;
    bool setRenamedFileNameNoExt(const std::string &newFileNameNoExt);
    bool setRenamedFileNameNoExt(std::string &&newFileNameNoExt);

    bool hasDataHash() const { return _dataHash != MYSmallHashRefEmpty; }
    const MYSmallHashRef getRefDataHash() const { return _dataHash; }
    // const MYHash& getDataHash() const;
    bool setDataHash(const MYHash &newDataHash);

    bool hasVisualEditHashHash() const { return _visualEditHash != MYSmallHashRefEmpty; }
    const MYSmallHashRef getRefVisualEditHashHash() const { return _visualEditHash; }
    // const MYHash& getVisualEditHash() const;
    bool setVisualEditHash(const MYHash &newVisualEditHash);

    bool hasBasisDataHash() const { return _basisDataHash != MYSmallHashRefEmpty; }
    const MYSmallHashRef getRefBasisDataHash() const { return _basisDataHash; }
    // const MYHash& getBasisDataHash() const;
    bool setBasisDataHash(const MYHash &newBasisDataHash);

    float getCropZoomFactor() const { return _cropZoomFactor; }
    bool setCropZoomFactor(float newCropZoomFactor);

    const MYSmallHashRef getRefParseHash() const { return _parseHash; }
    // const MYHash& getParseHash() const;
    bool setParseHash(const MYHash &newParseHash);

    bool reallyCantParse() const
    {
        return (_parseHash == _dataHash) && (getParsability() == MYParsability::NotParsable);
    }
    const MYParsability getParsability() const
    {
        if (_flags.test((int)Flags::parseAttempted))
        {
            if (_flags.test((int)Flags::parseFailed))
            {
                return MYParsability::NotParsable;
            }

            return MYParsability::Parsable;
        }

        return MYParsability::NotAttempted;
    }
    bool setParsability(MYParsability newParsability);

    bool getIsDraft() const { return _flags.test((int)Flags::isDraft); }
    bool setIsDraft(bool newCantParse);

    bool getIsHardWant() const { return _flags.test((int)Flags::hardWant); }
    bool setIsHardWant(bool newHardWant);

    const std::string &getALURL() const;
    bool setALURL(const std::string &newALUrl);
    bool setALURL(std::string &&newALUrl);

    bool getInInternalDataStorage() const { return _flags.test((int)Flags::inInternalData); }
    bool setInInternalDataStorage(bool newInInternalDataStorage);

    bool isModified() const;
    std::bitset<16> _modifiedFields;

private:
    friend MYSmallLocalFiles;
    MYSmallBucketLocalFiles *_myLocalFiles;

    MYSmallHashRef _dataHash;
    MYSmallHashRef _visualEditHash;

    MYSmallHashRef _basisDataHash;
    MYSmallHashRef _parseHash;

    float _cropZoomFactor;

    MYStringRef _fileNameNoExt;
    MYStringRef _alURL;

    MYStringRef _format;
    MYMediaFileType::Enum _mediaType;

    // We store this as flags - 1 (since 0 marshals as 'not exists'). So add 1 to read it.
    std::bitset<8> _flags;
    enum class Flags : uint8_t
    {
        parseAttempted = 0,
        inInternalData = 1,
        parseFailed = 2,
        isDraft = 3,
        hardWant = 4
    };

    bool _dirty = true;

    std::unique_ptr<MYBJsonSmallRW> _extensionData;

    BJSONINLINE void init(MYSmallBucketLocalFiles *local, MYMediaFileType::Enum type)
    {
        _myLocalFiles = local;
        _mediaType = type;
    }

    void clear();
    void prepareInternal();
    BJSONINLINE void prepare()
    {
        if (_dirty)
        {
            prepareInternal();
        }
    }

    bool empty() const
    {
        if (_dirty)
        {
            return true;
        }

        if (_format != MYStringRefEmpty)
        {
            return false;
        }

        if (_dataHash != MYSmallHashRefEmpty)
        {
            return false;
        }

        if (_visualEditHash != MYSmallHashRefEmpty)
        {
            return false;
        }

        if (_basisDataHash != MYSmallHashRefEmpty)
        {
            return false;
        }

        if (!_flags.any())
        {
            return false;
        }

        if (_cropZoomFactor != 1.0f)
        {
            return false;
        }

        if (_alURL != MYStringRefEmpty)
        {
            return false;
        }

        if (_fileNameNoExt != MYStringRefEmpty)
        {
            return false;
        }

        if (_parseHash != MYSmallHashRefEmpty)
        {
            return false;
        }

        if (_extensionData)
        {
            return false;
        }

        return true;
    }

    template <typename TBJson>
    void serializeToBJson(TBJson &writer) const
    {
        assert(!_dirty);
        // if (_mediaType != MYMediaFileType::NoType)
        //{
        //     writer.Uint32(MYLiterals::LocalFile::fileType, _mediaType);
        // }

        if (_format != MYStringRefEmpty)
        {
            writer.Uint32(MYLiterals::LocalFile::format, _format);
        }

        if (_dataHash != MYSmallHashRefEmpty)
        {
            writer.HashRefWrite(MYLiterals::LocalFile::dataHash, _dataHash);
        }

        if (_visualEditHash != MYSmallHashRefEmpty)
        {
            writer.HashRefWrite(MYLiterals::LocalFile::visualEditHash, _visualEditHash);
        }

        if (_basisDataHash != MYSmallHashRefEmpty)
        {
            writer.HashRefWrite(MYLiterals::LocalFile::basisDataHash, _basisDataHash);
        }

        if ((uint32_t)_flags.to_ulong() > 0)
        {
            writer.Uint32(MYLiterals::LocalFile::flags, (uint32_t)_flags.to_ulong() - 1);
        }

        if (_cropZoomFactor != 1.0f)
        {
            writer.Double(MYLiterals::LocalFile::cropZoomFactor, _cropZoomFactor);
        }

        if (_alURL != MYStringRefEmpty)
        {
            writer.Uint32(MYLiterals::LocalFile::alUrl, _alURL);
        }

        if (_fileNameNoExt != MYStringRefEmpty)
        {
            writer.Uint32(MYLiterals::LocalFile::fileNameNoExt, _fileNameNoExt);
        }

        if (_parseHash != MYSmallHashRefEmpty)
        {
            writer.HashRefWrite(MYLiterals::LocalFile::parseHash, _parseHash);
        }

        if (_extensionData)
        {
            writer.Raw(_extensionData->pbegin(), _extensionData->pend());
        }
    }
};

// For one media, across all media type
class MYSmallLocalFiles
{
public:
    MYSmallLocalFiles() {}
    MYSmallLocalFiles(MYSmallBucketLocalFiles *bucketLocal) : _bucketLocal(bucketLocal)
    {
        initLocalFileArray();
    }
    MYSmallLocalFiles(MYSmallBucketLocalFiles *bucketLocal, MYBJsonIterator &iter, const MYBJsonIterator &end, bool reInit);
    MYSmallLocalFiles(MYSmallBucketLocalFiles *bucketLocal, MYBJsonIterator &iter, const MYBJsonIterator &end);
    MYSmallLocalFiles(const MYSmallLocalFiles &);
    MYSmallLocalFiles(MYSmallLocalFiles &&other);

    void deserializeFromBJson(MYBJsonIterator &begin, const MYBJsonIterator &end);
    void clear();
    void initLocalFileArray();

    MYSmallLocalFiles &operator=(const MYSmallLocalFiles &);
    MYSmallLocalFiles &operator=(MYSmallLocalFiles &&other);

    bool operator==(const MYSmallLocalFiles &other) const;

    template <typename TBJson>
    void serializeToBJson(TBJson &writer) const
    {
        bool first = true;

        for (const auto &file : _knownMediaFiles)
        {
            if (!file.empty())
            {
                if (first)
                {
                    writer.StartObject(file.getMediaType());
                    first = false;
                }
                else
                {
                    writer.Separator(file.getMediaType());
                }

                file.serializeToBJson(writer);
            }
        }

        for (const auto &file : _extendedMediaFiles)
        {
            if (!file.empty())
            {
                if (first)
                {
                    writer.StartObject(file.getMediaType());
                    first = false;
                }
                else
                {
                    writer.Separator(file.getMediaType());
                }

                file.serializeToBJson(writer);
            }
        }

        if (!first)
            writer.EndObject();
    }

    const MYSmallLocalFile &getMediaFileOrEmpty(MYMediaFileType::Enum mediaFileType) const;
    MYSmallLocalFile *getMediaFileOrNull(MYMediaFileType::Enum mediaFileType);
    const MYSmallLocalFile *getMediaFileOrNull(MYMediaFileType::Enum mediaFileType) const;
    MYSmallLocalFile *getOrCreateMediaFile(MYMediaFileType::Enum mediaFileType);

    const NeedsBits getSupportMediaTypes() const;

    // MediaFilesVec& getFiles()
    //{
    //     return _mediaFiles;
    // }

    // const MediaFilesVec& getFiles() const
    //{
    //     return _mediaFiles;
    // }

    bool isModified() const;
    std::bitset<16> _modifiedFields;

    bool empty() const;

    // MediaFilesVec::iterator begin()
    //{
    //     return _mediaFiles.begin();
    // }

    // MediaFilesVec::iterator end()
    //{
    //     return _mediaFiles.end();
    // }

    // MediaFilesVec::const_iterator begin() const
    //{
    //     return _mediaFiles.begin();
    // }

    // MediaFilesVec::const_iterator end() const
    //{
    //     return _mediaFiles.end();
    // }

private:
    friend MYSmallLocalFile;
    friend MYSmallLocalFile;
    friend class Test;

    MYSmallBucketLocalFiles *_bucketLocal;
    std::vector<MYSmallLocalFile> _extendedMediaFiles;
    std::array<MYSmallLocalFile, 7> _knownMediaFiles;
};

class MYSmallBucketLocalFiles
{
public:
    typedef vector_map<MYHash, MYSmallLocalFiles> MYMediaMapType;

    MYSmallBucketLocalFiles(size_t estimatedMediaPerBucket) : _estimatedMediaPerBucket(estimatedMediaPerBucket)
    {
#if !defined(_DEBUG) && !defined(DEBUG)
        // We don't reserve in debug, because we want to take re-allocations. Otherwise it will hide bugs if
        // the pointer values always remain the same.
        _mediaFiles.reserve(estimatedMediaPerBucket);
#endif
    }

    MYSmallBucketLocalFiles(size_t estimatedMediaPerBucket, MYBJsonIterator &begin, const MYBJsonIterator &end);
    MYSmallBucketLocalFiles(const MYSmallBucketLocalFiles &);
    MYSmallBucketLocalFiles(MYSmallBucketLocalFiles &&other);

    void deserializeFromBJson(MYBJsonIterator &begin, const MYBJsonIterator &end);
    void clear();

    MYSmallBucketLocalFiles &operator=(const MYSmallBucketLocalFiles &);
    MYSmallBucketLocalFiles &operator=(MYSmallBucketLocalFiles &&other);

    bool operator==(const MYSmallBucketLocalFiles &other) const;

    template <typename TBJson>
    void serializeToBJson(TBJson &writer) const
    {
        prepare();

        if (!_mediaFiles.empty())
        {
            bool first = true;
            writer.StartObject(MYLiterals::BucketLocalFiles::media);
            for (const auto &mediaFiles : _mediaFiles)
            {
                if (!mediaFiles.second.empty())
                {
                    if (!first)
                    {
                        writer.Separator();
                    }
                    first = false;

                    writer.MYHash(mediaFiles.first);
                    mediaFiles.second.serializeToBJson(writer);
                }
            }
            writer.EndObject();
        }

        if (!_hashMap.empty())
        {
            writer.Binary(MYLiterals::Files::hashMap, (const uint8_t *)_hashMap.data(), _hashMap.size() * sizeof(uint32_t));
        }

        if (!_stringMap.empty())
        {
            writer.StartArray(MYLiterals::BucketLocalFiles::stringMap);
            for (const auto &stringEntry : _stringMap)
            {
                writer.String(0, stringEntry);
            }
            writer.EndArray();
        }

        if (_extensionData)
        {
            writer.Raw(_extensionData->pbegin(), _extensionData->pend());
        }
    }

    MYSmallLocalFiles *getOrCreateLocalFiles(const MYHash &media);

    MYMediaMapType &getFiles()
    {
        prepare();
        return _mediaFiles;
    }

    const MYMediaMapType &getFiles() const
    {
        prepare();
        return _mediaFiles;
    }

    bool isModified() const;
    std::bitset<16> _modifiedFields;

    bool empty() const;
    /*
        MYMediaMapType::iterator begin()
        {
            prepare();
            return _mediaFiles.begin();
        }

        MYMediaMapType::iterator end()
        {
            prepare();
            return _mediaFiles.end();
        }

        MYMediaMapType::const_iterator begin() const
        {
            prepare();
            return _mediaFiles.begin();
        }

        MYMediaMapType::const_iterator end() const
        {
            prepare();
            return _mediaFiles.end();
        }*/

private:
    void prepareInternal() const
    {
        _mediaFiles.clear();
    }

    BJSONINLINE void prepare() const
    {
        if (_dirty)
        {
            prepareInternal();
        }
    }

    MYSmallHashRef getOrCreateHashRef(const MYHash &hash);
    MYStringRef getOrCreateStringRef(const std::string &string);
    MYStringRef getOrCreateStringRef(std::string &&string);
    const std::string &getString(MYStringRef stringRef) const;
    std::vector<uint32_t> _hashMap;
    std::vector<std::string> _stringMap;

    friend class MYSmallLocalFile;
    friend class Test;

    bool _dirty = false;
    mutable MYMediaMapType _mediaFiles;

    size_t _estimatedMediaPerBucket = 0;

    std::unique_ptr<MYBJsonSmallRW> _extensionData;
};
