

    drop view if exists a0."Pin" cascade;

    create or replace view a0."Pin" as
    select
    flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		code_challenge as "codeChallenge",
		email,
		pin,
		expires_at as "expiresAt"
    from a0.pin;
    

drop function if exists a0.pin_create; 
        create function a0.pin_create(
            _flags int,
	_modified_time timestamptz,
	_created_time timestamptz,
	_code_challenge text,
	_email text,
	_pin text,
	_expires_at timestamptz
        )
        returns a0."Pin"
        as $$
        
    declare
        result a0."Pin";
        
    begin
        
        


       
        


        
        
        
        
        _modified_time := coalesce(_modified_time, now());
        _created_time := coalesce(_created_time, now());
        insert into a0.pin (
            flags,
	modified_time,
	created_time,
	code_challenge,
	email,
	pin,
	expires_at
        )
        values(
            _flags,
			_modified_time,
			_created_time,
			_code_challenge,
			_email,
			_pin,
			_expires_at
        )
        returning
            flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		code_challenge as "codeChallenge",
		email,
		pin,
		expires_at as "expiresAt"
        into result;

        



        

    
        



        return result;
    end;
        $$
        language plpgsql;
        

drop function if exists a0.pin_update; 
        create function a0.pin_update(
            _flags int,
	_modified_time timestamptz,
	_created_time timestamptz,
	_code_challenge text,
	_email text,
	_pin text,
	_expires_at timestamptz
        )
        returns a0."Pin"
        as $$
        
    declare
        result a0."Pin";
        
    begin
        
        


       
        


        
        
        _modified_time := now();
        update a0.pin
        set
            flags = _flags,
			modified_time = _modified_time,
			created_time = _created_time,
			pin = _pin,
			expires_at = _expires_at
        where email = _email and code_challenge = _code_challenge
        returning
            flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		code_challenge as "codeChallenge",
		email,
		pin,
		expires_at as "expiresAt"
        into result;

        

    
        



        return result;
    end;
        $$
        language plpgsql;
        

drop function if exists a0.pin_read_by_email_and_code_challenge; 
        create function a0.pin_read_by_email_and_code_challenge(
            _email text,
	_code_challenge text
        )
        returns a0."Pin"
        as $$
        select
        flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		code_challenge as "codeChallenge",
		email,
		pin,
		expires_at as "expiresAt"
        from a0.pin
        where email = _email and code_challenge = _code_challenge;
        $$
        language sql;
        

drop function if exists a0.pin_delete_by_email_and_code_challenge; 
        create function a0.pin_delete_by_email_and_code_challenge(
            _email text,
	_code_challenge text
        )
        returns void
        as $$
        
    declare
        
        
    begin
        
        


       
        


        

    delete from a0.pin
    where email = _email and code_challenge = _code_challenge;

    
    
        



        
    end;
        $$
        language plpgsql;
        

drop function if exists a0.pin_delete_by_email; 
        create function a0.pin_delete_by_email(
            _email text
        )
        returns void
        as $$
        
    declare
        
        
    begin
        
        


       
        


        

    delete from a0.pin
    where email = _email;

    
    
        



        
    end;
        $$
        language plpgsql;
        

drop function if exists a0.pin_find_by_email; 
        create function a0.pin_find_by_email(
            _email text
        )
        returns setof a0."Pin"
        as $$
        select
        flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		code_challenge as "codeChallenge",
		email,
		pin,
		expires_at as "expiresAt"
        from a0.pin
        where email = _email;
        $$
        language sql;
        
