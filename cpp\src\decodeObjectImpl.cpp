#include "helpers.h"
#include "getFieldInfo.h"
#include "bjson.h"
#include <napi.h>

Napi::Object helpers::decodeObject(Napi::Env env, MYBJsonIterator &current, int sectionSid)
{
	assert(current->type() == BJsonType::object);
	auto objectEnd = current.extent();

	auto jsObj = Napi::Object::New(env);

	MYBJsonRW d;
	// bool hasD = false;
	Field f;
	while (++current != objectEnd)
	{
		Field f;
		if (getFieldInfo(sectionSid, current->key(), f))
		{
			switch (f.type)
			{
			case BJsonType::string:
				jsObj.Set(f.name, current->asString());
				continue;

			case BJsonType::hash:
				jsObj.Set(f.name, helpers::hash64(current->asHash()));
				continue;

			case BJsonType::trev:
				jsObj.Set(f.name, helpers::trev64(current->asTrev()));
				continue;

			case BJsonType::binary:
			{
				auto bits = current->asBinary();
				jsObj.Set(f.name, helpers::bin2text(bits.data(), bits.size()));
				continue;
			}
			case BJsonType::object:
			{
				auto blob = current->asBinaryFromScoped(objectEnd.ptr());
				current = current.extent();
				jsObj.Set(f.name, helpers::bin2text(blob.data(), blob.size()));
				continue;
			}
			case BJsonType::varint:
				if (f.isDate)
				{
					jsObj.Set(f.name, Napi::Date::New(env, current->asUint64()));
				}
				else
				{
					if (f.isInt64)
						jsObj.Set(f.name, Napi::String::New(env, std::to_string(current->asInt64())));
					else
						jsObj.Set(f.name, Napi::Number::New(env, current->asInt32()));
				}
				continue;

			case BJsonType::true1:
				jsObj.Set(f.name, current->asBool());
				continue;

			default:
				break;
			}
		}

		d.addMember(current);
	}

	if (!d.empty())
	{
		std::string buffer((char *)d.pbegin(), d.psize());
		jsObj.Set("d", helpers::bin2text(d.pbegin(), d.psize()));
	}

	return jsObj;
}
