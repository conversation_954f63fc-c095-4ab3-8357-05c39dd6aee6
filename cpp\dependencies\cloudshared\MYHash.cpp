#include "MYHash.h"

#include "MYStringUtil.h"
#include "MYPrimitives.h"

#ifdef MYLIO_CLIENT
#include "MYPlatform.h"
#include "MYFileStream.h"
#include "MYImageDecoder.h"
#endif // MYLIO_CLIENT

#if defined(_MSC_VER)
#include <winsock2.h>
#else
#include <arpa/inet.h>
#endif

#include <sstream>
#include <cstring>

MYHash bucketPrefixHash = MYHash::fromHashString("00000000000000000000000000000000000000BC");

MYHash::MYHash()
{
    clear();
}

MYHash::MYHash(no_init_t)
{
    // Special use garbage memory constructor
#if defined(DEBUG) || defined(_DEBUG)
    memset(this, 0xcd, sizeof(MYHash));
#endif
}

MYHash MYHash::fromHashString(const std::string &inHashAsString, SerializationFlags flags)
{
    return fromHashString(inHashAsString, (flags & SerializationFlags::Base64Hash) != 0);
}

MYHash MYHash::fromHashString(const std::string &inHashAsString, bool base64 /*= false*/)
{
    MYHash hash;
    hash.buildFromHashString(inHashAsString, base64);
    return hash;
}

MYHash MYHash::fromBucketAndDeviceId(uint16_t bucketId, uint32_t deviceId)
{
    MYHash hash = bucketPrefixHash;
    hash.raw[0] = (uint8_t)(bucketId >> 4);
    hash.raw[1] = (uint8_t)(bucketId << 4);

    // Store the device id as big endian so that a sort by uniqueHash is also
    // a sort by device id
    uint32_t deviceId_big_endian = htonl(deviceId);
    memcpy(&hash.raw[14], &deviceId_big_endian, sizeof(deviceId));
    return hash;
}

bool MYHash::isValidHashString(const std::string &inString)
{
    if (inString.length() != (MYHASH_SIZE * 2))
    {
        return false;
    }

    for (const char &c : inString)
    {
        if (c >= '0' && c <= '9')
            continue;
        if (c >= 'a' && c <= 'f')
            continue;
        if (c >= 'A' && c <= 'F')
            continue;

        return false;
    }

    return true;
}

std::set<MYHash> MYHash::splitToSet(const std::string &input, char delimiter)
{
    std::set<std::string> strSet = MYString::splitToSet(input, delimiter);

    std::set<MYHash> ret;
    for (auto &str : strSet)
    {
        if (str.empty())
            continue;
        ret.insert(MYHash::fromHashString(str));
    }
    return ret;
}

bool MYHash::buildFromHashString(const char *hashString, SerializationFlags flags)
{
    return buildFromHashString(hashString, (flags & SerializationFlags::Base64Hash) != 0);
}

bool MYHash::buildFromHashString(const char *hashString, bool base64)
{
    if (stringIsNullOrEmpty(hashString))
        return false;

    if (base64)
    {
        if (!stringIsBase64(hashString))
            return false;

        std::string copy(hashString);
        std::string b64(base64_decode(copy));
        if (b64.size() != MYHASH_SIZE)
            return false;

        for (int i = 0; i < MYHASH_SIZE; i++)
            raw[i] = (unsigned char)b64[i];
    }
    else
    {
        if (!stringIsHex(hashString))
            return false;

        size_t len = strlen(hashString);
        if (len != MYHASH_SIZE * 2)
            return false;

        for (int i = 0; i < MYHASH_SIZE; i++)
            raw[i] = hex2byte(hashString + i * 2);
    }

    isSet = true;
    return true;
}

void MYHash::buildFromHashString(const std::string &inHashAsString, SerializationFlags flags)
{
    buildFromHashString(inHashAsString, (flags & SerializationFlags::Base64Hash) != 0);
}

void MYHash::buildFromHashString(const std::string &inHashAsString, bool base64 /*= false*/)
{
    if (inHashAsString.empty())
        return;

    if (inHashAsString.length() < MYHASH_SIZE * 2)
    {
        if (base64)
        {
            std::string b64(base64_decode(inHashAsString));
            if (b64.size() != MYHASH_SIZE)
            {
                isSet = false;
                return;
            }

            for (int i = 0; i < MYHASH_SIZE; i++)
                raw[i] = (unsigned char)b64[i];

            isSet = true;

            return;
        }

        return;
    }

    if (inHashAsString.length() != MYHASH_SIZE * 2)
        return;

    const char *chars = inHashAsString.c_str();

    for (int i = 0; i < MYHASH_SIZE; i++)
        raw[i] = hex2byte(chars + i * 2);

    isSet = true;
}

MYHash::MYHash(const unsigned char *inHash)
{
    if (inHash)
    {
        memcpy(&raw[0], inHash, MYHASH_SIZE);

        isSet = true;
    }
    else
    {
        clear();
    }
}

MYHash::MYHash(const MYHashStorage &inHashStorage)
{
    memcpy(&raw[0], &inHashStorage, MYHASH_SIZE);

    resetsetbit();
}

std::string MYHash::toSQLString() const
{
    return "x'" + toString() + "'";
}

std::string MYHash::toString(SerializationFlags flags) const
{
    if (flags & SerializationFlags::Short)
    {
        if (flags & SerializationFlags::Base64Hash)
        {
            return base64_encode(&raw[0], MYHASH_SIZE);
        }
        else
        {
            return MYString::blobToHexString(&raw[0], MYHASH_SIZE);
        }
    }

    if (flags & SerializationFlags::SendZeroHash)
    {
        if (empty())
        {
            return "0000000000000000000000000000000000000000";
        }
    }

    return toString((flags & SerializationFlags::Base64Hash) != 0);
}

std::string MYHash::toString(bool base64 /*= false*/) const
{
    if (empty())
        return "";
    else
    {
        if (base64)
        {
            return base64_encode(&raw[0], MYHASH_SIZE);
        }

        return MYString::blobToHexString(&raw[0], MYHASH_SIZE);
    }
}

std::string MYHash::toQuickString(bool base64, std::string &outString) const
{
    if (empty())
        return "";
    else
    {
        if (base64)
        {
            return base64_encode(&raw[0], MYHASH_SIZE);
        }

        return MYString::quickBlobToHexString(&raw[0], MYHASH_SIZE, outString);
    }
}

void MYHash::clear()
{
    isSet = false;
    memset(&raw[0], 0, MYHASH_SIZE);
}

bool MYHash::operator==(const MYHash &otherHash) const
{
    return raw == otherHash.raw;
}

bool MYHash::operator!=(const MYHash &otherHash) const
{
    return memcmp(&raw[0], &otherHash.raw[0], MYHASH_SIZE) != 0;
}

bool MYHash::operator<(const MYHash &otherHash) const
{
    return memcmp(&raw[0], &otherHash.raw[0], MYHASH_SIZE) < 0;
}

bool MYHash::operator>(const MYHash &otherHash) const
{
    return memcmp(&raw[0], &otherHash.raw[0], MYHASH_SIZE) > 0;
}

MYHash MYHash::onesCompliment() const
{
    MYHash result = *this;
    for (unsigned char &c : result.raw)
    {
        c ^= 0xFF;
    }

    result.resetsetbit();

    return result;
}

unsigned char MYHash::char2nibble(char hex)
{
    if (hex >= '0' && hex <= '9')
        return hex - '0';
    if (hex >= 'a' && hex <= 'f')
        return hex - 'a' + 10;
    if (hex >= 'A' && hex <= 'F')
        return hex - 'A' + 10;
    assert(false);
    return 0;
}

unsigned char MYHash::hex2byte(const char *hex)
{
    return (char2nibble(hex[0]) << 4) | char2nibble(hex[1]);
}

bool MYHash::empty() const
{
    if (!isSet)
        return true;

#ifdef MYLIO_64BIT
    if ((((size_t)(uint8_t *)this) & 0x7) == 0)
    {
        auto part = (uint64_t *)&raw[0];
        if (*part != 0)
            return false;

        part = (uint64_t *)&raw[8];
        if (*part != 0)
            return false;

        auto part32 = (uint32_t *)&raw[16];
        if (*part32 != 0)
            return false;

        return true;
    }
#else
    if ((((size_t)(uint8_t *)this) & 0x3) == 0)
    {
        uint32_t *part = (uint32_t *)&raw[0];
        if (*part != 0)
            return false;

        part = (uint32_t *)&raw[4];
        if (*part != 0)
            return false;

        part = (uint32_t *)&raw[8];
        if (*part != 0)
            return false;

        part = (uint32_t *)&raw[12];
        if (*part != 0)
            return false;

        part = (uint32_t *)&raw[16];
        if (*part != 0)
            return false;

        return true;
    }
#endif

    for (int i = 0; i < MYHASH_SIZE; i++)
        if (raw[i] != 0)
            return false;

    return true;
}

void MYHash::alignedPartialResetsetbit()
{
#ifdef MYLIO_64BIT
    auto part = (uint64_t *)&raw[8];
    if (*part != 0)
    {
        isSet = true;
        return;
    }

    auto part32 = (uint32_t *)&raw[16];
    if (*part32 != 0)
    {
        isSet = true;
        return;
    }
#else
    auto part = (uint32_t *)&raw[4];
    if (*part != 0)
    {
        isSet = true;
        return;
    }

    part = (uint32_t *)&raw[8];
    if (*part != 0)
    {
        isSet = true;
        return;
    }

    part = (uint32_t *)&raw[12];
    if (*part != 0)
    {
        isSet = true;
        return;
    }

    part = (uint32_t *)&raw[16];
    if (*part != 0)
    {
        isSet = true;
        return;
    }
#endif

    isSet = false;
}

void MYHash::unalignedresetsetbit()
{
    for (int i = 0; i < MYHASH_SIZE; i++)
    {
        if (raw[i] != 0)
        {
            isSet = true;
            return;
        }
    }
    isSet = false;
}

const MYHash &MYHash::getCloudHash()
{
    return g_cloudHash;
}

const MYHash &MYHash::emptyHash()
{
    return g_emptyHash;
}

#ifdef MYLIO_CLIENT
MYHash MYHash::computedFrom(const std::string &inString)
{
    MYHash hash;
    hash.computeFrom(inString);
    return hash;
}

MYHash MYHash::computedFrom(const void *buffer, size_t bufferSize)
{
    MYHash hash;
    hash.computeFrom(buffer, bufferSize);
    return hash;
}

MYHash MYHash::computedFromFile_Deprecated(const MYFSPath &filePath, Platform::MYFileStreamBase *stream, MYImageDecoderPtr &imageDecoder)
{
    MYHash hash;
    hash.computeFromFile_Deprecated(filePath, stream, imageDecoder);
    return hash;
}

MYHash MYHash::computedFromFile(const MYFSPath &filePath, const std::string &nominalExtension)
{
    MYHash hash;
    MYImageDecoderPtr imageDecoder;
    hash.computeFromFile(filePath, nominalExtension, imageDecoder, nullptr);
    return hash;
}

MYHash MYHash::computedFromFile(const MYFSPath &filePath, MYImageDecoderPtr &imageDecoder)
{
    MYHash hash;
    hash.computeFromFile(filePath, imageDecoder, nullptr);
    return hash;
}

MYHash MYHash::computedFromFile(const MYFSPath &filePath, MYImageDecoderPtr &imageDecoder, bool *quit)
{
    MYHash hash;
    if (!hash.computeFromFile(filePath, imageDecoder, quit))
    {
        hash.clear();
    }
    return hash;
}

MYHash MYHash::generated()
{
    MYHash hash;
    hash.generate();
    return hash;
}

void MYHash::generate()
{
    std::string guid1 = Platform::get()->generateGUID();
    std::string guid2 = Platform::get()->generateGUID();
    computeFrom(guid1 + guid2);
}

bool MYHash::computeFrom(const std::string &inString)
{
    return computeFrom(inString.c_str(), (unsigned int)inString.length());
}

bool MYHash::computeFrom(const void *buffer, size_t bufferSize)
{
    if (!Platform::get()->computeHash(buffer, (unsigned int)bufferSize, &raw[0]))
        return false;
    isSet = true;
    return true;
}

bool MYHash::computeFromFile_internal(const MYFSPath &filePath, const std::string &nominalExtension,
                                      Platform::MYFileStreamBase *stream, MYImageDecoderPtr &imageDecoder, bool *quit)
{
#if !defined(WIN32) || WINAPI_FAMILY == WINAPI_FAMILY_DESKTOP_APP
    // Only bother checking for RAWs (unless we come up with a magical JPG method someday...)

    if (computeFastHashFromLegacyRaw_internal(filePath, nominalExtension, imageDecoder))
    {
        return true;
    }
#endif

    //  Fall back to computing the hash of the file
    if (!Platform::get()->computeHashOfStream(stream, &raw[0], quit))
    {
        return false;
    }

    isSet = true;
    return true;
}

bool MYHash::computeFromFile_Deprecated(const MYFSPath &filePath, Platform::MYFileStreamBase *stream, MYImageDecoderPtr &imageDecoder)
{
    return computeFromFile_internal(filePath, filePath.getExtension(), stream, imageDecoder, nullptr);
}

bool MYHash::computeFromFile(const MYFSPath &filePath, bool *quit)
{
    MYImageDecoderPtr imageDecoder;
    return computeFromFile(filePath, imageDecoder, quit);
}

bool MYHash::computeFromFile(const MYFSPath &filePath, MYImageDecoderPtr &imageDecoder, bool *quit)
{
    return computeFromFile(filePath, filePath.getExtension(), imageDecoder, quit);
}

bool MYHash::computeFromFile(const MYFSPath &filePath, const std::string &nominalExtension, MYImageDecoderPtr &imageDecoder, bool *quit)
{
    Platform::MYFileStreamBase *stream = Platform::get()->openFileReaderStream(filePath);
    if (stream == nullptr)
    {
        return false;
    }

    bool result = computeFromFile_internal(filePath, nominalExtension, stream, imageDecoder, quit);

    Platform::get()->closeFileStream(stream);

    return result;
}

MYHash MYHash::fastHashFromLegacyRawOrExistingHash(const MYFSPath &url, const MYHash &existingHash,
                                                   const std::string &nominalExtension)
{
    MYHash hash;
    MYImageDecoderPtr decoder;

    if (hash.computeFastHashFromLegacyRaw_internal(url, nominalExtension, decoder))
    {
        //  We successfully got the hash from the legacy RAW file, return it.
        return hash;
    }

    //  Return the existing hash that was passed in to us.
    assert(existingHash.empty() == false && "Unanticipated scenario, talk to Dave or Deon.");
    return existingHash;
}

bool MYHash::computeFastHashFromLegacyRaw_internal(const MYFSPath &url,
                                                   const std::string &nominalExtension,
                                                   MYImageDecoderPtr &imageDecoder)
{
    //  If we don't have nominal extension, then use the extension from 'url'.
    auto extension = nominalExtension.empty() ? url.getExtension() : nominalExtension;

    //  Guard against accidental hashing of *.partial files. If you hit this assert
    //  you should be passing the correct "nominal extension".
    assert(stringAreEqual("partial", extension.c_str(), true) == false);

    if (MYMediaFileType::isSpecialDecoderRAW(extension) == false)
    {
        MYMediaFileType::Enum type = MYMediaFileType::getTypeFromExtension(extension);
        if (type == MYMediaFileType::RAW)
        {
            if (imageDecoder == nullptr)
            {
                imageDecoder = std::make_shared<MYImageDecoder>();
                if (imageDecoder->decodeFile(url, MYMediaFileType::RAW) != ImageDecoder_Success)
                {
                    imageDecoder.reset();
                }
            }

            if (imageDecoder != nullptr && imageDecoder->needsFullHash() == false)
            {
                //  If we have a loaded/decoded image, try to get the hash from the decoder.
                auto metadata = imageDecoder->metadata();
                if (metadata._imageDataHash.empty() == false)
                {
                    *this = metadata._imageDataHash;
                    return true;
                }
            }
        }
    }

    return false;
}

#endif // MYLIO_CLIENT

MYHash MYHash::getRangeStartForBucket(uint16_t bucketId)
{
    assert(bucketId < 4096);

    MYHash start = MYHash::emptyHash();
    start.raw[0] = (bucketId >> 4);
    start.raw[1] = (bucketId << 4);
    start.resetsetbit();

    return start;
}

MYHash MYHash::getRangeEndForBucket(uint16_t bucketId)
{
    MYHash finish(no_init);
    memset(&finish.raw[0], 0xFF, MYHASH_SIZE);
    finish.raw[0] = (bucketId >> 4);
    finish.raw[1] = (bucketId << 4);
    finish.raw[1] |= 0x0F;
    finish.resetsetbit();

    return finish;
}
