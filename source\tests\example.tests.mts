// import { test, describe, mock, before, after, it } from "node:test";
// import assert from "node:assert";
// import { read } from "node:fs";

// // The host you use depends on the test environment
// // you want to access. We have two main cloud environments
// //  * https://account-test-0.mylio.com - This is the stable test version of the cloud
// //      used for testing the master branch. This code will typically make it into
// //      production when we release a new version of Mylio
// //  * https://account-test-1.mylio.com - Unstable test environment, used for testing
// //      feature branches before they make it into master. Typically used by developers
// //      for dev testing and testers testing feature branches.
// const HOST = "https://account-test-0.mylio.com";

// describe("How to create a developer account", async (t) => {
//   before(async (t) => {
//     // assume the account exists, so attempt to sign in
//     let rtokenResponse = await fetch(`${HOST}/v4/accounts/x/rtoken`, {
//       body: JSON.stringify({
//         sub: "<EMAIL>",
//         password: "password",
//       }),
//       method: "POST",
//       headers: { "Content-Type": "application/json" },
//     });
//     let rtokenResult = await rtokenResponse.json();
//     // if the call succeeds the account exists
//     if (rtokenResponse.ok) {
//       //delete the account so we don't have an email conflict when we create the new account
//       let deleteAccountResponse = await fetch(
//         `${HOST}/accounts/${rtokenResult.account.accountId}`,
//         {
//           method: "DELETE",
//           headers: {
//             Authorization: "Bearer " + rtokenResult.token,
//           },
//         }
//       );
//       assert(deleteAccountResponse.ok, "Delete account failed");
//     }
//   });

//   it("Create a new Developer Account and upgrade it to 1015", async (t) => {
//     //create a new account first. A new account automatically has an indefinite subscription
//     //to featureset 1000
//     let createAccountResponse = await fetch(`${HOST}/accounts`, {
//       // You should specify a password unless you are testing pin links,
//       // unless you have an automated way of reading emails and parsing them
//       body: JSON.stringify({
//         sub: "<EMAIL>",
//         password: "password",
//       }),
//       method: "POST",
//       headers: {
//         "Content-Type": "application/json",
//         Accepts: "application/json",
//       },
//     });
//     let createAccountResult = await createAccountResponse.json();
//     // to have the account use featureset 1015 we have to create a new subscription
//     // every subscription must be based on a pre-existing template for testing
//     // purposes there are three templates to choose from depending on the purpose
//     // of the account. This is for tracking purposes. Please note that if you are
//     // testing subscription logic you should use the public templates. The three
//     //testing templates are:
//     // * "DEV" - Developers testing their code should use this template
//     // * "TEST" - Testers doing manual testing should use this template
//     // * "AUTO" - Users created for automation should use this template
//     let newSubscriptionResponse = await fetch(
//       `${HOST}/accounts/${createAccountResult.account.accountId}/subscriptions`,
//       {
//         method: "POST",
//         body: JSON.stringify({
//           accountId: createAccountResult.account.accountId,
//           templateId: "DEV",
//         }),
//         headers: {
//           "Content-Type": "application/json",
//           Accept: "application/json",
//           // You have to be signed in to the account to add a subscription
//           Authorization: "Bearer " + createAccountResult.token,
//         },
//       }
//     );
//     let newSubscriptionResult = await newSubscriptionResponse.json();
//     assert(newSubscriptionResult.featuresetId === "1015", "");

//     // the account record should not have a planId of 1015
//     let url = `${HOST}/accounts/${createAccountResult.account.accountId}`;
//     let readAccountResponse = await fetch(url, {
//       method: "GET",
//       headers: {
//         Accept: "application/json",
//         // You have to be signed in to the account to add a subscription
//         Authorization: "Bearer " + createAccountResult.token,
//       },
//     });

//     assert(readAccountResponse.ok, "Read account failed");
//     let readAccountResult = await readAccountResponse.json();
//     assert(readAccountResult.planId === "1015");
//   });

//   after(async (t) => {
//     console.log("done");
//     setTimeout(() => {
//       process.exit(0);
//     }, 200);
//   });
// });
