type MField = { name: string };
type MType = {
  name: string;
  flatten: number;
  abstract: number;
  base: string;
  fields: Array<MField>;
  noSqlTable: number;
  isResource: number;
  isPersistableObject: number;
};
type MSchema = { name: string; types: MType[] };
import _ = require("lodash");
import * as fs from "fs";
import { Entity, Field } from "./Entity.mjs";

class Hierarchy {
  public static merge(hierarchies: Array<Hierarchy>) {
    let first = hierarchies[0];
    let union: MType[] = first.types;
    for (let iH = 1; iH < hierarchies.length; ++iH) {
      union = _.unionBy(union, hierarchies[iH].types, "name");
    }
    return new Hierarchy(
      [first.tableType()].concat(
        union.filter((t) => t.name !== first.tableType().name)
      )
    );
  }

  constructor(public types: MType[] = []) {}
  public tableType() {
    for (let type of this.types) {
      if (!type.flatten) return type;
    }
  }
}

let schema23 = require("./entities/Schema23.json") as MSchema;
let schema30 = require("./entities/Schema.json") as MSchema;

function extractTypes(schema: any) {
  return Object.keys(schema.types).map((tname) => {
    let t = schema.types[tname] as MType;
    t.name = tname;
    return t;
  });
}

let types23 = extractTypes(schema23);
let types30 = extractTypes(schema30);

let hierarchies23 = new Array<Hierarchy>();
let hierarchies30 = new Array<Hierarchy>();
let entities23 = new Array<Entity>();
let entities30 = new Array<Entity>();

// this function parses the individual hierarchies in the type system, it does not yet take into account flattening
function parseHierarchy(
  types: Array<MType>,
  hierarchies: Hierarchy[],
  type: MType,
  hierarchy?: Hierarchy
) {
  if (!hierarchy) {
    if (
      (type.noSqlTable && !type.flatten) ||
      !type.fields ||
      type.fields.length === 0
    )
      return;
    if (!(type.isResource || type.isPersistableObject)) return;
    hierarchy = new Hierarchy();
    hierarchies.push(hierarchy);
  }
  hierarchy.types.push(type);
  if (type.base) {
    let parent = types.find((t) => t.name === type.base);
    parseHierarchy(types, hierarchies, parent, hierarchy);
  }
}

for (let t of types23) {
  parseHierarchy(types23, hierarchies23, t);
}

for (let t of types30) {
  parseHierarchy(types30, hierarchies30, t);
}

let groups23 = _.groupBy(hierarchies23, (h) => h.tableType().name);
let groups30 = _.groupBy(hierarchies30, (h) => h.tableType().name);
hierarchies23 = Object.keys(groups23).map((k) => Hierarchy.merge(groups23[k]));
hierarchies30 = Object.keys(groups30).map((k) => Hierarchy.merge(groups30[k]));

function makeEntities(h: Hierarchy, entities: Entity[]) {
  let e = new Entity(h.types[0].name);
  e.fields = _.flatten(
    h.types.map((t) => t.fields.map((f) => new Field(f.name, f.name)))
  );
  entities.push(e);
}

for (let h of hierarchies23) {
  makeEntities(h, entities23);
}

for (let h of hierarchies30) {
  makeEntities(h, entities30);
}

let filename = "../../database/vnext/upgrade_myliodb_from_2_3_to_3_0.sql";
let block = /\/\*\s*b::(\w+)\s*\*\/([^]*?)\/\*\s*end\s*\*\//gm;
let blocks = {} as any;
if (fs.existsSync(filename)) {
  let source = fs.readFileSync(filename, "utf8");
  let match = block.exec(source);
  while (match) {
    if (blocks[match[1]]) throw Error("Block is defined twice");
    blocks[match[1]] = match[2];
    match = block.exec(source);
  }
}

function iblock(blockName: string, expression: string) {
  let customCode = blocks[blockName];
  if (customCode) return `/* b::${blockName} */ ${customCode} /* end */`;
  else return expression;
}

function scriptInsert(target: Entity, source: Entity) {
  let union = _.intersectionBy(target.fields, source.fields, "name");
  return `
INSERT INTO ${target.name}(
    ${union
      .map((f) => {
        return iblock(`${target.name}_${f.name}_insert`, f.name);
      })
      .join(",\n\t")})
SELECT
    ${union
      .map((f) => {
        return iblock(`${target.name}_${f.name}`, f.name);
      })
      .join(",\n\t")}
FROM old${source.name};

-- drop table old${source.name};
`;
}

function scriptAlter(target: Entity, source: Entity) {
  let union = _.intersectionBy(target.fields, source.fields, "name");
  return `
ALTER TABLE ${source.name} RENAME TO old${source.name};
`;
}

function scriptDrop(target: Entity, source: Entity) {
  let union = _.intersectionBy(target.fields, source.fields, "name");
  return `
drop table old${source.name};
`;
}

let script = "";
for (let e30 of entities30) {
  let e23 = entities23.find((e) => e.name === e30.name);
  if (e23) {
    script += scriptAlter(e30, e23);
  }
}

for (let e30 of entities30) {
  let e23 = entities23.find((e) => e.name === e30.name);
  if (e23) {
    script += scriptInsert(e30, e23);
  }
}

for (let e30 of entities30) {
  let e23 = entities23.find((e) => e.name === e30.name);
  if (e23) {
    script += scriptDrop(e30, e23);
  }
}

fs.writeFileSync(filename, script, { encoding: "utf8" });
