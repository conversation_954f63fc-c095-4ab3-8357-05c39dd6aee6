

import Validation from "../system/Validation.mjs";
import { IModel } from "./IModel.mjs";







export interface IInvitationLogEntry {
    flags?: number;
	modifiedTime?: Date;
	createdTime?: Date;
	accountId?: number;
	email?: string;
	pin?: string;
	acceptedDate?: Date;
}


export class InvitationLogEntry 
implements IModel {
    private _state: IInvitationLogEntry;

    


    
    changed = false;

    constructor(state: IInvitationLogEntry) {
        this.state(state);
    }

    isUndefined() {
        return this._state !== void 0;
    }

    async validate(skipUndefined?: boolean) {
        let v = new Validation(this, skipUndefined);
        


        return v;
    }

    rtt() {
        return "InvitationLogEntry"; 
    }

    state (value?: IInvitationLogEntry) {
        if (value !== undefined) { 
            this._state = value;
            
            


        }
        return this._state;
    }

    flags(value?: number) {
                if (value !== void 0) {
                    if (this.state().flags !== value) {
                        this.state().flags = value;
                        this.changed = true;
                    }
                }
                return this.state().flags;
            };

		modifiedTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().modifiedTime !== value) {
                        this.state().modifiedTime = value;
                        this.changed = true;
                    }
                }
                return this.state().modifiedTime;
            };

		createdTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().createdTime !== value) {
                        this.state().createdTime = value;
                        this.changed = true;
                    }
                }
                return this.state().createdTime;
            };

		accountId(value?: number) {
                if (value !== void 0) {
                    if (this.state().accountId !== value) {
                        this.state().accountId = value;
                        this.changed = true;
                    }
                }
                return this.state().accountId;
            };

		email(value?: string) {
                if (value !== void 0) {
                    if (this.state().email !== value) {
                        this.state().email = value;
                        this.changed = true;
                    }
                }
                return this.state().email;
            };

		pin(value?: string) {
                if (value !== void 0) {
                    if (this.state().pin !== value) {
                        this.state().pin = value;
                        this.changed = true;
                    }
                }
                return this.state().pin;
            };

		acceptedDate(value?: Date) {
                if (value !== void 0) {
                    if (this.state().acceptedDate !== value) {
                        this.state().acceptedDate = value;
                        this.changed = true;
                    }
                }
                return this.state().acceptedDate;
            };

    differs(original: InvitationLogEntry) {
        return (
            (this.flags() !== void 0 && this.flags() !== original.flags())
		 || (this.modifiedTime() !== void 0 && this.modifiedTime() !== original.modifiedTime())
		 || (this.createdTime() !== void 0 && this.createdTime() !== original.createdTime())
		 || (this.accountId() !== void 0 && this.accountId() !== original.accountId())
		 || (this.email() !== void 0 && this.email() !== original.email())
		 || (this.pin() !== void 0 && this.pin() !== original.pin())
		 || (this.acceptedDate() !== void 0 && this.acceptedDate() !== original.acceptedDate())
        );
    }







}



export function sanitizeInput(source: InvitationLogEntry, amdin: boolean, mode: string) : IInvitationLogEntry;
export function sanitizeInput(source: IInvitationLogEntry, admin: boolean, mode: string) : IInvitationLogEntry;
export function sanitizeInput(source: InvitationLogEntry | IInvitationLogEntry, admin = false, mode="default"): IInvitationLogEntry {
    let s: IInvitationLogEntry;
    if (source instanceof InvitationLogEntry)
        s = source.state();
    else
        s = source;        
    let t = {} as IInvitationLogEntry;
    
    
        
            switch(mode) {
                
                
                
            }
            t.flags = s.flags;
		t.accountId = s.accountId;
		t.email = s.email;
		t.pin = s.pin;
		t.acceptedDate = s.acceptedDate;
        
    return t;
}

export function sanitizeOutput(source: InvitationLogEntry, amdin: boolean) : IInvitationLogEntry;
export function sanitizeOutput(source: IInvitationLogEntry, admin: boolean) : IInvitationLogEntry;
export function sanitizeOutput(source: InvitationLogEntry | IInvitationLogEntry, admin = false): IInvitationLogEntry {
    let s: IInvitationLogEntry;
    if (source instanceof InvitationLogEntry)
        s = source.state();
    else
        s = source;        
    let t = {} as IInvitationLogEntry;
    
    
    
    t.flags = s.flags;	
t.modifiedTime = s.modifiedTime;	
t.createdTime = s.createdTime;	
t.accountId = s.accountId;	
t.email = s.email;	
t.pin = s.pin;	
t.acceptedDate = s.acceptedDate;
    return t;
}

export function mergeState(dbVersion: IInvitationLogEntry, newVersion: IInvitationLogEntry) {
    let targetState: IInvitationLogEntry = {};
    targetState.flags = newVersion.flags === undefined ? dbVersion.flags : newVersion.flags;
	targetState.modifiedTime = newVersion.modifiedTime === undefined ? dbVersion.modifiedTime : newVersion.modifiedTime;
	targetState.createdTime = newVersion.createdTime === undefined ? dbVersion.createdTime : newVersion.createdTime;
	targetState.accountId = newVersion.accountId === undefined ? dbVersion.accountId : newVersion.accountId;
	targetState.email = newVersion.email === undefined ? dbVersion.email : newVersion.email;
	targetState.pin = newVersion.pin === undefined ? dbVersion.pin : newVersion.pin;
	targetState.acceptedDate = newVersion.acceptedDate === undefined ? dbVersion.acceptedDate : newVersion.acceptedDate;
    return targetState;
}

export function merge(dbVersion: InvitationLogEntry, newVersion: InvitationLogEntry) {
    return new InvitationLogEntry(mergeState(dbVersion.state(), newVersion.state()));
}
