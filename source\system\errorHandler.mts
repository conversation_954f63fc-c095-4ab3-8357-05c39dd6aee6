import express = require("express");
import _ = require("lodash");
import { Context } from "../system/Context.mjs";
import { humanReadable, ids } from "./Strings.mjs";
import { config, getServices } from "../system/Config.mjs";

const BAD_CLIENTS = {};

function badClientExists(key): boolean {
  return !!BAD_CLIENTS[key];
}

function sizeOfBadClients(): number {
  return _.keys(BAD_CLIENTS).length;
}

function addBadClient(key, value) {
  if (sizeOfBadClients() >= 100) {
    return;
  }
  if (!badClientExists(key)) {
    BAD_CLIENTS[key] = value;

    setTimeout(() => {
      delete BAD_CLIENTS[key];
    }, config.bad_client_timeout);
  }
}

export function dumpLog(
  req: express.Request,
  res: express.Response,
  next: any
) {
  let context = req.context;
  context.dumpLog();
  next();
}

export function errorHandler(
  err,
  req: express.Request,
  res: express.Response,
  next
) {
  let context: Context = req.context || new Context();
  let { code, message, errors, httpStatus } = err;
  let { requestId } = context;

  httpStatus = httpStatus || 500;
  code = code || message;

  if (err.name === "UnauthorizedError") {
    httpStatus = 401;
  }

  switch (err.type) {
    case "invalid_request_error":
      httpStatus = 400;
      break;
    default:
      httpStatus = httpStatus || 500;
  }

  message = humanReadable(code, message);

  if (
    code === ids.UNKNOWN_ERROR ||
    code === ids.SERVER_ERROR ||
    httpStatus >= 500
  ) {
    if (code !== ids.MAINTENANCE_MODE) {
      message = `A server error occurred. Contact <EMAIL> with the code ${requestId}.`;
    }

    errors = undefined;
  }

  let ip = req.headers["x-forwarded-for"] || req.connection.remoteAddress;
  let data: any = { method: req.method, path: req.path, ip };
  let userAgent = req.header("User-Agent");
  let badClientKey = `${userAgent}.${req.path}`;

  if (userAgent) {
    data.userAgent = userAgent;
  }
  if (!badClientExists(badClientKey)) {
    context.error(err, "ERROR_HANDLER", data, httpStatus >= 500);
    context.dumpLog();
  }

  addBadClient(badClientKey, code);

  if ((req as any).shouldRedirect) {
    const { account, token } = context;

    code = encodeURIComponent(code);

    let url = `${config.website}/#/error?code=${code}`;
    let email = (account && account.sub()) || (token && token.sub());

    if (email) {
      url += `&email=${encodeURIComponent(email)}`;
    }

    return res.redirect(307, url);
  }

  return res.status(httpStatus).json({ code, message, errors, requestId });
}
