import { Context } from "../system/Context.mjs";

export abstract class IdentityProvider {
  constructor(public driveName: string, public deviceType: number) {}

  public abstract getAccessToken(context: Context, authorizationCode: string);

  public abstract getRefreshToken(context: Context, refreshTokenString: string);

  public abstract storeRefreshToken(
    context: Context,
    aid: number,
    refreshTokenString: string
  );
}
