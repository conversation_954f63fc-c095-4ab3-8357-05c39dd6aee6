#include "MYFiles.h"
#include "MYLiterals.h"

#ifdef MYLIO_CLIENT
#include "MYDateTime.h"
#endif

MYFiles g_emptyMYFiles;

MYFile g_emptyFiles[] =
    {
        MYFile(&g_emptyMYFiles, MYMediaFileType::NoType),
        MYFile(&g_emptyMYFiles, MYMediaFileType::RAW),
        MYFile(&g_emptyMYFiles, MYMediaFileType::NonRAW),
        MYFile(&g_emptyMYFiles, MYMediaFileType::Video),
        MYFile(&g_emptyMYFiles, MYMediaFileType::DisplayImage),
        MYFile(&g_emptyMYFiles, MYMediaFileType::XMP),
        MYFile(&g_emptyMYFiles, MYMediaFileType::Preview),
        MYFile(&g_emptyMYFiles, MYMediaFileType::Thumbnail)};

////////////////////////////////////////////////////////////////////
//
// MYFILE
//
////////////////////////////////////////////////////////////////////
MYFile::MYFile(MYFile &&other) : _extensionData(std::move(other._extensionData))
{
    _myFiles = other._myFiles;

    _fileDateTime = other._fileDateTime;
    _fileSize = other._fileSize;

    _orientation = other._orientation;
    _width = other._width;

    _height = other._height;

    _format = other._format;
    _originalDataHash = other._originalDataHash;
    _mediaType = other._mediaType;

    _isGenerated = other._isGenerated;
    _modifiedFields = other._modifiedFields;
}

MYFile::MYFile(const MYFile &other)
{
    _myFiles = other._myFiles;

    _fileDateTime = other._fileDateTime;
    _fileSize = other._fileSize;

    _orientation = other._orientation;
    _width = other._width;

    _height = other._height;

    _format = other._format;
    _originalDataHash = other._originalDataHash;
    _mediaType = other._mediaType;

    _isGenerated = other._isGenerated;
    _modifiedFields = other._modifiedFields;

    if (other._extensionData)
    {
        _extensionData = std::make_unique<MYBJsonSmallRW>(other._extensionData->pbegin(), other._extensionData->pend());
    }
}

MYFile &MYFile::operator=(MYFile &&other)
{
    _myFiles = other._myFiles;

    _fileDateTime = other._fileDateTime;
    _fileSize = other._fileSize;

    _orientation = other._orientation;
    _width = other._width;

    _height = other._height;

    _format = other._format;
    _originalDataHash = other._originalDataHash;
    _mediaType = other._mediaType;

    _isGenerated = other._isGenerated;
    _modifiedFields = other._modifiedFields;

    _extensionData = std::move(other._extensionData);
    return *this;
}

MYFile &MYFile::operator=(const MYFile &other)
{
    _myFiles = other._myFiles;

    _fileDateTime = other._fileDateTime;
    _fileSize = other._fileSize;

    _orientation = other._orientation;
    _width = other._width;

    _height = other._height;

    _format = other._format;
    _originalDataHash = other._originalDataHash;
    _mediaType = other._mediaType;

    _isGenerated = other._isGenerated;
    _modifiedFields = other._modifiedFields;

    if (other._extensionData)
    {
        _extensionData = std::make_unique<MYBJsonSmallRW>(other._extensionData->pbegin(), other._extensionData->pend());
    }
    return *this;
}

const std::string &MYFile::getFormat() const
{
    return _myFiles->getString(_format, _mediaType);
}

bool MYFile::setFormat(const std::string &newFormat)
{
    auto ref = _myFiles->getOrCreateStringRef(newFormat, _mediaType);
    if (ref == _format)
    {
        return false;
    }

    _modifiedFields[MYLiterals::File::format] = true;
    _format = ref;
    return true;
}

bool MYFile::setFormat(std::string &&newFormat)
{
    auto ref = _myFiles->getOrCreateStringRef(std::move(newFormat), _mediaType);
    if (ref == _format)
    {
        return false;
    }

    _modifiedFields[MYLiterals::File::format] = true;
    _format = ref;
    return true;
}

bool MYFile::setFileDateTime(uint64_t newFileDateTime)
{
    if (newFileDateTime == _fileDateTime)
    {
        return false;
    }

    _modifiedFields[MYLiterals::File::fileDateTime] = true;
    _fileDateTime = newFileDateTime;
    return true;
}

bool MYFile::setFileSize(uint64_t newFileSize)
{
    if (newFileSize == _fileSize)
    {
        return false;
    }

    _modifiedFields[MYLiterals::File::fileSize] = true;
    _fileSize = newFileSize;
    return true;
}

const MYHash &MYFile::getOriginalDataHash() const
{
    return _myFiles->getHash(_originalDataHash);
}

bool MYFile::setOriginalDataHash(const MYHash &newOriginalDataHash)
{
    assert(!newOriginalDataHash.empty()); // delete the MYFile instead

    auto ref = _myFiles->getOrCreateHashRef(newOriginalDataHash);
    if (ref == _originalDataHash)
    {
        return false;
    }

    _modifiedFields[MYLiterals::File::originalDataHash] = true;
    _originalDataHash = ref;
    return true;
}

bool MYFile::setOrientation(unsigned int newOrientation)
{
    if (newOrientation == _orientation)
    {
        return false;
    }

    _modifiedFields[MYLiterals::File::orientation] = true;
    _orientation = newOrientation;
    return true;
}

bool MYFile::setWidth(unsigned int newWidth)
{
    if (newWidth == _width)
    {
        return false;
    }

    _modifiedFields[MYLiterals::File::width] = true;
    _width = newWidth;
    return true;
}

bool MYFile::setHeight(unsigned int newHeight)
{
    if (newHeight == _height)
    {
        return false;
    }

    _modifiedFields[MYLiterals::File::height] = true;
    _height = newHeight;
    return true;
}

bool MYFile::setIsGenerated(bool newIsGenerated)
{
    if (newIsGenerated == _isGenerated)
    {
        return false;
    }

    _modifiedFields[MYLiterals::File::isGenerated] = true;
    _isGenerated = newIsGenerated;
    return true;
}

////////////////////////////////////////////////////////////////////
//
// MYFILEs
//
//////////////////////////////////////////////////////////////////
const MYHash &MYFiles::getVisualEditHash() const
{
    return getHash(_visualEditHash);
}

bool MYFiles::setVisualEditHash(const MYHash &newVisualEditHash)
{
    auto ref = getOrCreateHashRef(newVisualEditHash);
    if (ref == _visualEditHash)
    {
        return false;
    }

    _modifiedFields[MYLiterals::Files::visualEditHash] = true;
    _visualEditHash = ref;
    return true;
}

bool MYFiles::setOrientation(unsigned int newOrientation)
{
    if (newOrientation == _orientation)
    {
        return false;
    }

    _modifiedFields[MYLiterals::Files::orientation] = true;
    _orientation = newOrientation;
    return true;
}

bool MYFiles::removeMediaFileForType(MYMediaFileType::Enum mediaFileType, bool fetchLegacyPreviewThumb)
{
    assert(fetchLegacyPreviewThumb || ((mediaFileType != MYMediaFileType::Preview) && (mediaFileType != MYMediaFileType::Thumbnail)));
    for (auto iter = _files.begin(); iter != _files.end(); ++iter)
    {
        if (iter->_mediaType == mediaFileType)
        {
            _modifiedFields[MYLiterals::Files::files] = true;
            _files.erase(iter);
            return true;
        }
    }

    return false;
}

const MYFile &MYFiles::getMediaFileOrEmpty(MYMediaFileType::Enum mediaFileType) const
{
    assert((mediaFileType != MYMediaFileType::Preview) && (mediaFileType != MYMediaFileType::Thumbnail));
    for (size_t i = 0; i < _files.size(); i++)
    {
        if (_files[i]._mediaType == mediaFileType)
        {
            return _files[i];
        }
    }

    assert(mediaFileType <= MYMediaFileType::Thumbnail);
    return g_emptyFiles[mediaFileType];
}

MYFile *MYFiles::getMediaFileOrNull(MYMediaFileType::Enum mediaFileType, bool fetchLegacyPreviewThumb)
{
    assert(fetchLegacyPreviewThumb || ((mediaFileType != MYMediaFileType::Preview) && (mediaFileType != MYMediaFileType::Thumbnail)));
    for (size_t i = 0; i < _files.size(); i++)
    {
        if (_files[i]._mediaType == mediaFileType)
        {
            return &_files[i];
        }
    }

    return nullptr;
}

const MYFile *MYFiles::getMediaFileOrNull(MYMediaFileType::Enum mediaFileType) const
{
    assert((mediaFileType != MYMediaFileType::Preview) && (mediaFileType != MYMediaFileType::Thumbnail));
    for (size_t i = 0; i < _files.size(); i++)
    {
        if (_files[i]._mediaType == mediaFileType)
        {
            return &_files[i];
        }
    }

    return nullptr;
}

MYFile *MYFiles::getOrCreateMediaFile(MYMediaFileType::Enum mediaFileType)
{
    assert((mediaFileType != MYMediaFileType::Preview) && (mediaFileType != MYMediaFileType::Thumbnail));
    for (size_t i = 0; i < _files.size(); i++)
    {
        if (_files[i]._mediaType == mediaFileType)
        {
            return &_files[i];
        }
    }

    _modifiedFields[MYLiterals::Files::files] = true;
    _files.emplace_back(this, mediaFileType);
    return &_files[_files.size() - 1];
}

const NeedsBits MYFiles::getSupportMediaTypes() const
{
    NeedsBits supported(0);

    for (size_t i = 0; i < _files.size(); i++)
    {
        supported |= NeedsBits(_files[i]._mediaType);
    }
    return supported;
}

MYHashRef MYFiles::getOrCreateHashRef(const MYHash &hash)
{
    if (hash.empty())
    {
        return MYHashRefEmpty;
    }

    auto iter = std::find(_hashMap.begin(), _hashMap.end(), hash);
    if (iter == _hashMap.end())
    {
        _modifiedFields[MYLiterals::Files::hashMap] = true;
        _hashMap.emplace_back(hash);
        return (MYHashRef)_hashMap.size() - 1;
    }

    return (MYHashRef)(iter - _hashMap.begin());
}

MYStringRef MYFiles::getOrCreateStringRef(const std::string &string, MYMediaFileType::Enum fileType)
{
    auto internedId = MYFileFormatInterner::getIdFromFormat(string, fileType);
    if (internedId)
    {
        return internedId;
    }

    auto iter = std::find(_stringMap.begin(), _stringMap.end(), string);
    if (iter == _stringMap.end())
    {
        _modifiedFields[MYLiterals::Files::stringMap] = true;
        _stringMap.emplace_back(string);
        return (MYStringRef)_stringMap.size() - 1;
    }

    return (MYStringRef)(iter - _stringMap.begin());
}

MYStringRef MYFiles::getOrCreateStringRef(std::string &&string, MYMediaFileType::Enum fileType)
{
    auto internedId = MYFileFormatInterner::getIdFromFormat(string, fileType);
    if (internedId)
    {
        return internedId;
    }

    auto iter = std::find(_stringMap.begin(), _stringMap.end(), string);
    if (iter == _stringMap.end())
    {
        _modifiedFields[MYLiterals::Files::stringMap] = true;
        _stringMap.emplace_back(std::move(string));
        return (MYStringRef)_stringMap.size() - 1;
    }

    return (MYStringRef)(iter - _stringMap.begin());
}

const MYHash &MYFiles::getHash(MYHashRef hashref) const
{
    if (hashref == MYHashRefEmpty)
    {
        return MYHash::emptyHash();
    }

    if (hashref < (MYHashRef)_hashMap.size())
    {
        return _hashMap[hashref];
    }
    else
    {
        assert(false);
        return MYHash::emptyHash();
    }
}

const std::string &MYFiles::getString(MYStringRef stringRef, MYMediaFileType::Enum fileType) const
{
    if (MYFileFormatInterner::isInternedId(stringRef))
    {
        return MYFileFormatInterner::getFormatFromId(stringRef, fileType);
    }

    if (_stringMap.empty())
    {
        return g_emptyString;
    }

    if (stringRef < (MYStringRef)_stringMap.size())
    {
        return _stringMap[stringRef];
    }
    else
    {
        assert(false);
        return g_emptyString;
    }
}

MYFile::MYFile(MYFiles *myFiles, MYMediaFileType::Enum fileType) : _myFiles(myFiles), _mediaType(fileType)
{
}

MYFile::MYFile(MYFiles *myFiles, MYBJsonIterator &iter, const MYBJsonIterator &end) : _myFiles(myFiles)
{
    assert(iter->type() == BJsonType::object);

    for (++iter; iter != end && iter->type() != BJsonType::end; ++iter)
    {
        auto key = iter->key();
        switch (key)
        {
        case MYLiterals::File::fileType:
            _mediaType = (MYMediaFileType::Enum)(iter->asUint32());
            break;

        case MYLiterals::File::format:
            _format = iter->asUint32();
            break;

        case MYLiterals::File::originalDataHash:
            _originalDataHash = iter->asUint32();
            break;

        case MYLiterals::File::fileSize:
            _fileSize = iter->asUint64();
            break;

        case MYLiterals::File::fileDateTime:
            _fileDateTime = iter->asUint64();
            break;

        case MYLiterals::File::orientation:
            _orientation = iter->asUint32();
            break;

        case MYLiterals::File::width:
            _width = iter->asUint32();
            break;

        case MYLiterals::File::height:
            _height = iter->asUint32();
            break;

        case MYLiterals::File::isGenerated:
            _isGenerated = iter->asBool();
            break;

        default:
            if (!_extensionData)
            {
                _extensionData = std::make_unique<MYBJsonSmallRW>();
            }
            _extensionData->addMember(iter);
            break;
        }
    }

    assert(iter->type() == BJsonType::end);
}

MYFile::MYFile(MYBJsonIterator &iter, const MYBJsonIterator &end, const myfile_construct_for_aggregation &) : _myFiles(nullptr)
{
    assert(iter->type() == BJsonType::object);

    for (++iter; iter != end && iter->type() != BJsonType::end; ++iter)
    {
        auto key = iter->key();
        switch (key)
        {
        case MYLiterals::File::fileType:
            _mediaType = (MYMediaFileType::Enum)(iter->asUint32());
            break;

        case MYLiterals::File::format:
            _format = iter->asUint32();
            break;

        case MYLiterals::File::originalDataHash:
            break;

        case MYLiterals::File::fileSize:
            _fileSize = iter->asUint64();
            break;

        case MYLiterals::File::fileDateTime:
            _fileDateTime = iter->asUint64();
            break;

        case MYLiterals::File::orientation:
            break;

        case MYLiterals::File::width:
            _width = iter->asUint32();
            break;

        case MYLiterals::File::height:
            _height = iter->asUint32();
            break;

        case MYLiterals::File::isGenerated:
            break;

        default:
            if (iter->isScopeBegin())
            {
                iter = iter.extent();
                assert(iter->isScopeEnd());
            }
            break;
        }
    }

    assert(iter->type() == BJsonType::end);
}

MYHashRef MYFile::getOriginalDataHashRefFromBJson(MYBJsonIterator &iter, const MYBJsonIterator &end, NeedsBits fileTypes)
{
    MYMediaFileType::Enum myMediaFileType = MYMediaFileType::Enum::NoType;
    MYHashRef originalDataHashRef = MYHashRefEmpty;

    assert(iter->type() == BJsonType::object);

    for (++iter; iter != end && iter->type() != BJsonType::end; ++iter)
    {
        auto key = iter->key();
        switch (key)
        {
        case MYLiterals::File::fileType:
            myMediaFileType = (MYMediaFileType::Enum)(iter->asUint32());
            break;

        case MYLiterals::File::originalDataHash:
            originalDataHashRef = iter->asUint32();
            assert(myMediaFileType != MYMediaFileType::Enum::NoType);
            {
                // Advance to the end quickly
                for (++iter; iter != end && iter->type() != BJsonType::end; ++iter)
                {
                    if (iter->isScopeBegin())
                    {
                        iter = iter.extent();
                        assert(iter->isScopeEnd());
                    }
                }

                assert(iter->type() == BJsonType::end);
                if (fileTypes.test(myMediaFileType))
                {
                    return originalDataHashRef;
                }

                return MYHashRefEmpty;
            }
            break;

        default:
            if (iter->isScopeBegin())
            {
                iter = iter.extent();
                assert(iter->isScopeEnd());
            }
            break;
        }
    }

    return MYHashRefEmpty;
}

MYFile &MYFile::operator+=(const MYFile &other)
{
    _fileSize += other._fileSize;
    if (_width != other._width)
    {
        _width = 0;
    }

    if (_height != other._height)
    {
        _height = 0;
    }

    if (_orientation != other._orientation)
    {
        _orientation = 0;
    }

    if (_fileDateTime != other._fileDateTime)
    {
        _fileDateTime = 0;
    }

    return *this;
}

void MYFile::serializeToBJson(MYBJsonRW &writer) const
{
    writer.StartObject();
    if (_mediaType != MYMediaFileType::Enum::NoType)
    {
        writer.Uint32(MYLiterals::File::fileType, _mediaType);
    }

    if (_format != MYStringRefEmpty)
    {
        writer.Uint32(MYLiterals::File::format, _format);
    }

    if (_originalDataHash != MYHashRefEmpty)
    {
        writer.Uint32(MYLiterals::File::originalDataHash, _originalDataHash);
    }

    if (_fileSize != 0)
    {
        writer.Uint64(MYLiterals::File::fileSize, _fileSize);
    }

    if (_fileDateTime != 0)
    {
        writer.Uint64(MYLiterals::File::fileDateTime, _fileDateTime);
    }

    if (_orientation != 0)
    {
        writer.Uint32(MYLiterals::File::orientation, _orientation);
    }

    if (_width != 0)
    {
        writer.Uint32(MYLiterals::File::width, _width);
    }

    if (_height != 0)
    {
        writer.Uint32(MYLiterals::File::height, _height);
    }

    if (_isGenerated != 0)
    {
        writer.Bool(MYLiterals::File::isGenerated, _isGenerated);
    }

    if (_extensionData)
    {
        writer.Raw(_extensionData->pbegin(), _extensionData->pend());
    }

    writer.EndObject();
}

MYFiles::MYFiles(MYBJsonIterator &iter, const MYBJsonIterator &end)
{
#if !defined(_DEBUG) && !defined(DEBUG)
    // We don't reserve in debug, because we want to take re-allocations. Otherwise it will hide bugs if
    // the pointer values always remain the same.
    _files.reserve(5);
    _hashMap.reserve(4);
    _stringMap.reserve(4);
#endif

    deserializeFromBJson(iter, end);
}

void MYFiles::clear()
{
    _files.clear();
    _hashMap.clear();
    _stringMap.clear();
    _orientation = 0;
    _visualEditHash = MYHashRefEmpty;
    _extensionData.reset();
}

void MYFiles::compact()
{
    std::vector<bool> inUseBuckets;
    std::vector<bool> inUseStrings;
    inUseBuckets.resize(_hashMap.size());
    inUseStrings.resize(_stringMap.size());

    if (_visualEditHash != MYHashRefEmpty)
    {
        inUseBuckets[_visualEditHash] = true;
    }

    for (const auto &file : *this)
    {
        if (file._originalDataHash != MYHashRefEmpty)
        {
            inUseBuckets[file._originalDataHash] = true;
        }

        if (!MYFileFormatInterner::isInternedId(file._format))
        {
            inUseStrings[file._format] = true;
        }
    }

    assert(inUseBuckets.size() <= _hashMap.size());
    assert(inUseStrings.size() <= _stringMap.size());

    bool needHashCompaction = false;
    for (auto inUse : inUseBuckets)
    {
        if (!inUse)
        {
            needHashCompaction = true;
            break;
        }
    }

    bool needStringCompaction = false;
    for (auto inUse : inUseStrings)
    {
        if (!inUse)
        {
            needStringCompaction = true;
            break;
        }
    }

    if (needHashCompaction)
    {
        std::vector<MYHash> oldHashMap = std::move(_hashMap);
        _hashMap.clear();
        _hashMap.reserve(oldHashMap.size());

        if (_visualEditHash != MYHashRefEmpty)
        {
            _visualEditHash = getOrCreateHashRef(oldHashMap[_visualEditHash]);
        }

        for (auto &file : *this)
        {
            if (file._originalDataHash != MYHashRefEmpty)
            {
                file._originalDataHash = getOrCreateHashRef(oldHashMap[file._originalDataHash]);
            }
        }
    }

    if (needStringCompaction)
    {
        std::vector<std::string> oldStringMap = std::move(_stringMap);
        _stringMap.clear();
        oldStringMap.reserve(_stringMap.size());
        for (auto &file : *this)
        {
            if (!MYFileFormatInterner::isInternedId(file._format))
            {
                file._format = getOrCreateStringRef(oldStringMap[file._format], file._mediaType);
            }
        }
    }
}

void MYFiles::deserializeFromBJson(MYBJsonIterator &iter, const MYBJsonIterator &end)
{
    for (; iter != end && iter->type() != BJsonType::end; ++iter)
    {
        auto key = iter->key();
        switch (key)
        {
        case MYLiterals::Files::files:
        {
            assert(iter->type() == BJsonType::array);
            for (++iter; iter != end && iter->type() != BJsonType::end; ++iter)
            {
                _files.emplace_back(this, iter, end);
            }
            assert(iter->type() == BJsonType::end);
        }
        break;

        case MYLiterals::Files::hashMap:
        {
            assert(iter->type() == BJsonType::array);
            for (++iter; iter != end && iter->type() != BJsonType::end; ++iter)
            {
                _hashMap.emplace_back(iter->asHash());
            }
            assert(iter->type() == BJsonType::end);
        }
        break;

        case MYLiterals::Files::stringMap:
        {
            assert(iter->type() == BJsonType::array);
            for (++iter; iter != end && iter->type() != BJsonType::end; ++iter)
            {
                _stringMap.emplace_back(iter->asString());
            }
            assert(iter->type() == BJsonType::end);
        }
        break;

        case MYLiterals::Files::visualEditHash:
            _visualEditHash = iter->asUint32();
            break;

        case MYLiterals::Files::orientation:
            _orientation = iter->asUint32();
            break;

        default:
            if (!_extensionData)
            {
                _extensionData = std::make_unique<MYBJsonSmallRW>();
            }
            _extensionData->addMember(iter);
            break;
        }
    }
}

MYHash MYFiles::getOriginalDataHashFromBJson(MYBJsonIterator &iter, const MYBJsonIterator &end, NeedsBits fileTypes)
{
    MYHashRef originalDataHashRef = MYHashRefEmpty;
    for (; iter != end && iter->type() != BJsonType::end; ++iter)
    {
        auto key = iter->key();
        switch (key)
        {
        case MYLiterals::Files::files:
        {
            assert(iter->type() == BJsonType::array);
            for (++iter; iter != end && iter->type() != BJsonType::end; ++iter)
            {
                if (originalDataHashRef == MYHashRefEmpty)
                {
                    originalDataHashRef = MYFile::getOriginalDataHashRefFromBJson(iter, end, fileTypes);
                }
                else
                {
                    // Advance over the file
                    iter = iter.extent();
                    assert(iter->isScopeEnd());
                }
            }
            assert(iter->type() == BJsonType::end);
        }
        break;

        case MYLiterals::Files::hashMap:
        {
            if (originalDataHashRef == MYHashRefEmpty)
            {
                return MYHash::emptyHash();
            }

            assert(iter->type() == BJsonType::array);
            for (++iter; iter != end && iter->type() != BJsonType::end; ++iter)
            {
                if (originalDataHashRef == 0)
                {
                    return iter->asHash();
                }
                --originalDataHashRef;
            }
            assert(iter->type() == BJsonType::end);
        }
        break;

        default:
            break;
        }
    }

    return MYHash::emptyHash();
}

MYFiles &MYFiles::operator+=(const MYBJsonView &view)
{
    auto iter = view.begin();
    auto end = view.end();

    for (; iter != end && iter->type() != BJsonType::end; ++iter)
    {
        auto key = iter->key();
        switch (key)
        {
        case MYLiterals::Files::files:
        {
            assert(iter->type() == BJsonType::array);
            for (++iter; iter != end && iter->type() != BJsonType::end; ++iter)
            {
                bool found = false;

                MYFile file(iter, end, myfile_construct_for_aggregation());
                for (auto fileIter = _files.begin(); fileIter != _files.end(); ++fileIter)
                {
                    if ((fileIter->_mediaType == file._mediaType) &&
                        ((file._mediaType != MYMediaFileType::NoType || fileIter->_format == file._format)))
                    {
                        if ((*fileIter)._isGenerated)
                            *fileIter = file;
                        else if (!file._isGenerated)
                            *fileIter += file;
                        found = true;
                    }
                }

                if (!found)
                {
                    _files.emplace_back(std::move(file));
                }
            }
            assert(iter->type() == BJsonType::end);
        }
        break;

        case MYLiterals::Files::hashMap:
        {
            assert(iter->type() == BJsonType::array);
            iter = iter.extent();
            assert(iter->type() == BJsonType::end);
        }
        break;

        case MYLiterals::Files::stringMap:
        {
            // if any file extensions do not match the existing ones, then clear the map
            assert(iter->type() == BJsonType::array);
            if (!_stringMap.empty())
            {
                int mapSize = 0;
                for (++iter; iter != end && iter->type() != BJsonType::end; ++iter)
                {
                    bool match = false;
                    mapSize++;
                    for (auto mapIter = _stringMap.begin(); mapIter != _stringMap.end(); ++mapIter)
                    {
                        if (*mapIter == iter->asString())
                        {
                            match = true;
                            break;
                        }
                    }
                    if (!match)
                    {
                        _stringMap.clear();
                        break;
                    }
                }
                // size of 2 vectors must match
                if (mapSize != _stringMap.size())
                {
                    _stringMap.clear();
                }
            }
        }
        break;

        case MYLiterals::Files::visualEditHash:
            break;

        case MYLiterals::Files::orientation:
            break;

        default:
            if (iter->isScopeBegin())
            {
                iter = iter.extent();
                assert(iter->isScopeEnd());
            }
            break;
        }
    }

    return *this;
}

void MYFiles::serializeToBJson(MYBJsonRW &writer) const
{
    if (!_files.empty())
    {
        writer.StartArray(MYLiterals::Files::files);
        for (const auto &file : _files)
        {
            file.serializeToBJson(writer);
        }
        writer.EndArray();
    }

    if (!_hashMap.empty())
    {
        writer.StartArray(MYLiterals::Files::hashMap);
        for (const auto &hashEntry : _hashMap)
        {
            writer.MYHash(0, hashEntry);
        }
        writer.EndArray();
    }

    if (!_stringMap.empty())
    {
        writer.StartArray(MYLiterals::Files::stringMap);
        for (const auto &stringEntry : _stringMap)
        {
            writer.String(0, stringEntry);
        }
        writer.EndArray();
    }

    if (_visualEditHash != MYHashRefEmpty)
    {
        writer.Uint32(MYLiterals::Files::visualEditHash, _visualEditHash);
    }

    if (_orientation != 0)
    {
        writer.Uint32(MYLiterals::Files::orientation, _orientation);
    }

    if (_extensionData)
    {
        writer.Raw(_extensionData->pbegin(), _extensionData->pend());
    }
}

bool MYFile::isModified() const
{
    if (_modifiedFields.any())
    {
        return true;
    }

    return false;
}

bool MYFiles::isModified() const
{
    if (_modifiedFields.any())
    {
        return true;
    }

    for (const auto &file : _files)
    {
        if (file.isModified())
        {
            return true;
        }
    }

    return false;
}

bool MYFiles::empty() const
{
    return _files.empty() && _visualEditHash == MYHashRefEmpty && _orientation == 0;
}

MYFiles::MYFiles(MYFiles &&other) : _extensionData(std::move(other._extensionData)), _files(std::move(other._files)), _hashMap(std::move(other._hashMap)), _stringMap(std::move(other._stringMap))
{
    _visualEditHash = other._visualEditHash;
    _orientation = other._orientation;
    _modifiedFields = other._modifiedFields;

    for (auto &files : _files)
    {
        files._myFiles = this;
    }
}

MYFiles::MYFiles(const MYFiles &other) : _files(other._files), _hashMap(other._hashMap), _stringMap(other._stringMap)
{
    _visualEditHash = other._visualEditHash;
    _orientation = other._orientation;
    _modifiedFields = other._modifiedFields;

    if (other._extensionData)
    {
        _extensionData = std::make_unique<MYBJsonSmallRW>(other._extensionData->pbegin(), other._extensionData->pend());
    }

    for (auto &files : _files)
    {
        files._myFiles = this;
    }
}

MYFiles &MYFiles::operator=(MYFiles &&other)
{
    _visualEditHash = other._visualEditHash;
    _orientation = other._orientation;
    _modifiedFields = other._modifiedFields;

    _extensionData = std::move(other._extensionData);
    _files = std::move(other._files);
    _hashMap = std::move(other._hashMap);
    _stringMap = std::move(other._stringMap);

    for (auto &files : _files)
    {
        files._myFiles = this;
    }

    return *this;
}

MYFiles &MYFiles::operator=(const MYFiles &other)
{
    _visualEditHash = other._visualEditHash;
    _orientation = other._orientation;
    _modifiedFields = other._modifiedFields;

    if (other._extensionData)
    {
        _extensionData = std::make_unique<MYBJsonSmallRW>(other._extensionData->pbegin(), other._extensionData->pend());
    }

    _files = other._files;
    _hashMap = other._hashMap;
    _stringMap = other._stringMap;

    for (auto &files : _files)
    {
        files._myFiles = this;
    }

    return *this;
}

bool MYFiles::operator==(const MYFiles &other) const
{
    MYBJsonRW one;
    one.StartObject();

    MYBJsonRW two;
    two.StartObject();

    serializeToBJson(one);
    other.serializeToBJson(two);

    if (one.psize() != two.psize())
        return false;

    return memcmp(one.pbegin(), two.pbegin(), one.psize()) == 0;
}

std::string MYFile::toString() const
{
    std::string s;

    s += MYMediaFileType::getName(getMediaType());
    s += " ";

    s += getFormat();
    s += " ";

    if (getFileDateTime())
    {
#ifdef MYLIO_CLIENT
        s += MYDateTime(getFileDateTime()).toDateString() + " ";
#else
        s += std::to_string(getFileDateTime()) + " ";
#endif
    }

    if (getWidth() && getHeight())
    {
        s += std::to_string(getWidth()) + "x" + std::to_string(getHeight()) + " ";
    }
    else
    {
        if (getWidth())
        {
            s += "Width: " + std::to_string(getWidth()) + " ";
        }

        if (getHeight())
        {
            s += "Height: " + std::to_string(getHeight()) + " ";
        }
    }

    if (getFileSize())
    {
        s += "Size: " + std::to_string(getFileSize()) + " ";
    }

    if (getOrientation())
    {
        s += "Orientation: " + std::to_string(getOrientation()) + " ";
    }

    if (getIsGenerated())
    {
        s += "Generated ";
    }

    if (hasOriginalDataHash())
    {
        s += "OriginalDataHash: " + getOriginalDataHash().toString() + " ";
    }

    return MYString::rtrim(s);
}

std::string MYFiles::toString() const
{
    std::string s;
    for (const auto &file : *this)
    {
        if (!s.empty())
        {
            s += ", ";
        }
        s += "{";
        s += file.toString();

        if (hasVisualEditHash())
        {
            s += "VisualEditHash: " + getVisualEditHash().toString() + " ";
        }

        if (getOrientation())
        {
            s += "Orientation: " + std::to_string(getOrientation()) + " ";
        }

        s += "}";
    }

    return s;
}

MYBJsonIterator MYFile::getExtensionProperty(int id) const
{
    if (_extensionData.get() && (_extensionData->psize() > 0))
    {
        for (auto iter = _extensionData->begin(); iter != _extensionData->end(); ++iter)
        {
            if (iter->key() == id)
            {
                return iter;
            }
        }
    }

    extern uint16_t zero16;
    return MYBJsonIterator((uint8_t *)&zero16, (uint8_t *)&zero16 + 1);
}

bool MYFile::clearExtensionProperty(int id)
{
    if (_extensionData->empty())
    {
        return false;
    }

    bool changes = false;

    std::unique_ptr<MYBJsonSmallRW> newExtensionData = std::make_unique<MYBJsonSmallRW>();
    for (auto iter = _extensionData->begin(); iter != _extensionData->end(); ++iter)
    {
        if (iter->key() == id)
        {
            changes = true;
        }
        else
        {
            newExtensionData->addMember(iter);
        }
    }

    if (changes)
    {
        _extensionData = std::move(newExtensionData);
    }

    return changes;
}

void SupportTicketInfo::SerializeToBJson(MYBJsonRW &bjson)
{
    if (this->takeScreenShot)
    {
        bjson.addMember(MYLiterals::supportTicket_takeScreenShot, this->takeScreenShot);
    }

    if (this->includeCatalog)
    {
        bjson.addMember(MYLiterals::supportTicket_includeCatalog, this->includeCatalog);
    }

    if (!this->additionalFile.empty())
    {
        bjson.addMember(MYLiterals::supportTicket_additionalFile, this->additionalFile);
    }

    if (!this->crashFile.empty())
    {
        bjson.addMember(MYLiterals::supportTicket_crashFile, this->crashFile);
    }

    if (this->logs != SupportTicketLogs::None)
    {
        bjson.addMember(MYLiterals::supportTicket_logs, (uint32_t)this->logs);
    }

    if (!this->subject.empty())
    {
        bjson.addMember(MYLiterals::supportTicket_subject, this->subject);
    }

    if (!this->commentsToSend.empty())
    {
        bjson.addMember(MYLiterals::supportTicket_commentsToSend, this->commentsToSend);
    }

    if (!this->supportTicketRequestId.empty())
    {
        bjson.addMember(MYLiterals::supportTicket_requestId, this->supportTicketRequestId);
    }

    if (this->requestLogs)
    {
        bjson.addMember(MYLiterals::supportTicket_requestLogs, this->requestLogs);
    }

    if (this->isFromWebSite)
    {
        bjson.addMember(MYLiterals::supportTicket_isFromWebsite, isFromWebSite);
    }

    if (!this->consoleCommands.empty())
    {
        bjson.StartArray(MYLiterals::supportTicket_consoleCommands);
        for (const auto &sql : this->consoleCommands)
        {
            bjson.String(sql);
        }
        bjson.EndArray();
    }

    if (this->validUntilTime != std::chrono::system_clock::time_point())
    {
        bjson.addMember(MYLiterals::supportTicket_validUntilTime, systemTimeToMylioTime(this->validUntilTime));
    }
}

void SupportTicketInfo::DeserializeFromBJson(MYBJsonView view)
{
    auto iter = view.begin();
    auto end = view.end();

    for (; iter != end && iter->type() != BJsonType::end; ++iter)
    {
        auto key = iter->key();
        switch (key)
        {
        case MYLiterals::supportTicket_takeScreenShot:
        {
            this->takeScreenShot = iter->asBool();
            break;
        }

        case MYLiterals::supportTicket_includeCatalog:
        {
            this->includeCatalog = iter->asBool();
            break;
        }

        case MYLiterals::supportTicket_logs:
        {
            this->logs = (SupportTicketLogs)iter->asUint32();
            break;
        }

        case MYLiterals::supportTicket_additionalFile:
        {
            this->additionalFile = iter->asString();
            break;
        }

        case MYLiterals::supportTicket_crashFile:
        {
            this->crashFile = iter->asString();
            break;
        }

        case MYLiterals::supportTicket_subject:
        {
            this->subject = iter->asString();
            break;
        }

        case MYLiterals::supportTicket_commentsToSend:
        {
            this->commentsToSend = iter->asString();
            break;
        }

        case MYLiterals::supportTicket_requestId:
        {
            this->supportTicketRequestId = iter->asHash();
            break;
        }

        case MYLiterals::supportTicket_requestLogs:
        {
            this->requestLogs = iter->asBool();
            break;
        }

        case MYLiterals::supportTicket_consoleCommands:
        {
            for (++iter; iter != end && iter->type() != BJsonType::end; ++iter)
            {
                consoleCommands.push_back(iter->asString());
            }
            break;
        }

        case MYLiterals::supportTicket_isFromWebsite:
        {
            this->isFromWebSite = iter->asBool();
            break;
        }

        case MYLiterals::supportTicket_validUntilTime:
        {
            this->validUntilTime = mylioTimeToSystemTime(iter->asUint32());
            break;
        }

        default:
        {
            if (iter->isScopeBegin())
            {
                iter = iter.extent();
                assert(iter->isScopeEnd());
            }
        }
        }
    }
}
