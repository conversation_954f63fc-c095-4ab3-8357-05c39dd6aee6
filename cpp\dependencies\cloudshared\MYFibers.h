#pragma once

#include <atomic>
#include <thread>
#include <cassert>
#include <condition_variable>

// LIBRARY
class M<PERSON><PERSON><PERSON> sealed
{
public:
    MYFiber() : _thread(&MYFiber::threadProc, this)
    {
    }

    ~MYFiber()
    {
        assert(!isCurrentThread());

        quit.store(true);
        shouldRun.store(true);
        cv.notify_one();

        if (_thread.joinable())
            _thread.join();
    }

    bool isCurrentThread() const
    {
        if (_threadId == std::this_thread::get_id())
        {
            return true;
        }
        return false;
    }

private:
    std::thread::id _threadId;

    std::atomic<bool> quit = false;
    std::atomic<bool> shouldRun = {false};
    std::atomic<bool> yielded = {false};
    std::atomic<bool> shouldReturn = {false};
    std::function<void(void)> _callFunc;
    std::mutex mutex;
    std::condition_variable cv;
    std::unique_lock<std::mutex> *_currentLock;

    void threadProc()
    {
        _threadId = std::this_thread::get_id();
        while (!quit.load())
        {
            std::unique_lock<std::mutex> lock(mutex);
            cv.wait(lock, [this]
                    { return shouldRun.load(); });
            shouldRun.store(false);

            if (!quit.load())
            {
                _currentLock = &lock;
                _callFunc();
            }

            shouldReturn.store(true);

            lock.unlock();
            cv.notify_one();
        }
    }

public:
    void yield()
    {
        {
            shouldReturn.store(true);
            yielded = true;

            cv.notify_one();
        }

        cv.wait(*_currentLock, [this]
                { return shouldRun.load(); });
        shouldRun.store(false);
        yielded = false;
    }

    enum CallState
    {
        None,
        Done,
        Yield
    };

    CallState resume()
    {
        {
            std::lock_guard<std::mutex> lock(mutex);
            shouldReturn.store(false);
            shouldRun.store(true);
        }
        cv.notify_one();

        {
            std::unique_lock<std::mutex> lock(mutex);
            cv.wait(lock, [this]
                    { return shouldReturn.load(); });
        }
        return yielded ? CallState::Yield : CallState::Done;
    }

    template <typename TFunc, typename... TArgs>
    void setFunc(TFunc func, TArgs... args)
    {
        typedef decltype(func(args...)) TReturnType;
        std::function<TReturnType(TArgs...)> fn(func);
        _callFunc = [=]
        {
            fn(std::forward(args)...);
        };
    }

private:
    // Very important that this is the last member of the class
    std::thread _thread;
};

// Fiber Runner
#ifdef MYLIO_CLIENT
template <typename TEvent>
#endif
class FiberRunner
{
    MYFiber _fiber;
    void *_event;
    int _eventType;

#ifdef MYLIO_CLIENT
    std::function<void(TEvent *)> _eventFunc;
#else
    // std::function will work better - this only allows for a C-style function,
    // but it ensures there are no C++ objects on the stack during the callback
    void (*_eventFunc)(int, void *);
#endif // MYLIO_CLIENT

public:
    MYFiber &fiber()
    {
        return _fiber;
    }

    void raise(int eventType, void *event)
    {
        _eventType = eventType;
        _event = event;
        _fiber.yield();
    }

    template <typename TFunc, typename TEventFunc, typename... TArgs>
    auto run(TFunc func, TEventFunc eventFunc, TArgs &&...args) -> decltype(func(args...))
    {
        typedef decltype(func(args...)) TReturnType;
        TReturnType retVal;
        {
            _eventFunc = eventFunc;
            _fiber.setFunc([&retVal, func, args...]
                           { retVal = func(args...); });
        }

        while (_fiber.resume() != MYFiber::CallState::Done)
        {
            _eventFunc(_eventType, _event);
        }

        return retVal;
    }
};
