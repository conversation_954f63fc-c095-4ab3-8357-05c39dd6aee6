import { config, getServices } from "../system/Config.mjs";
import { error } from "../system/error.mjs";
import { RefreshTokenService } from "./RefreshTokenService.mjs";
import { Context } from "../system/Context.mjs";
import { RefreshToken } from "../models/RefreshToken.model.mjs";
import {
  Device,
  DeviceType,
  sanitizeOutput as sanitizeDevice,
} from "../models/Device.model.mjs";
import { DeviceService } from "./DeviceService.mjs";
import { tx } from "../system/Postgres.mjs";
import { LockService } from "./LockService.mjs";
import { ids } from "../system/Strings.mjs";
import { get, post, postForm } from "../system/fetch.mjs";

export class AmazonOAuthService {
  public async getRefreshToken(code: string) {
    let form = {
      client_id: config.amazon.clientID,
      client_secret: config.amazon.clientSecret,
      code: code,
      grant_type: "authorization_code",
      redirect_uri: `${config.cloud}/amazon/authorize`,
    };

    let tokenResponse = await postForm("https://api.amazon.com/auth/o2/token", {
      client_id: config.amazon.clientID,
      client_secret: config.amazon.clientSecret,
      code: code,
      grant_type: "authorization_code",
      redirect_uri: `${config.cloud}/amazon/authorize`,
    });
    let tokenResult = await tokenResponse.json();
    return tokenResult.refresh_token;
  }

  public async getAccessToken(refreshToken: string) {
    let tokenResponse = await postForm("https://api.amazon.com/auth/o2/token", {
      client_id: config.amazon.clientID,
      client_secret: config.amazon.clientSecret,
      grant_type: "refresh_token",
      redirect_uri: `${config.cloud}/amazon/authorize`,
      refresh_token: refreshToken,
    });
    let tokenResult = await tokenResponse.json();
    return tokenResult.access_token;
  }

  public getDriveOAuthUrl(
    context: Context,
    aid: number,
    encrypt: boolean,
    allowLimited: boolean
  ) {
    /* tslint:disable */
    let state = Buffer.from(
      JSON.stringify({ aid, encrypt, allowLimited, task: "drive" }),
      "utf8"
    ).toString("base64");
    let scope = "clouddrive:read_all clouddrive:write profile profile:user_id";
    let url = `https://www.amazon.com/ap/oa?state=${state}&scope=${scope}&client_id=${config.amazon.clientID}&response_type=code&redirect_uri=${config.cloud}/amazon/authorize`;
    return encodeURI(url);
    /* tslint:enable */
  }

  public async getEndPoints(token: string) {
    let body = await get(
      "https://drive.amazonaws.com/drive/v1/account/endpoint",
      { Authorization: "Bearer " + token }
    );
    return body as { metadataUrl: string };
  }

  public async getAmazonDriveQuota(token: string) {
    let urls = await this.getEndPoints(token);
    let body = await get(`${urls.metadataUrl}/account/quota`, {
      Authorization: "Bearer " + token,
    });
    return body.quota as number;
  }
}

export class AmazonService {
  private _oauth = new AmazonOAuthService();
  constructor(
    private refreshTokenService: RefreshTokenService,
    private deviceService: DeviceService,
    private lockService: LockService
  ) {}

  public getDriveAccessToken(context: Context, aid: number) {
    return this.refreshTokenService
      .read(context, aid, "amazon", "drive")
      .then((refreshToken) => {
        if (!refreshToken) return error(400, "NO_REFRESH_TOKEN");
        return this._oauth.getAccessToken(refreshToken.token());
      });
  }

  public getDriveOAuthUrl(
    context: Context,
    aid: number,
    encrypt: boolean,
    allowLimited: boolean
  ) {
    return this._oauth.getDriveOAuthUrl(context, aid, encrypt, allowLimited);
  }

  public addRefreshToken(authorizationCode: string, stateString: string) {
    let rtokenString: string;
    let tokenString: string;
    let context = new Context();
    let state = JSON.parse(
      Buffer.from(stateString, "base64").toString("ascii")
    );
    let task = state.task;
    context.aid = state.aid;
    let aid = context.aid;

    return this._oauth
      .getRefreshToken(authorizationCode)
      .then((result) => {
        rtokenString = result;
        return this._oauth.getAccessToken(rtokenString);
      })
      .then((result) => {
        tokenString = result;
        return this._oauth.getAmazonDriveQuota(tokenString);
      })
      .then((quota) => {
        if (state.allowLimited || quota > ***********) {
          return this.refreshTokenService.tryRead(
            context,
            aid,
            "amazon",
            "drive"
          );
        }
        return error<RefreshToken>(400, "LIMITED_ACCOUNT");
      })
      .then((refreshToken) => {
        if (!refreshToken)
          return this.refreshTokenService.create(
            context,
            aid,
            new RefreshToken({
              idp: "amazon",
              accountId: aid,
              task: "drive",
              token: rtokenString,
            })
          );
        refreshToken.task(task);
        refreshToken.token(rtokenString);
        return this.refreshTokenService.update(context, aid, refreshToken);
      });
  }

  public revokeToken(context: Context, aid: number, task: string) {
    return this.refreshTokenService.delete(context, aid, "amazon", task);
  }

  public reserveDrive(context: Context, aid: number) {
    let ticket: string;
    return this.lockService
      .lock(context, aid, "amazon/drive/reservation", 10)
      .then((result) => {
        ticket = result;
        return this.deviceService.findByType(
          context,
          aid,
          DeviceType.AmazonDrive
        );
      })
      .then((result) => {
        if (result && result.length > 0 && !result[0].deleted) {
          return error<any>(400, ids.DUPLICATE_CLOUD_DRIVE);
        }
        return this.deviceService.newId(context, aid);
      })
      .then((result) => {
        return {
          ticket,
          deviceId: result,
          deviceName: "Amazon Drive",
        };
      });
  }
}
