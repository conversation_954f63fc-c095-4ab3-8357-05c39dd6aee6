#!/bin/bash

source ./_.postgres.sh
source ./_.helpers.sh

cd ..

echo "started $0"
echo ""

# default to "test" environment
ENVIRONMENT="${1:-test}"
BUILD="$2"
AUTOMATIC="$3" # flag used for automation to supress prompts
AWS=$(which aws)

# try to find file in default location
OSX_MIN_OS_VERSION="10.14"
OSX_FILE_NAME="Mylio.app.zip"
OSX_FILE="./$OSX_FILE_NAME"

WIN_DUAL_MIN_OS_VERSION=6.1
WIN_DUAL_FILE_NAME="SetupMylio.exe"
WIN_DUAL_FILE="./$WIN_DUAL_FILE_NAME"

WIN_64_MIN_OS_VERSION="10.0.18363"
WIN_64_FILE_NAME="SetupMylio64.exe"
WIN_64_FILE="./$WIN_64_FILE_NAME"

WIN_32_MIN_OS_VERSION=6.1
WIN_32_FILE_NAME="SetupMylio32.exe"
WIN_32_FILE="./$WIN_32_FILE_NAME"

WIN_ARM_MIN_OS_VERSION="10.0.18363"
WIN_ARM_FILE_NAME="SetupMylioArm.exe"
WIN_ARM_FILE="./$WIN_ARM_FILE_NAME"

case $ENVIRONMENT in
    "localhost")
        HOST="localhost"
        PORT="5432"
        USER="postgres"
        BUCKET="mylio-test-builds"
        ;;
    "test")
        HOST="test-0-rds.mylio.com"
        PORT=5432
        USER="superuser"
        BUCKET="mylio-test-builds"
        ;;
    "production")
        HOST="account0-rds.mylio.com"
        PORT=5432
        USER="superuser"
        BUCKET="mylio-builds"
        ;;
    *)
        echo "deployment $DEPLOYMENT not found"
        echo "options are:"
        echo "---> test"
        echo "---> production"
        exit 1
esac

if [[ $AUTOMATIC ]]
then
    SHOULD_UPDATE_OSX="y"
    SHOULD_UPDATE_WIN_DUAL="y"
    SHOULD_UPDATE_WIN_32="y"
    SHOULD_UPDATE_WIN_64="y"
    SHOULD_UPDATE_WIN_ARM="y"
    SHOULD_UPDATE_IOS="y"
    SHOULD_UPDATE_ANDROID="y"
fi

# # set s3 base url
S3_BASE_URL="http://$BUCKET.s3.amazonaws.com"

# prompt user for build
if [[ ! $AUTOMATIC ]]
then
    BUILD=$(prompt "Enter a build number:" $BUILD)
fi

# ask user if we should update osx
if [[ ! $AUTOMATIC ]]
then
    SHOULD_UPDATE_OSX=$(prompt "Do you want to update OSX? [y/n]:" "y")
fi

if [[ $(said_yes $SHOULD_UPDATE_OSX) ]] || [[ $AUTOMATIC ]]
then
    # prompt user for osx file
    if [[ ! $AUTOMATIC ]]
    then
        OSX_FILE=$(prompt_file "Enter OSX .zip file location:" "zip" $OSX_FILE)
    fi

    PUSHING_DESKTOP="y"
    OSX_PATH="osx/$BUILD/$OSX_FILE_NAME"
    OSX_URI="$S3_BASE_URL/$OSX_PATH"
    OSX_FILE_SIZE=$(stat -f%z $OSX_FILE)
    OSX_PROP_NAME="buildOs33"
    OSX_PROP_VALUE="{\"latestBuild\": $BUILD, \"uri\": \"$OSX_URI\", \"filesize\": $OSX_FILE_SIZE, \"filename\": \"$OSX_FILE_NAME\", \"minOSVersionRequired\": \"$OSX_MIN_OS_VERSION\"}"
fi

# ask user if we should update windows dual
if [[ ! $AUTOMATIC ]]
then
    SHOULD_UPDATE_WIN_DUAL=$(prompt "Do you want to update Windows (dual)? [y/n]:" "y")
fi

if [[ $(said_yes $SHOULD_UPDATE_WIN_DUAL) ]]
then
    # prompt user for windows file
    if [[ ! $AUTOMATIC ]]
    then
        WIN_DUAL_FILE=$(prompt_file "Enter Windows dual .exe file location:" "exe" $WIN_DUAL_FILE)
    fi

    PUSHING_DESKTOP="y"
    WIN_DUAL_PATH="windows/$BUILD/$WIN_DUAL_FILE_NAME"
    WIN_DUAL_URI="$S3_BASE_URL/$WIN_DUAL_PATH"
    WIN_DUAL_FILE_SIZE=$(stat -f%z $WIN_DUAL_FILE)
    WIN_DUAL_PROP_NAME="buildOs34"
    WIN_DUAL_PROP_VALUE="{\"latestBuild\": $BUILD, \"uri\": \"$WIN_DUAL_URI\", \"filesize\": $WIN_DUAL_FILE_SIZE, \"filename\": \"$WIN_DUAL_FILE_NAME\", \"minOSVersionRequired\": \"$WIN_DUAL_MIN_OS_VERSION\"}"
fi

# ask user if we should update windows 64-bit
if [[ ! $AUTOMATIC ]]
then
    SHOULD_UPDATE_WIN_64=$(prompt "Do you want to update Windows (64-bit)? [y/n]:" "y")
fi

if [[ $(said_yes $SHOULD_UPDATE_WIN_64) ]]
then
    # prompt user for windows file
    if [[ ! $AUTOMATIC ]]
    then
        WIN_64_FILE=$(prompt_file "Enter Windows 64-bit .exe file location:" "exe" $WIN_64_FILE)
    fi

    PUSHING_DESKTOP="y"
    WIN_64_PATH="windows/$BUILD/$WIN_64_FILE_NAME"
    WIN_64_URI="$S3_BASE_URL/$WIN_64_PATH"
    WIN_64_FILE_SIZE=$(stat -f%z $WIN_64_FILE)
    WIN_64_PROP_NAME="buildOs34-64bit"
    WIN_64_PROP_VALUE="{\"latestBuild\": $BUILD, \"uri\": \"$WIN_64_URI\", \"filesize\": $WIN_64_FILE_SIZE, \"filename\": \"$WIN_64_FILE_NAME\", \"minOSVersionRequired\": \"$WIN_64_MIN_OS_VERSION\"}"
fi

# ask user if we should update windows 32-bit
if [[ ! $AUTOMATIC ]]
then
    SHOULD_UPDATE_WIN_32=$(prompt "Do you want to update Windows (32-bit)? [y/n]:" "y")
fi

if [[ $(said_yes $SHOULD_UPDATE_WIN_32) ]]
then
    # prompt user for windows file
    if [[ ! $AUTOMATIC ]]
    then
        WIN_32_FILE=$(prompt_file "Enter Windows (32-bit) .exe file location:" "exe" $WIN_32_FILE)
    fi

    PUSHING_DESKTOP="y"
    WIN_32_PATH="windows/$BUILD/$WIN_32_FILE_NAME"
    WIN_32_URI="$S3_BASE_URL/$WIN_32_PATH"
    WIN_32_FILE_SIZE=$(stat -f%z $WIN_32_FILE)
    WIN_32_PROP_NAME="buildOs34-32bit"
    WIN_32_PROP_VALUE="{\"latestBuild\": $BUILD, \"uri\": \"$WIN_32_URI\", \"filesize\": $WIN_32_FILE_SIZE, \"filename\": \"$WIN_32_FILE_NAME\", \"minOSVersionRequired\": \"$WIN_32_MIN_OS_VERSION\"}"
fi

# ask user if we should update windows ARM
if [[ ! $AUTOMATIC ]]
then
    SHOULD_UPDATE_WIN_ARM=$(prompt "Do you want to update Windows (ARM)? [y/n]:" "y")
fi

if [[ $(said_yes $SHOULD_UPDATE_WIN_ARM) ]]
then
    # prompt user for windows file
    if [[ ! $AUTOMATIC ]]
    then
        WIN_ARM_FILE=$(prompt_file "Enter Windows (ARM) .exe file location:" "exe" $WIN_ARM_FILE)
    fi

    PUSHING_DESKTOP="y"
    WIN_ARM_PATH="windows/$BUILD/$WIN_ARM_FILE_NAME"
    WIN_ARM_URI="$S3_BASE_URL/$WIN_ARM_PATH"
    WIN_ARM_FILE_SIZE=$(stat -f%z $WIN_ARM_FILE)
    WIN_ARM_PROP_NAME="buildOs34-ARM64"
    WIN_ARM_PROP_VALUE="{\"latestBuild\": $BUILD, \"uri\": \"$WIN_ARM_URI\", \"filesize\": $WIN_ARM_FILE_SIZE, \"filename\": \"$WIN_ARM_FILE_NAME\", \"minOSVersionRequired\": \"$WIN_ARM_MIN_OS_VERSION\"}"
fi

if [[ ! $AUTOMATIC ]]
then
    SHOULD_UPDATE_IOS=$(prompt "Do you want to update iOS? [y/n]:" "y")
fi

if [[ $(said_yes $SHOULD_UPDATE_IOS) ]]
then
    IPAD_PROP_NAME="buildOs65"
    IPHONE_PROP_NAME="buildOs66"
    IOS_PROP_VALUE="{\"latestBuild\": $BUILD}"
fi

if [[ ! $AUTOMATIC ]]
then
    SHOULD_UPDATE_ANDROID=$(prompt "Do you want to update Android? [y/n]:" "y")
fi

if [[ $(said_yes $SHOULD_UPDATE_ANDROID) ]]
then
    ANDROID_PROP_NAME="buildOs67"
    ANDROID_PROP_VALUE="{\"latestBuild\": $BUILD}"
fi

if [[ ! $AUTOMATIC ]]
then
    if [[ ! $(said_yes $SHOULD_UPDATE_OSX) ]] && [[ ! $(said_yes $SHOULD_UPDATE_WIN_DUAL) ]] && [[ ! $(said_yes $SHOULD_UPDATE_WIN_64) ]] && [[ ! $(said_yes $SHOULD_UPDATE_WIN_32) ]] && [[ ! $(said_yes $SHOULD_UPDATE_WIN_ARM) ]] && [[ ! $(said_yes $SHOULD_UPDATE_IOS) ]] && [[ ! $(said_yes $SHOULD_UPDATE_ANDROID) ]]
    then
        echo "Noting to do"
        echo "Exiting..."

        exit 0
    fi
fi

if [[ $PUSHING_DESKTOP ]]
then
    AWS_CHECK=$($AWS s3api list-buckets 2>&1)

    if [[ $(echo $AWS_CHECK | grep "Unable to locate credentials") ]]
    then
        if [[ ! $AUTOMATIC ]]
        then
            export ACCESS_KEY_ID=$(prompt "Enter your AWS Access Key Id:")
            export SECRET_ACCESS_KEY=$(prompt "Enter your AWS Secret Access Key:")
        else
            echo "No AWS creds found. Exiting..."

            exit 1
        fi
    fi
fi

if [[ $(said_yes $SHOULD_UPDATE_OSX) ]] || [[ $(said_yes $SHOULD_UPDATE_WIN_DUAL) ]] || [[ $(said_yes $SHOULD_UPDATE_WIN_64) ]] || [[ $(said_yes $SHOULD_UPDATE_WIN_32) ]] || [[ $(said_yes $SHOULD_UPDATE_WIN_ARM) ]] || [[ $(said_yes $SHOULD_UPDATE_IOS) ]] || [[ $(said_yes $SHOULD_UPDATE_ANDROID) ]]
then
    SHOULD_UPLOAD_TO_S3="y"

    if [[ $ENVIRONMENT == "localhost" ]]
    then
        echo ""
        echo "NOTICE: Running in 'localhost' env. Skipping upload to S3."
        SHOULD_UPLOAD_TO_S3="n"
    fi

    if [[ $(said_yes $SHOULD_UPLOAD_TO_S3) ]]
    then
        echo ""
        echo "The following files will be pushed to S3:"
        echo ""

        if [[ $(said_yes $SHOULD_UPDATE_OSX) ]];      then echo "    $OSX_FILE --> $OSX_URI"; fi
        if [[ $(said_yes $SHOULD_UPDATE_WIN_DUAL) ]]; then echo "    $WIN_DUAL_FILE --> $WIN_DUAL_URI"; fi
        if [[ $(said_yes $SHOULD_UPDATE_WIN_64) ]];   then echo "    $WIN_64_FILE --> $WIN_64_URI"; fi
        if [[ $(said_yes $SHOULD_UPDATE_WIN_32) ]];   then echo "    $WIN_32_FILE --> $WIN_32_URI"; fi
        if [[ $(said_yes $SHOULD_UPDATE_WIN_ARM) ]];  then echo "    $WIN_ARM_FILE --> $WIN_ARM_URI"; fi
    fi

    echo ""
    echo "The following records will be updated in the database:"
    echo ""

    if [[ $(said_yes $SHOULD_UPDATE_OSX) ]];      then echo "    OSX: $OSX_PROP_VALUE"; fi
    if [[ $(said_yes $SHOULD_UPDATE_WIN_DUAL) ]]; then echo "    Windows (dual): $WIN_DUAL_PROP_VALUE"; fi
    if [[ $(said_yes $SHOULD_UPDATE_WIN_64) ]];   then echo "    Windows (64-bit): $WIN_64_PROP_VALUE"; fi
    if [[ $(said_yes $SHOULD_UPDATE_WIN_32) ]];   then echo "    Windows (32-bit): $WIN_32_PROP_VALUE"; fi
    if [[ $(said_yes $SHOULD_UPDATE_WIN_ARM) ]];  then echo "    Windows (ARM): $WIN_ARM_PROP_VALUE"; fi
    if [[ $(said_yes $SHOULD_UPDATE_IOS) ]];      then echo "    iOS: $IOS_PROP_VALUE"; fi
    if [[ $(said_yes $SHOULD_UPDATE_ANDROID) ]];  then echo "    Android: $ANDROID_PROP_VALUE"; fi
fi

if [[ ! $AUTOMATIC ]]
then
    echo ""
    SHOULD_CONTINUE=$(prompt "Would you like to continue? [y/n]:")
    echo ""
fi

if [[ $(said_yes $SHOULD_CONTINUE) ]] || [[ $AUTOMATIC ]]
then
    open_pg_connection $HOST $PORT $USER

    if [[ $PUSHING_DESKTOP ]] && [[ $(said_yes $SHOULD_UPLOAD_TO_S3) ]]
    then
        echo ""
        echo "Uploading files to S3 (this may take a while)..."

        if [[ $(said_yes $SHOULD_UPDATE_OSX) ]]
        then
            echo "Uploading $OSX_FILE to S3..."
            command=$($AWS s3api put-object --bucket $BUCKET --key "$OSX_PATH" --body $OSX_FILE --acl public-read) &
            spinner
        fi

        if [[ $(said_yes $SHOULD_UPDATE_WIN_DUAL) ]]
        then
            echo "Uploading $WIN_DUAL_FILE to S3..."
            command=$($AWS s3api put-object --bucket $BUCKET --key "$WIN_DUAL_PATH" --body $WIN_DUAL_FILE --acl public-read) &
            spinner
        fi

        if [[ $(said_yes $SHOULD_UPDATE_WIN_64) ]]
        then
            echo "Uploading $WIN_64_FILE to S3..."
            command=$($AWS s3api put-object --bucket $BUCKET --key "$WIN_64_PATH" --body $WIN_64_FILE --acl public-read) &
            spinner
        fi

        if [[ $(said_yes $SHOULD_UPDATE_WIN_32) ]]
        then
            echo "Uploading $WIN_32_FILE to S3..."
            command=$($AWS s3api put-object --bucket $BUCKET --key "$WIN_32_PATH" --body $WIN_32_FILE --acl public-read) &
            spinner
        fi

        if [[ $(said_yes $SHOULD_UPDATE_WIN_ARM) ]]
        then
            echo "Uploading $WIN_ARM_FILE to S3..."
            command=$($AWS s3api put-object --bucket $BUCKET --key "$WIN_ARM_PATH" --body $WIN_ARM_FILE --acl public-read) &
            spinner
        fi
    fi

    echo "Updating system properties in database..."
    cd scripts

    if [[ $(said_yes $SHOULD_UPDATE_OSX) ]]
    then
        command=$(upsert_system_property $OSX_PROP_NAME $OSX_PROP_VALUE) &
        spinner
    fi

    if [[ $(said_yes $SHOULD_UPDATE_WIN_DUAL) ]]
    then
        command=$(upsert_system_property $WIN_DUAL_PROP_NAME $WIN_DUAL_PROP_VALUE) &
        spinner
    fi

    if [[ $(said_yes $SHOULD_UPDATE_WIN_64) ]]
    then
        command=$(upsert_system_property $WIN_64_PROP_NAME $WIN_64_PROP_VALUE) &
        spinner
    fi

    if [[ $(said_yes $SHOULD_UPDATE_WIN_32) ]]
    then
        command=$(upsert_system_property $WIN_32_PROP_NAME $WIN_32_PROP_VALUE) &
        spinner
    fi

    if [[ $(said_yes $SHOULD_UPDATE_WIN_ARM) ]]
    then
        command=$(upsert_system_property $WIN_ARM_PROP_NAME $WIN_ARM_PROP_VALUE) &
        spinner
    fi

    if [[ $(said_yes $SHOULD_UPDATE_IOS) ]]
    then
        command=$(upsert_system_property $IPAD_PROP_NAME $IOS_PROP_VALUE && upsert_system_property $IPHONE_PROP_NAME $IOS_PROP_VALUE) &
        spinner
    fi

    if [[ $(said_yes $SHOULD_UPDATE_ANDROID) ]]
    then
        command=$(upsert_system_property $ANDROID_PROP_NAME $ANDROID_PROP_VALUE) &
        spinner
    fi

    close_pg_connection

    echo "Done!"
    exit 0
else
    echo "Exiting..."
    exit 0
fi
