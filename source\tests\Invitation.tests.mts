// import { test, describe, mock, before, after, it } from "node:test";
// import assert from "node:assert";
// import { CloudClient } from "./CloudClient.mjs";
// import { Builder, By, Browser, until } from "selenium-webdriver";
// import { <PERSON>ice<PERSON>, Manager } from "../models/License.model.mjs";
// import { createUsers, subscribe, sleep } from "./FastSpringTestHelpers.mjs";
// import moment from "moment";

// const HOST = "https://willem.mylio.com";
// const PASSWORD = "password";
// const ADMIN_USER = "<EMAIL>";
// const USER = "<EMAIL>";

// describe("The license Lifecycle", async (t) => {
//     let adminCloud = new CloudClient(HOST);
//     let licenseKey = "";
//     let l1Cloud: CloudClient;
//     let users: CloudClient[] = [];

//     before(async (t) => {
//         users = await createUsers([USER]);
//         l1Cloud = users[0];
//     });

//     it("buy a family plan", async (t) => {

//         export interface ILicense {
//             flags?: number;
//             modifiedTime?: Date;
//             createdTime?: Date;
//             licenseId?: string;
//             accountId?: number;
//             manager?: string;
//             featuresetId?: string;
//             endDate?: Date;
//             templateId?: string;
//             activationKey?: string;
//             availableUpgrades?: string;
//             deviceLimit?: number;
//             photoLimit?: number;
//             features?: number;
//             cloudStorageLimit?: number;
//             deleted?: boolean;
//             canceled?: boolean;
//             trial?: boolean;
//         }

//         let license = {
//             accountId: l1Cloud.account.accountId,
//             manager: Manager.Mylio,
//             templateId: "family-test",
//             endDate: moment().add(1, "year").toDate(),
//         };
//         await l1Cloud.post("/accounts/:aid/licenses", license);


//         licenseKey = await subscribe("one-year", "twoTBmonth");
//         //wait for webhook
//         await sleep(5000);
//     });

//     it("redeem license key", async (t) => {
//         await l1Cloud.get(`/license-keys/${licenseKey}/redeem`);
//         let licenses = await l1Cloud.get<ILicense[]>("/accounts/:aid/licenses");
//         let fsLicense = licenses.find(l => l.activationKey === licenseKey && l.accountId === l1Cloud.account.accountId);
//         assert(!!fsLicense, "license not found");
//     });

//     it("preview increase storage to 10tb", async (t) => {
//         let result = await l1Cloud.post<any>("/accounts/:aid/cloud-storage", { qty: 10, preview: true });
//         assert(!!result.proposedPlan);
//     });

//     it("increase storage to 10tb", async (t) => {
//         await l1Cloud.post("/accounts/:aid/cloud-storage", { qty: 10, preview: false });
//         await sleep(5000);
//         let licenses = await l1Cloud.get<ILicense[]>("/accounts/:aid/licenses");
//         let _5 = 0;
//         let _2 = 0;
//         for (let license of licenses) {
//             if (license.manager === Manager.FastSpring && !license.deleted) {
//                 if (license.templateId.includes("5"))
//                     _5++;
//                 if (license.templateId.includes("2"))
//                     _2++;
//             }
//         }
//         assert(_5 === 2, "5TB license not found");
//         assert(_2 === 0, "2TB license found");
//     });


//     it("decrease storage to 2tb", async (t) => {
//         await l1Cloud.post("/accounts/:aid/cloud-storage", { qty: 2, preview: false });
//         await sleep(5000);
//         let licenses = await l1Cloud.get<ILicense[]>("/accounts/:aid/licenses");
//         let _5 = 0;
//         let _2 = 0;
//         for (let license of licenses) {
//             if (license.manager === Manager.FastSpring && !license.deleted) {
//                 if (license.templateId.includes("5"))
//                     _5++;
//                 if (license.templateId.includes("2"))
//                     _2++;
//             }
//         }
//         assert(_5 === 0, "5TB license not found");
//         assert(_2 === 1, "2TB license found");
//     });

//     it("revoke license key", async (t) => {
//         await adminCloud.signin(ADMIN_USER, PASSWORD);
//         await adminCloud.impersonate(l1Cloud.account.accountId);
//         let licenses = await l1Cloud.get<ILicense[]>("/accounts/:aid/licenses");
//         for (let license of licenses) {
//             if (license.activationKey === licenseKey)
//                 await adminCloud.post(`/accounts/:aid/licenses/${license.licenseId}/revoke`);
//         }
//         licenses = await l1Cloud.get<ILicense[]>("/accounts/:aid/licenses");
//         let fsLicense = licenses.find(l => l.activationKey === licenseKey && l.accountId === l1Cloud.account.accountId);
//         assert(!fsLicense, "license not revoked");
//     });

//     after(async (t) => {
//         console.log("done");
//         setTimeout(() => {
//             process.exit(0);
//         }, 200);
//     });
// });
