"use strict";

import * as express from "express";
import { microservice as g } from "../microservices/account.microservice.mjs";
import { Context } from "../system/Context.mjs";
import { decodeJwt } from "jose";
import { config } from "../system/Config.mjs";
import { ids } from "../system/Strings.mjs";

export function addSsoRoutes(router: express.Router) {
  router.post(
    "/sso/google",
    express.urlencoded({ extended: true }),
    async (req, res, next) => {
      let x = req.body;
      let context = new Context();
      context.audience = "website";
      let idToken = decodeJwt(x.credential);
      try {
        //@ts-ignore
        const result = await g.idpService.subscribe(
          context,
          "google",
          idToken.sub,
          idToken.email as string
        );
        return res.redirect(`${config.website}?token=${result.token}`);
      } catch (error) {
        if (error.code === ids.DUPLICATE_SUB_AND_IDP) {
          const result = await g.idpService.token(
            context,
            idToken.sub,
            "google"
          );
          return res.redirect(`${config.website}?token=${result.token}`);
        }
      }
      res.status(400).send();
    }
  );

  router.post(
    "/sso/apple",
    express.urlencoded({ extended: true }),
    async (req, res, next) => {
      let x = req.body;
      let context = new Context();
      context.audience = "website";
      let idToken = decodeJwt(x.id_token);
      try {
        //@ts-ignore
        const result = await g.idpService.subscribe(
          context,
          "apple",
          idToken.sub,
          idToken.email as string
        );
        return res.redirect(`${config.website}?token=${result.token}`);
      } catch (error) {
        if (error.code === ids.DUPLICATE_SUB_AND_IDP) {
          const result = await g.idpService.token(
            context,
            idToken.sub,
            "apple"
          );
          return res.redirect(`${config.website}?token=${result.token}`);
        }
      }
      res.status(400).send();
    }
  );
}
