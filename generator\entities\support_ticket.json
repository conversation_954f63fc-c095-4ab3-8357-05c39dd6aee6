{"plural": "supportTickets", "flow": "web<>cloud<>postgres<>disk", "mixin": ["all"], "fields": {"subject": {"datatype": "string"}, "comments": {"datatype": "string"}, "sendScreenshot": {"datatype": "boolean"}, "includeCatalog": {"datatype": "boolean"}, "logLevel": {"datatype": "string"}, "logLevelFlags": {"datatype": "int32"}, "requestId": {"datatype": "string"}, "consoleCommandString": {"datatype": "string"}, "ttl": {"datatype": "int32"}}}