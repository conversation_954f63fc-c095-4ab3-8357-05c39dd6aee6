#pragma once

template <class TIter, class TType, class TFunc>
class iterator_adapter
    : public std::iterator<std::forward_iterator_tag, TType>
{
    typedef const iterator_adapter iterator;
    TIter cur_;
    TFunc func_;

public:
    iterator_adapter(TIter iter, TFunc func) : cur_(iter), func_(func) {}

    iterator operator++(int) /* postfix */
    {
        iterator old(cur_, func_);
        ++(*this);
        return old;
    }

    iterator &operator++() /* prefix */
    {
        ++cur_;
        return *this;
    }

    auto operator*() const -> decltype(*func_(*cur_))
    {
        return *func_(*cur_);
    }
    TType *operator->() const { return cur_; }
    bool operator==(const iterator &rhs) const
    {
        return cur_ == rhs.cur_;
    }
    bool operator!=(const iterator &rhs) const { return cur_ != rhs.cur_; }
};

// vector_map works like a map, but is implemented via a sorted vector.
// This makes finds a bit faster due to packing, and traversal significantly
// faster due to prefetching. Inserts can be slower or faster.
//
// This is a direct forward-to-vector type, except for the emplace & find functions
//
// Rule of thumb for insert speed:
//   If for each element newly inserted at random, there are 133 or more retrieves of existing elements,
//      a vector_map will insert faster. At a 1000-to-1 ratio, vector_map is twice as fast for
//      inserts (of integral key & integral value) than map.
//  At a lower hit-to-miss ratio, a std::map will have a faster insert.
template <typename _TKey, typename _TValue>
class vector_map
{
    typedef _TKey key_type;
    typedef _TValue referent_type;
    typedef std::pair<_TKey, _TValue> element_type;
    typedef const element_type &const_reference;
    typedef element_type &reference;
    typedef element_type *pointer;
    typedef const element_type *const_pointer;
    typedef std::vector<element_type> vector_type;

    vector_type _Myvec;

public:
    typedef typename vector_type::iterator iterator;
    typedef typename vector_type::const_iterator const_iterator;
    typedef typename vector_type::reverse_iterator reverse_iterator;
    typedef typename vector_type::const_reverse_iterator const_reverse_iterator;

    vector_map()
    {
    }

    vector_map(const vector_map &_Other) : _Myvec(_Other._Myvec)
    {
    }

    vector_map(vector_map &&_Other) : _Myvec(std::move(_Other._Myvec))
    {
    }

    vector_map &operator=(vector_map &&_Other)
    {
        _Myvec = std::move(_Other._Myvec);
        return *this;
    }

    vector_map &operator=(const vector_map &_Other)
    {
        _Myvec = _Other._Myvec;
        return *this;
    }

    bool operator==(const vector_map &other) const
    {
        return _Myvec == other._Myvec;
    }

    bool operator!=(const vector_map &other) const
    {
        return _Myvec != other._Myvec;
    }

    void sort_vector()
    {
        std::sort(_Myvec.begin(), _Myvec.end(), [](const element_type &key1, const element_type &key2)
                  { return key1.first < key2.first; });
    }

    // Faster emplaced if the input is guaranteed alread sorted and not duplicate
    template <class... _TArgs>
    iterator emplace_from_sorted(const _TKey &_Key, _TArgs &&..._Args)
    {
#if defined(DEBUG) || defined(_DEBUG)
        assert(find(_Key) == end());
        if (_Myvec.size() > 0)
        {
            // If this fires, you're not actually sorted
            assert(_Myvec[_Myvec.size() - 1].first < _Key);
        }
#endif
        _Myvec.emplace_back(
            std::piecewise_construct,
            std::forward_as_tuple(_Key),
            std::forward_as_tuple(std::forward<_TArgs>(_Args)...));

        // Inserted new item
        return _Myvec.begin() + (_Myvec.size() - 1);
    }

    template <class... _TArgs>
    iterator emplace_from_sorted_unchecked(const _TKey &_Key, _TArgs &&..._Args)
    {
        _Myvec.emplace_back(
            std::piecewise_construct,
            std::forward_as_tuple(_Key),
            std::forward_as_tuple(std::forward<_TArgs>(_Args)...));

        // Inserted new item
        return _Myvec.begin() + (_Myvec.size() - 1);
    }

    template <class... _TArgs>
    iterator emplace_now_sort_after(const _TKey &_Key, _TArgs &&..._Args)
    {
        _Myvec.emplace_back(
            std::piecewise_construct,
            std::forward_as_tuple(_Key),
            std::forward_as_tuple(std::forward<_TArgs>(_Args)...));

        // Inserted new item
        return _Myvec.begin() + (_Myvec.size() - 1);
    }

    // Faster emplaced if the input is guaranteed alread sorted and not duplicate
    template <class... _TArgs>
    iterator emplace_from_sorted(const_iterator _Position, const _TKey &_Key, _TArgs &&..._Args)
    {
        if (_Position == _Myvec.end())
        {
            return emplace_from_sorted(_Key, std::forward<_TArgs>(_Args)...);
        }

#if defined(DEBUG) || defined(_DEBUG)
        assert(find(_Key) == end());
        if (_Myvec.size() > 0)
        {
            if (_Position == _Myvec.begin())
            {
                assert(_Key < _Myvec[0].first);
            }
            else
            {
                assert(_Key < _Position->first);
                auto _Prev = std::prev(_Position);
                assert(_Prev->first < _Key);
            }
        }
#endif
        return _Myvec.emplace(
            _Position,
            std::piecewise_construct,
            std::forward_as_tuple(_Key),
            std::forward_as_tuple(std::forward<_TArgs>(_Args)...));
    }

    // This is not a pure implementation of emplace. It allows a _Key to be passed without doing a move.
    // This makes it slightly faster for value types.
    template <class... _TArgs>
    std::pair<iterator, bool> try_emplace(const _TKey &_Key, _TArgs &&..._Args)
    {
        iterator _Pos = std::lower_bound(
            _Myvec.begin(), _Myvec.end(), _Key,
            [](const element_type &a, const key_type &b)
            {
                return a.first < b;
            });

        if (_Pos == _Myvec.end())
        {
            _Myvec.emplace_back(
                std::piecewise_construct,
                std::forward_as_tuple(_Key),
                std::forward_as_tuple(std::forward<_TArgs>(_Args)...));

            // Inserted new item
            return std::make_pair(_Myvec.begin() + (_Myvec.size() - 1), true);
        }

        if (_Key < _Pos->first)
        {
            _Pos = _Myvec.emplace(
                _Pos,
                std::piecewise_construct,
                std::forward_as_tuple(_Key),
                std::forward_as_tuple(std::forward<_TArgs>(_Args)...));

            // Inserted new item
            return std::make_pair(_Pos, true);
        }

        // Existing item
        return std::make_pair(_Pos, false);
    }

    template <class... _TArgs>
    std::pair<iterator, bool> try_emplace(_TKey &&_Key, _TArgs &&..._Args)
    {
        iterator _Pos = std::lower_bound(
            _Myvec.begin(), _Myvec.end(), _Key,
            [](const element_type &a, const key_type &b)
            {
                return a.first < b;
            });

        if (_Pos == _Myvec.end())
        {
            _Myvec.emplace_back(
                std::piecewise_construct,
                std::forward_as_tuple(_Key),
                std::forward_as_tuple(std::forward<_TArgs>(_Args)...));

            // Inserted new item
            return std::make_pair(_Myvec.begin() + (_Myvec.size() - 1), true);
        }

        if (_Key < _Pos->first)
        {
            _Pos = _Myvec.emplace(
                _Pos,
                std::piecewise_construct,
                std::forward_as_tuple(_Key),
                std::forward_as_tuple(std::forward<_TArgs>(_Args)...));

            // Inserted new item
            return std::make_pair(_Pos, true);
        }

        // Existing item
        return std::make_pair(_Pos, false);
    }

    _TValue &operator[](_TKey &&_Keyval)
    {
        return (try_emplace(std::move(_Keyval)).first->second);
    }

    _TValue &operator[](const _TKey &_Keyval)
    {
        return (try_emplace(_Keyval).first->second);
    }

    void reserve(size_t _Count)
    {
        return _Myvec.reserve(_Count);
    }

    void resize(size_t _Count)
    {
        _Myvec.resize(_Count);
    }

    size_t capacity() const
    {
        return _Myvec.capacity();
    }

    iterator find(const _TKey &_Key)
    {
        iterator _Pos = std::lower_bound(_Myvec.begin(), _Myvec.end(), _Key,
                                         [](const element_type &a, const key_type &b)
                                         {
                                             return a.first < b;
                                         });

        if (_Pos == _Myvec.end())
        {
            return _Pos;
        }

        if (_Key < _Pos->first)
        {
            return _Myvec.end();
        }

        return _Pos;
    }

    const_iterator find(const _TKey &_Key) const
    {
        const_iterator _Pos = std::lower_bound(_Myvec.begin(), _Myvec.end(), _Key,
                                               [](const element_type &a, const key_type &b)
                                               {
                                                   return a.first < b;
                                               });

        if (_Pos == _Myvec.cend())
        {
            return _Pos;
        }

        if (_Key < _Pos->first)
        {
            return _Myvec.cend();
        }

        return _Pos;
    }

    iterator begin() { return _Myvec.begin(); }
    const_iterator begin() const { return _Myvec.begin(); }
    const_iterator cbegin() const { return _Myvec.cbegin(); }

    iterator end() { return _Myvec.end(); }
    const_iterator end() const { return _Myvec.end(); }
    const_iterator cend() const { return _Myvec.cend(); }

    reverse_iterator rbegin() { return _Myvec.rbegin(); }
    const_reverse_iterator rbegin() const { return _Myvec.rbegin(); }
    const_reverse_iterator crbegin() const { return _Myvec.crbegin(); }

    reverse_iterator rend() { return _Myvec.rend(); }
    const_reverse_iterator rend() const { return _Myvec.rend(); }
    const_reverse_iterator crend() const { return _Myvec.crend(); }

    pointer data()
    {
        return _Myvec.data();
    }

    const_pointer data() const
    {
        return _Myvec.data();
    }

    void clear()
    {
        _Myvec.clear();
    }

    bool empty() const
    {
        return _Myvec.empty();
    }

    size_t size() const
    {
        return _Myvec.size();
    }

    iterator insert(const_iterator _Where, const element_type &_Val)
    {
        return _Myvec.insert(_Where, _Val);
    }

    iterator insert(const_iterator _Where, size_t _Count,
                    const element_type &_Val)
    {
        return _Myvec.insert(_Where, _Count, _Val);
    }

    iterator erase_at(const_iterator _Iter)
    {
        return _Myvec.erase(_Iter);
    }

    iterator erase_at(const_iterator _First_arg, const_iterator _Last_arg)
    {
        return _Myvec.erase(_First_arg, _Last_arg);
    }

    size_t erase(const _TKey &_Key)
    {
        auto _Iter = find(_Key);
        if (_Iter != _Myvec.end())
        {
            _Myvec.erase(_Iter);
            return 1;
        }
        return 0;
    }

    iterator erase(iterator first, iterator last)
    {
        return _Myvec.erase(first, last);
    }

    void swap(vector_map &_Right)
    {
        return _Myvec.swap(_Right._Myvec);
    }

    class keys_extractor
    {
    public:
        const key_type *operator()(std::pair<key_type, referent_type> &_Element) const
        {
            return &_Element.first;
        }
    };

    typedef iterator_adapter<typename vector_map::iterator, key_type, keys_extractor> keys_iterator;
    class keys_collection
    {
        keys_iterator _MyBegin;
        keys_iterator _MyEnd;

    public:
        keys_collection(keys_iterator _Begin, keys_iterator _End) : _MyBegin(_Begin), _MyEnd(_End){};

        keys_iterator begin() { return _MyBegin; }
        keys_iterator end() { return _MyEnd; }
    };

    keys_collection keys()
    {
        keys_extractor extractor;
        return keys_collection(keys_iterator(begin(), extractor), keys_iterator(end(), extractor));
    }

    class values_extractor
    {
    public:
        referent_type *operator()(std::pair<key_type, referent_type> &_Element) const
        {
            return &_Element.second;
        }
    };

    typedef iterator_adapter<typename vector_map::iterator, referent_type, values_extractor> values_iterator;
    class values_collection
    {
        values_iterator _MyBegin;
        values_iterator _MyEnd;

    public:
        values_collection(values_iterator _Begin, values_iterator _End) : _MyBegin(_Begin), _MyEnd(_End){};

        values_iterator begin() { return _MyBegin; }
        values_iterator end() { return _MyEnd; }
    };

    values_collection values()
    {
        values_extractor extractor;
        return values_collection(values_iterator(begin(), extractor), values_iterator(end(), extractor));
    }

    class const_values_extractor
    {
    public:
        const referent_type *operator()(std::pair<key_type, const referent_type> &_Element) const
        {
            return &_Element.second;
        }
    };

    typedef iterator_adapter<typename vector_map::const_iterator, const referent_type, const_values_extractor> const_values_iterator;
    class const_values_collection
    {
        const_values_iterator _MyBegin;
        const_values_iterator _MyEnd;

    public:
        const_values_collection(const_values_iterator _Begin, const_values_iterator _End) : _MyBegin(_Begin), _MyEnd(_End){};

        const_values_iterator begin() const { return _MyBegin; }
        const_values_iterator end() const { return _MyEnd; }
    };

    const_values_iterator values() const
    {
        const_values_extractor extractor;
        return values_collection(const_values_iterator(begin(), extractor), const_values_iterator(end(), extractor));
    }
};

template <typename T>
bool vector_resize_if_needed(std::vector<T> &v, size_t i)
{
    if (i < v.size())
    {
        return false;
    }

    v.resize(i + 1);
    return true;
}

template <typename _TKey>
class vector_set
{
    typedef _TKey key_type;
    typedef _TKey element_type;
    typedef const element_type &const_reference;
    typedef element_type &reference;
    typedef element_type *pointer;
    typedef const element_type *const_pointer;
    typedef std::vector<element_type> vector_type;
    typedef typename vector_type::iterator iterator;
    typedef typename vector_type::const_iterator const_iterator;
    typedef typename vector_type::reverse_iterator reverse_iterator;
    typedef typename vector_type::const_reverse_iterator const_reverse_iterator;

    vector_type _Myvec;

public:
    vector_set()
    {
    }

    vector_set(const vector_set &_Other) : _Myvec(_Other._Myvec)
    {
    }

    vector_set(vector_set &&_Other) : _Myvec(std::move(_Other._Myvec))
    {
    }

    vector_set &operator=(vector_set &&_Other)
    {
        _Myvec = std::move(_Other._Myvec);
        return *this;
    }

    vector_set &operator=(const vector_set &_Other)
    {
        _Myvec = _Other._Myvec;
        return *this;
    }

    // This is not a pure implementation of emplace. It allows a _Key to be passed without doing a move.
    // This makes it slightly faster for value types.
    std::pair<iterator, bool> emplace(const _TKey &_Key)
    {
        iterator _Pos = std::lower_bound(
            _Myvec.begin(), _Myvec.end(), _Key,
            [](const element_type &a, const key_type &b)
            {
                return a < b;
            });

        if (_Pos == _Myvec.end())
        {
            _Myvec.emplace_back(_Key);

            // Inserted new item
            return std::make_pair(_Myvec.begin() + (_Myvec.size() - 1), true);
        }

        if (_Key < *_Pos)
        {
            _Pos = _Myvec.emplace(
                _Pos,
                _Key);

            // Inserted new item
            return std::make_pair(_Pos, true);
        }

        // Existing item
        return std::make_pair(_Pos, false);
    }

    std::pair<iterator, bool> emplace(_TKey &&_Key)
    {
        iterator _Pos = std::lower_bound(
            _Myvec.begin(), _Myvec.end(), _Key,
            [](const element_type &a, const key_type &b)
            {
                return a < b;
            });

        if (_Pos == _Myvec.end())
        {
            _Myvec.emplace_back(std::move(_Key));

            // Inserted new item
            return std::make_pair(_Myvec.begin() + (_Myvec.size() - 1), true);
        }

        if (_Key < *_Pos)
        {
            _Pos = _Myvec.emplace(
                _Pos,
                std::move(_Key));

            // Inserted new item
            return std::make_pair(_Pos, true);
        }

        // Existing item
        return std::make_pair(_Pos, false);
    }

    // Faster emplaced if the input is guaranteed alread sorted and not duplicate
    template <class... _TArgs>
    std::pair<iterator, bool> emplace_from_sorted(_TKey &&_Key)
    {
#if defined(DEBUG) || defined(_DEBUG)
        assert(find(_Key) == end());
        if (_Myvec.size() > 0)
        {
            // If this fires, you're not actually sorted
            assert(_Myvec[_Myvec.size() - 1] < _Key);
        }
#endif
        _Myvec.emplace_back(std::move(_Key));

        // Inserted new item
        return std::make_pair(_Myvec.begin() + (_Myvec.size() - 1), true);
    }

    template <class... _TArgs>
    std::pair<iterator, bool> emplace_from_sorted(const _TKey &_Key)
    {
#if defined(DEBUG) || defined(_DEBUG)
        assert(find(_Key) == end());
        if (_Myvec.size() > 0)
        {
            // If this fires, you're not actually sorted
            assert(_Myvec[_Myvec.size() - 1] < _Key);
        }
#endif
        _Myvec.emplace_back(_Key);

        // Inserted new item
        return std::make_pair(_Myvec.begin() + (_Myvec.size() - 1), true);
    }

    template <class... _TArgs>
    std::pair<iterator, bool> emplace_from_sorted_unchecked(const _TKey &_Key)
    {
        _Myvec.emplace_back(_Key);

        // Inserted new item
        return std::make_pair(_Myvec.begin() + (_Myvec.size() - 1), true);
    }

    void reserve(size_t _Count)
    {
        return _Myvec.reserve(_Count);
    }

    void resize(size_t _Count)
    {
        _Myvec.resize(_Count);
    }

    size_t capacity() const
    {
        return _Myvec.capacity();
    }

    iterator find(const _TKey &_Key)
    {
        iterator _Pos = std::lower_bound(_Myvec.begin(), _Myvec.end(), _Key,
                                         [](const element_type &a, const key_type &b)
                                         {
                                             return a < b;
                                         });

        if (_Pos == _Myvec.end())
        {
            return _Pos;
        }

        if (_Key < *_Pos)
        {
            return _Myvec.end();
        }

        return _Pos;
    }

    const_iterator find(const _TKey &_Key) const
    {
        const_iterator _Pos = std::lower_bound(_Myvec.cbegin(), _Myvec.cend(), _Key,
                                               [](const element_type &a, const key_type &b)
                                               {
                                                   return a < b;
                                               });

        if (_Pos == _Myvec.cend())
        {
            return _Pos;
        }

        if (_Key < *_Pos)
        {
            return _Myvec.cend();
        }

        return _Pos;
    }

    iterator begin() { return _Myvec.begin(); }
    const_iterator begin() const { return _Myvec.begin(); }
    const_iterator cbegin() const { return _Myvec.cbegin(); }

    iterator end() { return _Myvec.end(); }
    const_iterator end() const { return _Myvec.end(); }
    const_iterator cend() const { return _Myvec.cend(); }

    reverse_iterator rbegin() { return _Myvec.rbegin(); }
    const_reverse_iterator rbegin() const { return _Myvec.rbegin(); }
    const_reverse_iterator crbegin() const { return _Myvec.crbegin(); }

    reverse_iterator rend() { return _Myvec.rend(); }
    const_reverse_iterator rend() const { return _Myvec.rend(); }
    const_reverse_iterator crend() const { return _Myvec.crend(); }

    pointer data()
    {
        return _Myvec.data();
    }

    const_pointer data() const
    {
        return _Myvec.data();
    }

    void clear()
    {
        _Myvec.clear();
    }

    bool empty() const
    {
        return _Myvec.empty();
    }

    size_t size() const
    {
        return _Myvec.size();
    }

    iterator insert(const_iterator _Where, const element_type &_Val)
    {
        return _Myvec.insert(_Where, _Val);
    }

    iterator insert(const_iterator _Where, size_t _Count,
                    const element_type &_Val)
    {
        return _Myvec.insert(_Where, _Count, _Val);
    }

    iterator erase_at(const_iterator _Iter)
    {
        return _Myvec.erase(_Iter);
    }

    size_t erase(const _TKey &_Key)
    {
        auto _Iter = find(_Key);
        if (_Iter != _Myvec.end())
        {
            _Myvec.erase(_Iter);
            return 1;
        }
        return 0;
    }

    iterator erase(iterator first, iterator last)
    {
        return _Myvec.erase(first, last);
    }

    void swap(vector_set &_Right)
    {
        return _Myvec.swap(_Right._Myvec);
    }

    bool operator==(const vector_set &other) const
    {
        return _Myvec == other._Myvec;
    }

    bool operator!=(const vector_set &other) const
    {
        return _Myvec != other._Myvec;
    }
};
