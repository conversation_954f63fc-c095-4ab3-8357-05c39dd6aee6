

    drop view if exists a0."Device" cascade;

    create or replace view a0."Device" as
    select
    flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		deleted,
		encode(t, 'base64') as "t",
		encode(d, 'base64') as "d",
		device_id as "deviceId",
		name,
		device_type as "deviceType",
		nickname,
		encrypt,
		creation_time as "creationTime",
		encode(long_id, 'base64') as "longId",
		encode(support_ticket, 'base64') as "supportTicket"
    from a0.device;
    

drop function if exists a0.device_create; 
        create function a0.device_create(
            _flags int,
	_modified_time timestamptz,
	_created_time timestamptz,
	_account_id int,
	_deleted boolean,
	__t text,
	__d text,
	_device_id int,
	_name text,
	_device_type int,
	_nickname text,
	_encrypt boolean,
	_creation_time timestamptz,
	__long_id text,
	__support_ticket text
        )
        returns a0."Device"
        as $$
        
    declare
        result a0."Device";
        _t bytea;
		_d bytea;
		_long_id bytea;
		_support_ticket bytea;
    begin
        _t := decode(__t, 'base64');
		_d := decode(__d, 'base64');
		_long_id := decode(__long_id, 'base64');
		_support_ticket := decode(__support_ticket, 'base64');
        


       
        
/* b::before_create */
if (_device_id is null) then
	_device_id := a0.next_device_id(_account_id);
end if;
/* end */

        
        
        _t := public.new_trev4(_device_id, 4);
        
        _modified_time := coalesce(_modified_time, now());
        _created_time := coalesce(_created_time, now());
        insert into a0.device (
            flags,
	modified_time,
	created_time,
	account_id,
	deleted,
	t,
	d,
	device_id,
	name,
	device_type,
	nickname,
	encrypt,
	creation_time,
	long_id,
	support_ticket
        )
        values(
            _flags,
			_modified_time,
			_created_time,
			_account_id,
			_deleted,
			_t,
			_d,
			_device_id,
			_name,
			_device_type,
			_nickname,
			_encrypt,
			_creation_time,
			_long_id,
			_support_ticket
        )
        returning
            flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		deleted,
		encode(t, 'base64') as "t",
		encode(d, 'base64') as "d",
		device_id as "deviceId",
		name,
		device_type as "deviceType",
		nickname,
		encrypt,
		creation_time as "creationTime",
		encode(long_id, 'base64') as "longId",
		encode(support_ticket, 'base64') as "supportTicket"
        into result;

        



        perform a0.device_merkle(_account_id);

    
        



        return result;
    end;
        $$
        language plpgsql;
        

drop function if exists a0.device_update; 
        create function a0.device_update(
            _flags int,
	_modified_time timestamptz,
	_created_time timestamptz,
	_account_id int,
	_deleted boolean,
	__t text,
	__d text,
	_device_id int,
	_name text,
	_device_type int,
	_nickname text,
	_encrypt boolean,
	_creation_time timestamptz,
	__long_id text,
	__support_ticket text
        )
        returns a0."Device"
        as $$
        
    declare
        result a0."Device";
        _t bytea;
		_d bytea;
		_long_id bytea;
		_support_ticket bytea;
    begin
        _t := decode(__t, 'base64');
		_d := decode(__d, 'base64');
		_long_id := decode(__long_id, 'base64');
		_support_ticket := decode(__support_ticket, 'base64');
        


       
        


        
        _t := public.next_trev(_t);
        _modified_time := now();
        update a0.device
        set
            flags = _flags,
			modified_time = _modified_time,
			created_time = _created_time,
			deleted = _deleted,
			t = _t,
			d = _d,
			name = _name,
			device_type = _device_type,
			nickname = _nickname,
			encrypt = _encrypt,
			creation_time = _creation_time,
			long_id = _long_id,
			support_ticket = _support_ticket
        where account_id = _account_id and device_id = _device_id
        returning
            flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		deleted,
		encode(t, 'base64') as "t",
		encode(d, 'base64') as "d",
		device_id as "deviceId",
		name,
		device_type as "deviceType",
		nickname,
		encrypt,
		creation_time as "creationTime",
		encode(long_id, 'base64') as "longId",
		encode(support_ticket, 'base64') as "supportTicket"
        into result;

        perform a0.device_merkle(_account_id);

    
        



        return result;
    end;
        $$
        language plpgsql;
        

drop function if exists a0.device_read_by_account_id_and_device_id; 
        create function a0.device_read_by_account_id_and_device_id(
            _account_id int,
	_device_id int
        )
        returns a0."Device"
        as $$
        select
        flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		deleted,
		encode(t, 'base64') as "t",
		encode(d, 'base64') as "d",
		device_id as "deviceId",
		name,
		device_type as "deviceType",
		nickname,
		encrypt,
		creation_time as "creationTime",
		encode(long_id, 'base64') as "longId",
		encode(support_ticket, 'base64') as "supportTicket"
        from a0.device
        where account_id = _account_id and device_id = _device_id;
        $$
        language sql;
        

drop function if exists a0.device_delete_by_account_id_and_device_id; 
        create function a0.device_delete_by_account_id_and_device_id(
            _account_id int,
	_device_id int
        )
        returns void
        as $$
        
    declare
        
        
    begin
        
        


       
        


        

    delete from a0.device
    where account_id = _account_id and device_id = _device_id;

    perform a0.device_merkle(_account_id);
    
        



        
    end;
        $$
        language plpgsql;
        

drop function if exists a0.device_delete_by_account_id; 
        create function a0.device_delete_by_account_id(
            _account_id int
        )
        returns void
        as $$
        
    declare
        
        
    begin
        
        


       
        


        

    delete from a0.device
    where account_id = _account_id;

    perform a0.device_merkle(_account_id);
    
        



        
    end;
        $$
        language plpgsql;
        

drop function if exists a0.device_find_by_account_id; 
        create function a0.device_find_by_account_id(
            _account_id int
        )
        returns setof a0."Device"
        as $$
        select
        flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		deleted,
		encode(t, 'base64') as "t",
		encode(d, 'base64') as "d",
		device_id as "deviceId",
		name,
		device_type as "deviceType",
		nickname,
		encrypt,
		creation_time as "creationTime",
		encode(long_id, 'base64') as "longId",
		encode(support_ticket, 'base64') as "supportTicket"
        from a0.device
        where account_id = _account_id;
        $$
        language sql;
        

drop function if exists a0.device_find_by_account_id_and_device_type; 
        create function a0.device_find_by_account_id_and_device_type(
            _account_id int,
	_device_type int
        )
        returns setof a0."Device"
        as $$
        select
        flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		deleted,
		encode(t, 'base64') as "t",
		encode(d, 'base64') as "d",
		device_id as "deviceId",
		name,
		device_type as "deviceType",
		nickname,
		encrypt,
		creation_time as "creationTime",
		encode(long_id, 'base64') as "longId",
		encode(support_ticket, 'base64') as "supportTicket"
        from a0.device
        where account_id = _account_id and device_type = _device_type;
        $$
        language sql;
        
