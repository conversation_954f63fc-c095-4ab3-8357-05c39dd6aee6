

import Validation from "../system/Validation.mjs";
import { IModel } from "./IModel.mjs";




/* b::enums */
export enum PinFlags {
  createAccount = 1,
}
/* end */


export interface IPin {
    flags?: number;
	modifiedTime?: Date;
	createdTime?: Date;
	codeChallenge?: string;
	email?: string;
	pin?: string;
	expiresAt?: Date;
}


export class Pin 
implements IModel {
    private _state: IPin;

    
/* b::model_public_members */
public createAccount(value?: boolean) {
    if (value !== void 0) {
      if (value === true) {
        this.flags(this.flags() | PinFlags.createAccount);
      } else {
        this.flags(this.flags() & ~PinFlags.createAccount);
      }
    }
    return !!(this.flags() & PinFlags.createAccount);
  }
/* end */

    
    changed = false;

    constructor(state: IPin) {
        this.state(state);
    }

    isUndefined() {
        return this._state !== void 0;
    }

    async validate(skipUndefined?: boolean) {
        let v = new Validation(this, skipUndefined);
        


        return v;
    }

    rtt() {
        return "Pin"; 
    }

    state (value?: IPin) {
        if (value !== undefined) { 
            this._state = value;
            
            


        }
        return this._state;
    }

    flags(value?: number) {
                if (value !== void 0) {
                    if (this.state().flags !== value) {
                        this.state().flags = value;
                        this.changed = true;
                    }
                }
                return this.state().flags;
            };

		modifiedTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().modifiedTime !== value) {
                        this.state().modifiedTime = value;
                        this.changed = true;
                    }
                }
                return this.state().modifiedTime;
            };

		createdTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().createdTime !== value) {
                        this.state().createdTime = value;
                        this.changed = true;
                    }
                }
                return this.state().createdTime;
            };

		codeChallenge(value?: string) {
                if (value !== void 0) {
                    if (this.state().codeChallenge !== value) {
                        this.state().codeChallenge = value;
                        this.changed = true;
                    }
                }
                return this.state().codeChallenge;
            };

		email(value?: string) {
                if (value !== void 0) {
                    if (this.state().email !== value) {
                        this.state().email = value;
                        this.changed = true;
                    }
                }
                return this.state().email;
            };

		pin(value?: string) {
                if (value !== void 0) {
                    if (this.state().pin !== value) {
                        this.state().pin = value;
                        this.changed = true;
                    }
                }
                return this.state().pin;
            };

		expiresAt(value?: Date) {
                if (value !== void 0) {
                    if (this.state().expiresAt !== value) {
                        this.state().expiresAt = value;
                        this.changed = true;
                    }
                }
                return this.state().expiresAt;
            };

    differs(original: Pin) {
        return (
            (this.flags() !== void 0 && this.flags() !== original.flags())
		 || (this.modifiedTime() !== void 0 && this.modifiedTime() !== original.modifiedTime())
		 || (this.createdTime() !== void 0 && this.createdTime() !== original.createdTime())
		 || (this.codeChallenge() !== void 0 && this.codeChallenge() !== original.codeChallenge())
		 || (this.email() !== void 0 && this.email() !== original.email())
		 || (this.pin() !== void 0 && this.pin() !== original.pin())
		 || (this.expiresAt() !== void 0 && this.expiresAt() !== original.expiresAt())
        );
    }







}



export function sanitizeInput(source: Pin, amdin: boolean, mode: string) : IPin;
export function sanitizeInput(source: IPin, admin: boolean, mode: string) : IPin;
export function sanitizeInput(source: Pin | IPin, admin = false, mode="default"): IPin {
    let s: IPin;
    if (source instanceof Pin)
        s = source.state();
    else
        s = source;        
    let t = {} as IPin;
    
    
        
            switch(mode) {
                
                
                
            }
            t.flags = s.flags;
		t.codeChallenge = s.codeChallenge;
		t.email = s.email;
		t.pin = s.pin;
		t.expiresAt = s.expiresAt;
        
    return t;
}

export function sanitizeOutput(source: Pin, amdin: boolean) : IPin;
export function sanitizeOutput(source: IPin, admin: boolean) : IPin;
export function sanitizeOutput(source: Pin | IPin, admin = false): IPin {
    let s: IPin;
    if (source instanceof Pin)
        s = source.state();
    else
        s = source;        
    let t = {} as IPin;
    
    
    
    t.flags = s.flags;	
t.modifiedTime = s.modifiedTime;	
t.createdTime = s.createdTime;	
t.codeChallenge = s.codeChallenge;	
t.email = s.email;	
t.pin = s.pin;	
t.expiresAt = s.expiresAt;
    return t;
}

export function mergeState(dbVersion: IPin, newVersion: IPin) {
    let targetState: IPin = {};
    targetState.flags = newVersion.flags === undefined ? dbVersion.flags : newVersion.flags;
	targetState.modifiedTime = newVersion.modifiedTime === undefined ? dbVersion.modifiedTime : newVersion.modifiedTime;
	targetState.createdTime = newVersion.createdTime === undefined ? dbVersion.createdTime : newVersion.createdTime;
	targetState.codeChallenge = newVersion.codeChallenge === undefined ? dbVersion.codeChallenge : newVersion.codeChallenge;
	targetState.email = newVersion.email === undefined ? dbVersion.email : newVersion.email;
	targetState.pin = newVersion.pin === undefined ? dbVersion.pin : newVersion.pin;
	targetState.expiresAt = newVersion.expiresAt === undefined ? dbVersion.expiresAt : newVersion.expiresAt;
    return targetState;
}

export function merge(dbVersion: Pin, newVersion: Pin) {
    return new Pin(mergeState(dbVersion.state(), newVersion.state()));
}
