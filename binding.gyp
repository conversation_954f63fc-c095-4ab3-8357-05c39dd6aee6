{
  "targets": [
    {
      "cflags_cc": ["-fexceptions -fpermissive -g"],
      "cflags_cc!": ["-fno-exceptions -fpermissive"],
      "target_name": "binary_encoder",
      "sources": [
        "cpp/dependencies/X509.cpp",
        "cpp/dependencies/cloudshared/MYHash.cpp",
        "cpp/dependencies/cloudshared/MYStringUtil.cpp",
        "cpp/dependencies/cloudshared/MYTrev.cpp",
        "cpp/dependencies/cloudshared/base64.cpp",
        "cpp/dependencies/cloudshared/bjson.h",
        "cpp/dependencies/cloudshared/bjson.cpp",
        "cpp/dependencies/cloudshared/MyGlobals.cpp",
        "cpp/src/encode_functions.h",
        "cpp/src/node.cpp",
        "cpp/src/Field.h",
        "cpp/src/getFieldInfo.cpp",
        "cpp/src/getFieldInfo.h",
        "cpp/src/helpers.cpp",
        "cpp/src/helpers.h",
        "cpp/src/decode_functions.h",
        "cpp/src/decodeObjectImpl.cpp",
        "cpp/src/encodeObjectImpl.cpp",
        "cpp/src/resource_ping.cpp",
        "cpp/src/resource_ping256.cpp",
        "cpp/src/account_ping.cpp",
        "cpp/src/sync.cpp",
        "cpp/src/object.cpp",
        "cpp/src/token_and_rtoken.cpp",
        "cpp/src/support_ticket.cpp",
"cpp/src/generate_certificates.cpp"
      ],
      "conditions": [
        ["OS==\"mac\"", {
          "xcode_settings": {
            "GCC_ENABLE_CPP_EXCEPTIONS": "YES"
          }
        }]
      ],
      "include_dirs": [
        "cpp/dependencies/cloudshared",
        "cpp/dependencies",
        "node_modules/napi"
        "cpp/dependencies/cloudshared/util",
        "cpp/dependencies/cloudshared/util/",
        "cpp/dependencies/cloudshared/util/liboauthcpp/src",
        "node_modules/node-addon-api"
      ],
          "defines": [ "NAPI_DISABLE_CPP_EXCEPTIONS", "NDEBUG", "_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS" ]
    }
  ],
}
