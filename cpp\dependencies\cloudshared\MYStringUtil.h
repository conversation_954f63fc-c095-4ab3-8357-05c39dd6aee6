//
//  MYStringUtil.h
//  MyLoApp
//
//  Created by <PERSON> on 6/5/13.
//  Copyright (c) 2013 MyLO Development LLC. All rights reserved.
//

#pragma once

#include <vector>
#include <string>
#include <set>
#include <list>
#include <algorithm>
#include <locale> // std::locale, std::isspace
#include <functional>
#include <cctype>
#include <assert.h>
#include <math.h>
#include <sstream>

#ifdef MYLIO_CLIENT
#include "MYStrings.h"
#endif

#ifndef _ERRNO_T
#define _ERRNO_T
typedef int errno_t;
#endif /* _ERRNO_T */

// Helper for shorthand the call to stl any numeric type to std::string
#define STR(X) std::to_string(X)

namespace MYString
{
    std::vector<std::string> split(const std::string &input, char delimiter);
    std::vector<std::string> splitQuoted(const std::string &input, char delimiter);
    void split(const std::string &input, char delimiter, std::vector<std::string> &result);
    std::set<std::string> splitToSet(const std::string &input, char delimiter, bool trim = false);
    std::set<unsigned int> splitToUIntSet(const std::string &input, char delimiter);
    std::set<int> splitToIntSet(const std::string &input, char delimiter);
    std::list<std::string> splitToList(const std::string &input, char delimiter);
    std::vector<float> splitToFloatVector(const std::string &input, char delimiter);

    std::string toHex(const std::string &in);
    std::string toHex(const std::wstring &in);

    // #ifdef MYLIO_CLIENT
    template <typename TCollection>
    std::string join(const TCollection &collection, const std::string &delimiter)
    {
        bool first = true;
        std::stringstream o;
        for (const auto &elem : collection)
        {
            if (first)
            {
                first = false;
            }
            else
            {
                o << delimiter;
            }

            o << elem;
        }
        return o.str();
    }

    template <typename TIter>
    std::string join(TIter &begin, TIter &end, const std::string &delimiter)
    {
        bool first = true;
        std::stringstream o;
        for (auto iter = begin; iter != end; ++iter)
        {
            if (first)
            {
                first = false;
            }
            else
            {
                o << delimiter;
            }

            o << *iter;
        }
        return o.str();
    }
    // #endif // MYLIO_CLIENT

    std::string fromWString(const std::wstring &source);
    std::wstring toWString(const std::string &source);
    std::string random(int length);

    int countNewLines(const std::string &input);

    bool startsWith(const std::string &haystack, const std::string &startingNeedle);
    bool endsWith(const std::string &haystack, const std::string &endingNeedle);

    // wide string version
    bool startsWith(const std::wstring &haystack, const std::wstring &startingNeedle);
    bool endsWith(const std::wstring &haystack, const std::wstring &endingNeedle);

    // return true if all char in the string are numberic
    bool isNumber(const std::string &input);

    static inline int isspace(int c)
    {
        // This works around a bug in the Windows implementation of std::isspace
        // where it asserts on characters above 0x7F (when sign extended to an int).
        // casting to unsigned char keeps it in the 0-255 range
        return std::isspace((unsigned char)c);
    }

    // trim from start
    static inline std::string &ltrim(std::string &s)
    {
        s.erase(s.begin(), std::find_if(s.begin(), s.end(),
                                        [](int c)
                                        {
                                            return !isspace(c);
                                        }));
        return s;
    }

    // trim from start
    static inline std::string &ltrim(std::string &s, const std::string &removeThis)
    {
        if (startsWith(s, removeThis))
            s.erase(0, removeThis.size());

        return s;
    }

    // trim from start
    static inline std::string ltrim(const std::string &s, const std::string &removeThis)
    {
        std::string tmp = s;

        if (startsWith(tmp, removeThis))
            tmp.erase(0, removeThis.size());

        return tmp;
    }

    // trim from end
    static inline std::string &rtrim(std::string &s)
    {
        s.erase(std::find_if(s.rbegin(), s.rend(),
                             [](int c)
                             {
                                 return !isspace(c);
                             })
                    .base(),
                s.end());
        return s;
    }

    // trim from both ends in place
    static inline std::string &trim(std::string &s)
    {
        return ltrim(rtrim(s));
    }

    // trim from both ends, using a copy constructor
    static inline std::string trim(const std::string &s)
    {
        std::string tmp = s;
        return ltrim(rtrim(tmp));
    }

    static inline std::string trim(std::string &&s)
    {
        return ltrim(rtrim(s));
    }

    static inline std::string &trim(std::string &s, const std::string &textToTrimFromStartAndEnd)
    {
        if (startsWith(s, textToTrimFromStartAndEnd))
            s = s.substr(textToTrimFromStartAndEnd.size());

        if (endsWith(s, textToTrimFromStartAndEnd))
            s = s.substr(0, s.size() - textToTrimFromStartAndEnd.size());

        return ltrim(rtrim(s));
    }

    static inline std::string trim(const std::string &s, const std::string &textToTrimFromStartAndEnd)
    {
        auto tmp = s;
        return trim(tmp, textToTrimFromStartAndEnd);
    }

    bool contains(const std::string &input, const char toFind);
    bool containsIgnoreCase(const std::string &input, const std::string &findThis);

    std::string shrink(const std::string &input, int maxLength);

    std::vector<std::string> explode(const std::string &subject, char delimiter);
    std::string implode(const std::vector<std::string> &strings, char delimiter);
    std::string implode(const std::set<std::string> &strings, char delimiter);
    std::string implode(const std::set<std::string> &strings, const std::string &delimiter);

    std::string uintToString(unsigned int value);
    std::string uint64ToString(uint64_t value);
    std::string pointerToHexString(void *buffer);
    std::string floatToString(float value, int maxFractionalDigits = -1, int minFractionalDigits = -1);

    std::string blobToHexString(const std::vector<uint8_t> &buffer);
    std::string blobToHexString(const std::pair<unsigned char *, unsigned int> buffer);
    std::string blobToHexString(const std::string &blob);
    std::string hexStringToBlob(const std::string &hexString);
    std::string blobToString(const std::string &blob);
    std::string hexStringToBlob(const std::string &hexString);

    std::string fileSizeToString(uint64_t fileSize);

    std::wstring toLower(std::wstring input);

    std::string toLower(std::string input);
    std::string toUpper(std::string input);
    std::string removeSpaces(const std::string &input);
    std::string removeWhiteSpace(const std::string &input);
    std::string removeCrLf(const std::string &input);
    std::string removeQuotes(const std::string &input);

    std::string replace(const std::string &input, const std::string &findThis, const std::string &replaceWith, bool singlePass = true);

    std::string padFront(const std::string &input, const unsigned int totalLenght, const char padWith = ' ');
    std::string padBack(const std::string &input, const unsigned int totalLenght, const char padWith = ' ');

    void appendWithPrefix(std::string &destination, const std::string &prefix, const std::string &appendSource);
    std::string escapeChars(const std::string &input, const char *charsToEscape);
    bool containsFastIgnoreCaseSearchIsLowerCased(const std::string &strHaystack, const std::string &strNeedleInLowerCase);

    inline void reverse(char str[], int length)
    {
        int start = 0;
        int end = length - 1;
        while (start < end)
        {
            std::swap(*(str + start), *(str + end));
            start++;
            end--;
        }
    }

    // Implementation of itoa()
    // This method is used for casting when performance is critical
    template <typename t>
    std::string itoa(t num, int base = 10)
    {
        char str[sizeof(t) * 8 + 1];
        int i = 0;
        bool isNegative = false;

        if (num == 0)
        {
            str[i++] = '0';
            str[i] = '\0';
            return str;
        }

        // Negative only when base is 10
        if (num < 0 && base == 10)
        {
            isNegative = true;
            num = (t)(-1) * num;
        }

        while (num != 0)
        {
            int rem = (int)fmod(num, base);
            rem = abs(rem);
            str[i++] = (rem > 9) ? (rem - 10) + 'a' : rem + '0';
            num = num / base;
        }

        // If number is negative, append '-'
        if (isNegative)
            str[i++] = '-';

        // terminate string
        str[i] = '\0';

        reverse(str, i);

        return str;
    }

#ifdef MYLIO_CLIENT
    //
    // return a trim first line of input text will handle multibyte text
    // we will consider a line break being any of these '\n\r\f' and L'\n\r\f'
    //
    std::string getCleanFirstLine(const std::string &input);
    std::string getCleanText(const std::string &input);

    std::string fileSizeToString(uint64_t fileSize);
    std::string toUpperFirstLetter(const std::string &input);
    std::string transformToLowerCase(const std::string &input);
    std::string transformToUpperCase(const std::string &input);

    std::vector<unsigned char> toHMACsha256(std::vector<unsigned char> key, const std::string &data);
    std::vector<uint8_t> toSha256(const std::string &pass);
#endif // MYLIO_CLIENT

    static inline unsigned char char2nibble(char hex)
    {
        if (hex >= '0' && hex <= '9')
            return hex - '0';
        if (hex >= 'a' && hex <= 'f')
            return hex - 'a' + 10;
        if (hex >= 'A' && hex <= 'F')
            return hex - 'A' + 10;
        assert(false);
        return 0;
    }

    static inline unsigned char hex2byte(const char *hex)
    {
        return (char2nibble(hex[0]) << 4) | char2nibble(hex[1]);
    }

    static inline char charFromNibble(unsigned int nibble)
    {
        static const char *const hex = "0123456789abcdef";
        return hex[nibble];
    }

    static inline void byteToHex(char *dest, unsigned int data)
    {
        static const char *const hex = "0123456789abcdef";

        dest[0] = hex[data >> 4];
        dest[1] = hex[data & 0x0f];
    }

    static inline std::string blobToHexString(const unsigned char *data, const unsigned int len)
    {
        std::string outString;
        outString.resize(len * 2);

        for (unsigned int i = 0; i < len; i++)
        {
            byteToHex(&outString[i * 2], data[i]);
        }

        return outString;
    }

    static inline std::string quickBlobToHexString(const unsigned char *data, const unsigned int len, std::string &outString)
    {
        char *outdata = &outString[0];

        for (unsigned int i = 0; i < len; i++)
        {
            byteToHex(outdata, *data);
            data++;
            outdata += 2;
        }
        return outString;
    }

    std::string escapeToHtmlString(const std::string &input);

    std::string UnicodeToUTF8(const std::wstring &ws);
#ifdef MYLIO_CLIENT
    std::wstring convertToWideString(const std::string &input);
    std::string convertUnicodeEncodingsIfAnyToUTF8Encodings(const std::string &input);
#endif // MYLIO_CLIENT
}

std::string stringFormat(const char *formatText, ...);
std::string stringFormat(const std::string &formatText, ...);

#ifdef MYLIO_CLIENT
std::string stringFormat(const int formatId, ...);
#endif

void cleanupUpNegativeZeroFormatting(std::string &out);

bool stringIsNullOrEmpty(const char *str);
bool stringIsHex(const char *str);
bool stringIsBase64(const char *str);
bool stringAreEqual(const char *str1, const char *str2, bool ignoreCase);
bool stringEndsWith(const char *str, const char *ending, bool ignoreCase);
bool stringSafeCopy(char *dst, size_t dstSize, const char *src);

#define stringCopy(dest, src) stringSafeCopy(dest, sizeof(dest), src)

class LexicographicalVersionCompare
{
public:
    LexicographicalVersionCompare(const std::string &first, const std::string &second, int sect);

    bool firstLessThanorEqualSecond();

private:
    void parse();
    int sections;
    std::string strA;
    std::string strB;
    std::vector<int> parsedA;
    std::vector<int> parsedB;
};
