const AWS = require("aws-sdk");
const fs = require("fs");
const GetURLs = require("get-urls");
const GitHub = require("github-api");
const path = require("path");
const unzipper = require("unzipper");

const args = process.argv.slice(2);

const issueNumber = args[0];

// Set to true to keep zip files after extraction
const keepZips = false;

let logDir = path.join(__dirname, "logs");

if ("LOG_DESTINATION" in process.env) {
  logDir = process.env.LOG_DESTINATION;
}

if (issueNumber === undefined) {
  console.log(
    "Error: Github issue number missing. Please add an issue number to the run"
  );
  console.log(
    'Example usage: "node download-logs.js 45779" will download all logs in the issue 45779 issue body'
  );
  return;
}

logDir = path.join(logDir, issueNumber);

// console.log(`Creating log output directory: ${logDir}`)

if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir);
}

if (process.env.GH_TOKEN === undefined) {
  console.log(
    "Error: GH_TOKEN not set. Set the environment variable GH_TOKEN to your Github token before running"
  );
  return;
}

// Create an empty text body to attach the issue body and comments to - to be parsed for URLs later
let aggregatedTextBody = "";

console.log(
  `Downloading logs for: https://github.com/mylollc/Mylo/issues/${issueNumber}`
);

new GitHub({ token: process.env.GH_TOKEN })
  .getIssues("mylollc", "mylo")
  .getIssue(issueNumber)
  .then((data, err) => {
    aggregatedTextBody = data.data.body;
    new GitHub({ token: process.env.GH_TOKEN })
      .getIssues("mylollc", "mylo")
      .listIssueComments(issueNumber)
      .then((data, err) => {
        const comments = data.data;
        let commentIds = [];
        comments.forEach((comment) => {
          commentIds.push(comment.id);
        });
        let promiseArr = commentIds.map((commentId) => {
          return new GitHub({ token: process.env.GH_TOKEN })
            .getIssues("mylollc", "mylo")
            .getIssueComment(commentId)
            .then((data, err) => {
              aggregatedTextBody += `\n${data.data.body}`;
            });
        });
        Promise.all(promiseArr).then(() => {
          let sanitizedUrls = [];
          const urls = Array.from(GetURLs(aggregatedTextBody));
          urls.forEach((url) => {
            if (
              url.startsWith(
                "https://s3.console.aws.amazon.com/s3/object/mylo_support/logs_v3/"
              )
            ) {
              sanitizedUrls.push(url);
            }
          });
          console.log(
            `\n${sanitizedUrls.length} log files mentioned in issue #${issueNumber} and its comments.\n`
          );
          console.log(`Starting download. This may take some time...\n`);
          sanitizedUrls.forEach((url) => {
            // Create localfile for S3 readstream to write to
            const localZipFile = path.join(logDir, url.split("/").pop());
            console.log(localZipFile);

            // Download object and pipe stream to file
            const key = url
              .split(
                "https://s3.console.aws.amazon.com/s3/object/mylo_support/"
              )
              .pop();
            const params = { Bucket: "mylo_support", Key: key };
            // The bucket is hosted in us-east-1 for some reason
            new AWS.S3({ apiVersion: "2006-03-01", region: "us-east-1" })
              .getObject(params)
              // Create a stream from S3 to the local machine
              .createReadStream()
              // Pipe the stream to the localZipFile path
              .pipe(fs.createWriteStream(localZipFile))
              // When it's finished, unzip
              .on("close", () => {
                fs.createReadStream(localZipFile)
                  .pipe(unzipper.Extract({ path: logDir }))
                  // When it's done unzipping, delete the zip files
                  .on("close", () => {
                    if (keepZips) {
                      console.log(
                        `File ${localZipFile} downloaded, and unzipped.`
                      );
                      return;
                    }
                    fs.unlink(localZipFile, () => {});
                    console.log(
                      `File ${localZipFile} downloaded, unzipped, and removed.`
                    );
                  });
              });
          });
        });
      });
  });
