export enum LicenseFlagsEnum {
    Trial = 0x1,
    Addon = 0x2,
    Storage = 0x4,
    MultiUser = 0x8,
    Business = 0x10,
    Paid = 0x20
}

export class LicenseFlags {
    private state_: number;

    constructor(state: number = 0) {
        this.state_ = state;
    }

    onChange: (flags: number) => void;

    changeFlag(flag: LicenseFlagsEnum, value: boolean) {
        if (value !== void 0) {
            if (value) this.state_ |= flag;
            else this.state_ &= ~flag;
        }
        this.onChange(this.state_);
        return !!(this.state_ & flag);
    }

    multiUser(value: boolean | undefined = undefined) {
        return this.changeFlag(LicenseFlagsEnum.MultiUser, value);
    }

    storage(value: boolean | undefined = undefined) {
        return this.changeFlag(LicenseFlagsEnum.Storage, value);
    }

    addon(value: boolean | undefined = undefined) {
        return this.changeFlag(LicenseFlagsEnum.Addon, value);
    }

    trial(value: boolean | undefined = undefined) {
        return this.changeFlag(LicenseFlagsEnum.Trial, value);
    }

    paid(value: boolean | undefined = undefined) {
        return this.changeFlag(LicenseFlagsEnum.Paid, value);
    }
}
