

    drop view if exists a0."UserProperty" cascade;

    create or replace view a0."UserProperty" as
    select
    flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		deleted,
		encode(t, 'base64') as "t",
		encode(d, 'base64') as "d",
		encode(user_property_id, 'base64') as "userPropertyId",
		name,
		value
    from a0.user_property;
    

drop function if exists a0.user_property_create; 
        create function a0.user_property_create(
            _flags int,
	_modified_time timestamptz,
	_created_time timestamptz,
	_account_id int,
	_deleted boolean,
	__t text,
	__d text,
	__user_property_id text,
	_name text,
	_value text
        )
        returns a0."UserProperty"
        as $$
        
    declare
        result a0."UserProperty";
        _t bytea;
		_d bytea;
		_user_property_id bytea;
    begin
        _t := decode(__t, 'base64');
		_d := decode(__d, 'base64');
		_user_property_id := decode(__user_property_id, 'base64');
        
/* b::on_create_decoded */
_user_property_id := coalesce(_user_property_id, digest(_name::bytea, 'sha1'));
/* end */

       
        


        
        
        
        _t := public.new_trev_hash(_user_property_id, 34);
        _modified_time := coalesce(_modified_time, now());
        _created_time := coalesce(_created_time, now());
        insert into a0.user_property (
            flags,
	modified_time,
	created_time,
	account_id,
	deleted,
	t,
	d,
	user_property_id,
	name,
	value
        )
        values(
            _flags,
			_modified_time,
			_created_time,
			_account_id,
			_deleted,
			_t,
			_d,
			_user_property_id,
			_name,
			_value
        )
        returning
            flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		deleted,
		encode(t, 'base64') as "t",
		encode(d, 'base64') as "d",
		encode(user_property_id, 'base64') as "userPropertyId",
		name,
		value
        into result;

        



        perform a0.user_property_merkle(_account_id);

    
        



        return result;
    end;
        $$
        language plpgsql;
        

drop function if exists a0.user_property_update; 
        create function a0.user_property_update(
            _flags int,
	_modified_time timestamptz,
	_created_time timestamptz,
	_account_id int,
	_deleted boolean,
	__t text,
	__d text,
	__user_property_id text,
	_name text,
	_value text
        )
        returns a0."UserProperty"
        as $$
        
    declare
        result a0."UserProperty";
        _t bytea;
		_d bytea;
		_user_property_id bytea;
    begin
        _t := decode(__t, 'base64');
		_d := decode(__d, 'base64');
		_user_property_id := decode(__user_property_id, 'base64');
        


       
        


        
        _t := public.next_trev(_t);
        _modified_time := now();
        update a0.user_property
        set
            flags = _flags,
			modified_time = _modified_time,
			created_time = _created_time,
			deleted = _deleted,
			t = _t,
			d = _d,
			name = _name,
			value = _value
        where account_id = _account_id and user_property_id = _user_property_id
        returning
            flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		deleted,
		encode(t, 'base64') as "t",
		encode(d, 'base64') as "d",
		encode(user_property_id, 'base64') as "userPropertyId",
		name,
		value
        into result;

        perform a0.user_property_merkle(_account_id);

    
        



        return result;
    end;
        $$
        language plpgsql;
        

drop function if exists a0.user_property_read_by_account_id_and_user_property_id; 
        create function a0.user_property_read_by_account_id_and_user_property_id(
            _account_id int,
	__user_property_id text
        )
        returns a0."UserProperty"
        as $$
        select
        flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		deleted,
		encode(t, 'base64') as "t",
		encode(d, 'base64') as "d",
		encode(user_property_id, 'base64') as "userPropertyId",
		name,
		value
        from a0.user_property
        where account_id = _account_id and user_property_id = decode(__user_property_id, 'base64');
        $$
        language sql;
        

drop function if exists a0.user_property_delete_by_account_id_and_user_property_id; 
        create function a0.user_property_delete_by_account_id_and_user_property_id(
            _account_id int,
	__user_property_id text
        )
        returns void
        as $$
        
    declare
        
        _user_property_id bytea;
    begin
        _user_property_id := decode(__user_property_id, 'base64');
        


       
        


        

    delete from a0.user_property
    where account_id = _account_id and user_property_id = _user_property_id;

    perform a0.user_property_merkle(_account_id);
    
        



        
    end;
        $$
        language plpgsql;
        

drop function if exists a0.user_property_delete_by_account_id; 
        create function a0.user_property_delete_by_account_id(
            _account_id int
        )
        returns void
        as $$
        
    declare
        
        
    begin
        
        


       
        


        

    delete from a0.user_property
    where account_id = _account_id;

    perform a0.user_property_merkle(_account_id);
    
        



        
    end;
        $$
        language plpgsql;
        

drop function if exists a0.user_property_find_by_account_id; 
        create function a0.user_property_find_by_account_id(
            _account_id int
        )
        returns setof a0."UserProperty"
        as $$
        select
        flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		deleted,
		encode(t, 'base64') as "t",
		encode(d, 'base64') as "d",
		encode(user_property_id, 'base64') as "userPropertyId",
		name,
		value
        from a0.user_property
        where account_id = _account_id;
        $$
        language sql;
        

drop function if exists a0.user_property_find_by_name; 
        create function a0.user_property_find_by_name(
            _name text
        )
        returns setof a0."UserProperty"
        as $$
        select
        flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		deleted,
		encode(t, 'base64') as "t",
		encode(d, 'base64') as "d",
		encode(user_property_id, 'base64') as "userPropertyId",
		name,
		value
        from a0.user_property
        where name = _name;
        $$
        language sql;
        
