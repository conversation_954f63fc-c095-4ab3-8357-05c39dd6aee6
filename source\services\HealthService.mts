import _ = require("lodash");
import { Context } from "../system/Context.mjs";
import { query, cs as postgresCS } from "../system/Postgres.mjs";
import { config, getServices } from "../system/Config.mjs";
import { error, makeError } from "../system/error.mjs";
import { Account } from "../models/Account.model.mjs";
import { ids } from "../system/Strings.mjs";
import { result } from "lodash";

export const HealthGroups = {
  Account: "account",
  Resource: "resource",
  Telemetry: "telemetry",
};

export class HealthService {
  async status(context: Context) {
    let results = { status: {}, errors: {} };
    let error = false;
    let map = new Map<string, string[]>();
    for (let shard of Object.keys(config.connectionStrings)) {
      let cs = postgresCS(context, shard);
      let siblings = map.get(cs);
      if (!siblings) {
        siblings = [shard];
        map.set(cs, siblings);
      } else {
        siblings.push(shard);
      }
    }

    for (let [cs, shards] of map) {
      let selects = [];
      for (let shard of shards) {
        if (shard === "a0") {
          selects.push(
            `select account_id from a0.account where account_id = 1`
          );
        } else if (shard.startsWith("x")) {
          selects.push(
            `select account_id from ${shard}.b0ff_metadata where account_id = 1`
          );
        }
      }
      let select = `${selects.join("\nunion all\n")};`;
      let start = new Date().valueOf();
      try {
        let r = await query(context, select, [], cs);
        let end = new Date().valueOf();
        for (let shard of shards) {
          results.status[shard] = {
            operational: true,
            latency: `${end - start}ms`,
          };
        }
      } catch (err) {
        let end = new Date().valueOf();
        for (let shard of shards) {
          results.status[shard] = {
            operational: false,
            latency: `${end - start}ms`,
          };
          results.errors[shard] = err.cause;
          throw makeError(
            500,
            "HEALTH_SERVICE",
            "Unable to connect to database"
          );
          error = true;
        }
      }
    }

    return results.status;
  }
}

export let healthService = new HealthService();
