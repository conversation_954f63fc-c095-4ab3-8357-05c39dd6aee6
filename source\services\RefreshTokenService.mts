import { RefreshToken, merge } from "../models/RefreshToken.model.mjs";
import { error } from "../system/error.mjs";
import { RefreshTokenRestDataService } from "../dataServices/RefreshToken.rest.dataService.mjs";
import { Context } from "../system/Context.mjs";
import { Itx } from "../system/data.mjs";
import { check } from "../system/check.mjs";
import { ids } from "../system/Strings.mjs";

export class RefreshTokenService {
  constructor(
    private dataService: RefreshTokenRestDataService,
    private tx: Itx<RefreshToken>
  ) { }

  public async create(
    context: Context,
    aid: number,
    refreshToken: RefreshToken
  ) {
    refreshToken.accountId(aid);
    await check(refreshToken);
    return this.dataService.create(context, refreshToken);
  }

  public list(context: Context, aid: number) {
    return this.dataService.findByAccountId(context, aid);
  }

  public tryRead(context: Context, aid: number, idp: string, task: string) {
    return this.dataService.readByAccountIdAndIdpAndTask(
      context,
      aid,
      idp,
      task
    );
  }

  public read(context: Context, aid: number, idp: string, task: string) {
    return this.tryRead(context, aid, idp, task).then((rt) => {
      if (!rt) {
        return error<RefreshToken>(404, ids.REFRESHTOKEN_NOT_VALID_FOR_ACCOUNT);
      }
      return rt;
    }) as Promise<RefreshToken>;
  }

  public async update(context: Context, aid: number, rt: RefreshToken) {
    return this.tx(context, async () => {
      const dbVersion = await this.dataService.readByAccountIdAndIdpAndTask(
        context,
        rt.accountId(),
        rt.idp(),
        rt.task()
      );
      rt = merge(dbVersion, rt);
      await check(rt);
      return this.dataService.update(context, rt);
    });
  }

  public delete(context: Context, aid: number, idp: string, task: string) {
    return this.dataService.deleteByAccountIdAndIdpAndTask(
      context,
      aid,
      idp,
      task
    );
  }
}
