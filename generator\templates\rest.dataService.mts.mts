import { DataServiceTs } from "../DataServiceTs.mjs";
import { Entity } from "../Entity.mjs";
import { ScriptHelper } from "../ScriptHelper.mjs";

export function template(entity: Entity, h: ScriptHelper) {
  let ts = new DataServiceTs("rest", entity, h);
  let t = entity.fields.find((f) => f.name === "t");

  ts.func("create");
  ts.mfunc("read", entity.keys);
  ts.func("update");
  ts.mfunc("delete", entity.keys);
  ts.mfunc("find", entity.finds, true);
  if (t)
    ts.ffunc(
      "merkle",
      entity.fields.filter((f) => f.name === "account_id")
    );
  return ts.toString();
}
