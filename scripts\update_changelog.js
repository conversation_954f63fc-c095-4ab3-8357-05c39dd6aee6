const fs = require("fs");
const moment = require("moment");
const yargs = require("yargs");
const { spawn } = require("child_process");

function main() {
  var service = process.argv[2];
  var env = process.argv[3];
  var version = process.argv[4] || "";

  if (!service) {
    exit("You must provide a service (account, resource, telemetry)", 1);
  }
  if (!env) {
    exit("You must provide an environment (test, prod)", 1);
  }

  let changelog;

  const CHANGELOG_FILENAME = `../CHANGELOG.${env}.${service}${version}.md`;
  const TODAY = moment().format("YYYY-MM-DD");
  const TODAY_REGEXP = new RegExp(`(## \\[${TODAY}\\])`);
  const TOP_OF_FILE_REGEXP = new RegExp(
    `(# Changelog ${env}.${service}${version})`
  );

  console.log("Reading Changelog...");

  return readFile(CHANGELOG_FILENAME)
    .then((data) => {
      changelog = data;

      console.log("Fetching new commits...");

      let lastCommit = findLastCommitInChangelog(changelog);
      return getNewCommits(lastCommit);
    })
    .then(filterOutDeploymentCommits)
    .then((commits) => {
      if (!commits) {
        exit("No new commits found.");
      }

      let entryForToday = changelog.match(TODAY_REGEXP);
      let newChangelog;

      if (entryForToday && entryForToday.length) {
        let index = entryForToday.index + entryForToday[0].length;
        newChangelog =
          changelog.slice(0, index) + `\n${commits}` + changelog.slice(index);
      } else {
        let topOfFile = changelog.match(TOP_OF_FILE_REGEXP);
        if (topOfFile && topOfFile.length) {
          let index = topOfFile.index + topOfFile[0].length;
          newChangelog =
            changelog.slice(0, index) +
            `\n\n## [${TODAY}]\n${commits}` +
            changelog.slice(index);
        } else {
          console.log(`Poorly formatted changelog: ${CHANGELOG_FILENAME}`);
          exit("Done.", 1);
        }
      }

      console.log("Updating Changelog...");
      fs.writeFileSync(CHANGELOG_FILENAME, newChangelog);

      console.log("Commiting changes...");
      return gitAdd(CHANGELOG_FILENAME);
    })
    .then(() =>
      gitCommit(`Deploy ${service} microservice to ebs ${env} environment`)
    )
    .then(() => {
      console.log("Tagging commit...");
      let timestamp = moment().format("YYYYMMDD_HHmmss");
      let tag = `${service}${version}_${timestamp}`;

      return gitTag(
        tag,
        `Deploy ${service}${version} microservice to ebs ${env} environment`
      );
    })
    .then(() => exit("Done."))
    .catch((error) => {
      exit(error, 1);
    });
}

function filterOutDeploymentCommits(commits) {
  let filtered = commits.split("\n").filter((commit) => {
    return !commit.match(/- \[[a-z|0-9]+\] Deploy .*/);
  });

  return filtered.join("\n");
}

function runCommand(command, args) {
  return new Promise((resolve, reject) => {
    let proc = spawn(command, args);
    let result = "";

    proc.stdout.on("data", (data) => (result += data));
    proc.stderr.on("data", (data) => reject(data));
    proc.on("close", (code) => resolve(result));
  });
}

function getNewCommits(lastCommit) {
  let args = [
    "--no-pager",
    "log",
    "--no-merges",
    "--pretty=format:- [%h] %s",
    lastCommit ? `${lastCommit}..HEAD` : "HEAD",
  ];

  return runCommand("git", args);
}

function gitAdd(files) {
  let args = ["add", files];

  return runCommand("git", args);
}

function gitCommit(message) {
  let args = ["commit", "-m", message.trim()];

  return runCommand("git", args);
}

function gitTag(tag, message) {
  let args = ["tag", "-a", "-m", message.trim(), tag.trim()];

  runCommand("git", args);
}

function readFile(filename) {
  return new Promise((resolve, reject) => {
    fs.readFile(filename, (error, data) => {
      if (error) {
        return reject(error);
      }

      return resolve(data.toString());
    });
  });
}

function findLastCommitInChangelog(changelog) {
  // finds the first (latest) commit in a given changelog
  let match = changelog.match(/- \[([a-z|0-9]{7})\].*/);

  if (match && match.length >= 1) {
    return match[1];
  }

  return undefined;
}

function exit(message, status) {
  console.log(message);
  console.log("Exiting...");

  process.exit(status || 0);
}

main();
