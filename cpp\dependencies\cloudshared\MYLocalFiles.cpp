#include "MYLocalFiles.h"
#include "MYLiterals.h"

MYLocalFiles g_emptyMYLocalFiles(true);

////////////////////////////////////////////////////////////////////
//
// MYLocalFile
//
////////////////////////////////////////////////////////////////////
MYLocalFile::MYLocalFile(MYLocalFile &&other)
{
    _dirty = other._dirty;
    _mediaType = other._mediaType;
    if (_dirty)
    {
        return;
    }

    _format = other._format;

    _cropZoomFactor = other._cropZoomFactor;
    _dataHash = other._dataHash;
    _visualEditHash = other._visualEditHash;

    _basisDataHash = other._basisDataHash;
    _alURL = other._alURL;
    _byocId = other._byocId;
    _parseHash = other._parseHash;
    _flags = other._flags;

    _genVersion = other._genVersion;
    _fileSize = other._fileSize;
    _width = other._width;
    _height = other._height;

    _modifiedFields = other._modifiedFields;
}

MYLocalFile::MYLocalFile(const MYLocalFile &other)
{
    _dirty = other._dirty;
    _mediaType = other._mediaType;
    if (_dirty)
    {
        return;
    }

    _format = other._format;

    _cropZoomFactor = other._cropZoomFactor;
    _dataHash = other._dataHash;
    _visualEditHash = other._visualEditHash;

    _basisDataHash = other._basisDataHash;
    _alURL = other._alURL;
    _byocId = other._byocId;
    _parseHash = other._parseHash;
    _flags = other._flags;

    _genVersion = other._genVersion;
    _fileSize = other._fileSize;
    _width = other._width;
    _height = other._height;

    _modifiedFields = other._modifiedFields;
}

MYLocalFile &MYLocalFile::operator=(const MYLocalFile &other)
{
    _dirty = other._dirty;
    _mediaType = other._mediaType;
    if (_dirty)
    {
        return *this;
    }

    _format = other._format;

    _cropZoomFactor = other._cropZoomFactor;
    _dataHash = other._dataHash;
    _visualEditHash = other._visualEditHash;

    _basisDataHash = other._basisDataHash;
    _alURL = other._alURL;
    _byocId = other._byocId;
    _parseHash = other._parseHash;
    _flags = other._flags;

    _genVersion = other._genVersion;
    _fileSize = other._fileSize;
    _width = other._width;
    _height = other._height;

    _modifiedFields = other._modifiedFields;

    return *this;
}

MYLocalFile &MYLocalFile::operator=(MYLocalFile &&other)
{
    _dirty = other._dirty;
    _mediaType = other._mediaType;
    if (_dirty)
    {
        return *this;
    }

    _format = other._format;

    _cropZoomFactor = other._cropZoomFactor;
    _dataHash = other._dataHash;
    _visualEditHash = other._visualEditHash;

    _basisDataHash = other._basisDataHash;
    _alURL = other._alURL;
    _byocId = other._byocId;
    _parseHash = other._parseHash;
    _flags = other._flags;

    _genVersion = other._genVersion;
    _fileSize = other._fileSize;
    _width = other._width;
    _height = other._height;

    _modifiedFields = other._modifiedFields;

    return *this;
}

void MYLocalFile::clear()
{
    _dirty = true;
}

void MYLocalFile::prepareInternal()
{
    _dirty = false;
    _format = MYStringRefEmpty;

    _cropZoomFactor = 1.0f;
    _dataHash = MYHashRefEmpty;
    _visualEditHash = MYHashRefEmpty;

    _basisDataHash = MYHashRefEmpty;
    _alURL = MYStringRefEmpty;
    _byocId = MYStringRefEmpty;
    _parseHash = MYHashRefEmpty;
    _flags = 0;

    _genVersion = 0;
    _fileSize = 0;
    _width = 0;
    _height = 0;

    _modifiedFields.reset();
}

MYLocalFile::MYLocalFile(MYMediaFileType::Enum mediaType, MYBJsonIterator &iter, const MYBJsonIterator &end)
{
    prepare();
    _mediaType = mediaType;
    deserializeFromBJson(iter, end);
}

void MYLocalFile::deserializeFromBJson(MYBJsonIterator &iter, const MYBJsonIterator &end)
{
    for (; iter != end && !iter->isSeparatorOrEnd(); ++iter)
    {
        auto key = iter->key();
        switch (key)
        {
        case MYLiterals::LocalFile::dataHash:
            _dataHash = iter->asUint32();
            break;

        case MYLiterals::LocalFile::visualEditHash:
            _visualEditHash = iter->asUint32();
            break;

        case MYLiterals::LocalFile::basisDataHash:
            _basisDataHash = iter->asUint32();
            break;

        case MYLiterals::LocalFile::parseHash:
            _parseHash = iter->asUint32();
            break;

        case MYLiterals::LocalFile::flags:
            _flags = (iter->asUint32() + 1);
            break;

        case MYLiterals::LocalFile::cropZoomFactor:
            _cropZoomFactor = iter->asFloat();
            break;

        case MYLiterals::LocalFile::format:
            _format = iter->asUint32();
            break;

        case MYLiterals::LocalFile::alUrl:
            _alURL = iter->asUint32();
            break;

        case MYLiterals::LocalFile::byocId:
            _byocId = iter->asUint32();
            break;

        case MYLiterals::LocalFile::fileSize:
            _fileSize = iter->asUint64();
            break;

        case MYLiterals::LocalFile::width:
            _width = iter->asUint32();
            break;

        case MYLiterals::LocalFile::height:
            _height = iter->asUint32();
            break;

        case MYLiterals::LocalFile::genVersion:
            _genVersion = iter->asUint32();
            break;

        default:
            assert(false);
            break;
        }
    }

    assert(iter->isSeparatorOrEnd());
}

MYLocalFile &MYLocalFile::operator+=(const MYLocalFile &other)
{
    if (other._dirty)
        return *this;

    _dirty = false;
    _fileSize += other._fileSize;

    return *this;
}

const std::string &MYLocalFile::getALURL() const
{
    return getMyLocalFiles()->getString(_alURL, MYMediaFileType::NoType);
}

bool MYLocalFile::setALURL(const std::string &newALUrl)
{
    auto ref = getMyLocalFiles()->getOrCreateStringRef(newALUrl, MYMediaFileType::NoType);
    if (ref == _alURL)
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::alUrl] = true;
    _alURL = ref;
    return true;
}

bool MYLocalFile::setALURL(std::string &&newALUrl)
{
    auto ref = getMyLocalFiles()->getOrCreateStringRef(newALUrl, MYMediaFileType::NoType);
    if (ref == _alURL)
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::alUrl] = true;
    _alURL = ref;
    return true;
}

const std::string &MYLocalFile::getByocId() const
{
    return getMyLocalFiles()->getString(_byocId, MYMediaFileType::NoType);
}

bool MYLocalFile::setByocId(const std::string &newByocId)
{
    auto ref = getMyLocalFiles()->getOrCreateStringRef(newByocId, MYMediaFileType::NoType);
    if (ref == _byocId)
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::byocId] = true;
    _byocId = ref;
    return true;
}

bool MYLocalFile::setByocId(std::string &&newByocId)
{
    auto ref = getMyLocalFiles()->getOrCreateStringRef(newByocId, MYMediaFileType::NoType);
    if (ref == _byocId)
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::byocId] = true;
    _byocId = ref;
    return true;
}

const MYHash &MYLocalFile::getDataHash() const
{
    return getMyLocalFiles()->getHash(_dataHash);
}

bool MYLocalFile::setDataHash(const MYHash &newDataHash)
{
    auto ref = getHashRef(newDataHash);
    if (ref == _dataHash)
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::dataHash] = true;
    _dataHash = ref;
    return true;
}

const MYHash &MYLocalFile::getVisualEditHash() const
{
    return getMyLocalFiles()->getHash(_visualEditHash);
}

bool MYLocalFile::setVisualEditHash(const MYHash &newVisualEditHash)
{
    auto ref = getHashRef(newVisualEditHash);
    if (ref == _visualEditHash)
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::visualEditHash] = true;
    _visualEditHash = ref;
    return true;
}

const MYHash &MYLocalFile::getBasisDataHash() const
{
    return getMyLocalFiles()->getHash(_basisDataHash);
}

bool MYLocalFile::setBasisDataHash(const MYHash &newBasisDataHash)
{
    auto ref = getHashRef(newBasisDataHash);
    if (ref == _basisDataHash)
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::basisDataHash] = true;
    _basisDataHash = ref;
    return true;
}

bool MYLocalFile::setCropZoomFactor(float newCropZoomFactor)
{
    if (newCropZoomFactor == _cropZoomFactor)
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::cropZoomFactor] = true;
    _cropZoomFactor = newCropZoomFactor;
    return true;
}

const MYHash &MYLocalFile::getParseHash() const
{
    return getMyLocalFiles()->getHash(_parseHash);
}

bool MYLocalFile::setParseHash(const MYHash &newParseHash)
{
    auto ref = getHashRef(newParseHash);
    if (ref == _parseHash)
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::parseHash] = true;
    _parseHash = ref;
    return true;
}

bool MYLocalFile::setParsability(MYParsability newParsability)
{
    if (newParsability == getParsability())
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::flags] = true;
    if (newParsability == MYParsability::NotAttempted)
    {
        _flags.reset((int)MYLocalFileFlags::parseAttempted);
        _flags.reset((int)MYLocalFileFlags::parseFailed);
    }
    else if (newParsability == MYParsability::NotParsable)
    {
        _flags.set((int)MYLocalFileFlags::parseAttempted);
        _flags.set((int)MYLocalFileFlags::parseFailed);
    }
    else if (newParsability == MYParsability::Parsable)
    {
        _flags.set((int)MYLocalFileFlags::parseAttempted);
        _flags.reset((int)MYLocalFileFlags::parseFailed);
    }
    else
    {
        assert(false);
        _flags.reset((int)MYLocalFileFlags::parseAttempted);
        _flags.reset((int)MYLocalFileFlags::parseFailed);
    }

    return true;
}

bool MYLocalFile::setIsDraft(bool newIsDraft)
{
    if (newIsDraft == getIsDraft())
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::flags] = true;
    if (newIsDraft)
        _flags.set((int)MYLocalFileFlags::isDraft);
    else
        _flags.reset((int)MYLocalFileFlags::isDraft);

    return true;
}

bool MYLocalFile::setIsHardWant(bool newHardWant)
{
    if (newHardWant == getIsHardWant())
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::flags] = true;
    if (newHardWant)
        _flags.set((int)MYLocalFileFlags::hardWant);
    else
        _flags.reset((int)MYLocalFileFlags::hardWant);

    return true;
}

bool MYLocalFile::setIsHardDontWant(bool newHardDontWant)
{
    if (newHardDontWant == getIsHardDontWant())
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::flags] = true;
    if (newHardDontWant)
        _flags.set((int)MYLocalFileFlags::hardDontWant);
    else
        _flags.reset((int)MYLocalFileFlags::hardDontWant);

    return true;
}

bool MYLocalFile::getInInternalData() const
{
    if ((getMediaType() == MYMediaFileType::Preview) || (getMediaType() == MYMediaFileType::Thumbnail))
    {
        return true;
    }

    return getInInternalDataStorage();
}

bool MYLocalFile::setInInternalData(bool inLocalInternalData)
{
    if ((getMediaType() == MYMediaFileType::Preview) || (getMediaType() == MYMediaFileType::Thumbnail))
    {
        return false;
    }

    return setInInternalDataStorage(inLocalInternalData);
}

bool MYLocalFile::setInInternalDataStorage(bool newInInternalDataStorage)
{
    if (newInInternalDataStorage == getInInternalDataStorage())
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::flags] = true;
    if (newInInternalDataStorage)
        _flags.set((int)MYLocalFileFlags::inInternalData);
    else
        _flags.reset((int)MYLocalFileFlags::inInternalData);

    return true;
}

bool MYLocalFile::setFileSize(uint64_t newFileSize)
{
    if (newFileSize == _fileSize)
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::fileSize] = true;
    _fileSize = newFileSize;
    return true;
}

bool MYLocalFile::setWidth(unsigned int newWidth)
{
    if (newWidth == _width)
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::width] = true;
    _width = newWidth;
    return true;
}

bool MYLocalFile::setHeight(unsigned int newHeight)
{
    if (newHeight == _height)
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::height] = true;
    _height = newHeight;
    return true;
}

const std::string &MYLocalFile::getFormat() const
{
    return getMyLocalFiles()->getString(_format, _mediaType);
}

bool MYLocalFile::setFormat(const std::string &newFormat)
{
    auto ref = getMyLocalFiles()->getOrCreateStringRef(newFormat, _mediaType);
    if (ref == _format)
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::format] = true;
    _format = ref;
    return true;
}

bool MYLocalFile::setFormat(std::string &&newFormat)
{
    auto ref = getMyLocalFiles()->getOrCreateStringRef(std::move(newFormat), _mediaType);
    if (ref == _format)
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::format] = true;
    _format = ref;
    return true;
}

MYHashRef MYLocalFile::getHashRef(const MYHash &hash)
{
    return getMyLocalFiles()->getOrCreateHashRef(hash);
}

MYLocalFiles *MYLocalFile::getMyLocalFiles()
{
    static_assert(offsetof(MYKnownMediaForLocalFiles, _knownMediaFiles) == 0, "Unexpected offset");

    assert(_mediaType != MYMediaFileType::NoType);

    auto offset = (int)(_mediaType)-1;
    size_t thisRelativeOffset = sizeof(MYLocalFile) * offset;

    uint8_t *knownMediaFilesStart = ((uint8_t *)this) - thisRelativeOffset;
    uint8_t *localFilesStart = knownMediaFilesStart;

    MYKnownMediaForLocalFiles *knownMediaForLocalFiles = (MYKnownMediaForLocalFiles *)localFilesStart;
    MYLocalFiles *localFiles = static_cast<MYLocalFiles *>(knownMediaForLocalFiles);
    assert(&localFiles->_knownMediaFiles[_mediaType - 1] == this);

    return localFiles;
}

const MYLocalFiles *MYLocalFile::getMyLocalFiles() const
{
    return const_cast<MYLocalFile *>(this)->getMyLocalFiles();
}

////////////////////////////////////////////////////////////////////
//
// MYLocalFiles
//
//////////////////////////////////////////////////////////////////
const MYLocalFile &MYLocalFiles::getMediaFileOrEmpty(MYMediaFileType::Enum mediaFileType) const
{
    assert(mediaFileType != MYMediaFileType::NoType);
    if (_knownMediaFiles[(int)mediaFileType - 1]._dirty)
    {
        return *g_emptyMYLocalFiles.getMediaFileOrNull(mediaFileType);
    }

    return _knownMediaFiles[(int)mediaFileType - 1];
}

MYLocalFile *MYLocalFiles::getMediaFileOrNull(MYMediaFileType::Enum mediaFileType)
{
    assert(mediaFileType != MYMediaFileType::NoType);
    if (_knownMediaFiles[(int)mediaFileType - 1]._dirty)
    {
        return nullptr;
    }

    return &_knownMediaFiles[(int)mediaFileType - 1];
}

const MYLocalFile *MYLocalFiles::getMediaFileOrNull(MYMediaFileType::Enum mediaFileType) const
{
    assert(mediaFileType != MYMediaFileType::NoType);
    if (_knownMediaFiles[(int)mediaFileType - 1]._dirty)
    {
        return nullptr;
    }

    assert(_knownMediaFiles[(int)mediaFileType - 1].getMyLocalFiles() == this);
    return &_knownMediaFiles[(int)mediaFileType - 1];
}

MYLocalFile *MYLocalFiles::getOrCreateMediaFile(MYMediaFileType::Enum mediaFileType)
{
    assert(mediaFileType != MYMediaFileType::NoType);
    if (_knownMediaFiles[(int)mediaFileType - 1]._dirty)
    {
        _modifiedFields[MYLiterals::Files::files] = true;
        _knownMediaFiles[(int)mediaFileType - 1].prepare();
    }

    assert(_knownMediaFiles[(int)mediaFileType - 1].getMyLocalFiles() == this);
    return &_knownMediaFiles[(int)mediaFileType - 1];
}

bool MYLocalFiles::removeMediaFileForType(MYMediaFileType::Enum mediaFileType)
{
    assert(mediaFileType != MYMediaFileType::NoType);
    if (_knownMediaFiles[(int)mediaFileType - 1]._dirty)
    {
        return false;
    }

    _knownMediaFiles[(int)mediaFileType - 1].clear();
    _modifiedFields[MYLiterals::Files::files] = true;
    return true;
}

const NeedsBits MYLocalFiles::getSupportMediaTypes() const
{
    NeedsBits supported(0);

    for (size_t i = 0; i < _knownMediaFiles.size(); i++)
    {
        if (!_knownMediaFiles[i].empty())
        {
            supported |= NeedsBits((uint8_t)i + 1);
        }
    }
    return supported;
}

MYHashRef MYLocalFiles::getOrCreateHashRef(const MYHash &hash)
{
    if (hash.empty())
    {
        return MYHashRefEmpty;
    }

    auto iter = std::find(_hashMap.begin(), _hashMap.end(), hash);
    if (iter == _hashMap.end())
    {
        _modifiedFields[MYLiterals::Files::hashMap] = true;
        _hashMap.emplace_back(hash);
        return (MYHashRef)_hashMap.size() - 1;
    }

    return (MYHashRef)(iter - _hashMap.begin());
}

MYStringRef MYLocalFiles::getOrCreateStringRef(const std::string &string, MYMediaFileType::Enum fileType)
{
    auto internedId = MYFileFormatInterner::getIdFromFormat(string, fileType);
    if (internedId)
    {
        return internedId;
    }

    auto iter = std::find(_stringMap.begin(), _stringMap.end(), string);
    if (iter == _stringMap.end())
    {
        _modifiedFields[MYLiterals::Files::stringMap] = true;
        _stringMap.emplace_back(string);
        return (MYStringRef)_stringMap.size() - 1;
    }

    return (MYStringRef)(iter - _stringMap.begin());
}

MYStringRef MYLocalFiles::getOrCreateStringRef(std::string &&string, MYMediaFileType::Enum fileType)
{
    auto internedId = MYFileFormatInterner::getIdFromFormat(string, fileType);
    if (internedId)
    {
        return internedId;
    }

    auto iter = std::find(_stringMap.begin(), _stringMap.end(), string);
    if (iter == _stringMap.end())
    {
        _modifiedFields[MYLiterals::Files::stringMap] = true;
        _stringMap.emplace_back(std::move(string));
        return (MYStringRef)_stringMap.size() - 1;
    }

    return (MYStringRef)(iter - _stringMap.begin());
}

const MYHash &MYLocalFiles::getHash(MYHashRef hashRef) const
{
    if (hashRef == MYHashRefEmpty)
    {
        return g_emptyHash;
    }

    if (hashRef < (MYHashRef)_hashMap.size())
    {
        return _hashMap[hashRef];
    }
    else
    {
        assert(false);
        return MYHash::emptyHash();
    }
}

const std::string &MYLocalFiles::getString(MYStringRef stringRef, MYMediaFileType::Enum fileType) const
{
    if (MYFileFormatInterner::isInternedId(stringRef))
    {
        return MYFileFormatInterner::getFormatFromId(stringRef, fileType);
    }

    if (stringRef < (MYStringRef)_stringMap.size())
    {
        return _stringMap[stringRef];
    }
    else
    {
        assert(false);
        return g_emptyString;
    }
}

MYLocalFiles::MYLocalFiles(bool emptyInitializedStructure)
{
    initLocalFileArray();
    for (auto &file : _knownMediaFiles)
    {
        file.prepare();
    }
}

// MYLocalFiles::MYLocalFiles(MYBJsonIterator& iter, const MYBJsonIterator& end, bool reInit)
//{
//     _hashMap.reserve(16);
//     deserializeFromBJson(iter, end);
// }

MYLocalFiles::MYLocalFiles(MYBJsonIterator &iter, const MYBJsonIterator &end /*, bool useShortHash*/)
{
    _hashMap.reserve(16);
    initLocalFileArray();
    deserializeFromBJson(iter, end /*, useShortHash*/);
}

void MYLocalFiles::initLocalFileArray()
{
    for (int i = 0; i < 7; i++)
    {
        _knownMediaFiles[i].init((MYMediaFileType::Enum)(i + 1));
    }
}

void MYLocalFiles::clear()
{
    _hashMap.clear();
    _stringMap.clear();

    for (int i = 0; i < 7; i++)
    {
        _knownMediaFiles[i].clear();
    }

    _extendedMediaFiles.clear();
}

void MYLocalFiles::deserializeFromBJson(MYBJsonIterator &iter, const MYBJsonIterator &end /*, bool useShortHash*/)
{
    if (iter == end)
    {
        return;
    }

    assert(iter->isScopeBegin());

    assert(iter->key() == MYLiterals::Files::files);

    ++iter;
    assert(iter->type() == BJsonType::separator);

    auto mediaType = (MYMediaFileType::Enum)iter->key();

    for (++iter; iter != end && !iter->isScopeEnd(); iter->isScopeEnd() ? iter : ++iter)
    {
        if (mediaType == MYMediaFileType::NoType)
        {
            _extendedMediaFiles.emplace_back(mediaType, iter, end);
        }
        else
        {
            _knownMediaFiles[(int)mediaType - 1].prepare();
            _knownMediaFiles[(int)mediaType - 1].deserializeFromBJson(iter, end);
        }

        assert(iter->isSeparatorOrEnd());
        if (iter->type() == BJsonType::separator)
        {
            mediaType = (MYMediaFileType::Enum)iter->key();
        }
    }

    if (iter == end)
    {
        return;
    }

    // We can't chew up the last iter inside the loop, otherwise we'll miss separators
    assert(iter->isScopeEnd());
    ++iter;

    if (iter == end)
    {
        return;
    }

    if ((iter->key() == MYLiterals::Files::hashMap) && (iter->type() == BJsonType::array))
    {
        for (++iter; iter != end && iter->type() != BJsonType::end; ++iter)
        {
            // if (useShortHash)
            //{
            //     uint32_t shortHash = iter->fixed32();
            //     MYHash hash;
            //     memcpy(&hash.raw[0], &shortHash, 4);
            //     hash.resetsetbit();
            //     _hashMap.emplace_back(hash);
            // }
            // else
            {
                _hashMap.emplace_back(iter->asHash());
            }
        }
        assert(iter->isScopeEnd());

        ++iter;
    }

    if (iter == end)
    {
        return;
    }

    if ((iter->key() == MYLiterals::Files::stringMap) && (iter->type() == BJsonType::array))
    {
        for (++iter; iter != end && iter->type() != BJsonType::end; ++iter)
        {
            _stringMap.emplace_back(iter->asString());
        }
        assert(iter->isScopeEnd());

        ++iter;
    }
}

bool MYLocalFile::isModified() const
{
    return _modifiedFields.any();
}

bool MYLocalFiles::isModified() const
{
    if (_modifiedFields.any())
    {
        return true;
    }

    for (const auto &file : _extendedMediaFiles)
    {
        if (file.isModified())
        {
            return true;
        }
    }

    for (const auto &file : _knownMediaFiles)
    {
        if (file.isModified())
        {
            return true;
        }
    }

    return false;
}

bool MYLocalFiles::empty() const
{
    for (const auto &file : _knownMediaFiles)
    {
        if (!file.empty())
        {
            return false;
        }
    }

    return _extendedMediaFiles.empty();
}

MYLocalFiles::MYLocalFiles(MYLocalFiles &&other)
{
    _knownMediaFiles = other._knownMediaFiles;
    _extendedMediaFiles.insert(_extendedMediaFiles.end(), other._extendedMediaFiles.begin(), other._extendedMediaFiles.end());

    _modifiedFields = other._modifiedFields;
    _hashMap = std::move(other._hashMap);
    _stringMap = std::move(other._stringMap);
}

MYLocalFiles::MYLocalFiles(const MYLocalFiles &other)
{
    _knownMediaFiles = other._knownMediaFiles;
    _extendedMediaFiles.insert(_extendedMediaFiles.end(), other._extendedMediaFiles.begin(), other._extendedMediaFiles.end());

    _modifiedFields = other._modifiedFields;

    _hashMap = other._hashMap;
    _stringMap = other._stringMap;
}

MYLocalFiles &MYLocalFiles::operator=(MYLocalFiles &&other)
{
    _modifiedFields = other._modifiedFields;

    _knownMediaFiles = other._knownMediaFiles;
    _extendedMediaFiles.insert(_extendedMediaFiles.end(), other._extendedMediaFiles.begin(), other._extendedMediaFiles.end());

    _hashMap = std::move(other._hashMap);
    _stringMap = std::move(other._stringMap);

    return *this;
}

MYLocalFiles &MYLocalFiles::operator=(const MYLocalFiles &other)
{
    _modifiedFields = other._modifiedFields;

    _knownMediaFiles = other._knownMediaFiles;
    _extendedMediaFiles.insert(_extendedMediaFiles.end(), other._extendedMediaFiles.begin(), other._extendedMediaFiles.end());

    _hashMap = other._hashMap;
    _stringMap = other._stringMap;

    return *this;
}

bool MYLocalFiles::operator==(const MYLocalFiles &other) const
{
    MYBJsonRW one;
    one.StartObject();

    MYBJsonRW two;
    two.StartObject();

    serializeToBJson(one);
    other.serializeToBJson(two);

    if (one.psize() != two.psize())
        return false;

    return memcmp(one.pbegin(), two.pbegin(), one.psize()) == 0;
}

MYLocalFiles &MYLocalFiles::operator+=(const MYBJsonView &view)
{
    auto iter = view.begin();
    auto end = view.end();

    bool requireEstimatedThumbnailSize = true;
    bool requireEstimatedPreviewSize = true;

    for (; iter != end && iter->type() != BJsonType::end; ++iter)
    {
        auto key = iter->key();
        switch (key)
        {
        case MYLiterals::Files::files:
            assert(iter->type() == BJsonType::object);

            for (++iter; iter != end && !iter->isScopeEnd();)
            {
                assert(iter->type() == BJsonType::separator);

                auto mediaType = (MYMediaFileType::Enum)iter->key();

                ++iter;

                MYLocalFile file;
                file.prepare();
                file.deserializeFromBJson(iter, end);
                if (mediaType == MYMediaFileType::Preview && file.getFileSize() != 0)
                {
                    requireEstimatedPreviewSize = false;
                }

                if (mediaType == MYMediaFileType::Thumbnail && file.getFileSize() != 0)
                {
                    requireEstimatedThumbnailSize = false;
                }

                bool found = false;

                for (auto fileIter = _knownMediaFiles.begin(); fileIter != _knownMediaFiles.end(); ++fileIter)
                {
                    if ((fileIter->_mediaType == mediaType) &&
                        ((mediaType != MYMediaFileType::NoType || fileIter->_format == file._format)))
                    {
                        *fileIter += file;
                        found = true;
                    }
                }

                if (!found)
                {
                    _knownMediaFiles[file.getMediaType()] = std::move(file);
                }
            }

            assert(iter->type() == BJsonType::end);
            break;

        default:
            if (iter->isScopeBegin())
            {
                iter = iter.extent();
                assert(iter->isScopeEnd());
            }
            break;
        }
    }

    if (requireEstimatedPreviewSize)
    {
        auto file = this->getOrCreateMediaFile(MYMediaFileType::Preview);
        file->setFileSize(file->getFileSize() + 700000 /*ESTSIZE_PREVIEW*/);
    }

    if (requireEstimatedThumbnailSize)
    {
        auto file = this->getOrCreateMediaFile(MYMediaFileType::Thumbnail);
        file->setFileSize(file->getFileSize() + 10000 /*ESTSIZE_THUMBNAIL*/);
    }

    return *this;
}

void MYLocalFiles::compact()
{
    std::vector<bool> inUseBuckets;
    std::vector<bool> inUseStrings;
    inUseBuckets.resize(_hashMap.size());
    inUseStrings.resize(_stringMap.size());

    for (const auto &file : *this)
    {
        if (file._dataHash != MYHashRefEmpty)
        {
            inUseBuckets[file._dataHash] = true;
        }

        if (file._basisDataHash != MYHashRefEmpty)
        {
            inUseBuckets[file._basisDataHash] = true;
        }

        if (file._visualEditHash != MYHashRefEmpty)
        {
            inUseBuckets[file._visualEditHash] = true;
        }

        if (file._parseHash != MYHashRefEmpty)
        {
            inUseBuckets[file._parseHash] = true;
        }

        if (!MYFileFormatInterner::isInternedId(file._format))
        {
            inUseStrings[file._format] = true;
        }

        if (!MYFileFormatInterner::isInternedId(file._alURL))
        {
            inUseStrings[file._alURL] = true;
        }

        if (!MYFileFormatInterner::isInternedId(file._byocId))
        {
            inUseStrings[file._byocId] = true;
        }
    }

    assert(inUseBuckets.size() <= _hashMap.size());
    assert(inUseStrings.size() <= _stringMap.size());

    bool needHashCompaction = false;
    for (auto inUse : inUseBuckets)
    {
        if (!inUse)
        {
            needHashCompaction = true;
            break;
        }
    }

    bool needStringCompaction = false;
    for (auto inUse : inUseStrings)
    {
        if (!inUse)
        {
            needStringCompaction = true;
            break;
        }
    }

    if (needHashCompaction)
    {
        std::vector<MYHash> oldHashMap = std::move(_hashMap);
        _hashMap.clear();
        _hashMap.reserve(oldHashMap.size());
        for (auto &file : *this)
        {
            if (file._dataHash != MYHashRefEmpty)
            {
                file._dataHash = getOrCreateHashRef(oldHashMap[file._dataHash]);
            }

            if (file._basisDataHash != MYHashRefEmpty)
            {
                file._basisDataHash = getOrCreateHashRef(oldHashMap[file._basisDataHash]);
            }

            if (file._visualEditHash != MYHashRefEmpty)
            {
                file._visualEditHash = getOrCreateHashRef(oldHashMap[file._visualEditHash]);
            }

            if (file._parseHash != MYHashRefEmpty)
            {
                file._parseHash = getOrCreateHashRef(oldHashMap[file._parseHash]);
            }
        }
    }

    if (needStringCompaction)
    {
        std::vector<std::string> oldStringMap = std::move(_stringMap);
        _stringMap.clear();
        oldStringMap.reserve(_stringMap.size());
        for (auto &file : *this)
        {
            if (!MYFileFormatInterner::isInternedId(file._format))
            {
                file._format = getOrCreateStringRef(oldStringMap[file._format], file._mediaType);
            }

            if (!MYFileFormatInterner::isInternedId(file._alURL))
            {
                file._alURL = getOrCreateStringRef(oldStringMap[file._alURL], MYMediaFileType::NoType);
            }

            if (!MYFileFormatInterner::isInternedId(file._byocId))
            {
                file._byocId = getOrCreateStringRef(oldStringMap[file._byocId], MYMediaFileType::NoType);
            }
        }
    }
}

////////////////////////////////////////////////////////////////////
//
// MYBucketLocalFiles
//
////////////////////////////////////////////////////////////////////

MYBucketLocalFiles::MYBucketLocalFiles(size_t estimatedMediaPerBucket, MYBJsonIterator &begin, const MYBJsonIterator &end) : _estimatedMediaPerBucket(estimatedMediaPerBucket)
{
    // We don't reserve in debug, because we want to take re-allocations. Otherwise it will hide bugs if
    // the pointer values always remain the same.
    _media.reserve(estimatedMediaPerBucket);

    deserializeFromBJson(begin, end);
}

MYBucketLocalFiles::MYBucketLocalFiles(MYBucketLocalFiles &&other)
{
    _dirty = other._dirty;
    _media = std::move(other._media);
    _estimatedMediaPerBucket = other._estimatedMediaPerBucket;
    _modifiedFields = other._modifiedFields;
}

MYBucketLocalFiles &MYBucketLocalFiles::operator=(MYBucketLocalFiles &&other)
{
    _dirty = other._dirty;
    _media = std::move(other._media);
    _estimatedMediaPerBucket = other._estimatedMediaPerBucket;
    _modifiedFields = other._modifiedFields;
    return *this;
}

void MYBucketLocalFiles::clear()
{
    _dirty = true;
}

void MYBucketLocalFiles::deserializeFromBJson(MYBJsonIterator &iter, const MYBJsonIterator &end /*, bool useShortHash*/)
{
    if (iter == end)
        return;

    //_useShortHash = useShortHash;

    MYMediaMapType::iterator dirtyIter;
    MYMediaMapType::iterator dirtyEnd = _media.end();

    if (_dirty)
    {
        dirtyIter = _media.begin();
    }
    else
    {
        dirtyIter = _media.end();
    }

    assert(iter->isScopeBegin());
    for (++iter; iter != end && !iter->isSeparatorOrEnd(); iter->isScopeEnd() ? iter : ++iter)
    {
        assert(iter->key() == MYLiterals::Files::media);
        MYHash mediaHash = iter->asHash();

        ++iter;
        assert(iter->key() == MYLiterals::Files::files);

        if (dirtyIter != dirtyEnd)
        {
            dirtyIter->first = mediaHash;
            dirtyIter->second.clear();
            dirtyIter->second.deserializeFromBJson(iter, end /*, useShortHash*/);
            ++dirtyIter;
        }
        else
        {
            _dirty = false;
            _media.emplace_from_sorted(mediaHash, iter, end /*, useShortHash*/);
        }
    }

    if (_dirty)
    {
        _media.erase(dirtyIter, _media.end());
    }
}

MYLocalFiles *MYBucketLocalFiles::getFilesForMediaOrNull(const MYHash &mediaHash) const
{
    auto found = _media.find(mediaHash);
    if (found == _media.end())
    {
        return nullptr;
    }
    return &found->second;
}

MYLocalFiles *MYBucketLocalFiles::getOrCreateFilesForMedia(const MYHash &mediaHash)
{
    prepare();
    return &_media.try_emplace(mediaHash).first->second;
}

bool MYBucketLocalFiles::removeFilesForMedia(const MYHash &mediaHash)
{
    auto found = _media.find(mediaHash);
    if (found == _media.end())
    {
        return false;
    }

    _media.erase_at(found);
    return true;
}

std::string MYLocalFile::toString() const
{
    std::string s;

    s += MYMediaFileType::getName(getMediaType());
    s += " ";

    s += getFormat();
    s += " ";

    if (getIsDraft())
    {
        s += "draft ";
    }

    if (getInInternalData())
    {
        s += "internalData ";
    }

    if (getIsHardWant())
    {
        s += "hardWant ";
    }

    if (getIsHardDontWant())
    {
        s += "hardDontWant ";
    }

    switch (getParsability())
    {
    case MYParsability::NotParsable:
        s += "notParsable ";
        break;
    case MYParsability::Parsable:
        s += "parsable ";
        break;
    case MYParsability::NotAttempted:
        s += "notAttemptedParse ";
        break;
    default:
        break;
    }

    if (getCropZoomFactor() != 1.0f)
    {
        s += "CropZoomFactor: " + std::to_string(getCropZoomFactor()) + " ";
    }

    if (hasDataHash())
    {
        s += "DataHash: " + getDataHash().toString() + " ";
    }

    if (hasVisualEditHashHash())
    {
        s += "VisualEditHash: " + getVisualEditHash().toString() + " ";
    }

    if (hasBasisDataHash())
    {
        s += "BasisDataHash: " + getBasisDataHash().toString() + " ";
    }

    if (!getParseHash().empty())
    {
        s += "ParseHash: " + getParseHash().toString() + " ";
    }

    if (getFileSize())
    {
        s += "Size: " + std::to_string(getFileSize()) + " ";
    }

    if (getWidth())
    {
        s += "Width: " + std::to_string(getWidth()) + " ";
    }

    if (getHeight())
    {
        s += "Height: " + std::to_string(getHeight()) + " ";
    }

    if (getGenVersion())
    {
        s += "GenVersion: " + std::to_string(getGenVersion()) + " ";
    }

    if (!getByocId().empty())
    {
        s += "Byoc: " + getByocId() + " ";
    }

    return MYString::rtrim(s);
}

std::string MYLocalFiles::toString() const
{
    std::string s;
    for (const auto &localFile : *this)
    {
        if (!s.empty())
        {
            s += ", ";
        }
        s += "{";
        s += localFile.toString();
        s += "}";
    }

    return s;
}

std::string MYBucketLocalFiles::toString() const
{
    std::string s;
    s += "[";
    for (const auto &localFile : getFiles())
    {
        if (!s.empty())
        {
            s += ", ";
        }
        s += "\"";
        s += localFile.first.toString();
        s += "\"";
        s += ": ";
        s += localFile.second.toString();
    }

    s += "]";
    return s;
}

bool MYLocalFile::setGenVersion(unsigned int newGenVersion)
{
    if (newGenVersion == _genVersion)
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::genVersion] = true;
    _genVersion = newGenVersion;
    return true;
}
