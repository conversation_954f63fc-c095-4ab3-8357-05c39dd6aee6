import { test, describe, mock, before, after, it } from "node:test";
import assert from "node:assert";
import { CloudClient } from "./CloudClient.mjs";
import { <PERSON>uilder, By, Browser, until } from "selenium-webdriver";
import { ILicense, LicenseStatus, Manager } from "../models/License.model.mjs";
import { createUsers, subscribe, sleep } from "./FastSpringTestHelpers.mjs";

const HOST = "https://willem.mylio.com";
const PASSWORD = "password";
const ADMIN_USER = "<EMAIL>";
const BUYER = "<EMAIL>";




describe("The license Lifecycle", async (t) => {
    let adminCloud = new CloudClient(HOST);
    let licenseKey = "";
    let l1Cloud: CloudClient;
    let users: CloudClient[] = [];

    before(async (t) => {
        users = await createUsers(["<EMAIL>"]);
        l1Cloud = users[0];
    });

    it("buy license key", async (t) => {
        licenseKey = await subscribe("one-year", "twoTBmonth");
        //wait for webhook
        await sleep(5000);
    });

    it("redeem license key", async (t) => {
        await l1Cloud.get(`/license-keys/${licenseKey}/redeem`);
        let licenses = await l1Cloud.get<ILicense[]>("/accounts/:aid/licenses");
        let fsLicense = licenses.find(l => l.activationKey === licenseKey && l.accountId === l1Cloud.account.accountId);
        assert(!!fsLicense, "license not found");
    });

    it("preview increase storage to 10tb", async (t) => {
        let result = await l1Cloud.post<any>("/accounts/:aid/cloud-storage", { qty: 10, preview: true });
        assert(!!result.proposedPlan);
    });

    it("increase storage to 10tb", async (t) => {
        await l1Cloud.post("/accounts/:aid/cloud-storage", { qty: 10, preview: false });
        await sleep(5000);
        let licenses = await l1Cloud.get<ILicense[]>("/accounts/:aid/licenses");
        let _5 = 0;
        let _2 = 0;
        for (let license of licenses) {
            if (license.manager === Manager.FastSpring && license.status !== LicenseStatus.Deleted) {
                if (license.templateId.includes("5"))
                    _5++;
                if (license.templateId.includes("2"))
                    _2++;
            }
        }
        assert(_5 === 2, "5TB license not found");
        assert(_2 === 0, "2TB license found");
    });


    it("decrease storage to 2tb", async (t) => {
        await l1Cloud.post("/accounts/:aid/cloud-storage", { qty: 2, preview: false });
        await sleep(5000);
        let licenses = await l1Cloud.get<ILicense[]>("/accounts/:aid/licenses");
        let _5 = 0;
        let _2 = 0;
        for (let license of licenses) {
            if (license.manager === Manager.FastSpring && license.status !== LicenseStatus.Deleted) {
                if (license.templateId.includes("5"))
                    _5++;
                if (license.templateId.includes("2"))
                    _2++;
            }
        }
        assert(_5 === 0, "5TB license not found");
        assert(_2 === 1, "2TB license found");
    });

    it("revoke license key", async (t) => {
        await adminCloud.signin(ADMIN_USER, PASSWORD);
        await adminCloud.impersonate(l1Cloud.account.accountId);
        let licenses = await l1Cloud.get<ILicense[]>("/accounts/:aid/licenses");
        for (let license of licenses) {
            if (license.activationKey === licenseKey)
                await adminCloud.post(`/accounts/:aid/licenses/${license.licenseId}/revoke`);
        }
        licenses = await l1Cloud.get<ILicense[]>("/accounts/:aid/licenses");
        let fsLicense = licenses.find(l => l.activationKey === licenseKey && l.accountId === l1Cloud.account.accountId);
        assert(!fsLicense, "license not revoked");
    });

    after(async (t) => {
        console.log("done");
        setTimeout(() => {
            process.exit(0);
        }, 200);
    });
});
