#pragma once

#include <memory>
#include <string>
#include "MYHash.h"

struct MYDeviceId
{
private:
    uint32_t id = 0;

public:
    MYDeviceId()
    {
        id = Empty.id;
    }

    MYDeviceId(const MYDeviceId &other) : id(other.id)
    {
    }

    bool empty() const
    {
        return id == Empty.id;
    }

    void clear()
    {
        id = Empty.id;
    }

    bool operator==(MYDeviceId other) const
    {
        return this->id == other.id;
    }

    bool operator!=(MYDeviceId other) const
    {
        return this->id != other.id;
    }

    bool operator<(MYDeviceId other) const
    {
        return this->id < other.id;
    }

    bool operator>(MYDeviceId other) const
    {
        return this->id > other.id;
    }

    uint32_t toInt() const
    {
        return id;
    }

    static MYDeviceId BootStrap;               // Fake ID that is given to a device during first boot until it has a real ID from another source.
    static const MYDeviceId Empty;             // Empty ID - indicates "no specific device" or "all devices" to APIs
    static const MYDeviceId Cloud;             // Cloud device (0)
    static const MYDeviceId LikelyFirstDevice; // Likely first device on the account (1)
    static const MYDeviceId DupDeviceIdParent;

    bool isCloud() const
    {
        return getCloudId().id == id;
    }

    bool isBootStrap() const
    {
        return id == BootStrap.id;
    }

    std::string toSQLString() const
    {
        return std::to_string(id);
    }

    std::string toString() const
    {
        if (empty())
            return "";

        return std::to_string(id);
    }

    static MYDeviceId fromString(const std::string stringId)
    {
        if (stringId.empty())
        {
            return MYDeviceId::Empty;
        }

        return MYDeviceId::fromInt(atoi(stringId.c_str()));
    }

    static bool fromString(const char *stringId, MYDeviceId &deviceId)
    {
        if (stringIsNullOrEmpty(stringId))
            return false;

        deviceId.id = atoi(stringId);

        return true;
    }

    static MYDeviceId fromInt(uint32_t id)
    {
        MYDeviceId devId;
        devId.id = id;
        return devId;
    }

    static MYDeviceId getCloudId()
    {
        return MYDeviceId::Cloud;
    }
};

#ifdef MYLIO_CLIENT
struct MYToFromDevice
{
    union
    {
        struct
        {
            MYDeviceId to;
            MYDeviceId from;
        };
        uint64_t _value;
    };

    MYToFromDevice() {}
    MYToFromDevice(const MYToFromDevice &other) : _value(other._value) {}
    MYToFromDevice(MYDeviceId toId, MYDeviceId fromId) : to(toId), from(fromId) {}

    bool operator==(const MYToFromDevice &that)
    {
        return _value == that._value;
    }

    bool operator!=(const MYToFromDevice &that)
    {
        return _value != that._value;
    }

    bool operator<(const MYToFromDevice &that) const
    {
        return _value < that._value;
    }
};

static_assert(sizeof(MYToFromDevice) == sizeof(uint64_t), "Unexpected size");

namespace std
{
    template <>
    struct hash<MYToFromDevice>
    {
        size_t operator()(const MYToFromDevice &other) const
        {
            return hash<uint64_t>()(other._value);
        }
    };
}
#endif

namespace std
{
    inline std::string to_string(MYDeviceId deviceId)
    {
        return deviceId.toString();
    }
}
