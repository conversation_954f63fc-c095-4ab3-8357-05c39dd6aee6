

    drop view if exists a0."License" cascade;

    create or replace view a0."License" as
    select
    flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		license_id as "licenseId",
		account_id as "accountId",
		manager,
		end_date as "endDate",
		template_id as "templateId",
		activation_key as "activationKey",
		device_limit as "deviceLimit",
		photo_limit as "photoLimit",
		features,
		cloud_storage_limit as "cloudStorageLimit",
		available_upgrades as "availableUpgrades",
		status
    from a0.license;
    

drop function if exists a0.license_create; 
        create function a0.license_create(
            _flags int,
	_modified_time timestamptz,
	_created_time timestamptz,
	_license_id text,
	_account_id int,
	_manager text,
	_end_date timestamptz,
	_template_id text,
	_activation_key text,
	_device_limit int,
	_photo_limit int,
	_features int,
	_cloud_storage_limit int,
	_available_upgrades text,
	_status int
        )
        returns a0."License"
        as $$
        
    declare
        result a0."License";
        
    begin
        
        


       
        
/* b::before_create */
if (_license_id is null) then
	_license_id := a0.next_license_id(_account_id);
end if;
/* end */

        
        
        
        
        _modified_time := coalesce(_modified_time, now());
        _created_time := coalesce(_created_time, now());
        insert into a0.license (
            flags,
	modified_time,
	created_time,
	license_id,
	account_id,
	manager,
	end_date,
	template_id,
	activation_key,
	device_limit,
	photo_limit,
	features,
	cloud_storage_limit,
	available_upgrades,
	status
        )
        values(
            _flags,
			_modified_time,
			_created_time,
			_license_id,
			_account_id,
			_manager,
			_end_date,
			_template_id,
			_activation_key,
			_device_limit,
			_photo_limit,
			_features,
			_cloud_storage_limit,
			_available_upgrades,
			_status
        )
        returning
            flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		license_id as "licenseId",
		account_id as "accountId",
		manager,
		end_date as "endDate",
		template_id as "templateId",
		activation_key as "activationKey",
		device_limit as "deviceLimit",
		photo_limit as "photoLimit",
		features,
		cloud_storage_limit as "cloudStorageLimit",
		available_upgrades as "availableUpgrades",
		status
        into result;

        



        

    
        



        return result;
    end;
        $$
        language plpgsql;
        

drop function if exists a0.license_update; 
        create function a0.license_update(
            _flags int,
	_modified_time timestamptz,
	_created_time timestamptz,
	_license_id text,
	_account_id int,
	_manager text,
	_end_date timestamptz,
	_template_id text,
	_activation_key text,
	_device_limit int,
	_photo_limit int,
	_features int,
	_cloud_storage_limit int,
	_available_upgrades text,
	_status int
        )
        returns a0."License"
        as $$
        
    declare
        result a0."License";
        
    begin
        
        


       
        


        
        
        _modified_time := now();
        update a0.license
        set
            flags = _flags,
			modified_time = _modified_time,
			created_time = _created_time,
			account_id = _account_id,
			manager = _manager,
			end_date = _end_date,
			template_id = _template_id,
			activation_key = _activation_key,
			device_limit = _device_limit,
			photo_limit = _photo_limit,
			features = _features,
			cloud_storage_limit = _cloud_storage_limit,
			available_upgrades = _available_upgrades,
			status = _status
        where license_id = _license_id
        returning
            flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		license_id as "licenseId",
		account_id as "accountId",
		manager,
		end_date as "endDate",
		template_id as "templateId",
		activation_key as "activationKey",
		device_limit as "deviceLimit",
		photo_limit as "photoLimit",
		features,
		cloud_storage_limit as "cloudStorageLimit",
		available_upgrades as "availableUpgrades",
		status
        into result;

        

    
        



        return result;
    end;
        $$
        language plpgsql;
        

drop function if exists a0.license_read_by_license_id; 
        create function a0.license_read_by_license_id(
            _license_id text
        )
        returns a0."License"
        as $$
        select
        flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		license_id as "licenseId",
		account_id as "accountId",
		manager,
		end_date as "endDate",
		template_id as "templateId",
		activation_key as "activationKey",
		device_limit as "deviceLimit",
		photo_limit as "photoLimit",
		features,
		cloud_storage_limit as "cloudStorageLimit",
		available_upgrades as "availableUpgrades",
		status
        from a0.license
        where license_id = _license_id;
        $$
        language sql;
        

drop function if exists a0.license_delete_by_license_id; 
        create function a0.license_delete_by_license_id(
            _license_id text
        )
        returns void
        as $$
        
    declare
        
        
    begin
        
        


       
        


        

    delete from a0.license
    where license_id = _license_id;

    
    
        



        
    end;
        $$
        language plpgsql;
        

drop function if exists a0.license_find_by_account_id; 
        create function a0.license_find_by_account_id(
            _account_id int
        )
        returns setof a0."License"
        as $$
        select
        flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		license_id as "licenseId",
		account_id as "accountId",
		manager,
		end_date as "endDate",
		template_id as "templateId",
		activation_key as "activationKey",
		device_limit as "deviceLimit",
		photo_limit as "photoLimit",
		features,
		cloud_storage_limit as "cloudStorageLimit",
		available_upgrades as "availableUpgrades",
		status
        from a0.license
        where account_id = _account_id;
        $$
        language sql;
        

drop function if exists a0.license_find_by_activation_key; 
        create function a0.license_find_by_activation_key(
            _activation_key text
        )
        returns setof a0."License"
        as $$
        select
        flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		license_id as "licenseId",
		account_id as "accountId",
		manager,
		end_date as "endDate",
		template_id as "templateId",
		activation_key as "activationKey",
		device_limit as "deviceLimit",
		photo_limit as "photoLimit",
		features,
		cloud_storage_limit as "cloudStorageLimit",
		available_upgrades as "availableUpgrades",
		status
        from a0.license
        where activation_key = _activation_key;
        $$
        language sql;
        
