
ALTER TABLE Album RENAME TO <PERSON><PERSON><PERSON><PERSON><PERSON>;

ALTER TABLE Event RENAME TO oldEvent;

ALTER TABLE AuditEntry RENAME TO oldAuditEntry;

ALTER TABLE Channel RENAME TO oldChannel;

ALTER TABLE FaceRectangle RENAME TO old<PERSON><PERSON><PERSON><PERSON>tangle;

ALTER TABLE FilterHistoryItem RENAME TO oldFilterHistoryItem;

ALTER TABLE Folder RENAME TO oldFolder;

ALTER TABLE FolderDeviceLink RENAME TO oldFolderDeviceLink;

ALTER TABLE Location RENAME TO oldLocation;

ALTER TABLE ImportSession RENAME TO oldImportSession;

ALTER TABLE Link RENAME TO oldLink;

ALTER TABLE Media RENAME TO oldMedia;

ALTER TABLE MediaAlbumLink RENAME TO oldMediaAlbumLink;

ALTER TABLE NAS RENAME TO oldNAS;

ALTER TABLE NetworkNode RENAME TO oldNetworkNode;

ALTER TABLE Party RENAME TO oldParty;

ALTER TABLE Resource RENAME TO oldResource;

ALTER TABLE SharedConfiguration RENAME TO oldSharedConfiguration;

INSERT INTO Album(
    SmartFilter,
	ParentAlbum,
	DirectChildMedia,
	Name,
	RecursiveChildMedia,
	RecursiveChildMediaStartDateTime,
	RecursiveChildMediaEndDateTime,
	FSPathChanged,
	DirectMediaEndDate,
	DirectMediaStartDate,
	NormalizedName,
	DirectChildContainers,
	GeneratedCoverDirty,
	InitialCoverMedia,
	CoverMedia,
	GeneratedCoverMedia,
	ImportSession,
	UniqueHash,
	Id)
SELECT
    SmartFilter,
	ParentAlbum,
	DirectChildMedia,
	Name,
	RecursiveChildMedia,
	RecursiveChildMediaStartDateTime,
	RecursiveChildMediaEndDateTime,
	FSPathChanged,
	DirectMediaEndDate,
	DirectMediaStartDate,
	NormalizedName,
	DirectChildContainers,
	GeneratedCoverDirty,
	InitialCoverMedia,
	CoverMedia,
	GeneratedCoverMedia,
	ImportSession,
	UniqueHash,
	Id
FROM oldAlbum;

-- drop table oldAlbum;

INSERT INTO Event(
    Source,
	SourceId,
	Latitude,
	Longitude,
	Caption,
	ModifiedByUser,
	StartDateTime,
	EndDateTime,
	Scope,
	DisplayOrder,
	Rating,
	CategoryHash,
	Filter,
	ShowInCalendar,
	ParentEvent,
	Location,
	IsAllDay,
	IsRecurring,
	DirectChildMedia,
	Name,
	RecursiveChildMedia,
	RecursiveChildMediaStartDateTime,
	RecursiveChildMediaEndDateTime,
	FSPathChanged,
	DirectMediaEndDate,
	DirectMediaStartDate,
	NormalizedName,
	DirectChildContainers,
	GeneratedCoverDirty,
	InitialCoverMedia,
	CoverMedia,
	GeneratedCoverMedia,
	ImportSession,
	UniqueHash,
	Id,
	FBMessage,
	FBApplicationName,
	FBCaption,
	FBIsPhotoPost,
	FBEventDescription,
	RsvpStatus)
SELECT
    Source,
	SourceId,
	Latitude,
	Longitude,
	Caption,
	ModifiedByUser,
	StartDateTime,
	EndDateTime,
	Scope,
	DisplayOrder,
	Rating,
	CategoryHash,
	Filter,
	ShowInCalendar,
	ParentEvent,
	Location,
	IsAllDay,
	IsRecurring,
	DirectChildMedia,
	Name,
	RecursiveChildMedia,
	RecursiveChildMediaStartDateTime,
	RecursiveChildMediaEndDateTime,
	FSPathChanged,
	DirectMediaEndDate,
	DirectMediaStartDate,
	NormalizedName,
	DirectChildContainers,
	GeneratedCoverDirty,
	InitialCoverMedia,
	CoverMedia,
	GeneratedCoverMedia,
	ImportSession,
	UniqueHash,
	Id,
	FBMessage,
	FBApplicationName,
	FBCaption,
	FBIsPhotoPost,
	FBEventDescription,
	RsvpStatus
FROM oldEvent;

-- drop table oldEvent;

INSERT INTO AuditEntry(
    AuditType,
	DateLogged,
	DateSeen,
	Description,
	Count,
	Misc,
	ResourceHash,
	UniqueHash,
	Id)
SELECT
    AuditType,
	DateLogged,
	DateSeen,
	Description,
	Count,
	Misc,
	ResourceHash,
	UniqueHash,
	Id
FROM oldAuditEntry;

-- drop table oldAuditEntry;

INSERT INTO Channel(
    EmailAlias,
	IsGroup,
	UniqueHash,
	Id,
	IPAddress,
	URL,
	TelephoneNumber,
	Extension,
	CountryCode)
SELECT
    EmailAlias,
	IsGroup,
	UniqueHash,
	Id,
	IPAddress,
	URL,
	TelephoneNumber,
	Extension,
	CountryCode
FROM oldChannel;

-- drop table oldChannel;

INSERT INTO FaceRectangle(
    TopLeftX,
	TopLeftY,
	Width,
	Height,
	SourceId,
	Person,
	Media,
	ClusterId,
	Descriptor,
	FaceInfo,
	Ignore,
	ProposedPersonHash,
	Confidence,
	RejectedPersonHashes,
	UniqueHash,
	Id)
SELECT
    TopLeftX,
	TopLeftY,
	Width,
	Height,
	SourceId,
	Person,
	Media,
	ClusterId,
	Descriptor,
	FaceInfo,
	Ignore,
	ProposedPersonHash,
	Confidence,
	RejectedPersonHashes,
	UniqueHash,
	Id
FROM oldFaceRectangle;

-- drop table oldFaceRectangle;

INSERT INTO FilterHistoryItem(
    TimeStamp,
	FilterString,
	IsPinned,
	UniqueHash,
	Id)
SELECT
    TimeStamp,
	FilterString,
	IsPinned,
	UniqueHash,
	Id
FROM oldFilterHistoryItem;

-- drop table oldFilterHistoryItem;

INSERT INTO Folder(
    LocalRootOrTemporaryPath,
	IsMissing,
	IsPrivate,
	RootDeviceLabel,
	HasUnhandledFiles,
	ALURL,
	LocalName,
	ReadOnlyDeviceHash,
	ParentFolder,
	DirectChildMedia,
	Name,
	RecursiveChildMedia,
	RecursiveChildMediaStartDateTime,
	RecursiveChildMediaEndDateTime,
	FSPathChanged,
	DirectMediaEndDate,
	DirectMediaStartDate,
	NormalizedName,
	DirectChildContainers,
	GeneratedCoverDirty,
	InitialCoverMedia,
	CoverMedia,
	GeneratedCoverMedia,
	ImportSession,
	UniqueHash,
	Id)
SELECT
    LocalRootOrTemporaryPath,
	IsMissing,
	IsPrivate,
	RootDeviceLabel,
	HasUnhandledFiles,
	ALURL,
	LocalName,
	ReadOnlyDeviceHash,
	ParentFolder,
	DirectChildMedia,
	Name,
	RecursiveChildMedia,
	RecursiveChildMediaStartDateTime,
	RecursiveChildMediaEndDateTime,
	FSPathChanged,
	DirectMediaEndDate,
	DirectMediaStartDate,
	NormalizedName,
	DirectChildContainers,
	GeneratedCoverDirty,
	InitialCoverMedia,
	CoverMedia,
	GeneratedCoverMedia,
	ImportSession,
	UniqueHash,
	Id
FROM oldFolder;

-- drop table oldFolder;

INSERT INTO FolderDeviceLink(
    SmallIntention,
	SmallKeywords,
	SmallRatingFilter,
	SmallStarFilter,
	LargeIntention,
	LargeKeywords,
	LargeRatingFilter,
	LargeStarFilter,
	MediumIntention,
	MediumKeywords,
	MediumRatingFilter,
	MediumStarFilter,
	LargeIgnoreParent,
	MediumIgnoreParent,
	SmallIgnoreParent,
	OverrideChildMDLs,
	Folder,
	UniqueHash,
	Id)
SELECT
    SmallIntention,
	SmallKeywords,
	SmallRatingFilter,
	SmallStarFilter,
	LargeIntention,
	LargeKeywords,
	LargeRatingFilter,
	LargeStarFilter,
	MediumIntention,
	MediumKeywords,
	MediumRatingFilter,
	MediumStarFilter,
	LargeIgnoreParent,
	MediumIgnoreParent,
	SmallIgnoreParent,
	OverrideChildMDLs,
	Folder,
	UniqueHash,
	Id
FROM oldFolderDeviceLink;

-- drop table oldFolderDeviceLink;

INSERT INTO Location(
    LocationName,
	Source,
	CoverMedia,
	UTCOffset,
	City,
	Country,
	State,
	County,
	Street,
	Zip,
	Latitude,
	Longitude,
	Radius,
	ImportSession,
	UniqueHash,
	Id)
SELECT
    LocationName,
	Source,
	CoverMedia,
	UTCOffset,
	City,
	Country,
	State,
	County,
	Street,
	Zip,
	Latitude,
	Longitude,
	Radius,
	ImportSession,
	UniqueHash,
	Id
FROM oldLocation;

-- drop table oldLocation;

INSERT INTO ImportSession(
    StartDateTime,
	EndDateTime,
	Source,
	UniqueHash,
	Id)
SELECT
    StartDateTime,
	EndDateTime,
	Source,
	UniqueHash,
	Id
FROM oldImportSession;

-- drop table oldImportSession;

INSERT INTO Link(
    FromDateTime,
	ToDateTime,
	SourceResource,
	TargetResource,
	UniqueHash,
	Id)
SELECT
    FromDateTime,
	ToDateTime,
	SourceResource,
	TargetResource,
	UniqueHash,
	Id
FROM oldLink;

-- drop table oldLink;

INSERT INTO Media(
    DateCreated,
	StarRating,
	KeywordsStr,
	FSPathChanged,
	MonthNumber,
	YearNumber,
	Caption,
	DayNumber,
	FaceDetectionVersion,
	Orientation,
	SourceId,
	Title,
	Label,
	Copyright,
	CameraMake,
	CameraModel,
	CameraSerialNumber,
	GpsLat,
	GpsLong,
	FileNameNoExt,
	Aperture,
	Cropped,
	Flash,
	FocalLength,
	Iso,
	Lens,
	HasVideo,
	ShutterSpeed,
	Author,
	IsFlagged,
	IsEdited,
	OfflineEditingSupport,
	EditingMaster,
	WhiteBalanceAsShotTemp,
	WhiteBalanceAsShotTint,
	DateCreatedConfidence,
	NormalizedFileNameNoExt,
	CropZoomFactor,
	ExposureBias,
	MeteringMode,
	WhiteBalanceMode,
	LocalFileNameNoExt,
	LocalTempContainingFolderURL,
	IsInternal,
	InvolvedInDuplication,
	AddToCacheTime,
	SearchAttributes,
	AddToCacheTimeRemovable,
	PixelChecksumStorage,
	StampAttributes,
	ContainingFolder,
	ContainingEvent,
	Place,
	PlaceExtraInfo,
	RootFolder,
	ContainedInAlbum,
	BYOA,
	BYOG,
	ImportSession,
	UniqueHash,
	Id)
SELECT
    DateCreated,
	StarRating,
	KeywordsStr,
	FSPathChanged,
	MonthNumber,
	YearNumber,
	Caption,
	DayNumber,
	FaceDetectionVersion,
	Orientation,
	SourceId,
	Title,
	Label,
	Copyright,
	CameraMake,
	CameraModel,
	CameraSerialNumber,
	GpsLat,
	GpsLong,
	FileNameNoExt,
	Aperture,
	Cropped,
	Flash,
	FocalLength,
	Iso,
	Lens,
	HasVideo,
	ShutterSpeed,
	Author,
	IsFlagged,
	IsEdited,
	OfflineEditingSupport,
	EditingMaster,
	WhiteBalanceAsShotTemp,
	WhiteBalanceAsShotTint,
	DateCreatedConfidence,
	NormalizedFileNameNoExt,
	CropZoomFactor,
	ExposureBias,
	MeteringMode,
	WhiteBalanceMode,
	LocalFileNameNoExt,
	LocalTempContainingFolderURL,
	IsInternal,
	InvolvedInDuplication,
	AddToCacheTime,
	SearchAttributes,
	AddToCacheTimeRemovable,
	PixelChecksumStorage,
	StampAttributes,
	ContainingFolder,
	ContainingEvent,
	Place,
	PlaceExtraInfo,
	RootFolder,
	ContainedInAlbum,
	BYOA,
	BYOG,
	ImportSession,
	UniqueHash,
	Id
FROM oldMedia;

-- drop table oldMedia;

INSERT INTO MediaAlbumLink(
    DisplayOrder,
	SourceResource,
	TargetResource,
	UniqueHash,
	Id)
SELECT
    DisplayOrder,
	SourceResource,
	TargetResource,
	UniqueHash,
	Id
FROM oldMediaAlbumLink;

-- drop table oldMediaAlbumLink;

INSERT INTO NAS(
    CatalogPath,
	NetworkPath,
	UniqueHash,
	Id)
SELECT
    CatalogPath,
	NetworkPath,
	UniqueHash,
	Id
FROM oldNAS;

-- drop table oldNAS;

INSERT INTO NetworkNode(
    NodeName,
	DeviceType,
	Nickname,
	IsThisDevice,
	OverProtect,
	CatalogPath,
	FixedLocation,
	NASHash,
	DeviceLocation,
	Cipher)
SELECT
    NodeName,
	DeviceType,
	Nickname,
	IsThisDevice,
	OverProtect,
	CatalogPath,
	FixedLocation,
	NASHash,
	DeviceLocation,
	Cipher
FROM oldNetworkNode;

-- drop table oldNetworkNode;

INSERT INTO Party(
    Source,
	SourceId,
	NormalizedName,
	SecondaryHomeLocation,
	PrimaryHomeLocation,
	WorkLocation,
	WorkPhone,
	HomePhone,
	MobilePhone,
	WorkEmail,
	PrimaryEmail,
	SecondaryEmail,
	PrimaryWebsite,
	SecondaryWebsite,
	OtherLocation,
	ProfileMedia,
	OriginalProfileMedia,
	ImportSession,
	UniqueHash,
	Id,
	Gender,
	BirthDateYear,
	FirstName,
	LastName,
	BirthDateMonth,
	BirthDateDayOfMonth)
SELECT
    Source,
	SourceId,
	NormalizedName,
	SecondaryHomeLocation,
	PrimaryHomeLocation,
	WorkLocation,
	WorkPhone,
	HomePhone,
	MobilePhone,
	WorkEmail,
	PrimaryEmail,
	SecondaryEmail,
	PrimaryWebsite,
	SecondaryWebsite,
	OtherLocation,
	ProfileMedia,
	OriginalProfileMedia,
	ImportSession,
	UniqueHash,
	Id,
	Gender,
	BirthDateYear,
	FirstName,
	LastName,
	BirthDateMonth,
	BirthDateDayOfMonth
FROM oldParty;

-- drop table oldParty;

INSERT INTO Resource(
    /* b::Resource_UniqueHash_insert */   foo   /* end */,
	Id)
SELECT
    /* b::Resource_UniqueHash */   custom(field)   /* end */,
	Id
FROM oldResource;

-- drop table oldResource;

INSERT INTO SharedConfiguration(
    Key,
	Value,
	UniqueHash,
	Id)
SELECT
    Key,
	Value,
	UniqueHash,
	Id
FROM oldSharedConfiguration;

-- drop table oldSharedConfiguration;

drop table oldAlbum;

drop table oldEvent;

drop table oldAuditEntry;

drop table oldChannel;

drop table oldFaceRectangle;

drop table oldFilterHistoryItem;

drop table oldFolder;

drop table oldFolderDeviceLink;

drop table oldLocation;

drop table oldImportSession;

drop table oldLink;

drop table oldMedia;

drop table oldMediaAlbumLink;

drop table oldNAS;

drop table oldNetworkNode;

drop table oldParty;

drop table oldResource;

drop table oldSharedConfiguration;
