
import { makeAutoObservable } from "mobx"
    
/* b::mobx_imports */
import { FeatureBits } from "./FeatureBits.mjs"
import { LicenseFlags } from "./LicenseFlags.mjs"
/* end */

    
/* b::mobx_enums */
export class Manager {
	static TestPaid = "P";
	static FastSpring = "F";
	static Apple = "A";
	static Mylio = "M";
}

export enum LicenseStatus {
	Active = 1,
	Deleted = 2,
	Canceled = 3,
	Trial = 4
}
/* end */



export interface ILicense {
    flags?: number;
	modifiedTime?: Date;
	createdTime?: Date;
	licenseId?: string;
	accountId?: number;
	manager?: string;
	endDate?: Date;
	templateId?: string;
	activationKey?: string;
	deviceLimit?: number;
	photoLimit?: number;
	features?: number;
	cloudStorageLimit?: number;
	availableUpgrades?: string;
	status?: number;
}

export interface IWireLicense {
    flags?: number;
	modifiedTime?: string;
	createdTime?: string;
	licenseId?: string;
	accountId?: number;
	manager?: string;
	endDate?: string;
	templateId?: string;
	activationKey?: string;
	deviceLimit?: number;
	photoLimit?: number;
	features?: number;
	cloudStorageLimit?: number;
	availableUpgrades?: string;
	status?: number;
}

export class License implements ILicense {
    flags?: number;
	modifiedTime?: Date;
	createdTime?: Date;
	licenseId?: string;
	accountId?: number;
	manager?: string;
	endDate?: Date;
	templateId?: string;
	activationKey?: string;
	deviceLimit?: number;
	photoLimit?: number;
	features?: number;
	cloudStorageLimit?: number;
	availableUpgrades?: string;
	status?: number;
    setFlags(value: number) { this.changed = true; this.flags = value; }
	setModifiedTime(value: Date) { this.changed = true; this.modifiedTime = value; }
	setCreatedTime(value: Date) { this.changed = true; this.createdTime = value; }
	setLicenseId(value: string) { this.changed = true; this.licenseId = value; }
	setAccountId(value: number) { this.changed = true; this.accountId = value; }
	setManager(value: string) { this.changed = true; this.manager = value; }
	setEndDate(value: Date) { this.changed = true; this.endDate = value; }
	setTemplateId(value: string) { this.changed = true; this.templateId = value; }
	setActivationKey(value: string) { this.changed = true; this.activationKey = value; }
	setDeviceLimit(value: number) { this.changed = true; this.deviceLimit = value; }
	setPhotoLimit(value: number) { this.changed = true; this.photoLimit = value; }
	setFeatures(value: number) { this.changed = true; this.features = value; }
	setCloudStorageLimit(value: number) { this.changed = true; this.cloudStorageLimit = value; }
	setAvailableUpgrades(value: string) { this.changed = true; this.availableUpgrades = value; }
	setStatus(value: number) { this.changed = true; this.status = value; }
    changed = false;
    setChanged() {
        this.changed = true;
    }

    clearChanged() {
        this.changed = false;
    }

    constructor(state? : IWireLicense | ILicense) {
        if (!state)
            throw "An License must have a valid start state";
        this.flags = state.flags;;
	if (typeof(state.modifiedTime) === "string")
            this.modifiedTime = new Date(state.modifiedTime);
         else
            this.modifiedTime = state.modifiedTime;
	if (typeof(state.createdTime) === "string")
            this.createdTime = new Date(state.createdTime);
         else
            this.createdTime = state.createdTime;
	this.licenseId = state.licenseId;;
	this.accountId = state.accountId;;
	this.manager = state.manager;;
	if (typeof(state.endDate) === "string")
            this.endDate = new Date(state.endDate);
         else
            this.endDate = state.endDate;
	this.templateId = state.templateId;;
	this.activationKey = state.activationKey;;
	this.deviceLimit = state.deviceLimit;;
	this.photoLimit = state.photoLimit;;
	this.features = state.features;;
	this.cloudStorageLimit = state.cloudStorageLimit;;
	this.availableUpgrades = state.availableUpgrades;;
	this.status = state.status;
        makeAutoObservable(this, {
            flags: true,
			modifiedTime: true,
			createdTime: true,
			licenseId: true,
			accountId: true,
			manager: true,
			endDate: true,
			templateId: true,
			activationKey: true,
			deviceLimit: true,
			photoLimit: true,
			features: true,
			cloudStorageLimit: true,
			availableUpgrades: true,
			status: true
        });

    }

    state() : ILicense {
        return {
            flags : this.flags,
		modifiedTime : this.modifiedTime,
		createdTime : this.createdTime,
		licenseId : this.licenseId,
		accountId : this.accountId,
		manager : this.manager,
		endDate : this.endDate,
		templateId : this.templateId,
		activationKey : this.activationKey,
		deviceLimit : this.deviceLimit,
		photoLimit : this.photoLimit,
		features : this.features,
		cloudStorageLimit : this.cloudStorageLimit,
		availableUpgrades : this.availableUpgrades,
		status : this.status
        };
    }

    asWire() : IWireLicense {
        return {
            flags : this.flags,
		modifiedTime : this.modifiedTime ? this.modifiedTime.toISOString() : undefined,
		createdTime : this.createdTime ? this.createdTime.toISOString() : undefined,
		licenseId : this.licenseId,
		accountId : this.accountId,
		manager : this.manager,
		endDate : this.endDate ? this.endDate.toISOString() : undefined,
		templateId : this.templateId,
		activationKey : this.activationKey,
		deviceLimit : this.deviceLimit,
		photoLimit : this.photoLimit,
		features : this.features,
		cloudStorageLimit : this.cloudStorageLimit,
		availableUpgrades : this.availableUpgrades,
		status : this.status
        };
    }

    
/* b::mobx_public_members */
// public members

	// public members
	private featureBits_: FeatureBits;
	featureBits() {
		if (!this.featureBits_) {
			this.featureBits_ = new FeatureBits(this.features);
			this.featureBits_.onChange = ((value: number) => {
				this.setFeatures(value);
			});
		}
		return this.featureBits_;
	}

	licenseFlags_: LicenseFlags;
	licenseFlags() {
		if (!this.licenseFlags_) {
			this.licenseFlags_ = new LicenseFlags(this.flags);
			this.licenseFlags_.onChange = ((flags: number) => {
				this.setFlags(flags);
			});
		}
		return this.licenseFlags_;
	}

	paid() {
		return this.manager !== Manager.Mylio;
	}
/* end */


    


}


