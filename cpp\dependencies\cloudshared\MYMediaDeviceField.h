#pragma once

#include "MYStringUtil.h"
#include "MYEnums.h"
#include "MYPrimitives.h"
#include "MYHash.h"

// struct MYMediaColumnNames
//{
//     static std::string NeedsHasPrefix;
//     static std::string HRevPrefix;
//     static std::string NRevPrefix;
//
//     MYMediaColumnNames(int mediaColumn) :
//         _column(mediaColumn),
//         _needsHasFieldName(NeedsHasPrefix + std::to_string(mediaColumn)),
//         _hrevFieldName(HRevPrefix + std::to_string(mediaColumn)),
//         _nrevFieldName(NRevPrefix + std::to_string(mediaColumn))
//     {
//     }
//
// private:
//     int _column;
//     std::string _needsHasFieldName;
//     std::string _hrevFieldName;
//     std::string _nrevFieldName;
//
// public:
//     const std::string& getNeedsHasFieldName() const { assert(_needsHasFieldName.size() > 0);  return _needsHasFieldName; }
//     const std::string& getHRevFieldName() const { assert(_hrevFieldName.size() > 0); return _hrevFieldName; }
//     const std::string& getNRevFieldName() const { assert(_nrevFieldName.size() > 0); return _nrevFieldName; }
//
//     const std::string getLegacyRandomOrderFieldName() const;
//     const std::string getLegacyOtherDeviceNeedsFieldName() const;
//     const std::string getLegacyOtherDeviceNeedsIndex() const;
//     const std::string getLegacyRandomOrderIndexName() const;
// };
