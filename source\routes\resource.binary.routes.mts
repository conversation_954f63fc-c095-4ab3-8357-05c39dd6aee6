import express = require("express");
/* tslint:disable */
import { binaryEncoder } from "../system/bjson.cjs";
import { rateLimiter } from "../system/RateLimiter.mjs";
/* tslint:enable */

export function addBinaryRoutes(router: express.Router) {
  router.post("/accounts/:aid/ping", (req, res, next) => {
    if (req.header("Content-Type") === "application/octet-stream") {
      try {
        //let reqid = new Date().valueOf();
        //console.log(`{"r":${reqid}, "p":"${req.path}", "a":"${req.header("User-Agent")}"}`);
        req.body = binaryEncoder.decode_resource_ping_request(req.body);
        //console.log(`{"s":${reqid}, "p":"${req.path}", "a":"${req.header("User-Agent")}"}`);
      } catch (err) {
        next({ message: err });
      }
    }
    next();
  });

  router.post("/accounts/:aid/sync", rateLimiter(1, 60000), (req, res, next) => {
    if (req.header("Content-Type") === "application/octet-stream") {
      try {
        //let reqid = new Date().valueOf();
        //console.log(`{"r":${reqid}, "p":"${req.path}", "a":"${req.header("User-Agent")}"}`);
        req.body = binaryEncoder.decode_sync(req.body);
        //console.log(`{"s":${reqid}, "p":"${req.path}", "a":"${req.header("User-Agent")}"}`);
      } catch (err) {
        next({ message: err });
      }
    }
    next();
  });

  router.post("/accounts/:aid/ping256", (req, res, next) => {
    if (req.header("Content-Type") === "application/octet-stream") {
      try {
        //let reqid = new Date().valueOf();
        //console.log(`{"r":${reqid}, "p":"${req.path}", "a":"${req.header("User-Agent")}"}`);
        req.body = binaryEncoder.decode_ping256(req.body);
        //console.log(`{"s":${reqid}, "p":"${req.path}", "a":"${req.header("User-Agent")}"}`);
      } catch (err) {
        next(err);
      }
    }
    next();
  });
}
