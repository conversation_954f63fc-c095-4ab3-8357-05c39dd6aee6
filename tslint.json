{"rules": {"align": [true, "parameters", "statements"], "ban": false, "class-name": true, "comment-format": [true, "check-space", "check-lowercase"], "curly": false, "eofline": true, "forin": true, "indent": [true, "spaces"], "interface-name": true, "jsdoc-format": true, "label-position": true, "label-undefined": true, "max-line-length": [true, 140], "member-access": true, "member-ordering": [true, "public-before-private", "static-before-instance", "variables-before-functions"], "no-any": false, "no-arg": true, "no-bitwise": false, "no-conditional-assignment": true, "no-consecutive-blank-lines": false, "no-console": [true, "debug", "info", "time", "timeEnd", "trace"], "no-construct": true, "no-constructor-vars": false, "no-debugger": true, "no-duplicate-key": true, "no-duplicate-variable": true, "no-empty": true, "no-eval": true, "no-inferrable-types": false, "no-internal-module": true, "no-null-keyword": true, "no-shadowed-variable": true, "no-string-literal": false, "no-switch-case-fall-through": true, "no-trailing-whitespace": true, "no-unreachable": true, "no-unused-expression": true, "no-unused-variable": true, "no-use-before-declare": true, "no-var-keyword": true, "object-literal-sort-keys": false, "one-line": [true, "check-open-brace", "check-catch", "check-whitespace"], "quotemark": [true, "double", "avoid-escape"], "radix": true, "semicolon": true, "switch-default": true, "trailing-comma": [false, {"multiline": "always", "singleline": "never"}], "triple-equals": [true, "allow-null-check"], "typedef-whitespace": [true, {"call-signature": "nospace", "index-signature": "nospace", "parameter": "nospace", "property-declaration": "nospace", "variable-declaration": "nospace"}], "use-strict": [true, "check-module"], "variable-name": [true, "check-format", "allow-leading-underscore", "ban-keywords"], "whitespace": [true, "check-branch", "check-decl", "check-operator", "check-separator", "check-type"]}}