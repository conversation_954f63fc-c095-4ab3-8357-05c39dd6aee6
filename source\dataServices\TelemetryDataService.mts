import { query, cs } from "../system/Postgres.mjs";
import { Context } from "../system/Context.mjs";
import { EventType } from "../system/Strings.mjs";

export class TelemetryDataService {
  public saveCounters(
    context: Context,
    accountId: number,
    deviceId: number,
    counter: any
  ) {
    return query<any>(
      context,
      "select * from t0.save_counters($1, $2, $3)",
      [accountId, deviceId, counter],
      cs(context, "t0")
    );
  }

  public saveClientEvents(
    context: Context,
    longDeviceId: string,
    events: string
  ) {
    return query<any>(
      context,
      "select * from t0.save_client_events($1, $2)",
      [longDeviceId, JSON.stringify(events)],
      cs(context, "t0")
    );
  }

  public saveExitSurvey(context: Context, accountId: number, data: any) {
    return query<any>(
      context,
      "select * from t0.save_exit_survey($1, $2)",
      [accountId, JSON.stringify(data)],
      cs(context, "t0")
    );
  }

  public getLastAccessTimeForAccount(
    context: Context,
    accountId: number
  ): Promise<Date> {
    const SQL =
      'select max(modified_time) as "lastAccessTime" from t0.counters where account_id = $1';

    return query<Date>(context, SQL, [accountId], cs(context, "t0")).then(
      (results: Date[]) => results[0]
    );
  }

  public saveCloudEvent(
    context: Context,
    accountId: number,
    deviceId: number,
    eventType: EventType,
    data: any
  ) {
    return query<any>(
      context,
      "select * from t0.save_cloud_event($1, $2, $3, $4, $5)",
      [accountId, new Date(), deviceId, eventType, JSON.stringify(data)],
      cs(context, "t0")
    ).catch((error) => {
      console.error(error);
    });
  }
}
