#pragma once

#include <memory>
#include <string>
#include <set>
#include <array>
#include <bitset>
#include <algorithm>
#include "MyloAPI.h"
#include "base64.h"
#include "MYHash.h"
#include "MYStringUtil.h"
#include "MYDeviceId.h"

#define MYHASH_SIZE 20
#define MIRRORINFO_SIZE 3

typedef std::array<unsigned char, MYHASH_SIZE> MYHashStorage;

#ifdef MYLIO_CLIENT
enum MYKindCode : uint8_t;
#define KindCode_t MYKindCode
#else
#define KindCode_t uint8_t
#endif

#pragma pack(1)
struct MYTRevKindAndVersion
{
    std::array<uint8_t, 12> version;
    uint8_t kindCode;

    std::string toSQLString() const;
    bool empty() const;
    void clear();
};

extern MYTRevKindAndVersion g_emptyKindAndVersion;

struct MYTRev
{
private:
    ////////////////////////////////////////////////////
    // Immutable trev ID
    ////////////////////////////////////////////////////

    // Offset 0 to 19
    // Immutable after creation
    // Sequence based numbers: least significant bytes in little endian
    // HashId based numbers: Resource UniqueHash
    union
    {
        std::array<uint8_t, 20> _id_storage;
        struct // Using a 'rid' as an ID'
        {
            std::array<uint8_t, 20> _hashId;
        };
        struct // Using a 'uint32' as an ID'
        {
            uint32_t _uint32id_little_endian; // 0 to 3   UInt32 ID - stored in little endian
            uint32_t _padding1;               // 4 to 7   must be zero
            uint64_t _padding2;               // 8 to 15  must be zero
            uint32_t _padding3;               // 16 to 19 must be zero
        };
    };

    ////////////////////////////////////////////////////
    // Mutable 'version' part of trev
    ////////////////////////////////////////////////////

    // Offset 20 to 23
    // Mutable (read-increment-write)
    // 0 to 4 billion. After that, stays at 4 billion
    // Stored in network order (big endian): e.g.: 1 is stored as: 00 00 00 01
    // Postgres in: orev = (bytea(orev,0) << 24) + (bytea(orev,1) << 16) + (bytea(orev,2) << 8) + bytea(orev,3)
    // Postgres out: int4send(orev)
    uint32_t _orev_big_endian;

    // Offset 24 to 27
    // Stored in network order (big endian)
    // UNSIGNED time in seconds since mylio epoch (1/1/2012). Good until 02/07/2148.
    // At 2017/01/01 this value was 157'680'000.
    //
    // On generating a new trev:
    //       If current system time < mylio epoch, use milliseconds since UDT midnight of current day. [0 to 86'400'000]
    //       If current system time >  12/31/2069, use milliseconds since UDT midnight of current day minus 1. [86'400'001 to 172'800'000]
    //          |->  It's just arbitrary to protect against fat fingering a date/time and causing replication chaos.
    //          |->  Update this value again sometime after 2038 when 1970 became a meaningless legacy boundary.
    // On incrementing an existing trev:
    //       * If current system time < mylio epoch
    //       * If current system time > 12/31/2069
    //       * If current system time < newer trev time
    //           |-> Take previous trev mylioTime and add 1 to it.
    uint32_t _mylioTime_big_endian;

    // Offset 28 to 31
    // Executing device id - for cloud = 0
    // Stored in network order (big endian)
    uint32_t _deviceId_big_endian;

    // Offset 32
    // Kind code of the resource
    uint8_t _kindCode;

public:
    MYTRev();
    MYTRev(no_init_t);
    MYTRev(const MYHashStorage &rid, const MYTRevKindAndVersion &version);
    MYTRev(const MYHash &rid, const MYTRevKindAndVersion &version);
    MYTRev(const MYHash &rid, KindCode_t kindCode, MYDeviceId deviceId);
    MYTRev(uint32_t id, KindCode_t kindCode, MYDeviceId deviceId);
    MYTRev(const std::string &s, SerializationFlags flags);
    MYTRev(const std::string &s, bool base64);

    MYTRev(unsigned char *blob);

    MYTRev next(MYDeviceId deviceId, uint32_t incrementOrevBy = 1) const;
    void clear();
    bool empty() const;

    void setHashId(const MYHashStorage &hashId, uint8_t newKindCode);
    MYHash getHashId() const;
    void setUInt32Id(uint32_t newId, uint8_t newKindCode);
    uint32_t getUInt32Id() const;
    std::array<uint8_t, 20> getRawId() const;

    void setTRevKindAndVersion(const MYTRevKindAndVersion &newVersion);
    const MYTRevKindAndVersion &getTRevKindAndVersion() const;

    KindCode_t getKindCode() const;
    void setKindCode(KindCode_t kindCode);

    void setMylioTime(uint32_t timeValue); // Mylio time since epoch, in machine endian
    uint32_t getMylioTime() const;         // Mylio time since epoch, in machine endian
    uint32_t getUnixTime() const;          // Unix time in machine endian

    void setOrev(uint32_t orev); // Machine endian
    uint32_t getOrev() const;    // Machine endian

    void setDeviceId(MYDeviceId deviceId);
    MYDeviceId getDeviceId() const;

    // Used for compare when the id's are already known to match
    signed int quickRevisionCompare(const MYTRev &other) const; // < 0 if 'this' is smaller, 0 for equal, > 0 if 'this' is larger

    bool operator<(const MYTRev &other) const;
    bool operator==(const MYTRev &other) const;
    bool operator!=(const MYTRev &other) const;

    static MYTRev fromString(const std::string &s, SerializationFlags flags);
    static MYTRev fromString(const std::string &s, bool base64 = false);

    std::string toSQLString() const;
    std::string toString(SerializationFlags flags) const;
    std::string toString(bool base64 = false) const;
    std::string toTraceString() const;

private:
    bool isValid() const;

    static uint32_t getNewTimeStamp();                              // Machine endian out
    static uint32_t getUpdatedTimeStamp(uint32_t currentTimeStamp); // Machine endian in, out

} PACK_ENDER;
#pragma pack()

static_assert(sizeof(MYTRev) == 33, "TRev is expected to be 33 bytes");

template <size_t N>
class bitsetblob
{
public:
    std::bitset<N> _bitset;
    std::string toString(SerializationFlags flags)
    {
        static_assert(N > 0, "bitsetblobs must have more than 0 bits");
        static_assert(N % 8 == 0, "bitsetblobs must be 8-bit aligned");

        const size_t BYTES = N / 8;

        uint8_t buf[BYTES];
        memset(buf, 0, BYTES);

        int n = 0;
        for (int i = 0; i < BYTES; i++)
        {
            uint8_t *pbuf = &(buf[i]);
            for (signed int j = 7; j >= 0; j--)
            {
                int8_t bit = _bitset.test(n);
                *pbuf <<= 1;
                *pbuf |= bit;

                ++n;
            }
        }

        if (flags & SerializationFlags::Base64Hash)
        {
            std::string b64(base64_encode(buf, N / 8));
            return b64;
        }
        else
        {
            return MYString::blobToHexString(buf, BYTES);
        }
    }

    void fromString(const std::string &b64, SerializationFlags flags)
    {
        std::string bytes;

        if (flags & SerializationFlags::Base64Hash)
        {
            bytes = base64_decode(b64);
        }
        else
        {
            bytes = MYString::hexStringToBlob(b64);
        }

        uint8_t *ch = ((uint8_t *)bytes.c_str());

        const size_t BYTES = N / 8;

        if (bytes.length() > BYTES)
        {
            assert(false);
            return;
        }

        size_t maxSize = std::min(bytes.length(), BYTES);

        size_t n = 0;
        for (size_t i = 0; i < maxSize; i++)
        {
            for (signed int j = 7; j >= 0; j--)
            {
                bool v = (bool)((*ch >> j) & 1);
                _bitset.set(n, v);

                ++n;
            }
            ++ch;
        }
    }
};

struct MYTRevLookupRange
{
    uint16_t bucket;
    MYTRev greaterThanOrEqual;
    MYTRev smallerThanOrEqual;
    MYTRevLookupRange(const MYHash hash, KindCode_t kindCode);
};

extern MYTRev g_emptyTRev;
