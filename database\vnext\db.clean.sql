SELECT pg_terminate_backend(pg_stat_activity.pid)
FROM pg_stat_get_activity(NULL::integer) pg_stat_activity
WHERE datid=(SELECT oid from pg_database where datname = ':dbname');

\c :dbname

DO $$
  DECLARE
    __sql text;
  BEGIN
    FOR __sql IN
      SELECT 'DROP FUNCTION IF EXISTS ' || ns.nspname || '."' || proname  || '" (' || oidvectortypes(proargtypes) || ') CASCADE;'
      FROM pg_proc INNER JOIN pg_namespace ns ON (pg_proc.pronamespace = ns.oid)
      WHERE ns.nspname = 'a0'
      UNION
      SELECT 'DROP VIEW IF EXISTS ' || table_schema || '."' || table_name || '" CASCADE;'
      FROM information_schema.views
      WHERE table_schema NOT IN ('pg_catalog', 'information_schema')
      AND table_name !~ '^pg_'
    LOOP
      EXECUTE __sql;
    END LOOP;
END;
$$ LANGUAGE plpgsql;
