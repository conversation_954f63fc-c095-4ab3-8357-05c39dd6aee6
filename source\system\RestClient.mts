// @ts-nocheck
import * as https from "https";
import http = require("http");
import { raw, response } from "express";

function formEncodeObjectR(path: string, obj: any, parts: string[]) {
  if (typeof obj === "object") {
    for (let field of Object.getOwnPropertyNames(obj)) {
      if (path && path.length) {
        formEncodeObjectR(`${path}[${field}]`, obj[field], parts);
      } else {
        formEncodeObjectR(field, obj[field], parts);
      }
    }
  } else {
    parts.push(`${path}=${obj}`);
  }
}

function formEncodeObject(obj: object) {
  let parts = new Array<string>();
  formEncodeObjectR("", obj, parts);
  return parts.join("&");
}

export class RestClientMimeType {
  public static JSON = "application/json";
  public static Form = "application/x-www-form-urlencoded";
  public static None = "";
}

class Headers {
  public raw = {} as any;

  public parse(headers: any) {
    for (let header of Object.getOwnPropertyNames(headers)) {
      this.raw[header.toLowerCase()] = headers[header];
    }
  }

  public copy() {
    let result = new Headers();
    result.parse(this.raw);
    return result;
  }

  public accept(value?: string) {
    if (value !== void 0) {
      let isValid = false;
      for (let field of Object.getOwnPropertyNames(RestClientMimeType)) {
        if (RestClientMimeType[field] === value) {
          isValid = true;
          break;
        }
      }
      if (isValid) {
        this.raw["accept"] = value;
      } else {
        throw "Invalid Content Type";
      }
    }
    return this.raw["accept"] as string;
  }
  public contentType(value?: string) {
    if (value !== void 0) {
      this.raw["content-type"] = value;
    }
    return this.raw["content-type"] as string;
  }
  public bearerToken(value?: string) {
    if (value !== void 0) {
      this.raw["authorization"] = `Bearer ${value}`;
    }
    return this.raw["authorization"].split(" ")[1] as string;
  }
  public contentLength(value?: number) {
    if (value !== void 0) {
      this.raw["content-length"] = value.toString(10);
    }
    return parseInt(this.raw["content-length"], 10);
  }
}

export class RestClient {
  private defaultHeaders_: Headers;
  private url_: URL;

  constructor(
    url: string,
    bearerToken = "",
    defaultContentType = "application/json",
    defaultAccept = "application/json",
    public errorParser: (x: any) => {
      code: string;
      message: string;
      status?: number;
    } = (x) => x
  ) {
    this.defaultHeaders_ = new Headers();
    this.defaultHeaders_.bearerToken(bearerToken);
    this.defaultHeaders_.contentType(defaultContentType);
    this.defaultHeaders_.accept(defaultAccept);
    this.url_ = new URL(url);
  }

  public request(
    method: string,
    endpoint: string,
    data: any = undefined,
    contentType: string = undefined,
    accept: string = undefined
  ) {
    let headers = this.defaultHeaders_.copy();

    if (contentType && contentType.length) headers.contentType(contentType);

    if (accept && accept.length) headers.accept(accept);

    let requestBody = "";
    if (data) {
      if (headers.contentType() === RestClientMimeType.JSON)
        requestBody = JSON.stringify(data);

      if (headers.contentType() === RestClientMimeType.Form) {
        requestBody = formEncodeObject(data);
      }
      if (requestBody.length) headers.contentLength(requestBody.length);
    }

    let options: https.RequestOptions = {
      headers: headers.raw,
      protocol: this.url_.protocol,
      path: this.url_.pathname,
      hostname: this.url_.hostname,
      port: this.url_.port || 443,
      timeout: 3,
      method: method,
    };

    if (endpoint.startsWith("/") || options.path.endsWith("/"))
      options.path += endpoint;
    else options.path = options.path + "/" + endpoint;

    return new Promise<any>((resolve, reject) => {
      let service = https as any;
      if (options.protocol === "http:") service = http;

      let req = service.request(options, (res) => {
        let responseData = "";
        res.on("data", (chunk) => {
          responseData += chunk;
        });

        res.on("end", () => {
          if (res.statusCode >= 500)
            return reject({
              code: "REST.SERVER_ERROR",
              message: `Server error making REST call to ${this.url_.toString()}`,
            });

          let responseHeaders = new Headers();
          responseHeaders.parse(res.headers);
          let error = undefined;
          let responseBody = undefined;

          let validContentType = true;
          if (headers.accept())
            validContentType = responseHeaders
              .contentType()
              .includes(headers.accept());

          if (!validContentType) {
            error = {
              status: res.statusCode,
              code: "REST.INVALID_RESPONSE_CONTENT_TYPE",
              message: `Expected response content-type of ${headers.accept()} but received ${responseHeaders.contentType()}`,
            };
          }

          let isJson =
            responseHeaders.contentType() &&
            responseHeaders.contentType().includes(RestClientMimeType.JSON);

          if (isJson) responseBody = JSON.parse(responseData);

          if (res.statusCode > 400 && res.statusCode < 500) {
            error = isJson ? this.errorParser(responseBody) : {};
            error.status = res.statusCode;
          }

          if (error) reject(error);
          else resolve(responseBody);
        });
      });

      req.on("error", (err) => {
        reject({
          status: 500,
          code: "REST.ERROR_ON_REQUEST",
          message: `REST call to ${this.url_.toString()} failed with error ${JSON.stringify(
            err
          )}`,
        });
      });

      if (requestBody && requestBody.length) req.write(requestBody);

      req.end();
    });
  }

  public async get(
    endpoint: string,
    data = undefined,
    contentType = undefined,
    accept = undefined
  ) {
    return this.request("get", endpoint, data, contentType, accept);
  }

  public async post(
    endpoint: string,
    data = undefined,
    contentType = undefined,
    accept = undefined
  ) {
    return this.request("post", endpoint, data, contentType, accept);
  }

  public async delete(
    endpoint: string,
    data = undefined,
    contentType = undefined,
    accept = undefined
  ) {
    return this.request("delete", endpoint, data, contentType, accept);
  }
}

export async function get(url: string, body = undefined) {
  let urlObj = new URL(url);
  let r = new RestClient(url, urlObj.hostname);
  return r.get(urlObj.pathname, body);
}

export async function post(url: string, body = undefined) {
  let urlObj = new URL(url);
  let r = new RestClient(url, urlObj.hostname);
  return r.post(urlObj.pathname, body);
}
