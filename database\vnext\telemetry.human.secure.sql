DO
$body$
BEGIN
   IF NOT EXISTS (
      SELECT
      FROM   pg_catalog.pg_user
      WHERE  usename = 'datawarehouse') THEN
        CREATE USER datawarehouse NOSUPERUSER PASSWORD 'password';
   END IF;
   IF NOT EXISTS (
      SELECT
      FROM   pg_catalog.pg_user
      WHERE  usename = 'cloud') THEN
        CREATE USER cloud NOSUPERUSER PASSWORD 'password';
   END IF;
   IF NOT EXISTS (
      SELECT
      FROM   pg_catalog.pg_user
      WHERE  usename = 'reports') THEN
        CREATE USER reports NOSUPERUSER PASSWORD 'password';
   END IF;
END
$body$;

REVOKE ALL ON DATABASE telemetry FROM PUBLIC;
GRANT CONNECT ON DATABASE telemetry TO datawarehouse;
GRANT CONNECT ON DATABASE telemetry TO cloud;
GRANT CONNECT ON DATABASE telemetry TO reports;

\c telemetry;

-- remove all rights for everyone except postgres
REVOKE USAGE ON SCHEMA t0 FROM PUBLIC;
REVOKE ALL ON ALL TABLES IN SCHEMA t0 FROM PUBLIC;
R<PERSON>VOKE ALL ON ALL SEQUENCES IN SCHEMA t0 FROM PUBLIC;
REVOKE ALL ON ALL FUNCTIONS IN SCHEMA t0 FROM PUBLIC;

-- datawarehouse needs permission to get updated telemetry data
grant usage on schema t0 to datawarehouse;
grant usage on schema public to datawarehouse;

-- Create the telemetry user
grant usage on schema t0 to cloud;
grant usage on schema public to cloud;
grant temp on database telemetry to cloud;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA t0 TO cloud;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA t0 TO cloud;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA t0 TO cloud;

-- reports user
GRANT USAGE ON SCHEMA t0 TO reports;
GRANT SELECT ON ALL TABLES IN SCHEMA t0 TO reports;

grant execute on function digest(bytea, text) to public;
grant execute on function digest(text, text) to public;
grant execute on function gen_random_bytes(int) to public;