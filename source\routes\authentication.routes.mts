import express = require("express");
import { microservice as g } from "../microservices/account.microservice.mjs";
import {
  safeNone,
  safeAny,
  secure,
  safeAccount,
  secureURLToken,
} from "../system/safe.mjs";
import { sanitizeOutput, Account } from "../models/Account.model.mjs";
import { Context } from "../system/Context.mjs";
import { config, getServices } from "../system/Config.mjs";
import { IInvitation, sanitizeOutput as sanitizeInvitation } from "../models/Invitation.model.mjs";

/* tslint:disable */
import { binaryEncoder, sendResponse } from "../system/bjson.cjs";
import { recaptcha } from "../system/recaptcha.mjs";
import { Invitation } from "../models/Invitation.model.mjs";
/* tslint:enable */

export function composeResult(
  context: Context,
  token: string,
  unclean: Account,
  rtoken?: string,
  uncleanInvitation?: Invitation
) {
  const services = getServices(context);
  const key = unclean.rsaPrivateKey();
  const cert = unclean.x509Cert();
  const account = sanitizeOutput(unclean, context.hasAdminRights());
  let invitation: IInvitation;
  if (uncleanInvitation !== undefined) {
    invitation = sanitizeInvitation(uncleanInvitation, context.hasAdminRights());
    return { services, account, token, rtoken, key, cert, invitation };
  }
  else {
    return { services, account, token, rtoken, key, cert };
  }
}

export function addAuthenticationRoutes(router: express.Router) {
  router.get("/v2/accounts/:aid/token", safeNone, async (req, res, next) => {
    const context: Context = req.context;
    const { token, account } = await g.authenticationService.refreshAccessToken(
      context,
      context.tokenString
    );
    await g.accountService.showUpgradeWarnings(context, context.aid, context.userAgent?.deviceId, context.userAgent?.protocolVersion);
    let result = composeResult(context, token, account, context.tokenString);
    sendResponse(req, res, result, (r) =>
      binaryEncoder.encode_token_responseV2(r)
    );
  });

  let rtokenHandler = async (req, res, next) => {
    const context: Context = req.context;
    let params = context.any;
    const { token, account, rtoken } = await g.authenticationService.signin(
      context,
      context.audience,
      params.sub,
      "mylio",
      params.password,
      params.pin,
      params.codeVerifier
    );
    let result = composeResult(context, token, account, rtoken);
    sendResponse(req, res, result, (r) =>
      binaryEncoder.encode_token_responseV2(r)
    );
  };
  router.post("/v4/accounts/x/rtoken", safeAny, rtokenHandler);
  router.post("/v3/accounts/x/rtoken", safeAny, rtokenHandler);
  router.post(
    "/accounts/reset/:token",
    safeAccount,
    secureURLToken,
    (req, res, next) => {
      let context = req.context;
      let account = context.account;
      let token = context.token;
      account.accountId(token.aid());
      return g.authenticationService
        .resetPassword(context, account)
        .then(() => {
          context.dumpLog();
          return res.sendStatus(200);
        })
        .catch(next);
    }
  );

  router.post("/v2/emails/reset", safeAccount, recaptcha, (req, res, next) => {
    let context = req.context;

    let userAgent = req.header("User-Agent");
    console.log(`/emails/reset: ${userAgent}`);

    g.accountService.ensureEmailSubIdpIsLowerCase(context.account);
    let sub = context.account.sub();
    let idp = context.account.idp();

    return g.authenticationService
      .sendResetEmail(context, sub, idp)
      .then(() => {
        context.dumpLog();
        return res.sendStatus(200);
      });
  });
}
