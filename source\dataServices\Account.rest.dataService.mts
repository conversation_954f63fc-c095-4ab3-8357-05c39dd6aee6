
import { query } from "../system/Postgres.mjs";
import { Account, IAccount} from "../models/Account.model.mjs";
import { Context } from "../system/Context.mjs";
import { makeError } from "../system/error.mjs";


export class AccountRestDataService {

    
/* b::rest_public_members */
public readAccounts(context: Context, n = 10) {
		return this.query(
			context,
			`select * from a0."Account" where sub != 'system' and role != 'admin' order by "accountId" desc limit ${n}`,
			[]
		);
	}
/* end */


  public query(context: Context, sql: string, params: any[]) {
    return query < IAccount> (context, sql, params)
      .then(results => {
        if (results.length === 1) {
          if (!results[0].accountId) {
      return [];
    }
  }
  return results.filter(o => !!o).map(o => new Account(o));
});
    }

		public create (context: Context, entity: Account) {
          


  let params = [
    entity.flags(),
				entity.modifiedTime(),
				entity.createdTime(),
				entity.accountId(),
				entity.t(),
				entity.cipher(),
				entity.clientCipher(),
				entity.clientCipherVersion(),
				entity.minBuild(),
				entity.peerToPeerKey(),
				entity.clientPeerToPeerKey(),
				entity.clientPeerToPeerKeyVersion(),
				entity.rsaPrivateKey(),
				entity.x509Cert(),
				entity.tfa(),
				entity.idp(),
				entity.sub(),
				entity.email(),
				entity.passwordHash(),
				entity.passwordHashVersion(),
				entity.salt(),
				entity.passwordSetTime(),
				entity.planId(),
				entity.role(),
				entity.deviceLimit(),
				entity.photoLimit(),
				entity.cloudStorageLimit(),
				entity.features(),
				entity.nextPlanDate(),
				entity.availableUpgrades(),
				entity.licenseTemplateId(),
				entity.licenseDisplayName(),
				entity.licenseManager(),
				entity.licenseFlags(),
				entity.availableUpgradesFeatures(),
				entity.licenseId(),
				entity.affiliateId()
  ];
  return this
    .query(context, "select * from a0.account_create ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14,$15,$16,$17,$18,$19,$20,$21,$22,$23,$24,$25,$26,$27,$28,$29,$30,$31,$32,$33,$34,$35,$36,$37) ", params)
  .then(r => r[0]);
        }

		public readByAccountId (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.account_read_by_account_id  ($1) ", params).then(r => r[0]); 
                
        }

		public readBySubAndIdp (context: Context, sub: string, idp: string) {
  let params = [
    sub,
				idp
  ];
  return this
    .query(context, "select * from a0.account_read_by_sub_and_idp  ($1,$2) ", params).then(r => r[0]); 
                
        }

		public update (context: Context, entity: Account) {
          


  let params = [
    entity.flags(),
				entity.modifiedTime(),
				entity.createdTime(),
				entity.accountId(),
				entity.t(),
				entity.cipher(),
				entity.clientCipher(),
				entity.clientCipherVersion(),
				entity.minBuild(),
				entity.peerToPeerKey(),
				entity.clientPeerToPeerKey(),
				entity.clientPeerToPeerKeyVersion(),
				entity.rsaPrivateKey(),
				entity.x509Cert(),
				entity.tfa(),
				entity.idp(),
				entity.sub(),
				entity.email(),
				entity.passwordHash(),
				entity.passwordHashVersion(),
				entity.salt(),
				entity.passwordSetTime(),
				entity.planId(),
				entity.role(),
				entity.deviceLimit(),
				entity.photoLimit(),
				entity.cloudStorageLimit(),
				entity.features(),
				entity.nextPlanDate(),
				entity.availableUpgrades(),
				entity.licenseTemplateId(),
				entity.licenseDisplayName(),
				entity.licenseManager(),
				entity.licenseFlags(),
				entity.availableUpgradesFeatures(),
				entity.licenseId(),
				entity.affiliateId()
  ];
  return this
    .query(context, "select * from a0.account_update ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14,$15,$16,$17,$18,$19,$20,$21,$22,$23,$24,$25,$26,$27,$28,$29,$30,$31,$32,$33,$34,$35,$36,$37) ", params)
  .then(r => r[0]);
        }

		public deleteByAccountId (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.account_delete_by_account_id  ($1) ", params).then(r => r[0]); 
                
        }

		public deleteBySubAndIdp (context: Context, sub: string, idp: string) {
  let params = [
    sub,
				idp
  ];
  return this
    .query(context, "select * from a0.account_delete_by_sub_and_idp  ($1,$2) ", params).then(r => r[0]); 
                
        }

		public findByEmail (context: Context, email: string) {
  let params = [
    email
  ];
  return this
    .query(context, "select * from a0.account_find_by_email  ($1) ", params); 
                
        }

		public findBySub (context: Context, sub: string) {
  let params = [
    sub
  ];
  return this
    .query(context, "select * from a0.account_find_by_sub  ($1) ", params); 
                
        }

		public merkle (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.account_merkle ($1) ", params).then(r => r[0]); 
                
        }

}
