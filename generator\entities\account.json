{"plural": "accounts", "k": 5, "flow": "web<>cloud<>postgres<>disk", "mixin": ["all"], "datatype": "string", "fields": {"account_id": {"datatype": "int32", "autoNumber": true}, "t": {"datatype": "binary"}, "cipher": {"datatype": "binary"}, "client_cipher": {"datatype": "binary"}, "client_cipher_version": {"datatype": "int32"}, "min_build": {"datatype": "int32"}, "peer_to_peer_key": {}, "client_peer_to_peer_key": {}, "client_peer_to_peer_key_version": {"datatype": "int32"}, "rsa_private_key": {}, "x509_cert": {}, "tfa": {"datatype": "boolean"}, "idp": {}, "sub": {}, "email": {}, "group-a": {"flow": "cloud<>postgres<>disk", "fields": {"password_hash": {}, "password_hash_version": {"datatype": "int32"}, "salt": {}, "password_set_time": {"datatype": "date"}}}, "group-b": {"flow": "web<cloud<>postgres<>disk", "datatype": "int32", "fields": {"plan_id": {"datatype": "string"}, "role": {"datatype": "string"}, "device_limit": {}, "photo_limit": {}, "cloud_storage_limit": {}, "features": {}, "next_plan_date": {"datatype": "date"}, "available_upgrades": {"datatype": "string"}, "license_template_id": {"datatype": "string"}, "license_display_name": {"datatype": "string"}, "License_manager": {"datatype": "string"}, "license_flags": {"datatype": "int32"}, "available_upgrades_features": {"datatype": "int32"}, "license_id": {"datatype": "string"}}}, "password": {"flow": "web>cloud"}, "affiliate_id": {}}, "directives": {"identify": [["account_id"], ["sub", "idp"]], "find": [["email"], ["sub"]]}}