import { Entity } from "../Entity.mjs";
import {
  <PERSON>riptHelper,
  blockI,
  sql2js,
  sqldt,
  jsname,
  ppdt,
  pname,
  mname,
} from "../ScriptHelper.mjs";
import _ = require("lodash");

export function template(e: Entity, h: ScriptHelper) {
  for (let f of h.updatable) {
    let blockId = `__i_${f.name}`;
    let expr = pname(f);
    e.generatedBlocks.set(blockId, expr);
  }

  for (let f of h.updatable) {
    let blockId = `__u_${f.name}`;
    let expr = pname(f);
    e.generatedBlocks.set(blockId, expr);
  }

  for (let f of h.diskFields) {
    let blockId = `__s_${f.name}`;
    let outName = jsname(f);
    let expr = sql2js(f);
    if (expr !== f.name || outName !== f.name) expr = `${expr} as "${outName}"`;
    e.generatedBlocks.set(blockId, expr);
  }

  for (let f of _.difference(h.pgParams, h.diskFields)) {
    let blockId = `__s_${f.name}`;
    let expr = `null::${ppdt(f)} as "${jsname(f)}"`;
    e.generatedBlocks.set(blockId, expr);
  }

  h.selectList = h.pgParams
    .map((f) => blockI(e, `__s_${f.name}`))
    .join(",\n\t\t");

  return `

${h.diskFields
      .filter((f) => f.autoNumber)
      .map((f) => `create sequence a0.${e.name}_${f.name};`)}

create table a0.${e.name}(
    ${h.diskFields
      .map((f) => {
        return `${f.name} ${sqldt(f)} ${f.required ? "NOT NULL" : "NULL"}`;
      })
      .join(",\n\t")}
);

alter table a0.${e.name}
add primary key (${e.keys[0].map((f) => f.name)});

 ${e.keys
      .map((k, i) =>
        i > 0
          ? `create unique index ${mname(`ux_${e.name}`, k)} on a0.${e.name
          }(${k.map((f) => f.name)});`
          : ""
      )
      .join("\n")}

 ${e.finds
      .map(
        (f) =>
          `create index ${mname(`ix_${e.name}`, f)} on a0.${e.name}(${f.map(
            (f) => f.name
          )});`
      )
      .join("\n")}

`;
}
