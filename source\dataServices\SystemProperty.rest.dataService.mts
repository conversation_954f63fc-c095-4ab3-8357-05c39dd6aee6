
import { query } from "../system/Postgres.mjs";
import { SystemProperty, ISystemProperty} from "../models/SystemProperty.model.mjs";
import { Context } from "../system/Context.mjs";
import { makeError } from "../system/error.mjs";


export class SystemPropertyRestDataService {

    
/* b::rest_public_members */

/* end */


  public query(context: Context, sql: string, params: any[]) {
    return query < ISystemProperty> (context, sql, params)
      .then(results => {
        if (results.length === 1) {
          if (!results[0].accountId && !results[0].systemPropertyId) {
      return [];
    }
  }
  return results.filter(o => !!o).map(o => new SystemProperty(o));
});
    }

		public create (context: Context, entity: SystemProperty) {
          


  let params = [
    entity.flags(),
				entity.modifiedTime(),
				entity.createdTime(),
				entity.accountId(),
				entity.deleted(),
				entity.t(),
				entity.d(),
				entity.systemPropertyId(),
				entity.name(),
				entity.value()
  ];
  return this
    .query(context, "select * from a0.system_property_create ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ", params)
  .then(r => r[0]);
        }

		public readByAccountIdAndSystemPropertyId (context: Context, accountId: number, systemPropertyId: number) {
  let params = [
    accountId,
				systemPropertyId
  ];
  return this
    .query(context, "select * from a0.system_property_read_by_account_id_and_system_property_id  ($1,$2) ", params).then(r => r[0]); 
                
        }

		public readByAccountId (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.system_property_read_by_account_id  ($1) ", params).then(r => r[0]); 
                
        }

		public update (context: Context, entity: SystemProperty) {
          


  let params = [
    entity.flags(),
				entity.modifiedTime(),
				entity.createdTime(),
				entity.accountId(),
				entity.deleted(),
				entity.t(),
				entity.d(),
				entity.systemPropertyId(),
				entity.name(),
				entity.value()
  ];
  return this
    .query(context, "select * from a0.system_property_update ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ", params)
  .then(r => r[0]);
        }

		public deleteByAccountIdAndSystemPropertyId (context: Context, accountId: number, systemPropertyId: number) {
  let params = [
    accountId,
				systemPropertyId
  ];
  return this
    .query(context, "select * from a0.system_property_delete_by_account_id_and_system_property_id  ($1,$2) ", params).then(r => r[0]); 
                
        }

		public deleteByAccountId (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.system_property_delete_by_account_id  ($1) ", params).then(r => r[0]); 
                
        }

		public findByAccountId (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.system_property_find_by_account_id  ($1) ", params); 
                
        }

		public findByName (context: Context, name: string) {
  let params = [
    name
  ];
  return this
    .query(context, "select * from a0.system_property_find_by_name  ($1) ", params); 
                
        }

		public merkle (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.system_property_merkle ($1) ", params).then(r => r[0]); 
                
        }

}
