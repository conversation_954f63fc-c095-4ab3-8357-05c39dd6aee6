import express = require("express");
import { config, getServices } from "../system/Config.mjs";
import {
  safeAccount,
  safeNone,
  safeAny,
  secure,
  s,
  b,
} from "../system/safe.mjs";
import { Context } from "../system/Context.mjs";
import { microservice as g } from "../microservices/account.microservice.mjs";
import { sanitizeOutput } from "../models/Account.model.mjs";
import { ids } from "../system/Strings.mjs";
import { makeError } from "../system/error.mjs";

import { binaryEncoder, sendResponse } from "../system/bjson.cjs";
import { IdpService } from "../services/IdpService.mjs";

function validateMicrosoftJWT(
  req: express.Request,
  res: express.Response,
  next: express.NextFunction
) {
  let token = s(req.query.access_token);
  if (!token) {
    throw makeError(403, ids.INVALID_DATA);
  }

  return g.idpService
    .validateJWT("microsoft", token)
    .then((jwt) => {
      req.user = {
        id: jwt.sub,
        email: jwt.email,
      };
      req.context.audience = jwt.aud;
    })
    .then(next, next);
}

export function addMicrosoftRoutes(router: express.Router) {
  router.get("/microsoft/authenticate", (req, res, next) => {
    res.status(302).set("location", `com.mylio:${req.url}`).end();
    return next();
  });

  router.get("/microsoft/authorize", (req, res, next) => {
    let state = JSON.parse(
      Buffer.from(s(req.query.state), "base64").toString("ascii")
    );
    let isMobile = state.isMobile === true;

    let redirect = isMobile ? "com.mylio:" : config.cloud;

    return g.microsoftService
      .addRefreshToken(s(req.query.code), state)
      .then(() => {
        res.setHeader(
          "location",
          `${redirect}/microsoft/loggedin?success=true`
        );
        res.status(302).end();
        return res;
      })
      .catch((err) => {
        let reason = new Buffer(JSON.stringify(err.error)).toString("hex");
        res.setHeader(
          "location",
          `${redirect}/microsoft/loggedin?success=false&reason=${reason}`
        );
        res.status(302).end();
        return res;
      })
      .catch(next);
  });

  router.get(
    "/v2/microsoft/token",
    safeNone,
    validateMicrosoftJWT,
    async (req, res, next) => {
      const context: Context = req.context;
      const result = await g.idpService.token(
        context,
        req.user.id,
        "microsoft"
      );
      context.dumpLog();
      return sendResponse(req, res, result, (r) =>
        binaryEncoder.encode_token_responseV2(r)
      );
    }
  );

  router.get(
    "/accounts/:aid/microsoft/drive/url",
    safeNone,
    secure,
    (req, res, next) => {
      const userAgent = req.header("User-Agent");
      const isMobile =
        userAgent &&
        (userAgent.startsWith("Mylio iOS") ||
          userAgent.startsWith("Mylio Android"));
      const context = req.context;
      return res.status(200).json({
        url: g.microsoftService.getDriveOAuthUrl(
          context,
          context.aid,
          b(req.query.encrypt),
          isMobile
        ),
      });
    }
  );

  router.post(
    "/accounts/:aid/microsoft/drive/reservation",
    safeAny,
    secure,
    (req, res, next) => {
      try {
        return g.microsoftService
          .reserveDrive(req.context, req.context.aid)
          .then((reservation) => {
            return res.status(200).json(reservation);
          })
          .catch(next);
      } catch (err) {
        next(err);
      }
    }
  );

  router.get(
    "/accounts/:aid/microsoft/drive/access-token",
    safeNone,
    secure,
    (req, res, next) => {
      const context = req.context;
      return g.microsoftService
        .getDriveAccessToken(context, context.aid)
        .then((token) => {
          return res.status(200).json({ token });
        })
        .catch(next);
    }
  );

  router.put(
    "/accounts/:aid/microsoft/:task/revoke",
    safeNone,
    secure,
    (req, res, next) => {
      const context: Context = req.context;
      const aid = context.aid;
      const task = req.params.task;

      return g.microsoftService
        .revokeToken(context, aid, task)
        .then(() => {
          context.dumpLog();
          return res.sendStatus(200);
        })
        .catch(next);
    }
  );

  router.get("/microsoft/loggedin", (req, res, next) => {
    res
      .status(200)
      .contentType("text/html")
      /* tslint:disable */
      .write(
        `<html>
          <body>
            <h1 id="warning" style="display:none;color:red">SECURITY WARNING: Please treat the URL above as you would your password and do not share it with anyone<h1>
            <script>
              (function() {
                setTimeout(function() {
                  document.getElementById("warning").style.display = "inline-block";
                }, 2000);
              })();
            </script>
          </body>
        </html>`
      );
    /* tslint:enable */

    res.end();
  });
}
