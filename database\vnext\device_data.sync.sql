

create or replace function a0.device_data_presync()
returns void
as
$$
    create temp table if not exists __device_data_sync_data (
        _flags int,
	_modified_time timestamptz,
	_created_time timestamptz,
	_account_id int,
	_deleted boolean,
	_t bytea,
	_d bytea,
	_device_id int,
	_build text,
	_last_access_time timestamptz,
	_os text,
	_media_count int,
	_protocol_version int,
	_version text,
	_last_startup_time timestamptz,
	_last_hid_time timestamptz,
	_last_import_time timestamptz,
	_original_size int8,
	_local_original_size int8
    );
    truncate table __device_data_sync_data;

    create temp table if not exists __have (
        _account_id int,
        _t bytea
    );
    truncate table __have;
    
    
/* b::device_data_presync */
create temp table if not exists __snapshot__(account_id int, device_id int, protocol_version int, deleted boolean);
create temp table if not exists __upgrade_reminders__(null_value text);
/* end */

$$
language sql;


select * from a0.device_data_presync();



create or replace function a0.device_data_merkle(__account_id int)
returns void
as
$$
    update a0.account_metadata
        set device_data_merkle = agg.merkle
    from (
        select account_id, digest(string_agg(t, null), 'sha1') as merkle
        from (
            select account_id, t from a0.device_data where account_id = __account_id
            order by t
        ) as x
        group by account_id
    ) as agg
    where a0.account_metadata.account_id = agg.account_id
    and (a0.account_metadata.device_data_merkle != agg.merkle or a0.account_metadata.device_data_merkle is null);
$$
language sql;

drop function if exists a0.device_data_sync_have(int, text[]) cascade;
create or replace function a0.device_data_sync_have(__account_id int, _have_text text[])
returns table (
    op int,
    "flags" int,
"modifiedTime" timestamptz,
"createdTime" timestamptz,
"accountId" int,
"deleted" boolean,
"t" text,
"d" text,
"deviceId" int,
"build" text,
"lastAccessTime" timestamptz,
"os" text,
"mediaCount" int,
"protocolVersion" int,
"version" text,
"lastStartupTime" timestamptz,
"lastHidTime" timestamptz,
"lastImportTime" timestamptz,
"originalSize" text,
"localOriginalSize" text
)
as
$$
    insert into __have
    select __account_id, decode(t, 'base64') as _t from unnest(_have_text) as t;
    create index if not exists __have_index on __have(_account_id, substring(_t from 1 for 20), _t);

    select 
        3 as op,
        null,
	null,
	null,
	null,
	null,
	encode(_t, 'base64') as t,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null,
	null
    from __have
    left join a0.device_data cloud 
    on cloud.account_id = __have._account_id
    and substring(t from 1 for 20) = substring(_t from 1 for 20)
    where (t is null or t < _t)

    union all

    select
        2 as op,
        flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		deleted,
		encode(t, 'base64') as "t",
		encode(d, 'base64') as "d",
		device_id as "deviceId",
		build,
		last_access_time as "lastAccessTime",
		os,
		media_count as "mediaCount",
		protocol_version as "protocolVersion",
		version,
		last_startup_time as "lastStartupTime",
		last_hid_time as "lastHidTime",
		last_import_time as "lastImportTime",
		original_size::text as "originalSize",
		local_original_size::text as "localOriginalSize"
    from a0.device_data cloud
    left join __have on _account_id = account_id and substring(t from 1 for 20) = substring(_t from 1 for 20)
    where account_id = __account_id
    and (_t < t or _t is null);    

$$
language sql;

drop function if exists a0.device_data_sync_data(int, json) cascade;
create or replace function a0.device_data_sync_data(__account_id int, _data_json json)
returns table (
    op int,
    "flags" int,
"modifiedTime" timestamptz,
"createdTime" timestamptz,
"accountId" int,
"deleted" boolean,
"t" text,
"d" text,
"deviceId" int,
"build" text,
"lastAccessTime" timestamptz,
"os" text,
"mediaCount" int,
"protocolVersion" int,
"version" text,
"lastStartupTime" timestamptz,
"lastHidTime" timestamptz,
"lastImportTime" timestamptz,
"originalSize" text,
"localOriginalSize" text
)
as
$$


/* b::device_data_before_data_sync */
truncate table __snapshot__;
insert into __snapshot__
	select dd.account_id, dd.device_id, dd.protocol_version, coalesce(d.deleted, false) deleted
	from a0.device_data dd
	join a0.device d on dd.account_id = d.account_id and d.device_id = dd.device_id
	where dd.account_id = __account_id;
/* end */


insert into __device_data_sync_data 
select
    flags as _flags,
	now(),
	"createdTime" as _created_time,
	__account_id,
	deleted as _deleted,
	decode("t", 'base64') as _t,
	decode("d", 'base64') as _d,
	"deviceId" as _device_id,
	build as _build,
	"lastAccessTime" as _last_access_time,
	os as _os,
	"mediaCount" as _media_count,
	"protocolVersion" as _protocol_version,
	version as _version,
	"lastStartupTime" as _last_startup_time,
	"lastHidTime" as _last_hid_time,
	"lastImportTime" as _last_import_time,
	"originalSize"::int8 as _original_size,
	"localOriginalSize"::int8 as _local_original_size
    from json_to_recordset(_data_json) 
    as t(
        "flags" int,
	"createdTime" timestamptz,
	"accountId" int,
	"deleted" boolean,
	"t" text,
	"d" text,
	"deviceId" int,
	"build" text,
	"lastAccessTime" timestamptz,
	"os" text,
	"mediaCount" int,
	"protocolVersion" int,
	"version" text,
	"lastStartupTime" timestamptz,
	"lastHidTime" timestamptz,
	"lastImportTime" timestamptz,
	"originalSize" text,
	"localOriginalSize" text
    );

create index if not exists __device_data_sync_data_index on __device_data_sync_data(_account_id, substring(_t from 1 for 20), _t);


update a0.device_data cloud
    set flags = _flags,
	modified_time = _modified_time,
	created_time = _created_time,
	deleted = _deleted,
	t = _t,
	d = _d,
	build = _build,
	last_access_time = _last_access_time,
	os = _os,
	media_count = _media_count,
	protocol_version = _protocol_version,
	version = _version,
	last_startup_time = _last_startup_time,
	last_hid_time = _last_hid_time,
	last_import_time = _last_import_time,
	original_size = _original_size,
	local_original_size = _local_original_size
from __device_data_sync_data client
where account_id = _account_id and substring(t from 1 for 20) = substring(_t from 1 for 20)
and _t > t;

update __device_data_sync_data
    set _t = least(E'\\x000000000000000000000000000000000000000000000000000000000000000000', decode((a0.device_data_create(
        _flags,
		_modified_time,
		_created_time,
		__account_id,
		_deleted,
		null,
		encode(_d, 'base64'),
		_device_id,
		_build,
		_last_access_time,
		_os,
		_media_count,
		_protocol_version,
		_version,
		_last_startup_time,
		_last_hid_time,
		_last_import_time,
		_original_size::text,
		_local_original_size::text)).t, 'base64'))
where _device_id is null;

insert into a0.device_data (
    flags,
	modified_time,
	created_time,
	account_id,
	deleted,
	t,
	d,
	device_id,
	build,
	last_access_time,
	os,
	media_count,
	protocol_version,
	version,
	last_startup_time,
	last_hid_time,
	last_import_time,
	original_size,
	local_original_size
)
select
    _flags,
	_modified_time,
	_created_time,
	_account_id,
	_deleted,
	_t,
	_d,
	_device_id,
	_build,
	_last_access_time,
	_os,
	_media_count,
	_protocol_version,
	_version,
	_last_startup_time,
	_last_hid_time,
	_last_import_time,
	_original_size,
	_local_original_size
from __device_data_sync_data
where not exists (select * from a0.device_data where account_id = _account_id and substring(t from 1 for 20) = substring(_t from 1 for 20));

select from a0.device_data_merkle(__account_id);


/* b::device_data_after_data_sync */
truncate table __upgrade_reminders__;
insert into __upgrade_reminders__(null_value)
select case
	when dd.protocol_version < 23 and coalesce(d.deleted, false) = false then
		a0.show_upgrade_message(d.account_id, d.name)
	when dd.protocol_version >= 23 and coalesce(d.deleted, false) = false then 
		a0.hide_upgrade_message(d.account_id, d.name)
	when coalesce(d.deleted, false) = true then
		a0.hide_upgrade_message(d.account_id, d.name)
	end
from
(
	select distinct dd.account_id
	from a0.device_data dd
	left join __snapshot__ s on s.account_id = dd.account_id and s.device_id = dd.device_id
	where dd.account_id = __account_id
	and (
		(dd.protocol_version = 23 and s.protocol_version < 23)
		or (
			(dd.deleted and not s.deleted) 
			and 
			(s.protocol_version = 23 
				or 
				exists (
					select * from a0.device_data 
					where account_id = __account_id
					and protocol_version = 23
					and coalesce(deleted, false) = false
				)
			)
		)
		or (s.account_id is null and dd.protocol_version = 23)
	)
) x
join a0.device_data dd on dd.account_id = x.account_id
join a0.device d on d.account_id = dd.account_id and d.device_id = dd.device_id
where d.device_type & 96 > 0;
/* end */


select
    2 as op,
    flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		deleted,
		encode(t, 'base64') as "t",
		encode(d, 'base64') as "d",
		device_id as "deviceId",
		build,
		last_access_time as "lastAccessTime",
		os,
		media_count as "mediaCount",
		protocol_version as "protocolVersion",
		version,
		last_startup_time as "lastStartupTime",
		last_hid_time as "lastHidTime",
		last_import_time as "lastImportTime",
		original_size::text as "originalSize",
		local_original_size::text as "localOriginalSize"
from a0.device_data cloud
join __device_data_sync_data on account_id = _account_id and substring(t from 1 for 20) = substring(_t from 1 for 20)
where (_t < t or _device_id is null);

$$
language sql;

drop function if exists a0.device_data_sync(int, json, boolean, text[], boolean) cascade;
create or replace function a0.device_data_sync(__account_id int, _data_json json, _do_data boolean, _have text[], _do_have boolean)
returns table (
    op int,
    "flags" int,
"modifiedTime" timestamptz,
"createdTime" timestamptz,
"accountId" int,
"deleted" boolean,
"t" text,
"d" text,
"deviceId" int,
"build" text,
"lastAccessTime" timestamptz,
"os" text,
"mediaCount" int,
"protocolVersion" int,
"version" text,
"lastStartupTime" timestamptz,
"lastHidTime" timestamptz,
"lastImportTime" timestamptz,
"originalSize" text,
"localOriginalSize" text
)
as
$$
begin
    perform * 
    from a0.account_metadata 
    where account_id = __account_id for update;

    perform a0.device_data_presync();
    if (_do_data) then
        return query select * from a0.device_data_sync_data(__account_id, _data_json);
    end if;
    if (_do_have) then
        return query select * from a0.device_data_sync_have(__account_id, _have);
    end if;
end;
$$
language plpgsql;
