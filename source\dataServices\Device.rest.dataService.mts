
import { query } from "../system/Postgres.mjs";
import { Device, IDevice} from "../models/Device.model.mjs";
import { Context } from "../system/Context.mjs";
import { makeError } from "../system/error.mjs";


export class DeviceRestDataService {

    
/* b::rest_public_members */
public newId(context: Context, aid: number) {
    return query<{ next_device_id: number }>(
      context,
      "select * from a0.next_device_id($1)",
      [aid]
    ).then((results) => {
      return results[0].next_device_id;
    });
  }

  public reserve(context: Context, aid: number, dType: any) {
    return query<{ deviceId: number; desktopCount: number; planId: string }>(
      context,
      `select
        (select next_device_id from a0.next_device_id($1)) as "deviceId",
        (
            select coalesce(sum(1), 0) from a0.device
            where account_id = $1
            and (device_type & 32) = 32
            and not deleted
        ) as "desktopCount",
        (select plan_id from a0.account where account_id = $1) as "planId"`,
      [aid]
    ).then((results) => {
      return results[0];
    });
  }
/* end */


  public query(context: Context, sql: string, params: any[]) {
    return query < IDevice> (context, sql, params)
      .then(results => {
        if (results.length === 1) {
          if (!results[0].accountId && !results[0].deviceId) {
      return [];
    }
  }
  return results.filter(o => !!o).map(o => new Device(o));
});
    }

		public create (context: Context, entity: Device) {
          


  let params = [
    entity.flags(),
				entity.modifiedTime(),
				entity.createdTime(),
				entity.accountId(),
				entity.deleted(),
				entity.t(),
				entity.d(),
				entity.deviceId(),
				entity.name(),
				entity.deviceType(),
				entity.nickname(),
				entity.encrypt(),
				entity.creationTime(),
				entity.longId(),
				entity.supportTicket()
  ];
  return this
    .query(context, "select * from a0.device_create ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14,$15) ", params)
  .then(r => r[0]);
        }

		public readByAccountIdAndDeviceId (context: Context, accountId: number, deviceId: number) {
  let params = [
    accountId,
				deviceId
  ];
  return this
    .query(context, "select * from a0.device_read_by_account_id_and_device_id  ($1,$2) ", params).then(r => r[0]); 
                
        }

		public readByAccountId (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.device_read_by_account_id  ($1) ", params).then(r => r[0]); 
                
        }

		public update (context: Context, entity: Device) {
          


  let params = [
    entity.flags(),
				entity.modifiedTime(),
				entity.createdTime(),
				entity.accountId(),
				entity.deleted(),
				entity.t(),
				entity.d(),
				entity.deviceId(),
				entity.name(),
				entity.deviceType(),
				entity.nickname(),
				entity.encrypt(),
				entity.creationTime(),
				entity.longId(),
				entity.supportTicket()
  ];
  return this
    .query(context, "select * from a0.device_update ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14,$15) ", params)
  .then(r => r[0]);
        }

		public deleteByAccountIdAndDeviceId (context: Context, accountId: number, deviceId: number) {
  let params = [
    accountId,
				deviceId
  ];
  return this
    .query(context, "select * from a0.device_delete_by_account_id_and_device_id  ($1,$2) ", params).then(r => r[0]); 
                
        }

		public deleteByAccountId (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.device_delete_by_account_id  ($1) ", params).then(r => r[0]); 
                
        }

		public findByAccountId (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.device_find_by_account_id  ($1) ", params); 
                
        }

		public findByAccountIdAndDeviceType (context: Context, accountId: number, deviceType: number) {
  let params = [
    accountId,
				deviceType
  ];
  return this
    .query(context, "select * from a0.device_find_by_account_id_and_device_type  ($1,$2) ", params); 
                
        }

		public merkle (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.device_merkle ($1) ", params).then(r => r[0]); 
                
        }

}
