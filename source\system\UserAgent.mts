import express = require("express");
import { Context } from "./Context.mjs";

export class UserAgent {
    private str: string;
    private regex: RegExp;
    private matches: RegExpMatchArray | null;
    public build: number;
    public accountId: number;
    public deviceId: number;
    public protocolVersion: number;

    constructor(str: string) {
        this.str = str;
        this.regex = /Mylio (?<os>\w+)?\s(?<build>\w+)?\s(?<accountId>\w+)?\s(?<deviceId>\w+)?\s(?<protocolVersion>\w+)?\s(?<language>\w+)?\..*/g;
        this.matches = this.regex.exec(this.str);

        if (!this.matches || !this.matches.groups) {
            throw new Error('String does not match the expected pattern.');
        }

        const { build, accountId, deviceId, protocolVersion } = this.matches.groups;

        this.build = parseInt(build, 10);
        this.accountId = parseInt(accountId, 10);
        this.deviceId = parseInt(deviceId, 10);
        this.protocolVersion = parseInt(protocolVersion, 10);
    }

}

export function parseUserAgent(req: express.Request, res, next) {
    let context = req.context;
    if (!context) {
        req.context = new Context();
        context = req.context;
    }
    let userAgent: string = req.get("User-Agent");
    if (
        userAgent &&
        (userAgent.startsWith("mylio") || userAgent.startsWith("Mylio"))
    ) {
        context.userAgent = new UserAgent(userAgent);
    }
    next();
}