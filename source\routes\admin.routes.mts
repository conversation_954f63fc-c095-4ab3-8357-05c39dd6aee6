import express = require("express");
import {
  safeAccount,
  safeNone,
  safeAny,
  secure,
  admin,
  n,
  b,
} from "../system/safe.mjs";
import { sanitizeOutput } from "../models/Account.model.mjs";
import { microservice as g } from "../microservices/account.microservice.mjs";
import { Context } from "../system/Context.mjs";
import { Token } from "../models/Token.mjs";
import { RefreshToken } from "../models/RefreshToken.model.mjs";
import {
  S3Client,
  ListObjectsV2Command,
  GetObjectCommand,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";

export function addAdminRoutes(router: express.Router) {
  router.get(
    "/admin/tokens/email",
    safeNone,
    secure,
    admin,
    (req, res, next) => {
      let context = req.context;
      let sub = context.query.sub;

      return g.accountService
        .bySubAndIdp(context, sub, "mylio")
        .then((account) =>
          g.tokenService.shortLivedToken(context, account, "1h")
        )
        .then((token) => {
          context.dumpLog();
          return res.status(200).json({ token });
        })
        .catch(next);
    }
  );

  router.get("/admin/accounts", safeNone, secure, admin, (req, res, next) => {
    let context = req.context;
    let limit = n(req.query.limit, 100);

    return g.accountService
      .readAccounts(context, limit)
      .then((accounts) => {
        context.dumpLog();
        return res
          .status(200)
          .json(
            accounts.map((a) => sanitizeOutput(a, context.hasAdminRights()))
          );
      })
      .catch(next);
  });

  router.post(
    "/admin/impersonate",
    safeAny,
    secure,
    admin,
    async (req, res, next) => {
      let context: Context = req.context;
      let aid = context.any.aid;
      let result = await g.authenticationService.impersonate(
        context,
        context.token.aid(),
        aid
      );

      res.status(200).json({
        atoken: result.atoken,
        account: sanitizeOutput(
          result.account.state(),
          context.hasAdminRights()
        ),
      });
    }
  );

  router.get(
    "/admin/accounts/:aid/refreshtokens/:idp/:task",
    safeAny,
    secure,
    admin,
    (req, res, next) => {
      let context = req.context;
      let idp = context.idp;
      let aid = context.aid;
      let task = context.task;

      return g.refreshTokenService
        .read(context, aid, idp, task)
        .then((refreshToken) => {
          context.dumpLog();
          return res.status(200).json(refreshToken.state());
        })
        .catch(next);
    }
  );

  router.post(
    "/admin/accounts/:aid/refreshtokens/:idp/:task",
    safeAny,
    secure,
    admin,
    (req, res, next) => {
      let context = req.context;
      let idp = context.idp;
      let accountId = context.aid;
      let task = context.task;
      let token = context.any.refreshToken;
      let refreshToken = new RefreshToken({ idp, accountId, task, token });

      return g.refreshTokenService
        .create(context, accountId, refreshToken)
        .then(() => {
          context.dumpLog();
          return res.sendStatus(200);
        })
        .catch(next);
    }
  );

  router.get("/admin/v2/accounts", safeAny, secure, admin, (req, res, next) => {
    return res.status(404);
  });

  router.get(
    "/queries/:entity/:name/:value",
    safeAny,
    secure,
    admin,
    async (req, res, next) => {
      let context = req.context;
      switch (req.params.entity) {
        case "account":
          let accounts = await g.accountService.runQuery(
            context,
            req.params.name,
            req.params.value,
            100
          );
          res.status(200).json(accounts);
      }
    }
  );

  router.get(
    "/admin/maintenance-mode",
    safeNone,
    secure,
    admin,
    (req, res, next) => {
      return res.status(200).json({ status: g.maintenanceMode });
    }
  );

  router.put(
    "/admin/maintenance-mode",
    safeNone,
    secure,
    admin,
    (req, res, next) => {
      g.maintenanceMode = !g.maintenanceMode;

      return res.status(200).json({ status: g.maintenanceMode });
    }
  );

  router.get(
    "/admin/logs/:accountId/list",
    safeNone,
    secure,
    admin,
    async (req, res, next) => {
      const accountId = req.params.accountId;
      const client = new S3Client({ region: `us-east-1` });
      const command = new ListObjectsV2Command({
        Bucket: `mylo_support`,
        Prefix: `logs_v3/${accountId}/`,
      });
      const response = await client.send(command);
      return res.status(200).json(response);
    }
  );

  router.get(
    "/admin/logs/file",
    safeNone,
    secure,
    admin,
    async (req, res, next) => {
      const key = req.query.key as string;
      const client = new S3Client({ region: `us-east-1` });
      const command = new GetObjectCommand({
        Bucket: `mylo_support`,
        Key: key,
      });
      const url = await getSignedUrl(client, command, { expiresIn: 15 * 60 }); // 15 minutes
      return res.status(200).json({ url });
    }
  );
}
