
#include "encode_functions.h"
#include "decode_functions.h"

#include "helpers.h"
#include "X509.h"

Napi::Object generate_account_certificate(Napi::Env isolate, const Napi::Object &account)
{
	X509Certificate cert;
	cert.subject().commonName(account.Get("accountId").ToString());
	cert.subject().org("Account");
	cert.subject().countryName("US");
	cert.issuer(cert.subject());
	cert.sign();

	// X509Certificate cert2;
	// cert2.subject().commonName("CN");
	// cert2.subject().org("ORG");
	// cert2.subject().countryName("US");
	// cert2.sign(cert);

	auto root = Napi::Object::New(isolate);
	root.Set("certificatePEM", cert.certificatePEM());
	root.Set("keyPEM", cert.keyPEM());
	// root.setString("deviceCertificatePEM", cert2.certificatePEM());
	// root.setString("deviceKeyPEM", cert2.keyPEM());
	return root;
}
