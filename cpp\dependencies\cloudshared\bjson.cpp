#include "base64.h"
#include "./MYLiterals.h"
#include <cstring>
#include <sstream>
#include <iostream>
#include "bjson.h"
#include "MYHash.h"
#include "MYTrev.h"

std::shared_ptr<MYBJson> MYBJsonView::emptyObjectPtr()
{
    static std::shared_ptr<class MYBJson> _empty = std::make_shared<MYBJsonView>(emptyObject());
    return _empty;
}

MYBJsonView MYBJsonView::emptyObject()
{
    static std::array<const uint8_t, 3> _emptyObject{(uint8_t)BJsonType::object, (uint8_t)BJsonType::end, (uint8_t)BJsonType::end};
    return MYBJsonView(&_emptyObject[0], &_emptyObject[2]);
}

class MYBJsonUncheckedScopedIterator MYBJsonUncheckedIterator::inner(MYBJsonUncheckedIterator end) const
{
    auto iter = *this;
    assert(iter->isScopeBegin());

    ++iter;

    return MYBJsonUncheckedScopedIterator(iter.ptr(), end.ptr());
}

class MYBJsonUncheckedIterator MYBJsonUncheckedScopedIterator::unscoped() const
{
    return MYBJsonUncheckedIterator(pos_, end_);
}

MYBJsonUncheckedScopedIterator MYBJsonUncheckedScopedIterator::begin() const
{
    assert(pos_ != end_);
    assert(pos_->isScopeBegin());
    return inner(MYBJsonUncheckedIterator(end_, end_));
}

MYBJsonUncheckedScopedIterator MYBJsonUncheckedScopedIterator::end() const
{
    return MYBJsonUncheckedScopedIterator(end_, end_);
}

MYBJsonUncheckedScopedIterator MYBJsonUncheckedScopedIterator::findKey(uint8_t key, bool nullOnNotFound) const
{
    return MYBJson::findKey(begin(), end(), key, nullOnNotFound);
}

// CHECKED versions

class MYBJsonCheckedScopedIterator MYBJsonCheckedIterator::inner(MYBJsonCheckedIterator end) const
{
    auto iter = *this;
    assert(iter->isScopeBegin());

    ++iter;

    return MYBJsonCheckedScopedIterator(iter.ptr(), end.ptr());
}

class MYBJsonCheckedIterator MYBJsonCheckedScopedIterator::unscoped() const
{
    return MYBJsonCheckedIterator(pos_, end_);
}

MYBJsonCheckedScopedIterator MYBJsonCheckedScopedIterator::begin() const
{
    assert(pos_ != end_);
    assert(pos_->isScopeBegin());
    return inner(MYBJsonCheckedIterator(end_, end_));
}

MYBJsonCheckedScopedIterator MYBJsonCheckedScopedIterator::end() const
{
    return MYBJsonCheckedScopedIterator(end_, end_);
}

MYBJsonCheckedScopedIterator MYBJsonCheckedScopedIterator::findKey(uint8_t key, bool nullOnNotFound) const
{
    return MYBJson::findKey(begin(), end(), key, nullOnNotFound);
}

class MYBaseHandler
{
public:
    MYBaseHandler() {}

    bool Null()
    {
        return true;
    }

    bool Separator()
    {
        return true;
    }

    bool Bool(bool b)
    {
        return true;
    }

    bool Int32(int32_t i)
    {
        return true;
    }

    bool Uint32(unsigned i)
    {
        return true;
    }

    bool Int64(int64_t i)
    {
        return true;
    }

    bool Uint64(uint64_t i)
    {
        return true;
    }

    bool Double(double d)
    {
        return true;
    }

    bool String(const char *str, size_t length, bool copy)
    {
        return true;
    }

    bool StartObject()
    {
        return true;
    }

    bool Type(BJsonType type)
    {
        return true;
    }

    bool Key(uint16_t key)
    {
        return true;
    }

    bool Key(const char *str, size_t length, bool copy)
    {
        return true;
    }

    bool EndObject(size_t memberCount)
    {
        return true;
    }

    bool StartArray()
    {
        return true;
    }

    bool EndArray(size_t elementCount)
    {
        return true;
    }

    bool MYHash(const MYHash &hash)
    {
        return true;
    }

    bool MYTRev(const MYTRev &trev)
    {
        return true;
    }

    bool Binary(const uint8_t *raw, size_t length, bool copy)
    {
        return true;
    }
};

class MYBaseHandlerWithLevels : public MYBaseHandler
{
protected:
    int level = 0;
    typedef MYBaseHandler base;

    int currentKeyInt = 0;
    std::deque<std::pair<bool, int>> modes; // true is object, false is array

public:
    MYBaseHandlerWithLevels()
    {
    }

    bool StartObject()
    {
        modes.emplace_back(true, currentKeyInt);
        return base::StartObject();
    }

    bool Key(uint16_t key)
    {
        currentKeyInt = key;
        return base::Key(key);
    }

    bool Key(const char *str, size_t length, bool copy)
    {
        return base::Key(str, length, copy);
    }

    bool EndObject(size_t memberCount)
    {
        if (modes.size() > 0)
        {
            currentKeyInt = modes.back().second;
            modes.pop_back();
        }

        return base::EndObject(memberCount);
    }

    bool StartArray()
    {
        modes.emplace_back(false, currentKeyInt);
        currentKeyInt = 0;
        return base::StartArray();
    }

    bool EndArray(size_t elementCount)
    {
        if (modes.size() > 0)
        {
            currentKeyInt = modes.back().second;
            modes.pop_back();
        }
        return base::EndArray(elementCount);
    }

    size_t getLevel()
    {
        return modes.size();
    }

    int getKey()
    {
        return currentKeyInt;
    }
};

/********************************************************************************
Resembles MYToStringHandler only instead of using stringstream uses std::string
and uses itoa for casting numeric types.
********************************************************************************/

class MYFastToStringHandler : public MYBaseHandlerWithLevels
{
    typedef MYBaseHandlerWithLevels base;
    bool firstKeyAfterScope = true;
    MYBJsonReader *_reader;

    int separatorLevel = 0;
    bool _pretty;
    bool _useJsonAnonymousArrays = false;
    std::string _str;
    static std::string _hashString;

public:
    MYFastToStringHandler(MYBJsonReader *reader, bool pretty, bool useJsonAnonymousArrays = false) : _reader(reader), _pretty(pretty), _useJsonAnonymousArrays(useJsonAnonymousArrays)
    {
        // The content of the bjson is written to this string.We are reserving the capacity for optimum performance so
        // its not reallocated all the time.
        _str.reserve(1000);
    }

    static std::string intializeHashString()
    {
        std::string hash;
        hash.resize(MYHASH_SIZE * 2);
        return hash;
    }

    const std::string &toString()
    {
        return _str;
    }

    void writeIndent()
    {
        if (_pretty)
        {
            _str += "\n";
            for (size_t i = 0; i < modes.size(); ++i)
            {
                _str += "    ";
            }
        }
    }

    void writeComma(bool keyMethod = false)
    {
        if (!keyMethod && !_useJsonAnonymousArrays)
        {
            return;
        }

        bool inObject = modes.size() > 0 ? modes.back().first : false;
        if (inObject && keyMethod) // in object
        {
            if (!firstKeyAfterScope)
            {
                _str += ", ";
            }
        }
        else if (!inObject)
        {
            if (!firstKeyAfterScope)
            {
                _str += ", ";
            }
        }

        if (!keyMethod)
        {
            firstKeyAfterScope = false;
        }
    }

    bool Null()
    {
        writeComma();
        _str += "null";
        return base::Null();
    }

    bool Separator()
    {
        writeComma();
        _str += "\"<separator>\"";
        return base::Separator();
    }

    bool Bool(bool b)
    {
        writeComma();
        _str += (b ? "true" : "false");
        return base::Bool(b);
    }

    bool Int32(int32_t i)
    {
        writeComma();
        _str += MYString::itoa(i);
        return base::Int32(i);
    }

    bool Uint32(uint32_t i)
    {

        writeComma();
        if (i < 255)
            _str += MYString::itoa(i);
        else
            _str += "\"" + MYString::itoa(i, 16) + "\"";
        return base::Uint32(i);
    }

    bool Int64(int64_t i)
    {
        writeComma();
        _str += MYString::itoa(i);
        return true;
    }

    bool Uint64(uint64_t i)
    {
        writeComma();
        _str += MYString::itoa(i);
        return base::Uint64(i);
    }

    bool Double(double d)
    {
        writeComma();
        _str += std::to_string(d);
        return base::Double(d);
    }

    bool String(const char *cstr, size_t length, bool copy)
    {
        writeComma();
        _str += "\"" + std::string(cstr, length) + "\" ";
        return base::String(cstr, length, copy);
    }

    bool StartObject()
    {
        writeComma();
        firstKeyAfterScope = true;
        _str += " {";
        return base::StartObject();
    }

    bool Type(BJsonType type)
    {
        if (type == BJsonType::separator)
        {
            ++separatorLevel;
        }
        return true;
    }

    bool Key(uint16_t key)
    {
        writeComma(true);
        writeIndent();

        //_str +=  "\"" + MYString::itoa(key) + "\" : ";
        size_t len = _reader->getTypeParsePosition()->length();
        if (_reader->getTypeParsePosition()->isScopeBegin())
        {
            auto iter = _reader->getTypeParsePosition();

            auto end = _reader->getEnd().ptr();
            auto extentPosition = iter.extent_or_end(_reader->getEnd());
            MYBJsonIterator extent(extentPosition.ptr(), _reader->getEnd().ptr());
            if (extent != end)
            {
                ++extent;
            }
            else
            {
                _str += "<incomplete>";
            }

            auto psize = extent.ptr() - iter.ptr();
            len = psize;
        }

        std::string sep;
        if (separatorLevel > 0)
        {
            sep = MYString::itoa(separatorLevel) + " => ";
        }

        _str += "\"" + sep + MYString::itoa(key) + " (" + MYString::itoa(len) + ")" + "\" : ";
        return base::Key(key);
    }

    bool Key(const char *cstr, size_t length, bool copy)
    {
        writeComma(true);
        writeIndent();

        std::string sep;
        if (separatorLevel > 0)
        {
            sep = std::to_string(separatorLevel) + " => ";
        }

        _str += "\"" + sep + std::string(cstr, length) + "\" : ";
        return base::Key(cstr, length, copy);
    }

    bool EndObject(size_t memberCount)
    {
        writeIndent();
        _str += "} ";
        separatorLevel = 0;
        return base::EndObject(memberCount);
    }

    bool StartArray()
    {
        writeComma();
        firstKeyAfterScope = true;
        _str += "  [";
        return base::StartArray();
    }

    bool EndArray(size_t elementCount)
    {
        writeIndent();
        _str += "] ";

        separatorLevel = 0;
        return base::EndArray(elementCount);
    }

    bool MYHash(const ::MYHash &hash)
    {
        writeComma();
        _hashString.clear();
        _str += "\"" + hash.toQuickString(false, _hashString);
        return base::MYHash(hash);
    }

    bool MYTRev(const ::MYTRev &trev)
    {
        writeComma();
        _str += "\"" + trev.toString() + "\" ";
        return base::MYTRev(trev);
    }

    bool Binary(const uint8_t *raw, size_t length, bool copy)
    {
        writeComma();
        _str += "\"" + base64_encode(raw, length) + "\" ";
        return base::Binary(raw, length, copy);
    }

    void clearContent()
    {
        _str.clear();
    }
};

class MYToStringHandler : public MYBaseHandlerWithLevels
{
    typedef MYBaseHandlerWithLevels base;
    bool firstKeyAfterScope = true;
    MYBJsonReader *_reader;

    int separatorLevel = 0;
    bool _pretty;
    bool _useJsonAnonymousArrays = false;

public:
    MYToStringHandler(MYBJsonReader *reader, bool pretty, bool useJsonAnonymousArrays = false) : _reader(reader), _pretty(pretty), _useJsonAnonymousArrays(useJsonAnonymousArrays) {}

    std::stringstream ss;

    void writeIndent()
    {
        if (_pretty)
        {
            ss << std::endl;
            for (size_t i = 0; i < modes.size(); ++i)
            {
                ss << "    ";
            }
        }
    }

    void writeComma(bool keyMethod = false)
    {
        if (!keyMethod && !_useJsonAnonymousArrays)
        {
            return;
        }

        bool inObject = modes.size() > 0 ? modes.back().first : false;
        if (inObject && keyMethod) // in object
        {
            if (!firstKeyAfterScope)
            {
                ss << ", ";
            }
        }
        else if (!inObject)
        {
            if (!firstKeyAfterScope)
            {
                ss << ", ";
            }
        }

        if (!keyMethod)
        {
            firstKeyAfterScope = false;
        }
    }

    bool Null()
    {
        writeComma();
        ss << "null";
        return base::Null();
    }

    bool Separator()
    {
        writeComma();
        ss << "\"<separator>\"";
        return base::Separator();
    }

    bool Bool(bool b)
    {
        writeComma();
        ss << (b ? "true" : "false");
        return base::Bool(b);
    }

    bool Int32(int32_t i)
    {
        writeComma();
        ss << i;
        return base::Int32(i);
    }

    bool Uint32(uint32_t i)
    {
        writeComma();
        if (i < 255)
            ss << i;
        else
            ss << std::hex << "\"" << i << "\"" << std::dec;

        return base::Uint32(i);
    }

    bool Int64(int64_t i)
    {
        writeComma();
        ss << i;
        return base::Int64(i);
    }

    bool Uint64(uint64_t i)
    {
        writeComma();
        ss << i;
        return base::Uint64(i);
    }

    bool Double(double d)
    {
        writeComma();
        ss << d;
        return base::Double(d);
    }

    bool String(const char *str, size_t length, bool copy)
    {
        writeComma();
        ss << "\"" << std::string(str, length) << "\" ";
        return base::String(str, length, copy);
    }

    bool StartObject()
    {
        writeComma();
        firstKeyAfterScope = true;
        ss << " {";
        return base::StartObject();
    }

    bool Type(BJsonType type)
    {
        if (type == BJsonType::separator)
        {
            ++separatorLevel;
        }
        return true;
    }

    bool Key(uint16_t key)
    {
        writeComma(true);
        writeIndent();

        // ss << "\"" << key << "\" : ";

        size_t len = _reader->getTypeParsePosition()->length();
        if (_reader->getTypeParsePosition()->isScopeBegin())
        {
            auto iter = _reader->getTypeParsePosition();

            auto end = _reader->getEnd().ptr();
            auto extentPosition = iter.extent_or_end(_reader->getEnd());
            MYBJsonIterator extent(extentPosition.ptr(), _reader->getEnd().ptr());
            if (extent != end)
            {
                ++extent;
            }
            else
            {
                ss << "<incomplete>";
            }

            auto psize = extent.ptr() - iter.ptr();
            len = psize;
        }

        std::string sep;
        if (separatorLevel > 0)
        {
            sep = std::to_string(separatorLevel) + " => ";
        }

        ss << "\"" << sep << key << " (" << len << ")"
           << "\" : ";

        return base::Key(key);
    }

    bool Key(const char *str, size_t length, bool copy)
    {
        writeComma(true);
        writeIndent();

        std::string sep;
        if (separatorLevel > 0)
        {
            sep = std::to_string(separatorLevel) + " => ";
        }

        ss << "\"" << sep << std::string(str, length) << "\" : ";
        return base::Key(str, length, copy);
    }

    bool EndObject(size_t memberCount)
    {
        writeIndent();
        ss << "} ";
        separatorLevel = 0;
        return base::EndObject(memberCount);
    }

    bool StartArray()
    {
        writeComma();
        firstKeyAfterScope = true;

        ss << " [";
        return base::StartArray();
    }

    bool EndArray(size_t elementCount)
    {
        writeIndent();
        ss << "] ";

        separatorLevel = 0;
        return base::EndArray(elementCount);
    }

    bool MYHash(const ::MYHash &hash)
    {
        writeComma();
        ss << "\"" << hash.toString() << "\" ";
        return base::MYHash(hash);
    }

    bool MYTRev(const ::MYTRev &trev)
    {
        writeComma();
        ss << "\"" << trev.toString() << "\" ";
        return base::MYTRev(trev);
    }

    bool Binary(const uint8_t *raw, size_t length, bool copy)
    {
        writeComma();
        ss << "\"" << base64_encode(raw, length) << "\" ";
        return base::Binary(raw, length, copy);
    }
};

std::string MYBJson::toJsonStringInternal(bool pretty) const
{
    if (empty())
    {
        return "";
    }

    MYBJsonReader bReader;
    MYToStringHandler handler(&bReader, true);

    bool insertOuterObject = begin()->type() != BJsonType::object;
    if (insertOuterObject)
    {
        handler.StartObject();
    }

    bReader.Parse(*this, handler, pretty, insertOuterObject);
    if (insertOuterObject)
    {
        handler.EndObject(0);
    }
    return handler.ss.str();
}

std::string MYFastToStringHandler::_hashString = intializeHashString();

// This is an optimized method for returning the contents of a bjson
std::string MYBJson::getFastToString(bool pretty) const
{
    if (empty())
    {
        return "";
    }

    MYBJsonReader bReader;
    MYFastToStringHandler handler(&bReader, true);

    bool insertOuterObject = begin()->type() != BJsonType::object;
    if (insertOuterObject)
    {
        handler.StartObject();
    }

    bReader.generateQuickString(*this, handler, pretty, insertOuterObject);

    if (insertOuterObject)
    {
        handler.EndObject(0);
    }

    return handler.toString();
}

// You must supply a string that is being searched for
bool MYBJson::containsInBjsonUsingShortCircutting(bool pretty, const std::string &lowerCasedText) const
{
    MYBJsonReader bReader;
    MYFastToStringHandler handler(&bReader, true);

    bool insertOuterObject = begin()->type() != BJsonType::object;
    if (insertOuterObject)
    {
        handler.StartObject();
    }

    // This method generates the content of bjson while searching for the word in that content
    return bReader.quickShortCircuitFinder(*this, handler, lowerCasedText, pretty, insertOuterObject);
}

std::string MYBJsonElement::toString() const
{
    std::string s = "\"" + std::to_string(key()) + "\" : ";

    switch (type())
    {
    case BJsonType::null:
        return s + "\"null/0/false\"";
    case BJsonType::true1:
        return s + "\"true/1\"";
    case BJsonType::array:
        return s + "[";
    case BJsonType::object:
        return s + "{";

    case BJsonType::separator:
        return s + ", ";
    case BJsonType::end:
        return s + "} / ]";

    case BJsonType::uint8:
        return s + std::to_string(asUint64());

    case BJsonType::fixed32:
        return s + std::to_string(fixed32());

    case BJsonType::varint:
        return s + std::to_string(asUint64());

    case BJsonType::varint_neg:
        return s + std::to_string(asInt64());

    case BJsonType::float32:
        return s + std::to_string(asFloat());
    case BJsonType::double64:
        return s + std::to_string(asDouble());

    case BJsonType::hash:
        return s + "\"" + asHash().toString() + "\"";

    case BJsonType::trev:
        return s + "\"" + asTrev().toString() + "\"";

    case BJsonType::string:
        return s + "\"" + asString() + "\"";

    case BJsonType::binary:
        return s + "\"" + asBinaryBase64() + "\"";
    }

    return s + "\"<unknown>\"";
}

MYBJsonCheckedScopedIterator MYBJson::scopedChecked() const
{
    return MYBJsonCheckedScopedIterator(begin().ptr(), end().ptr());
}

MYBJsonUncheckedScopedIterator MYBJson::scopedUnchecked() const
{
    return MYBJsonUncheckedScopedIterator(begin().ptr(), end().ptr());
}

class MYBJsonScopedIterator MYBJson::scoped() const
{
    return MYBJsonScopedIterator(begin().ptr(), end().ptr());
}

uint16_t zero16 = 0;
static MYBJsonView nullView((uint8_t *)&zero16, (uint8_t *)&zero16 + 1);

MYBJsonIterator MYBJson::null_iterator() const
{
    static MYBJsonIterator nullIterator = nullView.begin();
    return nullIterator;
}

template <>
bool MYBJsonElement::asType() const
{
    return asBool();
}

template <>
int64_t MYBJsonElement::asType() const
{
    return asInt64();
}

template <>
uint64_t MYBJsonElement::asType() const
{
    return asUint64();
}

template <>
int32_t MYBJsonElement::asType() const
{
    return asInt32();
}

template <>
uint32_t MYBJsonElement::asType() const
{
    return asUint32();
}

template <>
uint8_t MYBJsonElement::asType() const
{
    return asUint8();
}

template <>
std::string MYBJsonElement::asType() const
{
    return asString();
}

template <>
MYHash MYBJsonElement::asType() const
{
    return asHash();
}

template <>
MYTRev MYBJsonElement::asType() const
{
    return asTrev();
}

#ifndef MYLIO_CLIENT
void bjson_bugcheck(const char *message)
{
    assert(false);
    int *nullaccess = nullptr;
    *nullaccess = 42;
}
#endif

dummy::BJsonType_dummy dummy_type = {0};

MYBJsonCheckedScopedIterator MYBJsonElement::innerChecked(class MYBJsonIteratorBase &end) const
{
    MYBJsonCheckedScopedIterator iterator(&sidAndType, end.ptr());
    return iterator;
}

MYBJsonUncheckedScopedIterator MYBJsonElement::innerUnchecked(class MYBJsonIteratorBase &end) const
{
    MYBJsonUncheckedScopedIterator iterator(&sidAndType, end.ptr());
    return iterator;
}

MYBJsonScopedIterator MYBJsonElement::inner(class MYBJsonIteratorBase &end) const
{
    MYBJsonScopedIterator iterator(&sidAndType, end.ptr());
    return iterator;
}

std::vector<uint8_t> MYBJsonElement::object_copy(const uint8_t *end) const
{
    assert((type() == BJsonType::array) || (type() == BJsonType::object));

    auto iter = MYBJsonIterator(&sidAndType, end);

    const uint8_t *startScope = iter.ptr();
    const uint8_t *endScope = iter.extent().ptr() + 1;

    std::vector<uint8_t> vec(startScope, endScope);
    return vec;
}

bool MYBJsonReader::quickShortCircuitFinder(const MYBJson &is, MYFastToStringHandler &handler, const std::string &lowerCasedWordToFind, bool copy, bool pretendOuterObject)
{
    return quickParse(is, handler, copy, pretendOuterObject, lowerCasedWordToFind);
}

bool MYBJsonReader::generateQuickString(const MYBJson &is, MYFastToStringHandler &handler, bool copy, bool pretendOuterObject)
{
    quickParse(is, handler, copy, pretendOuterObject);
    return true;
}

bool MYBJsonReader::quickParse(const MYBJson &is, MYFastToStringHandler &handler, bool copy, bool pretendOuterObject, const std::string lowerCasedWordToFind)
{
    _is = &is;

    std::deque<bool> modes; // true for object, false for array

    int currentMemberCount = 0;
    std::deque<int> memberCount;

    if (pretendOuterObject)
    {
        modes.push_back(true);
        memberCount.push_back(0);
    }

    for (const uint8_t *_ptr = is.pbegin(); _ptr != is.pend(); _ptr++)
    {
        _ptrType = _ptr;
        BJsonType type;
        if (!Type(_ptr, is.pend(), handler, type, modes.size() > 0 ? modes.back() : 0))
            return false;

        if (type == BJsonType::null)
        {
            currentMemberCount++;
            if (!handler.Null())
                return false;
        }
        else if (type == BJsonType::separator)
        {
            currentMemberCount++;
            if (!handler.Separator())
                return false;
        }
        else if (type == BJsonType::true1)
        {
            currentMemberCount++;
            if (!handler.Int32(1))
                return false;
        }
        else if (type == BJsonType::array)
        {
            currentMemberCount++;
            memberCount.push_back(currentMemberCount);
            currentMemberCount = 0;

            modes.push_back(false);
            if (!handler.StartArray())
                return false;
        }
        else if (type == BJsonType::object)
        {
            currentMemberCount++;
            memberCount.push_back(currentMemberCount);
            currentMemberCount = 0;

            modes.push_back(true);
            if (!handler.StartObject())
                return false;
        }
        else if (type == BJsonType::end)
        {
            if ((modes.size() > 0) && !modes.back())
            {
                if (!handler.EndArray(currentMemberCount))
                    return false;

                if (memberCount.size() > 0)
                {
                    currentMemberCount = memberCount.back();
                    memberCount.pop_back();
                }
            }
            else
            {
                if (!handler.EndObject(currentMemberCount))
                    return false;

                if (memberCount.size() > 0)
                {
                    currentMemberCount = memberCount.back();
                    memberCount.pop_back();
                }
            }

            if (modes.size() > 0)
                modes.pop_back();
        }
        else if (type == BJsonType::uint8)
        {
            currentMemberCount++;

            if (!next(_ptr, is.pend()))
                return false;

            if (!handler.Uint32(*_ptr))
                return false;
        }
        else if (type == BJsonType::fixed32)
        {
            currentMemberCount++;

            if (!next(_ptr, is.pend()))
                return false;

            if (_ptr + 3 >= is.pend())
                return false;

            uint32_t fixedInt32;
            memcpy(&fixedInt32, _ptr, sizeof(uint32_t));

            if (!handler.Uint32(fixedInt32))
                return false;

            _ptr += 3;
        }
        else if (type == BJsonType::varint)
        {
            currentMemberCount++;

            if (!next(_ptr, is.pend()))
                return false;

            uint64_t varInt;
            if (!readVarInt(_ptr, is.pend(), varInt))
                return false;

            if (!handler.Int64(varInt))
                return false;
        }
        else if (type == BJsonType::varint_neg)
        {
            currentMemberCount++;

            if (!next(_ptr, is.pend()))
                return false;

            uint64_t varInt;
            if (!readVarInt(_ptr, is.pend(), varInt))
                return false;

            varInt = ~varInt;
            if (!handler.Int64(varInt))
                return false;
        }
        else if (type == BJsonType::string)
        {
            currentMemberCount++;

            if (!next(_ptr, is.pend()))
                return false;

            uint64_t strlength;
            if (!readVarInt(_ptr, is.pend(), strlength))
                return false;

            assert(strlength < std::numeric_limits<size_t>::max());
            if (strlength == 0)
            {
                if (!handler.String("", 0, copy))
                    return false;
            }
            else
            {
                if (!next(_ptr, is.pend()))
                    return false;

                if (_ptr + strlength - 1 >= is.pend())
                    return false;

                if (!handler.String((const char *)_ptr, (size_t)strlength, copy))
                    return false;

                if (strlength > 1)
                {
                    _ptr += (strlength - 1);
                }
            }
        }
        else if (type == BJsonType::binary)
        {
            currentMemberCount++;

            if (!next(_ptr, is.pend()))
                return false;

            uint64_t strlength;
            if (!readVarInt(_ptr, is.pend(), strlength))
                return false;

            if (!next(_ptr, is.pend()))
                return false;

            if (_ptr + strlength - 1 >= is.pend())
                return false;

            assert(strlength < std::numeric_limits<size_t>::max());
            if (!handler.Binary(_ptr, (size_t)strlength, copy))
                return false;

            if (strlength > 1)
            {
                _ptr += (strlength - 1);
            }
        }
        else if (type == BJsonType::hash)
        {
            currentMemberCount++;

            if (!next(_ptr, is.pend()))
                return false;

            if (_ptr + (sizeof(MYHashStorage) - 1) >= is.pend())
                return false;

            MYHash hash;
            memcpy(&hash.raw[0], _ptr, sizeof(MYHashStorage));
            hash.resetsetbit();

            if (!handler.MYHash(hash))
                return false;

            _ptr += (sizeof(MYHashStorage) - 1);
        }
        else if (type == BJsonType::trev)
        {
            currentMemberCount++;

            if (!next(_ptr, is.pend()))
                return false;

            if (_ptr + (sizeof(MYTRev) - 1) >= is.pend())
                return false;

            MYTRev trev;
            memcpy(&trev, _ptr, sizeof(MYTRev));

            if (!handler.MYTRev(trev))
                return false;

            _ptr += (sizeof(MYTRev) - 1);
        }
        else if (type == BJsonType::float32)
        {
            currentMemberCount++;

            if (!next(_ptr, is.pend()))
                return false;

            uint32_t varInt;
            if (!readVarIntBigEndian32(_ptr, is.pend(), varInt))
                return false;

            assert(varInt <= std::numeric_limits<uint32_t>::max());

            uint32_t varInt32 = (uint32_t)varInt;
            float f;

            static_assert(sizeof(f) == sizeof(varInt32), "Size mismatch");
            memcpy(&f, &varInt32, sizeof(f));

            if (!handler.Double(f))
                return false;
        }
        else if (type == BJsonType::double64)
        {
            currentMemberCount++;

            if (!next(_ptr, is.pend()))
                return false;

            uint64_t varInt;
            if (!readVarIntBigEndian(_ptr, is.pend(), varInt))
                return false;

            double d;

            static_assert(sizeof(d) == sizeof(varInt), "Size mismatch");
            memcpy(&d, &varInt, sizeof(d));

            if (!handler.Double(d))
                return false;
        }

        // Check if we just want to generate the content or do we actually need to search for a string
        if (lowerCasedWordToFind != "")
        {
            const std::string &content = handler.toString();
            if (MYString::containsFastIgnoreCaseSearchIsLowerCased(content, lowerCasedWordToFind))
            {
                // Stop generating the bjson now - we know the word exists in here
                return true;
            }

            handler.clearContent();
        }
    }

    return false;
}
