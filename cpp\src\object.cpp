#include "encode_functions.h"
#include "helpers.h"

Napi::<PERSON>uffer<uint8_t> encode_object(Napi::Env env, const Napi::Object &jsObj, int sectionId)
{
	// {
	MYBJsonBigRW *writer = new MYBJsonBigRW();

	helpers::encodeObject(writer, sectionId, jsObj);

	return Napi::Buffer<uint8_t>::New(
			env,
			(uint8_t *)writer->pbegin(),
			writer->psize(),
			helpers::freeWriter,
			writer);
}

Napi::Object decode_object(Napi::Env isolate, const Napi::Buffer<uint8_t> &bjson, int sectionId)
{
	auto data = bjson.Data();
	auto size = bjson.Length();
	auto endPtr = data + size;

	MYBJsonIterator current = MYBJsonIterator(data, endPtr);
	MYBJsonIterator end(endPtr, endPtr);

	auto object = helpers::decodeObject(isolate, current, sectionId);
	return object;
}
