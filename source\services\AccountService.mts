import crypto = require("crypto");
import { Account, AccountFlags, IAccount } from "../models/Account.model.mjs";
import { chooseHasher } from "../system/PasswordHashers.mjs";
import { error, makeError } from "../system/error.mjs";
import { AccountRestDataService } from "../dataServices/Account.rest.dataService.mjs";
import { dbnull } from "../system/data.mjs";
import { Context } from "../system/Context.mjs";
import { check } from "../system/check.mjs";
import { merge, sanitizeOutput } from "../models/Account.model.mjs";
import { DeviceService } from "./DeviceService.mjs";
import { EmailService } from "../services/EmailService.mjs";
import { ids, EventType } from "../system/Strings.mjs";
import { ISyncService } from "./SystemService.mjs";
import { config, getServices } from "../system/Config.mjs";
import { Role, Token } from "../models/Token.mjs";
import { telemetry } from "../system/telemetry.mjs";
import moment = require("moment");
import { makeSalt } from "../system/PasswordHashers.mjs";
import { query } from "../system/Postgres.mjs";
import { CertificateService } from "./CertificateService.mjs";
import { tx } from "../system/Postgres.mjs";
import { LicenseRestDataService } from "../dataServices/License.rest.dataService.mjs";

export class AccountService implements ISyncService {
  constructor(
    private dataService: AccountRestDataService,
    private deviceService: DeviceService,
    private emailService: EmailService,
    private licenseDataService: LicenseRestDataService
  ) { }

  tryBySubAndIdp(context: Context, sub: string, idp: string) {
    sub = sub.toLowerCase();
    return this.dataService.readBySubAndIdp(context, sub, idp);
  }

  public bySub(context: Context, sub: string) {
    return this.tryBySub(context, sub).then((accounts) => {
      if (!accounts || !accounts.length) {
        return error<Account>(404, ids.ACCOUNT_NOT_FOUND);
      }

      return accounts[0];
    }) as Promise<Account>;
  }

  tryByEmail(context: Context, email: string) {
    email = email.toLowerCase();
    return this.dataService
      .findByEmail(context, email)
      .then((accounts) => accounts[0]) as Promise<Account>;
  }

  byEmail(context: Context, email: string) {
    return this.tryByEmail(context, email).then((account) => {
      if (!account) {
        return error<Account>(404, ids.ACCOUNT_NOT_FOUND);
      }

      return account;
    });
  }

  bySubAndIdp(context: Context, sub: string, idp: string) {
    return this.tryBySubAndIdp(context, sub, idp).then((account) => {
      if (!account) {
        return error<Account>(404, ids.ACCOUNT_NOT_FOUND);
      }
      return account;
    });
  }

  read(context: Context, aid: number) {
    return this.tryRead(context, aid).then((account) => {
      if (!account) {
        return error<Account>(404, ids.ACCOUNT_NOT_FOUND);
      }
      return account;
    }) as Promise<Account>;
  }

  readAccounts(context: Context, limit = 10) {
    return this.dataService.readAccounts(context, limit) as Promise<Account[]>;
  }

  tryRead(context: Context, aid: number) {
    return this.dataService.readByAccountId(context, aid);
  }

  async update(context: Context, account: Account) {
    return await tx<Account>(context, async () => {
      const dbAccount = await this.read(context, account.accountId());

      if (account.email() !== dbAccount.email()) {
        let accounts = await this.dataService.findByEmail(
          context,
          account.email()
        );
        if (accounts.length > 0)
          throw makeError(
            400,
            "DUPLIATE_EMAIL",
            `${account.email()} is already in use`
          );
      }

      account = merge(dbAccount, account);
      this.ensureEmailSubIdpIsLowerCase(account);

      if (account.password()) {
        account = await this.setPassword(context, account, account.password());
        let pem = CertificateService.CreateSelfSignedRootForAccount(
          context,
          account
        );
        account.rsaPrivateKey(pem.keyPEM);
        account.x509Cert(pem.certificatePEM);
      }

      account.email(account.email() || account.sub());
      await check(account);

      try {
        account = await this.dataService.update(context, account);
      } catch (err) {
        if (err.code === "23505" && err.constraint === "ui_account_idp_sub")
          throw makeError(
            400,
            ids.DUPLICATE_SUB_AND_IDP,
            "Account already exists"
          );
        throw err;
      }

      if (dbAccount.sub() !== account.sub()) {
        await telemetry(context, account.accountId(), EventType.EMAIL_CHANGED, {
          aid: account.accountId(),
          from: dbAccount.sub(),
          to: account.sub(),
        });
      }
      if (dbAccount.planId() !== account.planId()) {
        await telemetry(
          context,
          account.accountId(),
          EventType.SUBSCRIPTION_CHANGED,
          {
            aid: account.accountId(),
            from: dbAccount.planId(),
            to: account.planId(),
          }
        );
      }

      return await this.sendEmails(context, account, dbAccount);
    });
  }

  sendEmails(context: Context, account: Account, prev: Account) {
    let emails: Promise<any>[] = [];

    if (account.passwordHash() !== prev.passwordHash()) {
      emails.push(this.emailService.sendChangePasswordAlert(context, account));
    }

    if (account.email() !== prev.email()) {
      emails.push(this.emailService.sendChangeEmailAlert(context, prev));
    }

    return Promise.all(emails)
      .then(() => account)
      .catch((err) => {
        context.error(err, "SEND_EMAIL_ERROR");
        // don"t blow up on send email errors
        return Promise.resolve(account);
      });
  }

  setPassword(context: Context, account: Account, password: string) {
    return Promise.resolve(chooseHasher(2))
      .then((hasher) => {
        return hasher(password);
      })
      .then((result) => {
        account.salt(result.salt);
        account.passwordHash(result.passwordHash);
        account.passwordHashVersion(2);
        account.passwordSetTime(new Date());

        return account;
      });
  }

  async create(context: Context, account: Account) {
    if (account.role() === "admin" && !context.hasAdminRights()) {
      throw makeError(
        400,
        ids.AUTHENTICATION_FAILED,
        "Admin accounts can't be created using the API"
      );
    }
    let pem = CertificateService.CreateSelfSignedRootForAccount(
      context,
      account
    );
    // set the canDoCreateTrial and canTrial flags
    // they wil be needed until the client can update
    // to use the /available-upgrade route
    account.rsaPrivateKey(pem.keyPEM);
    account.x509Cert(pem.certificatePEM);
    this.ensureEmailSubIdpIsLowerCase(account);
    account.peerToPeerKey(
      account.peerToPeerKey() || crypto.randomBytes(64).toString("hex")
    );
    account.cipher(
      account.cipher() || crypto.randomBytes(20).toString("base64")
    );
    account.email(account.email() || account.sub());
    return await tx<Account>(context, async () => {
      await check(account);
      if (account.password()) {
        account = await this.setPassword(context, account, account.password());
      } else {
        account.salt(await makeSalt(undefined));
      }
      try {
        account = await this.dataService.create(context, account);
      } catch (err) {
        if (
          err.code === "23505" &&
          err.constraint === "ux_account_by_sub_and_idp"
        )
          throw makeError(
            400,
            ids.DUPLICATE_SUB_AND_IDP,
            "Account already exists"
          );
        throw err;
      }
      await this.deviceService.createCloudDevice(context, account.accountId());
      return account;
    });
  }

  async delete(context: Context, aid: number) {
    await tx(context, async () => {
      const licenses = await this.licenseDataService.findByAccountId(
        context,
        aid
      );
      const paid = licenses.find((s) => s.licenseFlags().paid() && !s.deleted());
      if (paid) throw makeError(400, "PAID_SUBSCRIPTION_EXISTS");
      return this.dataService.deleteByAccountId(context, aid);
    });
  }

  sync(context: Context, aid: number, input: any, output: any) {
    let root =
      input &&
      input.accounts &&
      input.accounts["0"] &&
      input.accounts["0"]["0"];
    if (root && root.have) {
      return this.read(context, aid)
        .then((account) => {
          let clientTString = root.have[0] as string;
          let cloudT = Buffer.from(account.t(), "base64");
          let clientT = Buffer.from(clientTString, "base64");
          if (cloudT.compare(clientT) === -1) {
            account.t(clientTString);
            return this.dataService.update(context, account);
          }
          return account;
        })
        .then((stolen) => {
          output = output || {};
          output.accounts = output.accounts || {};
          output.accounts["0"] = output.accounts["0"] || {};
          output.accounts["0"]["0"] = output.accounts["0"]["0"] || {};
          output.accounts["0"]["0"].data = [
            sanitizeOutput(stolen, context.hasAdminRights()),
          ];
          return output;
        });
    } else {
      return Promise.resolve(output);
    }
  }

  async showUpgradeWarnings(context: Context, aid: number, did: number, protocolVersion: number) {
    return query<void>(context, "select a0.show_upgrade_warnings($1, $2, $3)", [aid, did, protocolVersion]);
  }

  getSupportPolicy(context: Context, issue: string, ext: string) {
    return new Promise((resolve, reject) => {
      if (!issue) {
        return resolve({});
      }

      let bucket = "mylo_support";
      let filepath = `logs_v3/${context.token.aid()}/${issue}${ext}`;
      let filetype = "image/";
      let policy = this.getS3Creds(context, bucket, filepath, filetype);

      return resolve(policy);
    });
  }

  getS3Creds(
    context: Context,
    bucket: string,
    filepath: string,
    filetype: string
  ) {
    let folder = `logs_v3/${context.token.aid()}`;
    let now = new Date();
    let policy = {
      expiration:
        "" +
        now.getFullYear() +
        "-" +
        (now.getMonth() + 1) +
        "-" +
        (now.getDate() + 1) +
        "T" +
        now.getHours() +
        ":" +
        now.getMinutes() +
        ":" +
        now.getSeconds() +
        "Z",
      conditions: [
        { bucket: bucket },
        ["starts-with", "$key", folder],
        { acl: "private" },
        { success_action_status: "200" },
        ["starts-with", "$Content-Type", ""],
      ],
    };

    let policyStr = JSON.stringify(policy).replace(/\n/g, "");
    let base64Policy = new Buffer(policyStr, "utf8").toString("base64");
    let signature = crypto
      .createHmac("sha1", config.s3_secret)
      .update(base64Policy)
      .digest("base64");

    let s3Creds = {
      s3PolicyBase64: base64Policy,
      s3Signature: signature,
      s3Key: config.s3_key,
      s3Policy: policy,
      objectPath: filepath,
    };

    return s3Creds;
  }

  handleGrooveWebhook(context: Context, email: string, apiToken: string) {
    email = email.toLowerCase();
    if (apiToken !== config.groove_private_key) {
      return error<any>(403, ids.FORBIDDEN);
    }

    return this.bySubAndIdp(context, email, "mylio").then((account) => {
      if (!account) {
        return error<Account>(404, ids.ACCOUNT_NOT_FOUND);
      }

      //TODO: purchaseService is gone, must replace logic
    });
  }

  ensureEmailSubIdpIsLowerCase(account: Account) {
    account.email(this.makeValidSub(account.email()));
    account.sub(this.makeValidSub(account.sub()));
    account.idp(this.makeValidSub(account.idp()));
    return account;
  }

  makeValidSub(value: string) {
    if (value) return value.toLowerCase().replace(/\s+/g, "");

    return value;
  }

  public tryBySub(context: Context, sub: string) {
    sub = sub.toLocaleLowerCase();
    return this.dataService.findBySub(context, sub);
  }

  public async getSignInOptions(context: Context, email: string) {
    let account = await this.tryByEmail(context, email);
    if (!account) {
      let possibles = await this.tryBySub(context, email);
      if (possibles.length === 1) account = possibles[0];
    }

    if (!account) return { known: false };

    if (account.idp() === "mylio") {
      let magicLink = false;
      let password = !!account.passwordHash();
      if (account.email()) {
        magicLink = !password || account.tfa();
      }
      return {
        known: true,
        idp: account.idp(),
        sso: false,
        magicLink,
        password,
        tfa: account.tfa(),
      };
    } else {
      return {
        known: true,
        idp: account.idp(),
        sso: true,
        magicLink: false,
        password: false,
        tfa: account.tfa(),
      };
    }
  }


  async runQuery(context: Context, name: string, value: string, limit: number) {
    let results: IAccount[] = [];
    switch (name) {
      case "account-search":
        if (!isNaN(value as any)) {
          const queryRes = await query<IAccount>(
            context,
            `select * from a0."Account" where "accountId" = $1`,
            [value]
          );
          results = results.concat(queryRes);
        }
        const queryRes = await query<IAccount>(
          context,
          `select * from a0."Account" where email like $1::text || '%' limit $2`,
          [value, limit]
        );
        results = results.concat(queryRes);
        return results;

      default:
        throw makeError(400, "QUERY_NOT_FOUND");
    }
  }
}
