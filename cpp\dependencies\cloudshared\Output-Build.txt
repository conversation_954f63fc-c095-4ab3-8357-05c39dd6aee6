﻿1>------ Build started: Project: My<PERSON><PERSON><PERSON><PERSON>, Configuration: Debug x64 ------
1>  stdafx.cpp
1>  asyncrt_utils.cpp
1>  pplxwin.cpp
1>  MYStringUtil.cpp
1>  MYTrev.cpp
1>  json_reader.cpp
1>  json_value.cpp
1>  json_writer.cpp
1>  base64.cpp
1>  HMAC_SHA1.cpp
1>  liboauthcpp.cpp
1>  SHA1.cpp
1>  urlencode.cpp
1>  animutil.cpp
1>  autoenhance.cpp
1>  brushes.cpp
1>  MYDBConnection.cpp
1>  MYDBConnectionPool.cpp
1>  MYDBPooledConnection.cpp
1>  MYDBPreparedStatement.cpp
1>  MYDBResult.cpp
1>  MYDBTransaction.cpp
1>  MYSQLFunctions.cpp
1>  MYSQLVariant.cpp
1>  MYCatalog.cpp
1>  MYCatalogCursor.cpp
1>  MYCatalogSearcher.cpp
1>  MYCatalogServer.cpp
1>  MYCatalogStatistics.cpp
1>  MYCatalogStatisticsAdd.cpp
1>  MYCatalogStatisticsRemove.cpp
1>  MYFieldMapping.cpp
1>  MYResourceCache.cpp
1>  MYSchemaTransform.cpp
1>  MYSQLQueryBuilder.cpp
1>  MYClassifierSql.cpp
1>  MYEventClassifier.cpp
1>  MYNaiveBayes.cpp
1>  MYTokenizer.cpp
1>  dbgutil.cpp
1>  MYClusterizer.cpp
1>  MYDialogFSChanges.cpp
1>  MYEventGenerator.cpp
1>  MYFileLocator.cpp
1>  MYFiler.cpp
1>  MYFileSystemGlue.cpp
1>  MYFSGlueTask.cpp
1>  MYFSGlueTask_Arranger.cpp
1>  MYFSGlueTask_Deleter.cpp
1>  MYFSGlueTask_FolderMover.cpp
1>  MYFSGlueTask_FileMover.cpp
1>  MYFSGlueTask_FSCleanup.cpp
1>  MYFSGlueTask_MDLCleaner.cpp
1>  MYFSGlueTask_Scanner.cpp
1>  MYFSWatcher.cpp
1>  MYFSWatcher_Win.cpp
1>  histograms.cpp
1>  igloo.cpp
1>  igloo_dx.cpp
1>  MYAsset.cpp
1>  MYBitmapImageData.cpp
1>  MYBitmapImageDataSSE.cpp
1>  MYBitmapSource.cpp
1>  MYBitmapTests.cpp
1>  MYBitmapUtils.cpp
1>  MYBitmapUtilsSSE4.cpp
1>  MYBrushOverlayFilter.cpp
1>  MYCatmullRomCurve.cpp
1>  MYClutTransform.cpp
1>  MYColorManager.cpp
1>  MYColorMatrixFilter.cpp
1>  MYColorTransformFilter.cpp
1>  MYCropFilter.cpp
1>  MYDither.cpp
1>  MYFilterChain.cpp
1>  MYFilterFactory.cpp
1>  MYGammaCurve.cpp
1>  MYHalfFloat.cpp
1>  MYIccProfile.cpp
1>  MYIccTransform.cpp
1>  MYImageFilter.cpp
1>  MYImageMaster.cpp
1>  MYImageSettings.cpp
1>  MYImageSource.cpp
1>  MYImageUndoOperation.cpp
1>  MYJxrDecoder.cpp
1>  MYJxrEncoder.cpp
1>  MYLcmsTransform.cpp
1>  MYLensCorrectionFilter.cpp
1>  MYLoadImageWorkItem.cpp
1>  MYLoadPreviewWorkItem.cpp
1>  MYLocalContrastFilter.cpp
1>  MYLuminanceMap.cpp
1>  MYMapFunction.cpp
1>  MYMatrixTransform.cpp
1>  MYMipSource.cpp
1>  MYPDFDecoder.cpp
1>  MyPanasonicLensFilter.cpp
1>  MYPDFExploder.cpp
1>  MYProfileManager.cpp
1>  MYPSDDecoder.cpp
1>  MYRawDataFilter.cpp
1>  MYRawDataSource.cpp
1>  MYRawDecoderSource.cpp
1>  MYRedEyeFilter.cpp
1>  MYSharpenFilter.cpp
1>  MYTextureStats.cpp
1>  MYTile.cpp
1>  MYTiledBitmap.cpp
1>  MYTiledImage.cpp
1>  MYTileMap.cpp
1>  MYTileMapSource.cpp
1>  MYToneCurves.cpp
1>  MYToneFilter.cpp
1>  MYTransformManager.cpp
1>  MYStrings_en_US.cpp
1>  MYStrings_fr_CA.cpp
1>  lenslookup.cpp
1>  MYInstrumentation_base.cpp
1>  MYInstrumentation_GitVersion.cpp
1>  MYInstrumentation_Win.cpp
1>  MYLog.cpp
1>  MYActionMenu.cpp
1>  MYAlbum.cpp
1>  MYAlbumCarouselController.cpp
1>  MYAlbum_Base.cpp
1>  MYAlertControl.cpp
1>  MYApp.cpp
1>  MYAppAsyncWorkItems.cpp
1>  MYAppCommands.cpp
1>  MYAppointment.cpp
1>  MYAppScript.cpp
1>  MYAppUndoOperations.cpp
1>  MYAuditEntry.cpp
1>  MYAuditEntry_Base.cpp
1>  MYBasicTypes.cpp
1>  MYBlob.cpp
1>  MYBoard.cpp
1>  MYActionBarController.cpp
1>  MYBulkItemTasks.cpp
1>  MYCameraController.cpp
1>  MYCameraSupportUpdate.cpp
1>  MYCard.cpp
1>  MYCarousel.cpp
1>  MYCarouselController.cpp
1>  MYCatalogRecovery.cpp
1>  MYChannel.cpp
1>  MYCirclePlacementControl.cpp
1>  MYCloud.cpp
1>  MYConfig.cpp
1>  MYConnection.cpp
1>  MYConsole.cpp
1>  MYContainerViewCarousel.cpp
1>  MYControl.cpp
1>  MYCropControl.cpp
1>  MYCryptStream.cpp
1>  MYDateTime.cpp
1>  MYDebug.cpp
1>  MYDeletedResource.cpp
1>  MYDeletedResource_Base.cpp
1>  MYDetailsControls.cpp
1>  MYDetailsPanel_Edit.cpp
1>  MYDetailsPanel_GeoMap.cpp
1>  MYDetailsPanel_People.cpp
1>  MYDetailsPanel_Sync.cpp
1>  MYDeviceConnectionCloud.cpp
1>  MYDeviceConnectionMethod.cpp
1>  MYDeviceErrorReportGenerator.cpp
1>  MYDiagnostic.cpp
1>  MYDialogArrange.cpp
1>  MYDialogImport.cpp
1>  MYDialogs.cpp
1>  MYDialogsAlbumList.cpp
1>  MYDialogsFacebook.cpp
1>  MYDialogsFlickr.cpp
1>  MYDialogsKeywordList.cpp
1>  MYDialogsNameTemplates.cpp
1>  MYDialogsSettings.cpp
1>  MYDialog_Developer.cpp
1>  MYEC2Cloud.cpp
1>  MYEditControlBase.cpp
1>  MYEmail.cpp
1>  MYEvent.cpp
1>  MYEventController.cpp
1>  MYEvent_Base.cpp
1>  MYExifOrientation.cpp
1>  MYExportImageDialog.cpp
1>  MYExportVideoDialog.cpp
1>  MYExtensionResource.cpp
1>  MYExtensionResource_Base.cpp
1>  MYEyeFiCard.cpp
1>  MYFacebookCheckin.cpp
1>  MYFacebookEvent.cpp
1>  MYFaceClusterer.cpp
1>  MYFaceDetector.cpp
1>  MYFaceDetector_Dlib.cpp
1>  MYFaceSuggester.cpp
1>  MYFaceRecognitionDatabase.cpp
1>  MYFaceRectangle.cpp
1>  MYFaceRectangle_Base.cpp
1>  MYFaultInjection.cpp
1>  MYFileErrorReportGenerator.cpp
1>  MYForgottenResource_Base.cpp
1>  MYLocation_Base.cpp
1>  MYOAuthUI.cpp
1>  MYPDFExploderDialog.cpp
1>  MYPeopleControls.cpp
1>  MYPersonMetadata.cpp
1>  MYPicasaReader.cpp
1>  MYReplicator.cpp
1>  MYMediaDevice.cpp
1>  MYFileExporter.cpp
1>  MYFileExportTask.cpp
1>  MYFileIngestor.cpp
1>  MYFileSystemIngestor.cpp
1>  MYFilterHistoryItem.cpp
1>  MYFirstRunDialog.cpp
1>  MYFolder.cpp
1>  MYFolderContentCarouselController.cpp
1>  MYFolderDeviceLink.cpp
1>  MYFolderDeviceLink_Base.cpp
1>  MYFolder_Base.cpp
1>  MYForgottenResource.cpp
1>  MYGeoLocation.cpp
1>  MYGuidedExperienceDialog.cpp
1>  MYHash.cpp
1>  MYHistogramControl.cpp
1>  MYImageDecoder.cpp
1>  MYImageMetadata.cpp
1>  MYImageUtil.cpp
1>  MYImportableResource.cpp
1>  MYImportContext.cpp
1>  MYImportSession.cpp
1>  MYIngestHelpers.cpp
1>  MYInstrumenter.cpp
1>  MYInternet.cpp
1>  MYKnownDevice.cpp
1>  MYLensDB.cpp
1>  MYLink.cpp
1>  MYLink_Base.cpp
1>  MYLocation.cpp
1>  MYLocationCarouselController.cpp
1>  MYLock.cpp
1>  MYMapControl.cpp
1>  MYMargin.cpp
1>  MYMD5.cpp
1>  MYMedia.cpp
1>  MYMediaAlbumLink.cpp
1>  MYMediaAlbumLink_Base.cpp
1>  MYMediaContainer.cpp
1>  MYMediaContainer_Base.cpp
1>  MYMediaFile.cpp
1>  MYMediaFileType.cpp
1>  MYMediaFile_Base.cpp
1>  MYMediaMetadataService.cpp
1>  MYMedia_Base.cpp
1>  MYMetadataManager.cpp
1>  MYNAS.cpp
1>  MYNavPanelController.cpp
1>  MYNetworkNode.cpp
1>  MYNetworkNodeData.cpp
1>  MYNetworkNode_Base.cpp
1>  MYPageLayout.cpp
1>  MYPageLayoutDialog.cpp
1>  MYParty.cpp
1>  MYPathUtil.cpp
1>  MYPeerReplicator.cpp
1>  MYPeopleCarouselController.cpp
1>  MYPerson.cpp
1>  MYPerson_Base.cpp
1>  MYPlatform.cpp
1>  MYPlatformMenu.cpp
1>  MYPopupMenu.cpp
1>  MYPreferences.cpp
1>  MYFlutterLayout.cpp
1>  MYMainLineCarousel.cpp
1>  MYPocketCarouselController.cpp
1>  MYDetailsPanelController.cpp
1>  MYElement.cpp
1>  MYPropertiesHelpers.cpp
1>  MYProtectionConfigDialogs.cpp
1>  MYRatingBarController.cpp
1>  MYRatingOverlay.cpp
1>  MYRedEyeControl.cpp
1>  MYReplicationSendItems.cpp
1>  MYReplicationStream.cpp
1>  MYResource.cpp
1>  MYResourceReference.cpp
1>  MYResourceTypes.cpp
1>  MYResourceUpdateManager.cpp
1>  MYResource_Base.cpp
1>  MYSelectionContext.cpp
1>  MYSharedConfiguration.cpp
1>  MYSharedConfiguration_Base.cpp
1>  MYSharedResources.cpp
1>  MYSimpleTime.cpp
1>  MYSlideshow.cpp
1>  MYStopWatch.cpp
1>  MYStringTable.cpp
1>  MYStringUtilEx.cpp
1>  MYStyles.cpp
1>  MYFolderCarouselController.cpp
1>  MYItem.cpp
1>  MYMediaCarouselController.cpp
1>  MYPreviewCarouselController.cpp
1>  MYSearchParser.cpp
1>  MYNavigationBarController.cpp
1>  MYTaskScheduler.cpp
1>  MYTelephone.cpp
1>  MYTemplateProcessor.cpp
1>  MYTextureCache.cpp
1>  MYThread.cpp
1>  MYTimelineController.cpp
1>  MYUndoManager.cpp
1>  MYVideoExporter.cpp
1>  MYVirtualMediaNetworkNodeLink.cpp
1>  MYWelcomeDialog.cpp
1>  MYWorkflowState.cpp
1>  MYXMPFile.cpp
1>  MYHTTPClientService.cpp
1>  MYHTTPConnection.cpp
1>  MYHTTPFactory.cpp
1>  MYHTTPRequest.cpp
1>  MYHTTPUser.cpp
1>  MYRequestInfo.cpp
1>  MYHTTPRequestTests.cpp
1>  MYHTTPURLTests.cpp
1>  MYBinaryStream.cpp
1>  MYBonjourBrowser.cpp
1>  MYBonjourRegistration.cpp
1>  MYRunLoop.cpp
1>  MYRunLoopOperation.cpp
1>  MYSocket.cpp
1>  MYSocketListener.cpp
1>  MYStream.cpp
1>  MYStreamSendItems.cpp
1>  MYUDPBroadcaster.cpp
1>  CameraProfile.cpp
1>  CfaInfo.cpp
1>  ColorSpace.cpp
1>  ColorSpec.cpp
1>  Convolution.cpp
1>  Demosaic.cpp
1>  DemosaicAHD.cpp
1>  DemosaicAhdXTrans.cpp
1>  DemosaicDebug.cpp
1>  HueSatMap.cpp
1>  HueSatUtils.cpp
1>  HuffmanTable.cpp
1>  ImageTransforms.cpp
1>  InflateFp.cpp
1>  LosslessJpegDecoder.cpp
1>  MYStreamReader.cpp
1>  NikonDecoder.cpp
1>  NikonYCrCbDecoder.cpp
1>  NumericUtils.cpp
1>  OlympusDecoder.cpp
1>  PackedDecoder.cpp
1>  PixelTileIterator.cpp
1>  RawData.cpp
1>  RawDataBuffer.cpp
1>  RawDecodeInfo.cpp
1>  RawDecoder.cpp
1>  RawDecoderCanon.cpp
1>  RawDecoderDng.cpp
1>  RawDecoderFuji.cpp
1>  RawDecoderNikon.cpp
1>  RawDecoderOlympus.cpp
1>  RawDecoderPanasonic.cpp
1>  RawDecoderSamsung.cpp
1>  RawDecoderSony.cpp
1>  RawDecompressor.cpp
1>  TiffEncoder.cpp
1>  TiffTypes.cpp
1>  TiledPixelWriter.cpp
1>  ToneCurves.cpp
1>  XyChromaCoordinate.cpp
1>  MYCloudErrorManager.cpp
1>  MYCloudPortalDialog.cpp
1>  MYCloudService.cpp
1>  MYCloudServiceDiag.cpp
1>  MYCloudV2Service.cpp
1>  MYRemoteConsoleClient.cpp
1>  MYAdHocWifiService.cpp
1>  MYAuditService.cpp
1>  MYBatchService.cpp
1>  MYBufferDataReceiver.cpp
1>  MYCalendarService.cpp
1>  MYCameraDeviceService.cpp
1>  MYClientUpdaterService.cpp
1>  MYCoreDataReceiver.cpp
1>  MYFacebookAlbumListService.cpp
1>  MYFacebookImportService.cpp
1>  MYFacebookPublishService.cpp
1>  MYFacebookService.cpp
1>  MYFacebookJob.cpp
1>  MYFaceRecognitionService.cpp
1>  MYFileDataReceiver.cpp
1>  MYFlickrImportService.cpp
1>  MYFlickrJob.cpp
1>  MYFlickrPublishService.cpp
1>  MYFlickrService.cpp
1>  MYFlickrSetlistService.cpp
1>  MYHTTPService.cpp
1>  MYImporterService.cpp
1>  MYLocationService.cpp
1>  MYLoginService.cpp
1>  MYNetworkService.cpp
1>  MYPreviewGenerationService.cpp
1>  MYQueryWatcherService.cpp
1>  MYRawDecoderService.cpp
1>  MYRemoteAlbum.cpp
1>  MYServices.cpp
1>  MYStampingService.cpp
1>  MYTelemetryService.cpp
1>  MYWebServiceJob.cpp
1>  SIMDUtils.cpp
1>  MYCatalogTests.cpp
1>  MYClassifierTests.cpp
1>  MYClusterizerTests.cpp
1>  MYEasyTestClient.cpp
1>  MYEventAssociationTests.cpp
1>  MYFacebookTests.cpp
1>  MYFileExportTests.cpp
1>  MYFileLocatorTests.cpp
1>  MYFileMoverTests.cpp
1>  MYFilePathTests.cpp
1>  MYFileSystemGlueTests.cpp
1>  MYImageTests.cpp
1>  MYItemSetTests.cpp
1>  MYNetworkTests.cpp
1>  MYPlatformImageTests.cpp
1>  MYPlatformTests.cpp
1>  MYVariantTests.cpp
1>  MYVectorTests.cpp
1>  MYXMPTests.cpp
1>  MYZipArchive.cpp
1>  pugixml.cpp
1>  untar.cpp
1>  MYDXContext.cpp
1>  MYDXTexture3D.cpp
1>  MYDXTile.cpp
1>  MYIccProfileWin.cpp
1>  MYImageFilterDX.cpp
1>  MYProgramDX.cpp
1>  MYHTTPConnection_Win.cpp
1>  MYHTTPRequest_Win.cpp
1>  MYHTTPURL_Win.cpp
1>  MYAdhocWifiService_Win.cpp
1>  MYCalendarService_Win.cpp
1>  MYCalendar_Win.cpp
1>  MYCameraIngestor_Win.cpp
1>  MYCloudPortalDialog_Win.cpp
1>  MYFacebookLoginDialog.cpp
1>  MYFacebookService_Win.cpp
1>  MYFileBrowser_Win.cpp
1>  MYFlickrService_Win.cpp
1>  MYFlickrLoginDialog.cpp
1>  MyLoApp.cpp
1>  MyloWindow.cpp
1>  MYNetworkService_Win.cpp
1>  MYOAuthUI_win.cpp
1>  MYPlatformImage_win32.cpp
1>  MYPrinter_Win.cpp
1>  MYVideoEncoder_Win.cpp
1>  MYWebController.cpp
1>  MYPlatform_Win32.cpp
1>     Creating library D:\git\mylo2\out\\win64\Debug\Mylio.lib and object D:\git\mylo2\out\\win64\Debug\Mylio.exp
1>  Generating code
1>  Finished generating code
1>  MyLoApp.vcxproj -> D:\git\mylo2\out\\win64\Debug\Mylio.exe
1>  File not found - dnssd.dll
1>  0 File(s) copied
1>  D:\git\mylo2\source\win32\MyLoApp\..\..\shared\resources\facerec\FaceLandmark.dat
1>  D:\git\mylo2\source\win32\MyLoApp\..\..\shared\resources\facerec\frstatic.bin
1>          2 file(s) copied.
========== Build: 1 succeeded, 0 failed, 0 up-to-date, 0 skipped ==========
