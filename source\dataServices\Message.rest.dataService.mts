
import { query } from "../system/Postgres.mjs";
import { Message, IMessage} from "../models/Message.model.mjs";
import { Context } from "../system/Context.mjs";
import { makeError } from "../system/error.mjs";


export class MessageRestDataService {

    
/* b::rest_public_members */

/* end */


  public query(context: Context, sql: string, params: any[]) {
    return query < IMessage> (context, sql, params)
      .then(results => {
        if (results.length === 1) {
          if (!results[0].accountId && !results[0].messageId) {
      return [];
    }
  }
  return results.filter(o => !!o).map(o => new Message(o));
});
    }

		public create (context: Context, entity: Message) {
          


  let params = [
    entity.flags(),
				entity.modifiedTime(),
				entity.createdTime(),
				entity.accountId(),
				entity.deleted(),
				entity.t(),
				entity.d(),
				entity.deviceId(),
				entity.messageId(),
				entity.secondsToDisplay(),
				entity.displayed(),
				entity.message(),
				entity.link()
  ];
  return this
    .query(context, "select * from a0.message_create ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13) ", params)
  .then(r => r[0]);
        }

		public readByAccountIdAndMessageId (context: Context, accountId: number, messageId: number) {
  let params = [
    accountId,
				messageId
  ];
  return this
    .query(context, "select * from a0.message_read_by_account_id_and_message_id  ($1,$2) ", params).then(r => r[0]); 
                
        }

		public readByAccountId (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.message_read_by_account_id  ($1) ", params).then(r => r[0]); 
                
        }

		public update (context: Context, entity: Message) {
          


  let params = [
    entity.flags(),
				entity.modifiedTime(),
				entity.createdTime(),
				entity.accountId(),
				entity.deleted(),
				entity.t(),
				entity.d(),
				entity.deviceId(),
				entity.messageId(),
				entity.secondsToDisplay(),
				entity.displayed(),
				entity.message(),
				entity.link()
  ];
  return this
    .query(context, "select * from a0.message_update ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13) ", params)
  .then(r => r[0]);
        }

		public deleteByAccountIdAndMessageId (context: Context, accountId: number, messageId: number) {
  let params = [
    accountId,
				messageId
  ];
  return this
    .query(context, "select * from a0.message_delete_by_account_id_and_message_id  ($1,$2) ", params).then(r => r[0]); 
                
        }

		public deleteByAccountId (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.message_delete_by_account_id  ($1) ", params).then(r => r[0]); 
                
        }

		public findByAccountId (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.message_find_by_account_id  ($1) ", params); 
                
        }

		public merkle (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.message_merkle ($1) ", params).then(r => r[0]); 
                
        }

}
