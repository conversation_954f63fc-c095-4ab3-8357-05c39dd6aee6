import moment from "moment";
import { decodeReceipt } from "../appstore/Decoding.mjs";
import {
  AppStoreServerAPI,
  Environment,
  decodeNotificationPayload,
  SendTestNotificationResponse,
  SubscriptionStatusesQuery,
  decodeRenewalInfo,
  decodeTransaction,
  decodeTransactions,
  APPLE_ROOT_CA_G3_FINGERPRINT,
  NotificationType,
  SubscriptionStatus,
  JWSTransactionDecodedPayload,
  JWSRenewalInfoDecodedPayload,
  AutoRenewStatus,
} from "../appstore/index.mjs";
import { Manager, License, LicenseStatus } from "../models/License.model.mjs";
import { config } from "../system/Config.mjs";
import { Context } from "../system/Context.mjs";
import { post } from "../system/fetch.mjs";
import { LicenseService } from "./LicenseService.mjs";
import { makeError } from "../system/error.mjs";
import { ids } from "../system/Strings.mjs";
import { last } from "lodash";
import { Templates } from "../models/LicenseTemplate.model.mjs";

const KEY = `*****************************************************************************************************************************************************************************************************************************************************************`;

const KEY_ID = "5CT6MAGYSL";
const ISSUER_ID = "69a6de7a-91fa-47e3-e053-5b8c7c11a4d1";
const APP_BUNDLE_ID = "com.mylollc.Mylio";

console.log("using appstore environment " + config.apple.appstoreEnvironment);

const api = new AppStoreServerAPI(
  KEY,
  KEY_ID,
  ISSUER_ID,
  APP_BUNDLE_ID,
  config.apple.appstoreEnvironment as Environment
);

export default class AppStoreService {
  constructor(private licenseService: LicenseService) { }

  async requestTestNotification() {
    await await api.requestTestNotification();
  }

  async handleNotification(context: Context, spayload: string) {
    const snotification = await decodeNotificationPayload(
      spayload,
      APPLE_ROOT_CA_G3_FINGERPRINT
    );
    const tx = await decodeTransaction(
      snotification.data.signedTransactionInfo,
      APPLE_ROOT_CA_G3_FINGERPRINT
    );
    switch (snotification.notificationType) {
      case NotificationType.DidRenew:
      case NotificationType.Expired:
      case NotificationType.DidChangeRenewalPref:
      case NotificationType.DidChangeRenewalStatus:
      case NotificationType.OfferRedeemed:
        let subs = await this.licenseService.findByPartialId(context, `${Manager.Apple}.${tx.originalTransactionId}`);
        if (subs && subs.length) {
          await this.sync(context, subs[0].accountId(), tx.originalTransactionId);
        } else throw makeError(400, ids.SUBSCRIPTION_NOT_FOUND);
    }
  }

  private async verifyReceipt(receipt: any) {
    let r: any;
    try {
      let payload = {
        "receipt-data": receipt,
        password: config.apple.appStorePassword,
      };
      if (config.apple.appStoreUrl == void 0) {
        r = await post("https://buy.itunes.apple.com/verifyReceipt", payload);
        if (r?.status == 21007) {
          console.log("production appstore failed with 21007 trying sandbox");
          r = await post(
            "https://sandbox.itunes.apple.com/verifyReceipt",
            payload
          );
          console.log("used sandbox appstore");
        } else {
          console.log("used production appstore");
        }
      } else
        r = await post(config.apple.appStoreUrl, {
          uri: config.apple.appStoreUrl,
        });
    } catch (err) {
      console.log("verify receipt failed with " + JSON.stringify(err));
      throw err;
    }
    return r;
  }

  async sync(context: Context, accountId: number, originalTxId: string) {
    console.log(
      "calling sync for account " +
      accountId.toString() +
      " using transaction id : " +
      originalTxId
    );

    const licenses = await this.licenseService.findByPartialId(context, `${Manager.Apple}.${originalTxId}`);
    let ourLicense = licenses.find((s) => !s.deleted());
    let appleTransactions = await api.getSubscriptionStatuses(originalTxId);
    let appleSub = appleTransactions.data[0].lastTransactions[0];
    let lastAppleTx = await decodeTransaction(appleSub.signedTransactionInfo);
    let lastAppleRenewalInfo = await decodeRenewalInfo(
      appleSub.signedRenewalInfo
    );

    if (!ourLicense && appleSub.status === SubscriptionStatus.Active) {
      ourLicense = await this.createNewLicense(
        context,
        accountId,
        lastAppleTx,
        lastAppleRenewalInfo
      );
    }

    if (ourLicense) {
      switch (appleSub.status) {
        case SubscriptionStatus.Expired:
        case SubscriptionStatus.Revoked:
          ourLicense.status(LicenseStatus.Deleted);
          ourLicense.endDate(moment.utc().toDate());
          break;
        case SubscriptionStatus.Active:
          let canceled =
            lastAppleRenewalInfo.autoRenewStatus === AutoRenewStatus.Off;
          if (canceled !== (ourLicense.status() === LicenseStatus.Canceled)) {
            ourLicense.status(LicenseStatus.Canceled);
          }
          ourLicense.endDate(moment.unix(lastAppleTx.expiresDate / 1000).toDate());
        // if (ourLicense.templateId() !== lastAppleTx.productId) {
        //   ourLicense.deleted(true);
        //   ourLicense.endDate(moment.utc().toDate());
        //   await this.createNewLicense(
        //     context,
        //     accountId,
        //     lastAppleTx,
        //     lastAppleRenewalInfo
        //   );
        // }
      }
      if (ourLicense.changed)
        await this.licenseService.update(context, ourLicense);
    }
  }

  async createNewLicense(
    context: Context,
    accountId: number,
    tx: JWSTransactionDecodedPayload,
    rinfo: JWSRenewalInfoDecodedPayload
  ) {
    let license = new License({
      accountId,
      licenseId: this.licenseService.makeLicenseId(Manager.Apple, tx.originalTransactionId, "NOKEY", Templates.personal, 0),
      templateId: Templates.personal,
      status: LicenseStatus.Active,
      endDate: new Date(tx.expiresDate),
    });
    return this.licenseService.create(context, license);
  }

  async handleReceipt(
    context: Context,
    accountId: number,
    encodedReceipt: string
  ) {
    let r = await this.verifyReceipt(encodedReceipt);
    let orignalTxId =
      r &&
      r.latest_receipt_info &&
      r.latest_receipt_info[0].original_transaction_id;
    if (orignalTxId)
      await this.sync(context, accountId, orignalTxId);
  }
}
