#!/bin/bash

echo "started $0"

source ./_.postgres.sh
source ./_.helpers.sh

HOST=$1
PORT=$2
USER=$3
ACCOUNT_HOST=${4:-localhost}
TELEMETRY_HOST=${5:-localhost}
ACCOUNT_USER=${6:-$USER}
TELEMETRY_USER=${7:-$USER}

if [[ $HOST == "localhost" ]]
then
    export PGPASSWORD="password"
fi

open_pg_connection $HOST $PORT $USER

A0_PASSWORD="password"
T0_PASSWORD="password"
D0_PASSWORD="password"

if [[ ! $HOST == "localhost" ]]
then
    A0_PASSWORD=$(prompt_password "Enter the password for account user '$ACCOUNT_USER' (default is 'password'):" "password")
    echo ""
    T0_PASSWORD=$(prompt_password "Enter the password for telemetry user '$TELEMETRY_USER' (default is 'password')" "password")
    echo ""
    D0_PASSWORD=$(prompt_password "Enter the password for datawarehouse user 'datawarehouse' (default is 'password'):" "password")
    echo ""
fi

run_script '../database/vnext/create_datawarehouse_db.sql'

psql -U postgres \
     -d datawarehouse \
     -f ../database/vnext/setup/datawarehouse.ddl.sql \
     -v account_host="'$ACCOUNT_HOST'" \
     -v telemetry_host="'$TELEMETRY_HOST'" \
     -v a0_password="'$A0_PASSWORD'" \
     -v t0_password="'$T0_PASSWORD'" \
     -v d0_password="'$D0_PASSWORD'" \
     -v account_user="'$ACCOUNT_USER'" \
     -v telemetry_user="'$TELEMETRY_USER'" \
     -v account_user_mapping="$ACCOUNT_USER" \
     -v telemetry_user_mapping="$TELEMETRY_USER"

psql -U postgres -d datawarehouse -f ../database/vnext/setup/datawarehouse.dml.sql

if [[ $HOST == "localhost" ]]
then
    run_script '../database/vnext/create_datawarehouse_test_db.sql'

    psql -U postgres \
         -d datawarehouse_test \
         -f ../database/vnext/setup/datawarehouse.ddl.sql \
         -v account_host="'$ACCOUNT_HOST'" \
         -v telemetry_host="'$TELEMETRY_HOST'" \
         -v a0_password="'$A0_PASSWORD'" \
         -v t0_password="'$T0_PASSWORD'" \
         -v d0_password="'$D0_PASSWORD'" \
         -v account_user="'$ACCOUNT_USER'" \
         -v telemetry_user="'$TELEMETRY_USER'" \
         -v account_user_mapping="$ACCOUNT_USER" \
         -v telemetry_user_mapping="$TELEMETRY_USER"

    psql -U postgres -d datawarehouse_test -f ../database/vnext/setup/datawarehouse.dml.sql
fi

close_pg_connection

echo "completed $0"
