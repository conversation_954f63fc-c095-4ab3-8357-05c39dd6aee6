
import { query } from "../system/Postgres.mjs";
import { Invitation, IInvitation} from "../models/Invitation.model.mjs";
import { Context } from "../system/Context.mjs";
import { makeError } from "../system/error.mjs";


export class InvitationRestDataService {

    



  public query(context: Context, sql: string, params: any[]) {
    return query < IInvitation> (context, sql, params)
      .then(results => {
        if (results.length === 1) {
          if (!results[0].accountId && !results[0].email && !results[0].pin) {
      return [];
    }
  }
  return results.filter(o => !!o).map(o => new Invitation(o));
});
    }

		public create (context: Context, entity: Invitation) {
          


  let params = [
    entity.flags(),
				entity.modifiedTime(),
				entity.createdTime(),
				entity.accountId(),
				entity.email(),
				entity.pin(),
				entity.expiresAt(),
				entity.deviceConfig()
  ];
  return this
    .query(context, "select * from a0.invitation_create ($1,$2,$3,$4,$5,$6,$7,$8) ", params)
  .then(r => r[0]);
        }

		public readByAccountIdAndEmailAndPin (context: Context, accountId: number, email: string, pin: string) {
  let params = [
    accountId,
				email,
				pin
  ];
  return this
    .query(context, "select * from a0.invitation_read_by_account_id_and_email_and_pin  ($1,$2,$3) ", params).then(r => r[0]); 
                
        }

		public readByPin (context: Context, pin: string) {
  let params = [
    pin
  ];
  return this
    .query(context, "select * from a0.invitation_read_by_pin  ($1) ", params).then(r => r[0]); 
                
        }

		public readByAccountId (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.invitation_read_by_account_id  ($1) ", params).then(r => r[0]); 
                
        }

		public update (context: Context, entity: Invitation) {
          


  let params = [
    entity.flags(),
				entity.modifiedTime(),
				entity.createdTime(),
				entity.accountId(),
				entity.email(),
				entity.pin(),
				entity.expiresAt(),
				entity.deviceConfig()
  ];
  return this
    .query(context, "select * from a0.invitation_update ($1,$2,$3,$4,$5,$6,$7,$8) ", params)
  .then(r => r[0]);
        }

		public deleteByAccountIdAndEmailAndPin (context: Context, accountId: number, email: string, pin: string) {
  let params = [
    accountId,
				email,
				pin
  ];
  return this
    .query(context, "select * from a0.invitation_delete_by_account_id_and_email_and_pin  ($1,$2,$3) ", params).then(r => r[0]); 
                
        }

		public deleteByPin (context: Context, pin: string) {
  let params = [
    pin
  ];
  return this
    .query(context, "select * from a0.invitation_delete_by_pin  ($1) ", params).then(r => r[0]); 
                
        }

		public deleteByAccountId (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.invitation_delete_by_account_id  ($1) ", params).then(r => r[0]); 
                
        }

}
