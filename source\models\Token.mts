import jsonwebtoken = require("jsonwebtoken");

export enum Role {
  user = 0,
  admin = 1,
}

export enum TokenType {
  access = 0,
  refresh = 1,
  url = 2,
  rendezvous = 4,
}

export interface IToken {
  sub?: string;
  role?: Role;
  iss?: string;
  aud?: string;
  exp?: number;
  iat?: number;
  aid?: number;
  did?: number;
  idp?: string;
  shard?: number;
  adminAid?: number;
  tokenType?: TokenType;
  passwordHash?: string;
  planId?: string;
}

interface IVerifyResult {
  success?: boolean;
  token?: Token;
  error?: string;
}

export class Token {
  private _state: IToken;

  public static decode(tokenString: string) {
    let state = jsonwebtoken.decode(tokenString) as any;
    return new Token(state);
  }

  public static tryDecode(tokenString: string) {
    try {
      return Token.decode(tokenString);
    } catch (e) {
      return undefined;
    }
  }

  public static verify(tokenString: string, secret: string): Token {
    return new Token(jsonwebtoken.verify(tokenString, secret) as any);
  }

  public static tryVerify(tokenString: string, secret: string) {
    try {
      return { success: true, token: Token.verify(tokenString, secret) };
    } catch (e: any) {
      return { name: e.name, success: false, error: e.message };
    }
  }

  constructor(state: IToken) {
    this.state(state);
  }

  public state(value?: IToken) {
    if (value !== void 0) {
      this._state = value;
    }
    return this._state;
  }

  public sub(value?: string) {
    if (value !== void 0) {
      this._state.sub = value;
    }
    return this._state.sub;
  }

  public idp(value?: string) {
    if (value !== void 0) {
      this._state.idp = value;
    }
    return this._state.idp;
  }

  public aid(value?: number) {
    if (value !== void 0) {
      this._state.aid = value;
    }
    return this._state.aid;
  }

  public adminAid(value?: number) {
    if (value !== void 0) {
      this._state.adminAid = value;
    }
    return this._state.adminAid;
  }

  public did(value?: number) {
    if (value !== void 0) {
      this._state.did = value;
    }
    return this._state.did;
  }

  public aud(value?: string) {
    if (value !== void 0) {
      this._state.aud = value;
    }
    return this._state.aud;
  }

  public iss(value?: string) {
    if (value !== void 0) {
      this._state.iss = value;
    }
    return this._state.iss;
  }

  public exp(value?: number) {
    if (value !== void 0) {
      this._state.exp = value;
    }
    return this._state.exp;
  }

  public iat(value?: number) {
    if (value !== void 0) {
      this._state.iat = value;
    }
    return this._state.iat;
  }

  public shard(value?: number) {
    if (value !== void 0) {
      this._state.shard = value;
    }
    return this._state.shard;
  }

  public role(value?: Role) {
    if (value !== void 0) {
      this._state.role = value;
    }
    return this._state.role;
  }

  public tokenType(value?: TokenType) {
    if (value !== void 0) {
      this._state.tokenType = value;
    }
    return this._state.tokenType;
  }

  public passwordHash(value?: string) {
    if (value !== void 0) {
      this._state.passwordHash = value;
    }
    return this._state.passwordHash;
  }

  public planId(value?: string) {
    if (value !== void 0) {
      this._state.planId = value;
    }
    return this._state.planId;
  }

  public canAccessAccount(aid: number) {
    return this.aid() === aid || this.hasAdminRights();
  }

  public hasRole(role: string | Role) {
    if (!this.role()) {
      return false;
    }
    let roleID: Role;
    if (typeof role === "string") {
      //@ts-ignore
      roleID = Role[role];
    } else {
      roleID = role;
    }
    return !!(this.role() === roleID);
  }

  public hasAdminRights() {
    return this.hasRole(Role.admin) || !!this.adminAid();
  }

  public sign(secret: string, expiresIn?: string) {
    let options = {
      audience: this.aud(),
      expiresIn: expiresIn,
      issuer: "*.mylio.com",
      subject: this.sub(),
    };
    this.iss(options.issuer);
    let payload = {
      idp: this.idp(),
      aid: this.aid(),
      did: this.did(),
      role: this.role(),
      adminAid: this.adminAid(),
      tokenType: this.tokenType(),
      passwordHash: this.passwordHash(),
    };
    return jsonwebtoken.sign(payload, secret, options);
  }
}
