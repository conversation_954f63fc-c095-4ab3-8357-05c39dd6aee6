import { config } from "../system/Config.mjs";
import { error, makeError } from "../system/error.mjs";
import { RefreshTokenService } from "./RefreshTokenService.mjs";
import { Context } from "../system/Context.mjs";
import { RefreshToken } from "../models/RefreshToken.model.mjs";
import { ids } from "../system/Strings.mjs";
import { parameterizeUrl as addQs, get, postForm } from "../system/fetch.mjs";
import { URL } from "url";

export class InstagramOAuthService {
  public async getAccessToken(code: string) {
    let form = {
      client_id: config.instagram.clientID,
      client_secret: config.instagram.clientSecret,
      code: code,
      grant_type: "authorization_code",
      redirect_uri: `${config.cloud}/instagram/authorize`,
    };
    let accessTokenResponse = await postForm(
      "https://api.instagram.com/oauth/access_token",
      form
    );
    let body = await accessTokenResponse.json();
    return body.access_token;
  }

  public async getLongLivedAccessToken(token: string) {
    let query_params = {
      client_secret: config.instagram.clientSecret,
      grant_type: "ig_exchange_token",
      access_token: token,
    };
    let url = addQs("https://graph.instagram.com/access_token", query_params);
    let body = await get(url);
    return body.access_token;
  }

  public async refreshLongLivedAccessToken(token: string) {
    let query_params = {
      grant_type: "ig_refresh_token",
      access_token: token,
    };
    let url = addQs(
      "https://graph.instagram.com/refresh_access_token",
      query_params
    );
    let body = await get(url);
    return body.access_token;
  }

  public getImportImagesOAuthUrl(
    context: Context,
    aid: number,
    encrypt: boolean,
    redirect: string
  ) {
    return this.getOAuthUrl(
      context,
      aid,
      "import-images",
      encrypt,
      redirect,
      `user_media`
    );
  }

  private getOAuthUrl(
    context: Context,
    aid,
    task: string,
    encrypt: boolean,
    redirect: string,
    scope: string
  ) {
    let state = Buffer.from(
      JSON.stringify({ aid, encrypt, task, redirect }),
      "utf8"
    ).toString("base64");
    let redirect_uri = `${config.cloud}/instagram/authorize`;
    // tslint:disable
    return `https://api.instagram.com/oauth/authorize?scope=${scope}&state=${state}&response_type=code&redirect_uri=${redirect_uri}&client_id=${config.instagram.clientID}`;
    // tslint:enable
  }
}

export class InstagramService {
  private _oauth = new InstagramOAuthService();

  constructor(private refreshTokenService: RefreshTokenService) {}

  public getImportImagesAccessToken(context: Context, aid: number) {
    const idp = "instagram";
    const task = "import-images";

    return this.refreshTokenService
      .read(context, aid, idp, task)
      .then((refreshToken) => {
        if (!refreshToken) return error<string>(400, ids.NO_REFRESH_TOKEN);
        return refreshToken.token();
      });
  }

  public getImportImagesOAuthUrl(
    context: Context,
    aid: number,
    encrypt: boolean,
    redirect: string
  ) {
    return this._oauth.getImportImagesOAuthUrl(context, aid, encrypt, redirect);
  }

  public addRefreshToken(authorizationCode: string, state: any) {
    let refreshTokenString: string;
    let context = new Context();
    let task = state.task;
    context.aid = state.aid;
    let aid = context.aid;

    return this._oauth
      .getAccessToken(authorizationCode)
      .then((result) => {
        return this._oauth.getLongLivedAccessToken(result);
      })
      .then((result) => {
        refreshTokenString = result;
        return this.refreshTokenService.tryRead(
          context,
          aid,
          "instagram",
          task
        );
      })
      .then((refreshToken) => {
        if (!refreshToken)
          return this.refreshTokenService.create(
            context,
            aid,
            new RefreshToken({
              idp: "instagram",
              accountId: aid,
              task,
              token: refreshTokenString,
            })
          );

        refreshToken.token(refreshTokenString);
        return this.refreshTokenService.update(context, aid, refreshToken);
      });
  }

  public revokeToken(context: Context, aid: number, task: string) {
    return this.refreshTokenService.delete(context, aid, "instagram", task);
  }
}
