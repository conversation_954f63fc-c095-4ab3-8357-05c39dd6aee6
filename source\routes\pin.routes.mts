"use strict";

import * as express from "express";
import { microservice as g } from "../microservices/account.microservice.mjs";
import {
  safeNone,
  safeAny,
  secure,
  secureURLToken,
  secureURLTokenImpl,
} from "../system/safe.mjs";
import { sanitizeOutput, Account } from "../models/Account.model.mjs";
import { Context } from "../system/Context.mjs";

/* tslint:disable */
import { PinRestDataService } from "../dataServices/Pin.rest.dataService.mjs";
import { recaptcha } from "../system/recaptcha.mjs";
/* tslint:enable */

export function addPinRoutes(router: express.Router) {
  router.get("/debug-delete/:email", safeAny, async (req, res, next) => {
    let context = req.context;
    let email = req.params.email.toLocaleLowerCase();
    let account = await g.accountService.tryByEmail(context, req.params.email);
    if (account) g.accountService.delete(context, account.accountId());
    let linkService = new PinRestDataService();
    await linkService.deleteByEmail(context, email);
    res.status(200).send();
  });

  router.get("/debug-zero-pin/:email", safeAny, async (req, res, next) => {
    let context = req.context;
    let email = req.params.email.toLocaleLowerCase();
    let account = await g.accountService.tryByEmail(context, req.params.email);
    let linkService = new PinRestDataService();
    let links = await linkService.findByEmail(context, email);
    for (let link of links) {
      link.pin("000000");
      linkService.update(context, link);
    }
    res.status(200).send();
  });

  router.get("/debug-enable-tfa/:email", safeAny, async (req, res, next) => {
    let context = req.context;
    let email = req.params.email.toLocaleLowerCase();
    let account = await g.accountService.tryByEmail(context, req.params.email);
    account.tfa(true);
    account.password("password");
    account = await g.accountService.update(context, account);
    res.status(200).send();
  });

  router.get("/set-password/:email", safeAny, async (req, res, next) => {
    let context = req.context;
    let email = req.params.email.toLocaleLowerCase();
    let account = await g.accountService.tryByEmail(context, req.params.email);
    account.password("password");
    account = await g.accountService.update(context, account);
    res.status(200).send();
  });

  router.get("/debug-disable-tfa/:email", safeAny, async (req, res, next) => {
    let context = req.context;
    let email = req.params.email.toLowerCase();
    let account = await g.accountService.tryByEmail(context, req.params.email);
    account.tfa(false);
    account = await g.accountService.update(context, account);
    res.status(200).send();
  });

  router.post("/send-pin", safeAny, recaptcha, async (req, res, next) => {
    try {
      const context: Context = req.context;
      const email = context.any.email.toLowerCase();
      const target = context.any.target || "app";
      const query = context.any.query || "";
      const website = context.any.website || "";
      const codeChallenge = context.any.codeChallenge;
      const createAccount = !!context.any.createAccount;
      const password = context.any.password;
      let protocol = context.any.protocol;
      if (!protocol) protocol = target === "website" ? "https" : "mylio";
      await g.authenticationService.sendPin(
        context,
        email,
        target,
        query,
        protocol,
        codeChallenge,
        createAccount,
        password,
        website
      );
      return res.status(200).send();
    } catch (err) {
      next(err);
    }
  });

  router.get("/redirect-pin", safeNone, async (req, res, next) => {
    let context = req.context;
    let state = JSON.parse(atob(req.query.s as string));
    res.contentType(".html");
    res.status(200).send(`<html>
        <head>
        </head>
        <body>
          <a href="${state.protocol}://launch-with-pin/${req.query.s}">Launch Mylio</a>
        </body>
      </html>`);
  });
}
