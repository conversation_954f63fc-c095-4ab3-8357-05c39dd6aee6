#!/bin/bash

echo "started $0"
source ./_.postgres.sh

HOST=$1
PORT=$2
USER=$3
ENV="${4:-test}"
DATABASE_DIR="../database/vnext"
SEED_FILE="seed.$ENV.sql"
OUT_DIR="$DATABASE_DIR/setup"
export PGCLIENTENCODING=utf8

if [[ ! -f $DATABASE_DIR/$SEED_FILE ]]
then
    echo "no seed file $SEED_FILE found"
    exit 1
fi

if [[ $HOST == "localhost" ]]
then
    export PGPASSWORD="password"
fi

cp $DATABASE_DIR/$SEED_FILE $OUT_DIR
open_pg_connection $HOST $PORT $USER
run_script '../database/vnext/setup/account0.ddl.sql'
run_script '../database/vnext/setup/account0.dml.sql'
run_script '../database/vnext/setup/account0.secure.sql'
run_script "$OUT_DIR/$SEED_FILE"

close_pg_connection

echo "completed $0"
