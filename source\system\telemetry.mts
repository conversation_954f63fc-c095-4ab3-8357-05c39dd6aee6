import { config } from "./Config.mjs";
import { RestClient } from "../system/RestClient.mjs";
import { Context } from "./Context.mjs";
import { makeError } from "./error.mjs";
import { ids, EventType } from "./Strings.mjs";

let telemetryURL = config.services.find((s) => s.name === "telemetry").uri[0];

export async function telemetry(
  context: Context,
  aid: number,
  eventType: EventType,
  data?: any
) {
  aid = aid || context.aid || data.aid;
  let rest = new RestClient(telemetryURL);
  rest.errorParser = (x) => {
    let error = (x.errors && x.errors.length && x.errors[0]) || x;
    return error ? error : x;
  };
  rest
    .post(`accounts/${aid}/cloud-events`, { aid: context.aid, eventType, data })
    .catch((error) => {
      console.error(JSON.stringify(error));
      return Promise.resolve();
    });
  context.telemetry(EventType[eventType], data);
}
