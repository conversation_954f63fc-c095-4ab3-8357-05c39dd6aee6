{"name": "cloudv3", "version": "1.0.0", "description": "", "main": "index.mjs", "scripts": {"init": " cd ./scripts && bash ./init.sh && cd ..", "build": "cd ./scripts && bash ./build.sh && cd ..", "build-slow": "cd ./scripts && bash ./build.slow.sh && cd ..", "build-microservices": "cd ./scripts && bash ./build.microservices.sh && cd ..", "build-database": "cd ./scripts && bash ./build.database.sh && cd ..", "build-fast": "cd ./scripts && bash ./build.fast.sh && cd ..", "generate": "cd ./scripts && bash ./generate.sh && cd ..", "start": "node --max-old-space-size=8192 --nolazy --harmony ./microservices/<service>.microservice.express.mjs", "debug": "node --inspect-brk --nolazy --harmony ./source/<service>.microservice.express.mjs", "start-debug": "node --inspect-brk --nolazy --harmony ./source/<service>.microservice.express.mjs", "start-account": "node --nolazy --harmony ./debug/source/account.microservice.express", "start-account-localhost": "cd ./scripts && bash ./start.account.localhost.sh && cd ..", "start-account-debug": "node --nolazy --harmony --inspect ./debug/source/account.microservice.express", "start-account-nodemon": "cd ./scripts && bash ./start.account.nodemon.sh", "start-resource-nodemon": "cd ./scripts && bash ./start.resource.nodemon.sh", "start-resource": "node --nolazy --harmony ./debug/source/resource.microservice.express", "start-resource-localhost": "cd ./scripts && bash ./start.resource.localhost.sh && cd ..", "start-resource-debug": "cd ./scripts && bash ./start.resource.localhost.sh debug && cd ..", "start-telemetry": "node --nolazy --harmony ./debug/source/telemetry.microservice.express", "start-telemetry-localhost": "cd ./scripts && bash ./start.telemetry.localhost.sh && cd ..", "start-telemetry-debug": "node --nolazy --harmony --inspect ./debug/source/telemetry.microservice.express", "start-telemetry-nodemon": "cd ./scripts && bash ./start.telemetry.nodemon.sh", "copy-cloudshared": "cd ./scripts && bash ./copy.cloudshared.sh && cd ..", "deploy-account-database-localhost": "cd ./scripts && bash ./deploy.account.database.localhost.sh && cd ..", "deploy-account-database-dml-localhost": "cd ./scripts && bash ./deploy.account.database.dml.localhost.sh && cd ..", "deploy-resource-database-localhost": "cd ./scripts && bash ./deploy.resource.database.localhost.sh && cd ..", "deploy-resource-database-dml-localhost": "cd ./scripts && bash ./deploy.resource.database.dml.localhost.sh && cd ..", "deploy-account-localhost": "cd ./scripts && bash ./deploy.account.localhost.sh && cd ..", "deploy-resource-localhost": "cd ./scripts && bash ./deploy.resource.localhost.sh && cd ..", "deploy-telemetry-database-localhost": "cd ./scripts && bash ./deploy.telemetry.database.localhost.sh && cd ..", "deploy-telemetry-database-dml-localhost": "cd ./scripts && bash ./deploy.telemetry.database.dml.localhost.sh && cd ..", "deploy-telemetry-database-rds": "cd ./scripts && bash ./deploy.telemetry.database.rds.sh && cd ..", "deploy-telemetry-database-dml-rds": "cd ./scripts && bash ./deploy.telemetry.database.dml.rds.sh && cd ..", "deploy-telemetry-database-rds-test": "cd ./scripts && bash ./deploy.telemetry.database.rds.test.sh && cd ..", "deploy-telemetry-database-dml-rds-test": "cd ./scripts && bash ./deploy.telemetry.database.dml.rds.test.sh && cd ..", "deploy-datawarehouse-database-localhost": "cd ./scripts && bash ./deploy.datawarehouse.database.localhost.sh && cd ..", "deploy-datawarehouse-database-rds-test": "cd ./scripts && bash ./deploy.datawarehouse.database.rds.test.sh && cd ..", "deploy-datawarehouse-database-dml-rds-test": "cd ./scripts && bash ./deploy.datawarehouse.database.dml.rds.test.sh && cd ..", "deploy-datawarehouse-database-rds": "cd ./scripts && bash ./deploy.datawarehouse.database.rds.sh && cd ..", "deploy-datawarehouse-database-dml-rds": "cd ./scripts && bash ./deploy.datawarehouse.database.dml.rds.sh && cd ..", "publish": "cd ./scripts && bash ./_.publish2.sh && cd ..", "publish:ecs": "cd ./scripts && bash ./_.publish.ecs.sh", "deploy-account-database-rds-production": "cd ./scripts && bash ./deploy.account.database.rds.production.sh && cd ..", "deploy-account-database-dml-rds-production": "cd ./scripts && bash ./deploy.account.database.dml.rds.production.sh && cd ..", "deploy-account-database-rds-test": "cd ./scripts && bash ./deploy.account.database.rds.test.sh && cd ..", "deploy-account-database-dml-rds-test": "cd ./scripts && bash ./deploy.account.database.dml.rds.test.sh && cd ..", "deploy-resource-database-rds": "cd ./scripts && bash ./deploy.resource.database.rds.sh && cd ..", "deploy-resource-database-dml-rds": "cd ./scripts && bash ./deploy.resource.database.dml.rds.sh && cd ..", "deploy-resource-database-rds-test": "cd ./scripts && bash ./deploy.resource.database.rds.test.sh && cd ..", "deploy-resource-database-dml-rds-test": "cd ./scripts && bash ./deploy.resource.database.dml.rds.test.sh && cd ..", "deploy-binaries-localhost": "cd ./scripts && bash ./deploy.binaries.localhost.sh && cd ..", "deploy-binaries-test": "cd ./scripts && bash ./deploy.binaries.test.sh && cd ..", "deploy-binaries-production": "cd ./scripts && bash ./deploy.binaries.production.sh && cd ..", "patch-account-database-localhost": "cd ./scripts && bash ./patch.account.database.localhost.sh && cd ..", "patch-account-database-rds-test": "cd ./scripts && bash ./patch.account.database.rds.test.sh && cd ..", "patch-account-database-rds": "cd ./scripts && bash ./patch.account.database.rds.sh && cd ..", "patch-telemetry-database-localhost": "cd ./scripts && bash ./patch.telemetry.database.localhost.sh && cd ..", "patch-telemetry-database-rds-test": "cd ./scripts && bash ./patch.telemetry.database.rds.test.sh && cd ..", "patch-telemetry-database-rds": "cd ./scripts && bash ./patch.telemetry.database.rds.sh && cd ..", "patch-resource-database-localhost": "cd ./scripts && bash ./patch.resource.database.localhost.sh && cd ..", "patch-resource-database-rds-test": "cd ./scripts && bash ./patch.resource.database.rds.test.sh && cd ..", "patch-resource-database-rds": "cd ./scripts && bash ./patch.resource.database.rds.sh && cd ..", "test": "cd ./scripts && bash ./test.sh && cd ..", "run-script-localhost": "cd ./scripts && bash run.script.sh local && cd ..", "run-script-test": "cd ./scripts && bash run.script.sh test && cd ..", "run-script-production": "cd ./scripts && bash run.script.sh prod && cd ..", "build-and-debug": "cd ./scripts && bash ./build.fast.sh && cd .. && bash ./devs/willem/debug.account.sh"}, "repository": {"type": "git", "url": "git+https-//github.com/mylollc/cloudv3.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/mylollc/cloudv3/issues"}, "homepage": "https-//github.com/mylollc/cloudv3#readme", "dependencies": {"@api/fastspring": "file:.api/apis/fastspring", "@aws-sdk/client-s3": "^3.433.0", "@aws-sdk/client-secrets-manager": "^3.245.0", "@aws-sdk/s3-request-presigner": "^3.433.0", "@types/selenium-webdriver": "^4.1.24", "aws-sdk": "^2.104.0", "change-case": "^5.4.4", "config": "^3.3.6", "cors": "^2.8.5", "express": "^5.0.1", "express-rate-limit": "^7.5.0", "jose": "^4.14.4", "jsonwebtoken": "^9.0.0", "jwks-rsa": "^3.0.1", "lodash": "^4.17.21", "moment": "^2.30.1", "node-addon-api": "^8.2.1", "node-gyp": "^10.1.0", "passport": "^0.6.0", "passport-facebook": "^3.0.0", "passport-facebook-token": "^4.0.0", "pg": "^8.6.0", "pg-pool": "^3.6.0"}, "devDependencies": {"@types/config": "0.0.38", "@types/cors": "2.8.5", "@types/express": "^4.17.13", "@types/jsonwebtoken": "^9.0.1", "@types/lodash": "^4.14.169", "@types/node": "^20.0", "@types/pg": "^8.6.0", "@types/pg-pool": "^2.0.3", "@types/rimraf": "0.0.28", "@types/yargs": "^6.3.2", "mobx": "^6.9.0", "react": "^18.2.0", "rimraf": "^5.0.7", "selenium-webdriver": "^4.25.0", "typescript": "^5.0.4", "yargs": "^17.1.1"}}