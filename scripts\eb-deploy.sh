#!/bin/bash

# Get a list of all subdirectories
subdirs=(*/)
num_subdirs=${#subdirs[@]}


# Define function
function get_segment() {
    local token="$1"
    local n="$2"
    local string="$3"
    IFS="$token" read -ra segments <<< "$string"
    echo "${segments[$n-1]}"
}

# Example usage



# Display a menu of all subdirectories
echo "Please choose a subdirectory:"
for (( i=0; i<num_subdirs; i++ )); do
    subdir=${subdirs[$i]}
    echo "$i) ${subdir%/}"
done

# Read user input and cd into the selected subdirectory
read -p "Enter the number of the subdirectory: " selection
if [[ "$selection" =~ ^[0-9]+$ ]] && (( selection < num_subdirs )); then
    DIR="${subdirs[$selection]%/}"
    VER=$DIR$(date +%s)
    APP=$(get_segment "-" "1" "$DIR")
    echo $APP
    rm -f $VER.zip
    7z a -tzip -r $VER.zip ./$DIR/*
    aws s3 cp $VER.zip s3://elasticbeanstalk-us-west-2-************ --profile MFA  
    aws elasticbeanstalk create-application-version --region us-west-2 --application-name $APP --version-label $VER --source-bundle S3Bucket=elasticbeanstalk-us-west-2-************,S3Key=$VER.zip --profile MFA
    aws elasticbeanstalk update-environment --region us-west-2 --environment-name $DIR --version-label $VER --profile MFA
    aws elasticbeanstalk describe-events --region us-west-2 --environment-name $DIR --profile MFA    
else
    echo "Invalid selection. Exiting."
fi



#rm $VER.zip
#7z a -tzip -r $VER.zip ./$APP-$ENV-$INS/*
#aws s3 cp $VER.zip s3://elasticbeanstalk-us-west-2-************ --profile MFA  
#aws elasticbeanstalk create-application-version --region us-west-2 --application-name $APP --version-label $VER --source-bundle S3Bucket=elasticbeanstalk-us-west-2-************,S3Key=$VER.zip --profile MFA
#aws elasticbeanstalk update-environment --region us-west-2 --environment-name account-test-0 --version-label $VER --profile MFA
#aws elasticbeanstalk describe-events --region us-west-2 --environment-name account-test-0 --profile MFA