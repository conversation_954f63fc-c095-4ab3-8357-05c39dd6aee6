import express = require("express");
import { config, getServices } from "../system/Config.mjs";
import { safeNone } from "../system/safe.mjs";
import { healthService, HealthGroups } from "../services/HealthService.mjs";
import { microservice as g } from "../microservices/account.microservice.mjs";
import { makeError } from "../system/error.mjs";

const VERSION = "2023-07-19T21:13:35.362Z";

export function addHealthRoutes(router: express.Router) {
  router.get("/health", safeNone, (req, res, next) => {
    let context = req.context;

    return healthService
      .status(context)
      .then((results) => {
        context.dumpLog();
        (results as any).works = true
        return res.status(200).json(results);
      })
      .catch(next);
  });

  router.get("/status", safeNone, (req, res, next) => {
    return res.status(200).send(VERSION);
  });

  router.get("/maintenance-mode", safeNone, (req, res, next) => {
    return res.status(200).json({ status: g.maintenanceMode });
  });

  router.get("/ver", safeNone, (req, res, next) => {
    return res.json({ ver: 1 }).sendStatus(200);
  });
}
