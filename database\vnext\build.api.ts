"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
import path = require("path");
const fs = require("fs");
function fileExists(filename: string) {
  try {
    fs.accessSync(filename);
    return true;
  } catch (e) {
    return false;
  }
}
exports.fileExists = fileExists;
exports.eol = "\n";
// used for 0-padding of table names e.g. x00 or x0F
function lpad(str: string, padString: string, length: number) {
  while (str.length < length) str = padString + str;
  return str;
}
exports.lpad = lpad;
// injects the scripts referenced by psql \i directives into the root script
// the replace will occur repeatedly until all nested \i directives
// have been resolved
function inject(root: string, directories: string[]) {
  let result = root;
  let found = false;
  do {
    found = false;
    result = result.replace(
      /\\i ((?:\w|\.)+\.sql)/gm,
      (a, filename: string) => {
        for (let dir of directories) {
          if (fileExists(path.join(dir, filename))) {
            found = true;
            console.log(`injecting file: ${filename}`);
            return fs.readFileSync(path.join(dir, filename), "utf8");
          } else
            throw `file ${filename} could not be found in directories ${directories.join()}`;
        }
      }
    );
  } while (found);
  return result;
}
exports.inject = inject;
// replaces comment encoded variables e.g. /* database */ resource0 with the
// config value from the shard e.g. { database: "resource1"}
function replace(root: string, shard: string) {
  return root.replace(
    /\/\*\s*r::(\w+)\s*\*\/\s*(')?\w+(')?/gm,
    (full, key, lq = "", rq = "") => `${lq}${shard[key]}${rq}`
  );
}
exports.replace = replace;
//# sourceMappingURL=build.api.js.map
