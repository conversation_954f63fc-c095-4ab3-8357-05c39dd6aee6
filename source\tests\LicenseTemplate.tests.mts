// import {test, describe, mock, before, after, it} from "node:test";
// import assert from "node:assert";
// import { Account } from "../models/Account.model.mjs";
// import { AccountService } from "../services/AccountService.mjs";
// import { AccountRestDataService } from "../dataServices/Account.rest.dataService.mjs";
// import { DeviceService } from "../services/DeviceService.mjs";
// import { DeviceRestDataService } from "../dataServices/Device.rest.dataService.mjs";
// import { DeviceDataRestDataService } from "../dataServices/DeviceData.rest.dataService.mjs";
// import { tx } from "../system/Postgres.mjs";
// import { EmailService } from "../services/EmailService.mjs";
// import { TokenService } from "../services/TokenService.mjs";
// import { Context } from "../system/Context.mjs";
// import { Providers } from "../models/SubscriptionProvider.mjs";
// import { ISubscription, Subscription } from "../models/Subscription.model.mjs";
// import { SubscriptionService } from "../services/SubscriptionService.mjs";
// import { SubscriptionRestDataService } from "../dataServices/Subscription.rest.dataService.mjs";
// import {  FreeSmrService } from "../services/FreeSmrService.mjs";
// import { FeaturesetRestDataService } from "../dataServices/Featureset.rest.dataService.mjs";
// import moment = require("moment");
// import {
//     StripeService,
// } from "../services/StripeService.mjs";
// import Stripe from "stripe";
// import { CouponRestDataService } from "../dataServices/Coupon.rest.dataService.mjs";
// import { PromoCodeRestDataService } from "../dataServices/PromoCode.rest.dataService.mjs";
// import { config } from "../system/Config.mjs";
// import { StripeData } from "../models/StripeData.model.mjs";
// import { FreeSmrRestDataService } from "../dataServices/FreeSmr.rest.dataService.mjs";
// import { FreeSmr, IFreeSmr } from "../models/FreeSmr.model.mjs";
// import { StripeCard } from "../models/StripeCard.model.mjs";
// import { CouponService } from "../services/CouponService.mjs";
// import { Coupon } from "../models/Coupon.model.mjs";
// const stripe = new Stripe(config.stripe_private_key, {
//     apiVersion: "2022-11-15"
// });

// describe("The subscription Lifecycle", async (t) => {
//     const idp = "mylio";
//     const sub = "<EMAIL>";
//     let accountService: AccountService;
//     let deviceService: DeviceService;
//     let subscriptionService: SubscriptionService
//     let context = new Context();
//     context.hasAdminRights = () => true;
//     let account: Account;
//     let _1000F: FreeSmr;
//     let _1015F: FreeSmr;
//     let freeSmrs : FreeSmrService;
//     let stripeService : StripeService;
//     let customer: Stripe.Customer;
//     let _1015S: StripeData
//     let couponService : CouponService;
//     let couponId = "1015P30D"

//     before( async (t) => {
//         deviceService = new DeviceService(
//             new DeviceRestDataService()
//             , new DeviceDataRestDataService()
//             , tx);
//         accountService = new AccountService(
//             new AccountRestDataService()
//             , deviceService
//             , new EmailService(new TokenService())
//         , tx);
//         subscriptionService = new SubscriptionService(
//             new SubscriptionRestDataService(),
//             accountService,
//             new FeaturesetRestDataService(),
//             tx
//         );
//         freeSmrs = new FreeSmrService(
//                         subscriptionService
//                         , accountService
//                         , new CouponService(new CouponRestDataService())
//                         ,  new PromoCodeRestDataService()
//                         ,  new FreeSmrRestDataService()
//                         );

//         couponService = new CouponService(new CouponRestDataService());
//         account = await accountService.tryBySubAndIdp(context, sub, idp);
//         if (account)
//             await accountService.delete(context, account.accountId());
//     });

//     it("Create the account", async (t) => {
//         account = new Account({
//             sub: "<EMAIL>",
//             email: "<EMAIL>",
//             idp: "mylio",
//             role: "user",
//             password: "P@ssw0rd"
//         });
//         account = await freeSmrs.createAccountAndSubscribe(context, account);
//         assert(!!account, "Account not created");
//         context.aid = account.accountId();
//         _1000F = (await freeSmrs.list(context, account.accountId()))[0];
//         assert(!!_1000F, "FSMR not created on account creation");
//         assert(account.planId() === "1000", "Account plan id not set");
//     });

//     it ("Create a single use 6 month trial 1015 coupon", async t => {
//         let oldCoupon = await couponService.read(context, couponId);
//         if (oldCoupon)
//             await couponService.delete(context, couponId);
//         let coupon = new Coupon({
//             duration: "P6M",
//             planId: "1015",
//             usesRemaining: 1,
//             couponId
//         });
//         coupon.trial(false);
//         await couponService.create(context, coupon);
//     });

//     it ("Crate a new 1015 Trial using the coupon", async t => {
//         context.hasAdminRights = () => false;
//         let fsmr = new FreeSmr({
//             planId: "1015",
//             couponId,
//             accountId: account.accountId()
//         });
//         _1015F = await freeSmrs.create(context, fsmr);
//         assert(!!_1015F, "1015 Trial not created");
//         account = await accountService.read(context, account.accountId());
//         assert.equal(account.planId(), "1015", "Account not upgraded to 1015" );
//         assert.equal(account.deviceLimit(), 64, "Account device limit not assigned");
//     });

//     it("Try and fail to use the coupon a second time", async t => {
//         let fsmr = new FreeSmr({
//             planId: "1015",
//             couponId,
//             accountId: account.accountId()
//         });
//         try {
//             _1015F = await freeSmrs.create(context, fsmr);
//             assert(!!_1015F, "expected second use to fail");
//         }
//         catch(err) {
//             console.log(err);
//         }

//     });

//     after( async t => {
//         console.log("done");
//         setTimeout(() => {
//             process.exit(0);
//         }, 200);
//     });

// });
