#!/bin/bash

env="$1"

source ./_.helpers.sh

case $env in
    "local")
        export HOSTNAME="localhost.account"
        export NODE_ENV="development"
        ;;
    "test")
        export HOSTNAME="ebs.account"
        export NODE_ENV="development"
        export NODE_APP_INSTANCE="test"
        ;;
    "prod")
        export HOSTNAME="ebs.account"
        export NODE_ENV="production"
        export NODE_APP_INSTANCE="production"
        ;;
    *)
        echo "invalid environment: $env"
        exit 1
esac

echo "available scripts:"
echo ""

for file in $(ls *.ts)
do
    echo "${file%.*}"
done

echo ""

SCRIPT=$(prompt "enter the name of the script you'd like to run:" $SCRIPT)
USERNAME=$(prompt "enter database user:" "superuser")
PASSWORD=$(prompt_password "enter database password (typing will be invisible):" "password")

export PGCREDS=$USERNAME:$PASSWORD

echo ""
echo ""
echo "compiling script $SCRIPT ..."
echo ""

TSC=../node_modules/.bin/tsc
$TSC --project ../tsconfig.json ./$SCRIPT.ts

echo ""
echo "running script $SCRIPT ..."
echo ""

cd ..
node ./scripts/$SCRIPT.js
