const Addcouponcodestoacoupon = {"body":{"title":"AddcouponcodestoacouponRequest","type":"object","properties":{"codes":{"type":"array","items":{"type":"string","examples":["code4"]}}},"required":["codes"],"$schema":"http://json-schema.org/draft-04/schema#"},"metadata":{"allOf":[{"type":"object","properties":{"coupon_id":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Coupon Id"}},"required":["coupon_id"]}]},"response":{"200":{"type":"object","properties":{}}}} as const
;
const CancelQuote = {"metadata":{"allOf":[{"type":"object","properties":{"id":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"A unique identifier for the quote"}},"required":["id"]}]},"response":{"200":{"type":"object","properties":{"id":{"type":"string","description":"The quote id","examples":["QUVJIVYZTQDFBOBEO7IGNXER3VBQ"]},"buyerGenerated":{"type":"boolean"},"coupon":{"type":"string","examples":["TENOFF"]},"created":{"type":"string","format":"date-time","examples":["2021-03-02T19:48:56.395Z"]},"createdBy":{"type":"object","properties":{"company":{"type":"string"},"email":{"type":"string"},"first":{"type":"string"},"last":{"type":"string"},"phone":{"type":"string"},"userId":{"type":"string"}}},"currency":{"type":"string","examples":["USD"]},"discount":{"type":"number"},"discountDisplay":{"type":"string"},"discountInPayoutCurrency":{"type":"number"},"discountInPayoutCurrencyDisplay":{"type":"string"},"expires":{"type":"string","format":"date-time","examples":["2021-04-01T19:48:56.395Z"]},"expirationDateDays":{"type":"integer","format":"int32","examples":[30],"minimum":-**********,"maximum":**********},"fulfillmentTerm":{"type":"string","enum":["ON_PAYMENT","ON_QUOTE_ACCEPTANCE"],"examples":["ON_QUOTE_ACCEPTANCE"],"description":"`ON_PAYMENT` `ON_QUOTE_ACCEPTANCE`"},"items":{"type":"array","items":{"type":"object","properties":{"product":{"type":"string"},"customPrice":{"type":"boolean"},"display":{"type":"string","examples":["Book The Ring"]},"image":{"type":"string","examples":["icon.png"]},"intervalCount":{"type":"string","examples":["2"]},"period":{"type":"string","examples":["Monthly"]},"periodDays":{"type":"string","examples":["0"]},"quantity":{"type":"integer","format":"int32","examples":[1],"minimum":-**********,"maximum":**********},"renewIntoProducts":{"uniqueItems":true,"type":"array","items":{"type":"object","properties":{"renewIntoProductId":{"type":"string","examples":["DG4UH337PVYDC33XEP3ZH3JHD"]},"renewIntoPath":{"type":"string","examples":["video-subscription"]},"renewIntoPrice":{"type":"number","examples":[9.99]},"renewIntoPeriod":{"type":"string","examples":["Weekly"]},"renewIntoLevel":{"type":"integer","format":"int32","examples":[1],"minimum":-**********,"maximum":**********},"renewIntoIntervalCount":{"type":"string","examples":["2"]},"renewIntoUpcomingProduct":{"type":"string","examples":["audio-subscription"]}}}},"taxes":{"type":"array","items":{"type":"object","properties":{"taxValue":{"type":"number","examples":[0]},"totalTaxable":{"type":"number","examples":[0]}}}},"trialDays":{"type":"integer","format":"int32","examples":[30],"minimum":-**********,"maximum":**********},"subscription":{"type":"boolean"},"type":{"type":"string"},"unitCouponDiscount":{"type":"number"},"unitCouponDiscountDisplay":{"type":"string"},"unitCouponDiscountInPayoutCurrency":{"type":"number"},"unitCouponDiscountInPayoutCurrencyDisplay":{"type":"string"},"unitDiscount":{"type":"number"},"unitDiscountDisplay":{"type":"string"},"unitDiscountInPayoutCurrency":{"type":"number"},"unitDiscountInPayoutCurrencyDisplay":{"type":"string"},"unitListPrice":{"type":"number"},"unitListPriceDisplay":{"type":"string"},"unitListPriceInPayoutCurrency":{"type":"number"},"unitListPriceInPayoutCurrencyDisplay":{"type":"string"},"unitPrice":{"type":"number"},"unitPriceDisplay":{"type":"string"},"unitPriceInPayoutCurrency":{"type":"number"},"unitPriceInPayoutCurrencyDisplay":{"type":"string"},"unitPriceWithoutTax":{"type":"number"},"unitPriceWithoutTaxDisplay":{"type":"string"},"unitListPriceWithoutTax":{"type":"number"},"unitListPriceWithoutTaxDisplay":{"type":"string"}}}},"name":{"type":"string","examples":["Quote for ABC Company"]},"notes":{"type":"string","examples":["This is a Note"]},"netTermsDays":{"type":"integer","format":"int32","examples":[30],"minimum":-**********,"maximum":**********},"quoteUrl":{"type":"string","examples":["https://josemunoz.test.qa2.onfastspring.com/popup-defaultB2B/account/order/quote/QUVJIVYZTQDFBOBEO7IGNXER3VBQ"]},"recipient":{"type":"object","properties":{"company":{"type":"string"},"email":{"type":"string"},"first":{"type":"string"},"last":{"type":"string"},"phone":{"type":"string"},"userId":{"type":"string"}}},"recipientAddress":{"type":"object","properties":{"addressLine1":{"type":"string"},"addressLine2":{"type":"string"},"city":{"type":"string"},"country":{"type":"string"},"postalCode":{"type":"string"},"region":{"type":"string"}}},"siteId":{"type":"string","examples":["pOBehkkfTGo"]},"status":{"type":"string","enum":["OPEN","CANCELED","AWAITING_PAYMENT","COMPLETED","EXPIRED"],"examples":["OPEN"],"description":"`OPEN` `CANCELED` `AWAITING_PAYMENT` `COMPLETED` `EXPIRED`"},"statusHistory":{"uniqueItems":true,"type":"array","items":{"type":"object","properties":{"statusUpdatedTo":{"type":"string","enum":["OPEN","CANCELED","AWAITING_PAYMENT","COMPLETED","EXPIRED"],"examples":["OPEN"],"description":"`OPEN` `CANCELED` `AWAITING_PAYMENT` `COMPLETED` `EXPIRED`"},"statusUpdatedByFullName":{"type":"string","examples":["John Smith"]},"statusUpdatedByEmail":{"type":"string","examples":["<EMAIL>"]},"statusUpdatedOn":{"type":"string","format":"date-time","examples":["2021-03-02T19:48:56.395Z"]}}}},"subtotal":{"type":"number"},"subtotalDisplay":{"type":"string"},"subtotalInPayoutCurrency":{"type":"number"},"subtotalInPayoutCurrencyDisplay":{"type":"string"},"tags":{"type":"array","items":{"type":"object","properties":{"key":{"maxLength":255,"minLength":0,"type":"string","examples":["tag-key"]},"value":{"maxLength":255,"minLength":0,"type":"string","examples":["Tag Value"]}}}},"tax":{"type":"number"},"taxType":{"type":"string"},"total":{"type":"number"},"totalDisplay":{"type":"string"},"totalInPayoutCurrency":{"type":"number"},"totalInPayoutCurrencyDisplay":{"type":"string"},"updated":{"type":"string","format":"date-time","examples":["2021-03-02T19:48:56.395Z"]},"taxId":{"type":"string","examples":["BE09999999XX"]},"source":{"type":"string","examples":["MANAGER"]},"sourceIP":{"type":"string","examples":["*************"]},"isGrossTax":{"type":"boolean","examples":[false]},"invoiceId":{"type":"string"},"links":{"type":"array","items":{"type":"object","properties":{"rel":{"type":"string"},"href":{"type":"string"},"hreflang":{"type":"string"},"media":{"type":"string"},"title":{"type":"string"},"type":{"type":"string"},"deprecation":{"type":"string"},"profile":{"type":"string"},"name":{"type":"string"}}}}},"$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Cancelsubscriptioninstances = {"metadata":{"allOf":[{"type":"object","properties":{"subscription_id":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Subscription Id"}},"required":["subscription_id"]}]},"response":{"200":{"title":"CancelSubscriptionResponse","$schema":"http://json-schema.org/draft-04/schema#"},"500":{"title":"SubscriptionCancelErrorResponse","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Changetheproductforanactivesubscription = {"body":{"title":"ChangetheproductforanactivesubscriptionRequest","type":"object","properties":{"subscriptions":{"type":"array","items":{"title":"Subscription","type":"object","properties":{"subscription":{"type":"string","examples":["{subscription_id}"]},"product":{"type":"string","examples":["{subscription_product_path}"]},"quantity":{"type":"integer","format":"int32","examples":[1],"minimum":-**********,"maximum":**********},"taxExemptId":{"type":"string","examples":["tax-exempt-id"]}},"required":["subscription","product","quantity"]}}},"required":["subscriptions"],"$schema":"http://json-schema.org/draft-04/schema#"},"response":{"200":{"title":"ChangeProductResponse","$schema":"http://json-schema.org/draft-04/schema#"},"500":{"title":"ChangeProductResponseBad","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const ConvertExpiredTrialWithoutPaymentMethod = {"metadata":{"allOf":[{"type":"object","properties":{"subscription_id":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Subscription Id"}},"required":["subscription_id"]}]},"response":{"200":{"title":"ConvertExpiredTrialWithoutPaymentMethodResponse","$schema":"http://json-schema.org/draft-04/schema#"},"400":{"title":"ConvertExpiredTrialWithoutPaymentMethodErrorResponse","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const CreateQuote = {"body":{"required":["items","name","recipient","recipientAddress"],"type":"object","properties":{"coupon":{"maxLength":255,"minLength":0,"type":"string","examples":["TENOFF"]},"currency":{"maxLength":3,"minLength":3,"type":"string","examples":["USD"]},"expirationDateDays":{"maximum":90,"minimum":1,"type":"integer","format":"int32","examples":[30]},"fulfillmentTerm":{"type":"string"},"items":{"maxItems":**********,"minItems":1,"type":"array","items":{"required":["product"],"type":"object","properties":{"product":{"maxLength":255,"minLength":0,"type":"string"},"unitListPrice":{"minimum":0,"exclusiveMinimum":false,"type":"number"},"quantity":{"minimum":1,"type":"integer","format":"int32","maximum":**********}}}},"name":{"type":"string","examples":["Quote for ABC Company"]},"notes":{"maxLength":5000,"minLength":0,"type":"string","examples":["This is a Note"]},"netTermsDays":{"type":"integer","format":"int32","minimum":-**********,"maximum":**********},"recipientAddress":{"type":"object","properties":{"addressLine1":{"maxLength":255,"minLength":0,"type":"string","examples":["801 Garden St"]},"addressLine2":{"maxLength":255,"minLength":0,"type":"string","examples":["Suite 201"]},"city":{"maxLength":255,"minLength":0,"type":"string","examples":["Santa Barbara"]},"country":{"maxLength":2,"minLength":2,"type":"string","examples":["US"]},"postalCode":{"maxLength":255,"minLength":0,"type":"string","examples":["93101"]},"region":{"maxLength":255,"minLength":0,"type":"string","examples":["California"]}}},"recipient":{"required":["email","first","last"],"type":"object","properties":{"company":{"maxLength":255,"minLength":0,"type":"string","examples":["ABC Company"]},"email":{"maxLength":255,"minLength":0,"type":"string","examples":["<EMAIL>"]},"first":{"maxLength":255,"minLength":0,"type":"string","examples":["Leeroy"]},"last":{"maxLength":255,"minLength":0,"type":"string","examples":["Jenkins"]},"phone":{"maxLength":255,"minLength":0,"type":"string","examples":["+**********"]}}},"tags":{"type":"array","items":{"type":"object","properties":{"key":{"maxLength":255,"minLength":0,"type":"string","examples":["tag-key"]},"value":{"maxLength":255,"minLength":0,"type":"string","examples":["Tag Value"]}}}},"taxId":{"maxLength":255,"minLength":0,"type":"string","examples":["BE09999999XX"]},"source":{"maxLength":255,"minLength":0,"type":"string","examples":["MANAGER"]},"sourceIP":{"maxLength":25,"minLength":0,"type":"string","examples":["*************"]}},"$schema":"http://json-schema.org/draft-04/schema#"},"response":{"200":{"type":"object","properties":{"id":{"type":"string","description":"The quote id","examples":["QUVJIVYZTQDFBOBEO7IGNXER3VBQ"]},"buyerGenerated":{"type":"boolean"},"coupon":{"type":"string","examples":["TENOFF"]},"created":{"type":"string","format":"date-time","examples":["2021-03-02T19:48:56.395Z"]},"createdBy":{"type":"object","properties":{"company":{"type":"string"},"email":{"type":"string"},"first":{"type":"string"},"last":{"type":"string"},"phone":{"type":"string"},"userId":{"type":"string"}}},"currency":{"type":"string","examples":["USD"]},"discount":{"type":"number"},"discountDisplay":{"type":"string"},"discountInPayoutCurrency":{"type":"number"},"discountInPayoutCurrencyDisplay":{"type":"string"},"expires":{"type":"string","format":"date-time","examples":["2021-04-01T19:48:56.395Z"]},"expirationDateDays":{"type":"integer","format":"int32","examples":[30],"minimum":-**********,"maximum":**********},"fulfillmentTerm":{"type":"string","enum":["ON_PAYMENT","ON_QUOTE_ACCEPTANCE"],"examples":["ON_QUOTE_ACCEPTANCE"],"description":"`ON_PAYMENT` `ON_QUOTE_ACCEPTANCE`"},"items":{"type":"array","items":{"type":"object","properties":{"product":{"type":"string"},"customPrice":{"type":"boolean"},"display":{"type":"string","examples":["Book The Ring"]},"image":{"type":"string","examples":["icon.png"]},"intervalCount":{"type":"string","examples":["2"]},"period":{"type":"string","examples":["Monthly"]},"periodDays":{"type":"string","examples":["0"]},"quantity":{"type":"integer","format":"int32","examples":[1],"minimum":-**********,"maximum":**********},"renewIntoProducts":{"uniqueItems":true,"type":"array","items":{"type":"object","properties":{"renewIntoProductId":{"type":"string","examples":["DG4UH337PVYDC33XEP3ZH3JHD"]},"renewIntoPath":{"type":"string","examples":["video-subscription"]},"renewIntoPrice":{"type":"number","examples":[9.99]},"renewIntoPeriod":{"type":"string","examples":["Weekly"]},"renewIntoLevel":{"type":"integer","format":"int32","examples":[1],"minimum":-**********,"maximum":**********},"renewIntoIntervalCount":{"type":"string","examples":["2"]},"renewIntoUpcomingProduct":{"type":"string","examples":["audio-subscription"]}}}},"taxes":{"type":"array","items":{"type":"object","properties":{"taxValue":{"type":"number","examples":[0]},"totalTaxable":{"type":"number","examples":[0]}}}},"trialDays":{"type":"integer","format":"int32","examples":[30],"minimum":-**********,"maximum":**********},"subscription":{"type":"boolean"},"type":{"type":"string"},"unitCouponDiscount":{"type":"number"},"unitCouponDiscountDisplay":{"type":"string"},"unitCouponDiscountInPayoutCurrency":{"type":"number"},"unitCouponDiscountInPayoutCurrencyDisplay":{"type":"string"},"unitDiscount":{"type":"number"},"unitDiscountDisplay":{"type":"string"},"unitDiscountInPayoutCurrency":{"type":"number"},"unitDiscountInPayoutCurrencyDisplay":{"type":"string"},"unitListPrice":{"type":"number"},"unitListPriceDisplay":{"type":"string"},"unitListPriceInPayoutCurrency":{"type":"number"},"unitListPriceInPayoutCurrencyDisplay":{"type":"string"},"unitPrice":{"type":"number"},"unitPriceDisplay":{"type":"string"},"unitPriceInPayoutCurrency":{"type":"number"},"unitPriceInPayoutCurrencyDisplay":{"type":"string"},"unitPriceWithoutTax":{"type":"number"},"unitPriceWithoutTaxDisplay":{"type":"string"},"unitListPriceWithoutTax":{"type":"number"},"unitListPriceWithoutTaxDisplay":{"type":"string"}}}},"name":{"type":"string","examples":["Quote for ABC Company"]},"notes":{"type":"string","examples":["This is a Note"]},"netTermsDays":{"type":"integer","format":"int32","examples":[30],"minimum":-**********,"maximum":**********},"quoteUrl":{"type":"string","examples":["https://josemunoz.test.qa2.onfastspring.com/popup-defaultB2B/account/order/quote/QUVJIVYZTQDFBOBEO7IGNXER3VBQ"]},"recipient":{"type":"object","properties":{"company":{"type":"string"},"email":{"type":"string"},"first":{"type":"string"},"last":{"type":"string"},"phone":{"type":"string"},"userId":{"type":"string"}}},"recipientAddress":{"type":"object","properties":{"addressLine1":{"type":"string"},"addressLine2":{"type":"string"},"city":{"type":"string"},"country":{"type":"string"},"postalCode":{"type":"string"},"region":{"type":"string"}}},"siteId":{"type":"string","examples":["pOBehkkfTGo"]},"status":{"type":"string","enum":["OPEN","CANCELED","AWAITING_PAYMENT","COMPLETED","EXPIRED"],"examples":["OPEN"],"description":"`OPEN` `CANCELED` `AWAITING_PAYMENT` `COMPLETED` `EXPIRED`"},"statusHistory":{"uniqueItems":true,"type":"array","items":{"type":"object","properties":{"statusUpdatedTo":{"type":"string","enum":["OPEN","CANCELED","AWAITING_PAYMENT","COMPLETED","EXPIRED"],"examples":["OPEN"],"description":"`OPEN` `CANCELED` `AWAITING_PAYMENT` `COMPLETED` `EXPIRED`"},"statusUpdatedByFullName":{"type":"string","examples":["John Smith"]},"statusUpdatedByEmail":{"type":"string","examples":["<EMAIL>"]},"statusUpdatedOn":{"type":"string","format":"date-time","examples":["2021-03-02T19:48:56.395Z"]}}}},"subtotal":{"type":"number"},"subtotalDisplay":{"type":"string"},"subtotalInPayoutCurrency":{"type":"number"},"subtotalInPayoutCurrencyDisplay":{"type":"string"},"tags":{"type":"array","items":{"type":"object","properties":{"key":{"maxLength":255,"minLength":0,"type":"string","examples":["tag-key"]},"value":{"maxLength":255,"minLength":0,"type":"string","examples":["Tag Value"]}}}},"tax":{"type":"number"},"taxType":{"type":"string"},"total":{"type":"number"},"totalDisplay":{"type":"string"},"totalInPayoutCurrency":{"type":"number"},"totalInPayoutCurrencyDisplay":{"type":"string"},"updated":{"type":"string","format":"date-time","examples":["2021-03-02T19:48:56.395Z"]},"taxId":{"type":"string","examples":["BE09999999XX"]},"source":{"type":"string","examples":["MANAGER"]},"sourceIP":{"type":"string","examples":["*************"]},"isGrossTax":{"type":"boolean","examples":[false]},"invoiceId":{"type":"string"},"links":{"type":"array","items":{"type":"object","properties":{"rel":{"type":"string"},"href":{"type":"string"},"hreflang":{"type":"string"},"media":{"type":"string"},"title":{"type":"string"},"type":{"type":"string"},"deprecation":{"type":"string"},"profile":{"type":"string"},"name":{"type":"string"}}}}},"$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Createanaccount = {"body":{"title":"CreateOneAccount","type":"object","properties":{"contact":{"type":"object","required":["email","first","last"],"properties":{"company":{"maxLength":255,"minLength":0,"type":"string","examples":["ABC Company"]},"email":{"maxLength":255,"minLength":0,"type":"string","examples":["<EMAIL>"]},"first":{"maxLength":255,"minLength":0,"type":"string","examples":["Leeroy"]},"last":{"maxLength":255,"minLength":0,"type":"string","examples":["Jenkins"]},"phone":{"maxLength":255,"minLength":0,"type":"string","examples":["+**********"]}}}},"$schema":"http://json-schema.org/draft-04/schema#"},"response":{"200":{"title":"CreateOneAccountResponse","$schema":"http://json-schema.org/draft-04/schema#"},"500":{"title":"CreateOneAccountError","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Createanewcoupon = {"body":{"title":"CreateanewcouponRequest","$schema":"http://json-schema.org/draft-04/schema#"},"response":{"200":{"type":"object","properties":{}}}} as const
;
const Createasessionwithoutoverridinganydefaultvalues = {"body":{"title":"CreateasessionwithoutoverridinganydefaultvaluesRequest","type":"object","properties":{"account":{"type":"string","examples":["uKj7izONRfanVwBL9eiG_A"]},"items":{"type":"array","items":{"title":"Item2","type":"object","properties":{"product":{"type":"string","examples":["{product_path}"]},"quantity":{"type":"integer","format":"int32","examples":[1],"minimum":-**********,"maximum":**********}},"required":["product","quantity"]}}},"required":["account","items"],"$schema":"http://json-schema.org/draft-04/schema#"},"response":{"200":{"title":"PostSessionsResponse","$schema":"http://json-schema.org/draft-04/schema#"},"400":{"title":"PostSessionsResponseBad","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Createoneormorenewproducts = {"body":{"title":"CreateoneormorenewproductsRequest","type":"object","properties":{"products":{"type":"array","items":{"title":"Product","type":"object","properties":{"product":{"type":"string","examples":["{product_path}"]},"display":{"title":"Display","type":"object","properties":{"en":{"type":"string","examples":["String"]}},"required":["en"]},"description":{"title":"Description","type":"object","properties":{"summary":{"title":"Summary","type":"object","properties":{"en":{"type":"string","examples":["String"]}},"required":["en"]},"action":{"title":"Action","type":"object","properties":{"en":{"type":"string","examples":["String"]}},"required":["en"]},"full":{"title":"Full","type":"object","properties":{"en":{"type":"string","examples":["String"]}},"required":["en"]}},"required":["summary","action","full"]},"fulfillment":{"title":"Fulfillment","type":"object","properties":{"instructions":{"title":"Instructions","type":"object","properties":{"en":{"type":"string","examples":["String"]},"es":{"type":"string","examples":["String"]}},"required":["en","es"]}},"required":["instructions"]},"image":{"type":"string","examples":["https://d8y8nchqlnmka.cloudfront.net/NVaGM-nhSpQ/-FooqIP-R84/photio-imac-hero.png"]},"format":{"type":"string","examples":["digital"]},"sku":{"type":"string","examples":["string"]},"attributes":{"title":"Attributes2","type":"object","properties":{"key1":{"type":"string","examples":["value1"]},"key2":{"type":"string","examples":["value2"]}},"required":["key1","key2"]},"pricing":{"title":"Pricing1","type":"object","properties":{"trial":{"type":"integer","format":"int32","examples":[2],"minimum":-**********,"maximum":**********},"interval":{"type":"string","examples":["month"]},"intervalLength":{"type":"integer","format":"int32","examples":[1],"minimum":-**********,"maximum":**********},"quantityBehavior":{"type":"string","examples":["allow"]},"quantityDefault":{"type":"integer","format":"int32","examples":[1],"minimum":-**********,"maximum":**********},"paymentCollected":{"type":"boolean","examples":[true]},"paidTrial":{"type":"boolean","examples":[true]},"trialPrice":{"title":"Price","type":"object","properties":{"USD":{"type":"number","format":"double","examples":[14.95],"minimum":-1.7976931348623157e+308,"maximum":1.7976931348623157e+308},"EUR":{"type":"number","format":"double","examples":[10.99],"minimum":-1.7976931348623157e+308,"maximum":1.7976931348623157e+308}},"required":["USD","EUR"]},"price":{"title":"Price","type":"object","properties":{"USD":{"type":"number","format":"double","examples":[14.95],"minimum":-1.7976931348623157e+308,"maximum":1.7976931348623157e+308},"EUR":{"type":"number","format":"double","examples":[10.99],"minimum":-1.7976931348623157e+308,"maximum":1.7976931348623157e+308}},"required":["USD","EUR"]},"quantityDiscounts":{"type":"object","additionalProperties":{"type":"number","format":"double","minimum":-1.7976931348623157e+308,"maximum":1.7976931348623157e+308}},"discountReason":{"title":"DiscountReason","type":"object","properties":{"en":{"type":"string","examples":["The Reason"]}},"required":["en"]},"discountDuration":{"type":"integer","format":"int32","examples":[1],"minimum":-**********,"maximum":**********}},"required":["trial","interval","intervalLength","quantityBehavior","quantityDefault","price","quantityDiscounts","discountReason","discountDuration"]}},"required":["product","display","description","fulfillment","image","format","sku","attributes","pricing"]}}},"required":["products"],"$schema":"http://json-schema.org/draft-04/schema#"},"response":{"200":{"oneOf":[{"title":"UpdateProductsResponse"},{"title":"CreateProductsResponse"}],"$schema":"http://json-schema.org/draft-04/schema#"},"201":{"title":"CreateProductsResponse","$schema":"http://json-schema.org/draft-04/schema#"},"500":{"title":"CreateProductsResponseError","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Createorupdateproductoffers = {"body":{"title":"CreateorupdateProductOffersRequest","$schema":"http://json-schema.org/draft-04/schema#"},"metadata":{"allOf":[{"type":"object","properties":{"product_path":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Product path"}},"required":["product_path"]}]},"response":{"200":{"oneOf":[{"title":"CreateProductOffersResponse"},{"title":"UpdateProductOffersResponse"}],"$schema":"http://json-schema.org/draft-04/schema#"},"500":{"title":"CreateorupdateProductOffersError","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const DeleteProducts = {"metadata":{"allOf":[{"type":"object","properties":{"id":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Product Id"}},"required":["id"]}]},"response":{"200":{"title":"DeleteProductsResponse","$schema":"http://json-schema.org/draft-04/schema#"},"500":{"title":"DeleteProductsResponseBad","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const DeleteQuote = {"metadata":{"allOf":[{"type":"object","properties":{"id":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"A unique identifier for the quote"}},"required":["id"]}]},"response":{"200":{"type":"object","properties":{"id":{"type":"string","description":"The quote id","examples":["QUVJIVYZTQDFBOBEO7IGNXER3VBQ"]},"buyerGenerated":{"type":"boolean"},"coupon":{"type":"string","examples":["TENOFF"]},"created":{"type":"string","format":"date-time","examples":["2021-03-02T19:48:56.395Z"]},"createdBy":{"type":"object","properties":{"company":{"type":"string"},"email":{"type":"string"},"first":{"type":"string"},"last":{"type":"string"},"phone":{"type":"string"},"userId":{"type":"string"}}},"currency":{"type":"string","examples":["USD"]},"discount":{"type":"number"},"discountDisplay":{"type":"string"},"discountInPayoutCurrency":{"type":"number"},"discountInPayoutCurrencyDisplay":{"type":"string"},"expires":{"type":"string","format":"date-time","examples":["2021-04-01T19:48:56.395Z"]},"expirationDateDays":{"type":"integer","format":"int32","examples":[30],"minimum":-**********,"maximum":**********},"fulfillmentTerm":{"type":"string","enum":["ON_PAYMENT","ON_QUOTE_ACCEPTANCE"],"examples":["ON_QUOTE_ACCEPTANCE"],"description":"`ON_PAYMENT` `ON_QUOTE_ACCEPTANCE`"},"items":{"type":"array","items":{"type":"object","properties":{"product":{"type":"string"},"customPrice":{"type":"boolean"},"display":{"type":"string","examples":["Book The Ring"]},"image":{"type":"string","examples":["icon.png"]},"intervalCount":{"type":"string","examples":["2"]},"period":{"type":"string","examples":["Monthly"]},"periodDays":{"type":"string","examples":["0"]},"quantity":{"type":"integer","format":"int32","examples":[1],"minimum":-**********,"maximum":**********},"renewIntoProducts":{"uniqueItems":true,"type":"array","items":{"type":"object","properties":{"renewIntoProductId":{"type":"string","examples":["DG4UH337PVYDC33XEP3ZH3JHD"]},"renewIntoPath":{"type":"string","examples":["video-subscription"]},"renewIntoPrice":{"type":"number","examples":[9.99]},"renewIntoPeriod":{"type":"string","examples":["Weekly"]},"renewIntoLevel":{"type":"integer","format":"int32","examples":[1],"minimum":-**********,"maximum":**********},"renewIntoIntervalCount":{"type":"string","examples":["2"]},"renewIntoUpcomingProduct":{"type":"string","examples":["audio-subscription"]}}}},"taxes":{"type":"array","items":{"type":"object","properties":{"taxValue":{"type":"number","examples":[0]},"totalTaxable":{"type":"number","examples":[0]}}}},"trialDays":{"type":"integer","format":"int32","examples":[30],"minimum":-**********,"maximum":**********},"subscription":{"type":"boolean"},"type":{"type":"string"},"unitCouponDiscount":{"type":"number"},"unitCouponDiscountDisplay":{"type":"string"},"unitCouponDiscountInPayoutCurrency":{"type":"number"},"unitCouponDiscountInPayoutCurrencyDisplay":{"type":"string"},"unitDiscount":{"type":"number"},"unitDiscountDisplay":{"type":"string"},"unitDiscountInPayoutCurrency":{"type":"number"},"unitDiscountInPayoutCurrencyDisplay":{"type":"string"},"unitListPrice":{"type":"number"},"unitListPriceDisplay":{"type":"string"},"unitListPriceInPayoutCurrency":{"type":"number"},"unitListPriceInPayoutCurrencyDisplay":{"type":"string"},"unitPrice":{"type":"number"},"unitPriceDisplay":{"type":"string"},"unitPriceInPayoutCurrency":{"type":"number"},"unitPriceInPayoutCurrencyDisplay":{"type":"string"},"unitPriceWithoutTax":{"type":"number"},"unitPriceWithoutTaxDisplay":{"type":"string"},"unitListPriceWithoutTax":{"type":"number"},"unitListPriceWithoutTaxDisplay":{"type":"string"}}}},"name":{"type":"string","examples":["Quote for ABC Company"]},"notes":{"type":"string","examples":["This is a Note"]},"netTermsDays":{"type":"integer","format":"int32","examples":[30],"minimum":-**********,"maximum":**********},"quoteUrl":{"type":"string","examples":["https://josemunoz.test.qa2.onfastspring.com/popup-defaultB2B/account/order/quote/QUVJIVYZTQDFBOBEO7IGNXER3VBQ"]},"recipient":{"type":"object","properties":{"company":{"type":"string"},"email":{"type":"string"},"first":{"type":"string"},"last":{"type":"string"},"phone":{"type":"string"},"userId":{"type":"string"}}},"recipientAddress":{"type":"object","properties":{"addressLine1":{"type":"string"},"addressLine2":{"type":"string"},"city":{"type":"string"},"country":{"type":"string"},"postalCode":{"type":"string"},"region":{"type":"string"}}},"siteId":{"type":"string","examples":["pOBehkkfTGo"]},"status":{"type":"string","enum":["OPEN","CANCELED","AWAITING_PAYMENT","COMPLETED","EXPIRED"],"examples":["OPEN"],"description":"`OPEN` `CANCELED` `AWAITING_PAYMENT` `COMPLETED` `EXPIRED`"},"statusHistory":{"uniqueItems":true,"type":"array","items":{"type":"object","properties":{"statusUpdatedTo":{"type":"string","enum":["OPEN","CANCELED","AWAITING_PAYMENT","COMPLETED","EXPIRED"],"examples":["OPEN"],"description":"`OPEN` `CANCELED` `AWAITING_PAYMENT` `COMPLETED` `EXPIRED`"},"statusUpdatedByFullName":{"type":"string","examples":["John Smith"]},"statusUpdatedByEmail":{"type":"string","examples":["<EMAIL>"]},"statusUpdatedOn":{"type":"string","format":"date-time","examples":["2021-03-02T19:48:56.395Z"]}}}},"subtotal":{"type":"number"},"subtotalDisplay":{"type":"string"},"subtotalInPayoutCurrency":{"type":"number"},"subtotalInPayoutCurrencyDisplay":{"type":"string"},"tags":{"type":"array","items":{"type":"object","properties":{"key":{"maxLength":255,"minLength":0,"type":"string","examples":["tag-key"]},"value":{"maxLength":255,"minLength":0,"type":"string","examples":["Tag Value"]}}}},"tax":{"type":"number"},"taxType":{"type":"string"},"total":{"type":"number"},"totalDisplay":{"type":"string"},"totalInPayoutCurrency":{"type":"number"},"totalInPayoutCurrencyDisplay":{"type":"string"},"updated":{"type":"string","format":"date-time","examples":["2021-03-02T19:48:56.395Z"]},"taxId":{"type":"string","examples":["BE09999999XX"]},"source":{"type":"string","examples":["MANAGER"]},"sourceIP":{"type":"string","examples":["*************"]},"isGrossTax":{"type":"boolean","examples":[false]},"invoiceId":{"type":"string"},"links":{"type":"array","items":{"type":"object","properties":{"rel":{"type":"string"},"href":{"type":"string"},"hreflang":{"type":"string"},"media":{"type":"string"},"title":{"type":"string"},"type":{"type":"string"},"deprecation":{"type":"string"},"profile":{"type":"string"},"name":{"type":"string"}}}}},"$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Deleteallcouponcodesfromacoupon = {"metadata":{"allOf":[{"type":"object","properties":{"coupon_id":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Coupon Id"}},"required":["coupon_id"]}]},"response":{"200":{"type":"object","properties":{}}}} as const
;
const DownloadReport = {"metadata":{"allOf":[{"type":"object","properties":{"id":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#"}},"required":["id"]}]},"response":{"200":{"type":"string","examples":["arr,subscriptions,subscriber_loss,mrr_add_on,product_name 0.0,1.0,0.0,0.0,primary subscription (addon-subscription)"],"$schema":"http://json-schema.org/draft-04/schema#"},"400":{"title":"GetDownloadIdResponseBad","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const GenerateRevenueReport = {"body":{"title":"GenerateRevenueReportRequest","type":"object","properties":{"filter":{"type":"object","properties":{"startDate":{"type":"string","format":"date","examples":["YYYY-MM-DD"]},"endDate":{"type":"string","format":"date","examples":["YYYY-MM-DD"]},"countryISO":{"type":"array","items":{"type":"string","examples":["CO"]}},"productNames":{"type":"array","items":{"type":"string","examples":["toxin product (toxin-product)"]}},"productPaths":{"type":"array","items":{"type":"string","examples":["toxin-product"]}},"syncDate":{"type":"string","format":"date","examples":["YYYY-MM-DD"]}}},"reportColumns":{"type":"array","items":{"type":"string","examples":["buyer_id"]}},"groupBy":{"type":"array","items":{"type":"string","examples":["buyer_email"]}},"pageCount":{"type":"integer","minimum":1,"maximum":1000,"format":"int32","examples":[30]},"pageNumber":{"type":"integer","minimum":0,"format":"int32","maximum":**********},"async":{"type":"boolean"},"notificationEmails":{"type":"array","items":{"type":"string","examples":["<EMAIL>"]}}},"$schema":"http://json-schema.org/draft-04/schema#"},"response":{"200":{"oneOf":[{"title":"PostRevenueSyncResponse"},{"title":"PostRevenueAsyncResponse"}],"$schema":"http://json-schema.org/draft-04/schema#"},"400":{"title":"PostRevenueResponseBad","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const GenerateSubscriptionReport = {"body":{"title":"GenerateSubscriptionReportRequest","type":"object","properties":{"filter":{"type":"object","properties":{"startDate":{"type":"string","format":"date","examples":["YYYY-MM-DD"]},"endDate":{"type":"string","format":"date","examples":["YYYY-MM-DD"]},"countryISO":{"type":"array","items":{"type":"string","examples":["CO"]}},"productNames":{"type":"array","items":{"type":"string","examples":["primary subscription (addon-subscription)"]}},"productPaths":{"type":"array","items":{"type":"string","examples":["primary subscription (addon-subscription)"]}},"syncDate":{"type":"string","format":"date","examples":["YYYY-MM-DD"]}}},"reportColumns":{"type":"array","items":{"type":"string","examples":["activations"]}},"groupBy":{"type":"array","items":{"type":"string","examples":["buyer_email"]}},"pageCount":{"type":"integer","minimum":1,"maximum":1000,"format":"int32","examples":[30]},"pageNumber":{"type":"integer","minimum":0,"format":"int32","maximum":**********},"async":{"type":"boolean"},"notificationEmails":{"type":"array","items":{"type":"string","examples":["<EMAIL>"]}}},"$schema":"http://json-schema.org/draft-04/schema#"},"response":{"200":{"oneOf":[{"title":"PostSubscriptionSyncResponse"},{"title":"PostSubscriptionAsyncResponse"}],"$schema":"http://json-schema.org/draft-04/schema#"},"400":{"title":"PostSubscriptionResponseBad","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const GetAllQuotes = {"metadata":{"allOf":[{"type":"object","properties":{"createdBegin":{"type":"string","format":"date-time","$schema":"http://json-schema.org/draft-04/schema#","description":"The format must be YYYY-MM-DD"},"createdEnd":{"type":"string","format":"date-time","$schema":"http://json-schema.org/draft-04/schema#","description":"The format must be YYYY-MM-DD"},"createdEmail":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#"},"expiresBegin":{"type":"string","format":"date-time","$schema":"http://json-schema.org/draft-04/schema#","description":"The format must be YYYY-MM-DD"},"expiresEnd":{"type":"string","format":"date-time","$schema":"http://json-schema.org/draft-04/schema#","description":"The format must be YYYY-MM-DD"},"searchParam":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#"},"expiredBefore":{"type":"string","format":"date-time","$schema":"http://json-schema.org/draft-04/schema#","description":"The format must be YYYY-MM-DD"},"onlyQuoteId":{"type":"boolean","$schema":"http://json-schema.org/draft-04/schema#"},"statuses":{"type":"array","items":{"type":"string"},"$schema":"http://json-schema.org/draft-04/schema#"}},"required":[]}]},"response":{"200":{"type":"object","properties":{"links":{"type":"array","items":{"type":"object","properties":{"rel":{"type":"string"},"href":{"type":"string"},"hreflang":{"type":"string"},"media":{"type":"string"},"title":{"type":"string"},"type":{"type":"string"},"deprecation":{"type":"string"},"profile":{"type":"string"},"name":{"type":"string"}}}},"content":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","description":"The quote id","examples":["QUVJIVYZTQDFBOBEO7IGNXER3VBQ"]},"buyerGenerated":{"type":"boolean"},"coupon":{"type":"string","examples":["TENOFF"]},"created":{"type":"string","format":"date-time","examples":["2021-03-02T19:48:56.395Z"]},"createdBy":{"type":"object","properties":{"company":{"type":"string"},"email":{"type":"string"},"first":{"type":"string"},"last":{"type":"string"},"phone":{"type":"string"},"userId":{"type":"string"}}},"currency":{"type":"string","examples":["USD"]},"discount":{"type":"number"},"discountDisplay":{"type":"string"},"discountInPayoutCurrency":{"type":"number"},"discountInPayoutCurrencyDisplay":{"type":"string"},"expires":{"type":"string","format":"date-time","examples":["2021-04-01T19:48:56.395Z"]},"expirationDateDays":{"type":"integer","format":"int32","examples":[30],"minimum":-**********,"maximum":**********},"fulfillmentTerm":{"type":"string","enum":["ON_PAYMENT","ON_QUOTE_ACCEPTANCE"],"examples":["ON_QUOTE_ACCEPTANCE"],"description":"`ON_PAYMENT` `ON_QUOTE_ACCEPTANCE`"},"items":{"type":"array","items":{"type":"object","properties":{"product":{"type":"string"},"customPrice":{"type":"boolean"},"display":{"type":"string","examples":["Book The Ring"]},"image":{"type":"string","examples":["icon.png"]},"intervalCount":{"type":"string","examples":["2"]},"period":{"type":"string","examples":["Monthly"]},"periodDays":{"type":"string","examples":["0"]},"quantity":{"type":"integer","format":"int32","examples":[1],"minimum":-**********,"maximum":**********},"renewIntoProducts":{"uniqueItems":true,"type":"array","items":{"type":"object","properties":{"renewIntoProductId":{"type":"string","examples":["DG4UH337PVYDC33XEP3ZH3JHD"]},"renewIntoPath":{"type":"string","examples":["video-subscription"]},"renewIntoPrice":{"type":"number","examples":[9.99]},"renewIntoPeriod":{"type":"string","examples":["Weekly"]},"renewIntoLevel":{"type":"integer","format":"int32","examples":[1],"minimum":-**********,"maximum":**********},"renewIntoIntervalCount":{"type":"string","examples":["2"]},"renewIntoUpcomingProduct":{"type":"string","examples":["audio-subscription"]}}}},"taxes":{"type":"array","items":{"type":"object","properties":{"taxValue":{"type":"number","examples":[0]},"totalTaxable":{"type":"number","examples":[0]}}}},"trialDays":{"type":"integer","format":"int32","examples":[30],"minimum":-**********,"maximum":**********},"subscription":{"type":"boolean"},"type":{"type":"string"},"unitCouponDiscount":{"type":"number"},"unitCouponDiscountDisplay":{"type":"string"},"unitCouponDiscountInPayoutCurrency":{"type":"number"},"unitCouponDiscountInPayoutCurrencyDisplay":{"type":"string"},"unitDiscount":{"type":"number"},"unitDiscountDisplay":{"type":"string"},"unitDiscountInPayoutCurrency":{"type":"number"},"unitDiscountInPayoutCurrencyDisplay":{"type":"string"},"unitListPrice":{"type":"number"},"unitListPriceDisplay":{"type":"string"},"unitListPriceInPayoutCurrency":{"type":"number"},"unitListPriceInPayoutCurrencyDisplay":{"type":"string"},"unitPrice":{"type":"number"},"unitPriceDisplay":{"type":"string"},"unitPriceInPayoutCurrency":{"type":"number"},"unitPriceInPayoutCurrencyDisplay":{"type":"string"},"unitPriceWithoutTax":{"type":"number"},"unitPriceWithoutTaxDisplay":{"type":"string"},"unitListPriceWithoutTax":{"type":"number"},"unitListPriceWithoutTaxDisplay":{"type":"string"}}}},"name":{"type":"string","examples":["Quote for ABC Company"]},"notes":{"type":"string","examples":["This is a Note"]},"netTermsDays":{"type":"integer","format":"int32","examples":[30],"minimum":-**********,"maximum":**********},"quoteUrl":{"type":"string","examples":["https://josemunoz.test.qa2.onfastspring.com/popup-defaultB2B/account/order/quote/QUVJIVYZTQDFBOBEO7IGNXER3VBQ"]},"recipient":{"type":"object","properties":{"company":{"type":"string"},"email":{"type":"string"},"first":{"type":"string"},"last":{"type":"string"},"phone":{"type":"string"},"userId":{"type":"string"}}},"recipientAddress":{"type":"object","properties":{"addressLine1":{"type":"string"},"addressLine2":{"type":"string"},"city":{"type":"string"},"country":{"type":"string"},"postalCode":{"type":"string"},"region":{"type":"string"}}},"siteId":{"type":"string","examples":["pOBehkkfTGo"]},"status":{"type":"string","enum":["OPEN","CANCELED","AWAITING_PAYMENT","COMPLETED","EXPIRED"],"examples":["OPEN"],"description":"`OPEN` `CANCELED` `AWAITING_PAYMENT` `COMPLETED` `EXPIRED`"},"statusHistory":{"uniqueItems":true,"type":"array","items":{"type":"object","properties":{"statusUpdatedTo":{"type":"string","enum":["OPEN","CANCELED","AWAITING_PAYMENT","COMPLETED","EXPIRED"],"examples":["OPEN"],"description":"`OPEN` `CANCELED` `AWAITING_PAYMENT` `COMPLETED` `EXPIRED`"},"statusUpdatedByFullName":{"type":"string","examples":["John Smith"]},"statusUpdatedByEmail":{"type":"string","examples":["<EMAIL>"]},"statusUpdatedOn":{"type":"string","format":"date-time","examples":["2021-03-02T19:48:56.395Z"]}}}},"subtotal":{"type":"number"},"subtotalDisplay":{"type":"string"},"subtotalInPayoutCurrency":{"type":"number"},"subtotalInPayoutCurrencyDisplay":{"type":"string"},"tags":{"type":"array","items":{"type":"object","properties":{"key":{"maxLength":255,"minLength":0,"type":"string","examples":["tag-key"]},"value":{"maxLength":255,"minLength":0,"type":"string","examples":["Tag Value"]}}}},"tax":{"type":"number"},"taxType":{"type":"string"},"total":{"type":"number"},"totalDisplay":{"type":"string"},"totalInPayoutCurrency":{"type":"number"},"totalInPayoutCurrencyDisplay":{"type":"string"},"updated":{"type":"string","format":"date-time","examples":["2021-03-02T19:48:56.395Z"]},"taxId":{"type":"string","examples":["BE09999999XX"]},"source":{"type":"string","examples":["MANAGER"]},"sourceIP":{"type":"string","examples":["*************"]},"isGrossTax":{"type":"boolean","examples":[false]},"invoiceId":{"type":"string"},"links":{"type":"array","items":{"type":"object","properties":{"rel":{"type":"string"},"href":{"type":"string"},"hreflang":{"type":"string"},"media":{"type":"string"},"title":{"type":"string"},"type":{"type":"string"},"deprecation":{"type":"string"},"profile":{"type":"string"},"name":{"type":"string"}}}}}}}},"$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const GetJobById = {"metadata":{"allOf":[{"type":"object","properties":{"id":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#"}},"required":["id"]}]},"response":{"200":{"title":"GetJobIdResponse","$schema":"http://json-schema.org/draft-04/schema#"},"400":{"title":"GetJobIdResponseBad","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const GetJobs = {"response":{"200":{"title":"GetJobsResponse","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const GetQuoteById = {"metadata":{"allOf":[{"type":"object","properties":{"id":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#"}},"required":["id"]}]},"response":{"200":{"type":"object","properties":{"id":{"type":"string","description":"The quote id","examples":["QUVJIVYZTQDFBOBEO7IGNXER3VBQ"]},"buyerGenerated":{"type":"boolean"},"coupon":{"type":"string","examples":["TENOFF"]},"created":{"type":"string","format":"date-time","examples":["2021-03-02T19:48:56.395Z"]},"createdBy":{"type":"object","properties":{"company":{"type":"string"},"email":{"type":"string"},"first":{"type":"string"},"last":{"type":"string"},"phone":{"type":"string"},"userId":{"type":"string"}}},"currency":{"type":"string","examples":["USD"]},"discount":{"type":"number"},"discountDisplay":{"type":"string"},"discountInPayoutCurrency":{"type":"number"},"discountInPayoutCurrencyDisplay":{"type":"string"},"expires":{"type":"string","format":"date-time","examples":["2021-04-01T19:48:56.395Z"]},"expirationDateDays":{"type":"integer","format":"int32","examples":[30],"minimum":-**********,"maximum":**********},"fulfillmentTerm":{"type":"string","enum":["ON_PAYMENT","ON_QUOTE_ACCEPTANCE"],"examples":["ON_QUOTE_ACCEPTANCE"],"description":"`ON_PAYMENT` `ON_QUOTE_ACCEPTANCE`"},"items":{"type":"array","items":{"type":"object","properties":{"product":{"type":"string"},"customPrice":{"type":"boolean"},"display":{"type":"string","examples":["Book The Ring"]},"image":{"type":"string","examples":["icon.png"]},"intervalCount":{"type":"string","examples":["2"]},"period":{"type":"string","examples":["Monthly"]},"periodDays":{"type":"string","examples":["0"]},"quantity":{"type":"integer","format":"int32","examples":[1],"minimum":-**********,"maximum":**********},"renewIntoProducts":{"uniqueItems":true,"type":"array","items":{"type":"object","properties":{"renewIntoProductId":{"type":"string","examples":["DG4UH337PVYDC33XEP3ZH3JHD"]},"renewIntoPath":{"type":"string","examples":["video-subscription"]},"renewIntoPrice":{"type":"number","examples":[9.99]},"renewIntoPeriod":{"type":"string","examples":["Weekly"]},"renewIntoLevel":{"type":"integer","format":"int32","examples":[1],"minimum":-**********,"maximum":**********},"renewIntoIntervalCount":{"type":"string","examples":["2"]},"renewIntoUpcomingProduct":{"type":"string","examples":["audio-subscription"]}}}},"taxes":{"type":"array","items":{"type":"object","properties":{"taxValue":{"type":"number","examples":[0]},"totalTaxable":{"type":"number","examples":[0]}}}},"trialDays":{"type":"integer","format":"int32","examples":[30],"minimum":-**********,"maximum":**********},"subscription":{"type":"boolean"},"type":{"type":"string"},"unitCouponDiscount":{"type":"number"},"unitCouponDiscountDisplay":{"type":"string"},"unitCouponDiscountInPayoutCurrency":{"type":"number"},"unitCouponDiscountInPayoutCurrencyDisplay":{"type":"string"},"unitDiscount":{"type":"number"},"unitDiscountDisplay":{"type":"string"},"unitDiscountInPayoutCurrency":{"type":"number"},"unitDiscountInPayoutCurrencyDisplay":{"type":"string"},"unitListPrice":{"type":"number"},"unitListPriceDisplay":{"type":"string"},"unitListPriceInPayoutCurrency":{"type":"number"},"unitListPriceInPayoutCurrencyDisplay":{"type":"string"},"unitPrice":{"type":"number"},"unitPriceDisplay":{"type":"string"},"unitPriceInPayoutCurrency":{"type":"number"},"unitPriceInPayoutCurrencyDisplay":{"type":"string"},"unitPriceWithoutTax":{"type":"number"},"unitPriceWithoutTaxDisplay":{"type":"string"},"unitListPriceWithoutTax":{"type":"number"},"unitListPriceWithoutTaxDisplay":{"type":"string"}}}},"name":{"type":"string","examples":["Quote for ABC Company"]},"notes":{"type":"string","examples":["This is a Note"]},"netTermsDays":{"type":"integer","format":"int32","examples":[30],"minimum":-**********,"maximum":**********},"quoteUrl":{"type":"string","examples":["https://josemunoz.test.qa2.onfastspring.com/popup-defaultB2B/account/order/quote/QUVJIVYZTQDFBOBEO7IGNXER3VBQ"]},"recipient":{"type":"object","properties":{"company":{"type":"string"},"email":{"type":"string"},"first":{"type":"string"},"last":{"type":"string"},"phone":{"type":"string"},"userId":{"type":"string"}}},"recipientAddress":{"type":"object","properties":{"addressLine1":{"type":"string"},"addressLine2":{"type":"string"},"city":{"type":"string"},"country":{"type":"string"},"postalCode":{"type":"string"},"region":{"type":"string"}}},"siteId":{"type":"string","examples":["pOBehkkfTGo"]},"status":{"type":"string","enum":["OPEN","CANCELED","AWAITING_PAYMENT","COMPLETED","EXPIRED"],"examples":["OPEN"],"description":"`OPEN` `CANCELED` `AWAITING_PAYMENT` `COMPLETED` `EXPIRED`"},"statusHistory":{"uniqueItems":true,"type":"array","items":{"type":"object","properties":{"statusUpdatedTo":{"type":"string","enum":["OPEN","CANCELED","AWAITING_PAYMENT","COMPLETED","EXPIRED"],"examples":["OPEN"],"description":"`OPEN` `CANCELED` `AWAITING_PAYMENT` `COMPLETED` `EXPIRED`"},"statusUpdatedByFullName":{"type":"string","examples":["John Smith"]},"statusUpdatedByEmail":{"type":"string","examples":["<EMAIL>"]},"statusUpdatedOn":{"type":"string","format":"date-time","examples":["2021-03-02T19:48:56.395Z"]}}}},"subtotal":{"type":"number"},"subtotalDisplay":{"type":"string"},"subtotalInPayoutCurrency":{"type":"number"},"subtotalInPayoutCurrencyDisplay":{"type":"string"},"tags":{"type":"array","items":{"type":"object","properties":{"key":{"maxLength":255,"minLength":0,"type":"string","examples":["tag-key"]},"value":{"maxLength":255,"minLength":0,"type":"string","examples":["Tag Value"]}}}},"tax":{"type":"number"},"taxType":{"type":"string"},"total":{"type":"number"},"totalDisplay":{"type":"string"},"totalInPayoutCurrency":{"type":"number"},"totalInPayoutCurrencyDisplay":{"type":"string"},"updated":{"type":"string","format":"date-time","examples":["2021-03-02T19:48:56.395Z"]},"taxId":{"type":"string","examples":["BE09999999XX"]},"source":{"type":"string","examples":["MANAGER"]},"sourceIP":{"type":"string","examples":["*************"]},"isGrossTax":{"type":"boolean","examples":[false]},"invoiceId":{"type":"string"},"links":{"type":"array","items":{"type":"object","properties":{"rel":{"type":"string"},"href":{"type":"string"},"hreflang":{"type":"string"},"media":{"type":"string"},"title":{"type":"string"},"type":{"type":"string"},"deprecation":{"type":"string"},"profile":{"type":"string"},"name":{"type":"string"}}}}},"$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Getalloffersforproductbyoffertype = {"metadata":{"allOf":[{"type":"object","properties":{"product_path":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Product path"}},"required":["product_path"]},{"type":"object","properties":{"type":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Offer Type"}},"required":[]}]},"response":{"200":{"title":"GetAllOffersforProductByOfferTypeSuccessResponse","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Getallproductsprice = {"response":{"200":{"title":"GetAllProductsPrice","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Getallproductspricewithcountry = {"metadata":{"allOf":[{"type":"object","properties":{"country":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Country code"}},"required":["country"]}]},"response":{"200":{"title":"GetAllProductsPrice","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Getallproductspricewithcountryandcurrency = {"metadata":{"allOf":[{"type":"object","properties":{"country":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Country code"},"currency":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Currency code"}},"required":["country","currency"]}]},"response":{"200":{"title":"GetAllProductsPrice","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Getallsubscriptioninstances = {"response":{"200":{"title":"GetAllSubscriptionsInstances","$schema":"http://json-schema.org/draft-04/schema#"},"500":{"title":"SubscriptionGetErrorResponse","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const GetauthenticatedaccountmanagementUrl = {"metadata":{"allOf":[{"type":"object","properties":{"account_id":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Account Id"}},"required":["account_id"]}]},"response":{"200":{"title":"AuthenticateAccount","$schema":"http://json-schema.org/draft-04/schema#"},"500":{"title":"AuthenticateAccountError","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Getcouponcodesassignedtoacoupon = {"body":{"title":"GetCouponCodesRequest","$schema":"http://json-schema.org/draft-04/schema#"},"metadata":{"allOf":[{"type":"object","properties":{"coupon_id":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Coupon Id"}},"required":["coupon_id"]}]},"response":{"200":{"title":"GetCouponCodesResponse","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Getlistofallproductids = {"response":{"200":{"title":"GetAllProducts","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Getoneaccount = {"metadata":{"allOf":[{"type":"object","properties":{"account_id":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Account Id"}},"required":["account_id"]}]},"response":{"200":{"title":"Getoneaccount","$schema":"http://json-schema.org/draft-04/schema#"},"500":{"title":"GetOneAccountError","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Getoneormoresubscriptioninstances = {"metadata":{"allOf":[{"type":"object","properties":{"subscription_id":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Subscription Id"}},"required":["subscription_id"]},{"type":"object","properties":{"accountId":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"specify a specific customer account whose subscriptions you want to retrieve"},"begin":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"specify the beginning of a date range in yyyy-mm-dd-format"},"end":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"specify the end of a date range in yyyy-mm-dd format"},"event":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"In each event, use begin and end to retrieve the corresponding subscriptions"},"products":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"enter one or more product ids to filter the response to include only subscriptions for the specified products; use commas to separate multiple values"},"scope":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#"},"status":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#"}},"required":[]}]},"response":{"200":{"title":"GetASubscriptionResponse","$schema":"http://json-schema.org/draft-04/schema#"},"500":{"title":"SubscriptionGetErrorResponse","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Getoneormultiplereturns = {"metadata":{"allOf":[{"type":"object","properties":{"id":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#"}},"required":["id"]}]},"response":{"200":{"title":"GetReturnsResponse","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const GetordersbyId = {"metadata":{"allOf":[{"type":"object","properties":{"order_id":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Order Id"}},"required":["order_id"]}]},"response":{"200":{"title":"GetOrdersById","$schema":"http://json-schema.org/draft-04/schema#"},"400":{"title":"GetOrdersByIdError","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Getordersbydaterange = {"metadata":{"allOf":[{"type":"object","properties":{"begin_date":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"filters results to include transactions after the specified begin date (must be at least one day before the specified end date), the format must be MM/DD/YY"},"end_date":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"filters results to include transactions before the specified end date, the format must be MM/DD/YY"},"limit":{"type":"number","$schema":"http://json-schema.org/draft-04/schema#","description":"integer limits the number of order records returned per page (default is 50 records)"},"page":{"type":"number","$schema":"http://json-schema.org/draft-04/schema#","description":"specifies page number of results to be returned; used together with limit to control pagination"}},"required":["begin_date","end_date","limit","page"]}]},"response":{"200":{"title":"GetOrdersByDates","$schema":"http://json-schema.org/draft-04/schema#"},"400":{"title":"GetOrdersByDatesError","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Getordersbyenddate = {"metadata":{"allOf":[{"type":"object","properties":{"end_date":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"filters results to include transactions before the specified end date, the format must be MM/DD/YY"}},"required":["end_date"]}]},"response":{"200":{"title":"GetOrdersByEndDate","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Getordersbyproductdaterange = {"metadata":{"allOf":[{"type":"object","properties":{"product_path":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Product path"},"begin_date":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"filters results to include transactions after the specified begin date (must be at least one day before the specified end date), the format must be yyyy-mm-dd"},"end_date":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"filters results to include transactions before the specified end date, the format must be yyyy-mm-dd"}},"required":["product_path","begin_date","end_date"]},{"type":"object","properties":{"limit":{"type":"number","$schema":"http://json-schema.org/draft-04/schema#","description":"integer limits the number of order records returned per page (default is 50 records)"},"page":{"type":"number","$schema":"http://json-schema.org/draft-04/schema#","description":"specifies page number of results to be returned; used together with limit to control pagination"}},"required":[]}]},"response":{"200":{"title":"GetOrdersByProductPathDates","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Getordersbyproductpath = {"metadata":{"allOf":[{"type":"object","properties":{"product_path":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Product path"},"limit":{"type":"number","$schema":"http://json-schema.org/draft-04/schema#","description":"integer limits the number of order records returned per page (default is 50 records)"},"page":{"type":"number","$schema":"http://json-schema.org/draft-04/schema#","description":"specifies page number of results to be returned; used together with limit to control pagination"}},"required":["product_path","limit","page"]}]},"response":{"200":{"title":"GetOrdersByProductPath","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Getordersbyreturn = {"metadata":{"allOf":[{"type":"object","properties":{"begin_date":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"filters results to include transactions after the specified begin date (must be at least one day before the specified end date), the format must be MM/DD/YY"},"end_date":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"filters results to include transactions before the specified end date, the format must be MM/DD/YY"},"return":{"type":"boolean","$schema":"http://json-schema.org/draft-04/schema#"}},"required":["begin_date","end_date","return"]}]},"response":{"200":{"title":"GetOrdersWithReturnOnly","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Getprocessedevents = {"metadata":{"allOf":[{"type":"object","properties":{"days":{"type":"integer","format":"int32","minimum":-**********,"maximum":**********,"$schema":"http://json-schema.org/draft-04/schema#"}},"required":["days"]}]},"response":{"200":{"title":"GetEventsResponse","$schema":"http://json-schema.org/draft-04/schema#"},"500":{"title":"GetEventsResponseBad","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Getproductsbyid = {"metadata":{"allOf":[{"type":"object","properties":{"product_path":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Product path"}},"required":["product_path"]}]},"response":{"200":{"title":"GetProductsByPth","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Getspecificproductprice = {"metadata":{"allOf":[{"type":"object","properties":{"id":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Product Id"}},"required":["id"]}]},"response":{"200":{"title":"GetProductPrice","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Getspecificproductpricecountry = {"metadata":{"allOf":[{"type":"object","properties":{"id":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Product Id"},"country":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"country code"}},"required":["id","country"]}]},"response":{"200":{"title":"GetProductPrice","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Getspecificproductpricecountrycurrency = {"metadata":{"allOf":[{"type":"object","properties":{"id":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Product Id"},"country":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Country code"},"currency":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"currency code"}},"required":["id","country","currency"]}]},"response":{"200":{"title":"GetProductPrice","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Getsubscriptioninstanceentries = {"metadata":{"allOf":[{"type":"object","properties":{"subscription_id":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Subscription Id"}},"required":["subscription_id"]}]},"response":{"200":{"type":"object","properties":{}},"500":{"title":"SubscriptionGetErrorResponse","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const GetsubscriptionplanCChangehistory = {"metadata":{"allOf":[{"type":"object","properties":{"subscription_id":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Subscription Id"}},"required":["subscription_id"]},{"type":"object","properties":{"scope":{"type":"string","enum":["base_plan","add_on"],"$schema":"http://json-schema.org/draft-04/schema#","description":"Type of items to return"},"order":{"type":"string","enum":["increasing","decreasing"],"$schema":"http://json-schema.org/draft-04/schema#","description":"Sort Order for the results"}},"required":[]}]},"response":{"200":{"type":"object","properties":{}},"500":{"title":"SubscriptionGetErrorResponse","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Getunprocessedevents = {"metadata":{"allOf":[{"type":"object","properties":{"begin":{"type":"integer","format":"int64","minimum":-9223372036854776000,"maximum":9223372036854776000,"$schema":"http://json-schema.org/draft-04/schema#"}},"required":["begin"]}]},"response":{"200":{"title":"GetEventsResponse","$schema":"http://json-schema.org/draft-04/schema#"},"500":{"title":"GetEventsResponseBad","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const LookUpAccountsbyParameters = {"metadata":{"allOf":[{"type":"object","properties":{"email":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Account email"},"custom":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Account custom key"},"global":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Account global key"},"orderID":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Order id"},"orderReference":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Order Reference"},"subscriptionId":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Subscription ID"},"products":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Product ID"},"subscriptions":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"\"active\", \"ended\", \"canceled\", \"started\" will return accounts with subscriptions in the corresponding state"},"refunds":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"true"},"limit":{"type":"number","$schema":"http://json-schema.org/draft-04/schema#","description":"integer value indicating the maximum number of records to be returned Or, when used together with page, the maximum number of records to be returned per page"},"page":{"type":"number","$schema":"http://json-schema.org/draft-04/schema#","description":"Integer value that must be used in conjunction with limit to specify which page of results should be returned"}},"required":[]}]},"response":{"200":{"oneOf":[{"title":"LookUpAccount"},{"title":"GetAllAccounts"}],"$schema":"http://json-schema.org/draft-04/schema#"},"500":{"title":"LookUpAccountError","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Pauseasubscription = {"body":{"title":"PauseSubscriptionRequest","properties":{"pausePeriodCount":{"type":"integer","format":"int32","minimum":-**********,"maximum":**********}},"type":"object","$schema":"http://json-schema.org/draft-04/schema#"},"metadata":{"allOf":[{"type":"object","properties":{"subscription_id":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Subscription Id"}},"required":["subscription_id"]}]},"response":{"200":{"title":"PauseSubscriptionResponse","$schema":"http://json-schema.org/draft-04/schema#"},"500":{"title":"PauseSubscriptionErrorResponse","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const PostOneMoreOrdersReturns = {"body":{"title":"PostReturnsRequest","$schema":"http://json-schema.org/draft-04/schema#"},"response":{"200":{"title":"PostReturnsResponse","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Rebillmanagedsubscriptioninstance = {"body":{"title":"ChargeSubscriptionRequest","$schema":"http://json-schema.org/draft-04/schema#"},"response":{"200":{"title":"ChargeSubscriptionResponse","$schema":"http://json-schema.org/draft-04/schema#"},"500":{"title":"SubscriptionChargeErrorResponse","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const ResetCache = {"response":{"200":{"type":"string","examples":["Cache reset success"],"$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Resumeapausedsubscription = {"metadata":{"allOf":[{"type":"object","properties":{"subscription_id":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Subscription Id"}},"required":["subscription_id"]}]},"response":{"200":{"title":"GetASubscriptionResponse","$schema":"http://json-schema.org/draft-04/schema#"},"500":{"title":"ResumeSubscriptionErrorResponse","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Retrievecoupondetails = {"metadata":{"allOf":[{"type":"object","properties":{"coupon_id":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Coupon Id"}},"required":["coupon_id"]}]},"response":{"200":{"title":"GetCouponsDetailsResponse","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const RotateWebhookKey = {"body":{"type":"object","required":["url","hmacSecret"],"properties":{"url":{"type":"string","format":"uri","description":"The URL associated with the webhook."},"hmacSecret":{"type":"string","description":"The new HMAC Secret key.","maxLength":100}},"$schema":"http://json-schema.org/draft-04/schema#"},"response":{"200":{"type":"object","properties":{"url":{"type":"string"},"action":{"type":"string","examples":["webhooks.update"]},"result":{"type":"string","examples":["success"]}},"$schema":"http://json-schema.org/draft-04/schema#"},"400":{"type":"object","properties":{"action":{"type":"string","examples":["webhooks.update"]},"result":{"type":"string","examples":["error"]},"error":{"type":"object","properties":{"url":{"type":"string","examples":["A URL is required"]},"hmacSecret":{"oneOf":[{"type":"string","examples":["A HMAC Secret is required"]},{"type":"string","examples":["Maximum length exceeded. The HMAC Secret must be no longer than 100 characters long."]}]}}}},"$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const SubscriptoinProratePreviewEstimate = {"body":{"title":"EstimateSubscriptionRequest","required":["subscription"],"type":"object","properties":{"subscription":{"type":"string"},"product":{"type":"string"},"quantity":{"type":"integer","format":"int32","minimum":-**********,"maximum":**********},"pricing":{"title":"Pricing","type":"object","properties":{"USD":{"type":"number","format":"double","examples":[14.95],"minimum":-1.7976931348623157e+308,"maximum":1.7976931348623157e+308},"EUR":{"type":"number","format":"double","examples":[10.99],"minimum":-1.7976931348623157e+308,"maximum":1.7976931348623157e+308}},"required":["USD","EUR"]},"addons":{"type":"array","items":{"title":"AddOn","type":"object","properties":{"product":{"type":"string"},"quantity":{"type":"integer","format":"int32","minimum":-**********,"maximum":**********},"pricing":{"title":"Pricing","type":"object","properties":{"USD":{"type":"number","format":"double","examples":[14.95],"minimum":-1.7976931348623157e+308,"maximum":1.7976931348623157e+308},"EUR":{"type":"number","format":"double","examples":[10.99],"minimum":-1.7976931348623157e+308,"maximum":1.7976931348623157e+308}},"required":["USD","EUR"]}}}}},"$schema":"http://json-schema.org/draft-04/schema#"},"response":{"200":{"title":"EstimateSubscriptionSuccessResponse","$schema":"http://json-schema.org/draft-04/schema#"},"500":{"title":"EstimateSubscriptionErrorResponse","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Uncancelasubscriptionpriortodeactivation = {"body":{"title":"UncancelasubscriptionpriortodeactivationRequest","type":"object","properties":{"subscriptions":{"type":"array","items":{"title":"Subscription6","type":"object","properties":{"subscription":{"type":"string","examples":["{subscription_id}"]},"deactivation":{"type":"string"}},"required":["subscription","deactivation"]}}},"required":["subscriptions"],"$schema":"http://json-schema.org/draft-04/schema#"},"metadata":{"allOf":[{"type":"object","properties":{"subscription_id":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Subscription Id"}},"required":["subscription_id"]}]},"response":{"200":{"type":"object","properties":{}},"500":{"title":"SubscriptionGetErrorResponse","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const UpdateQuote = {"body":{"type":"object","properties":{"updateQuoteRequest":{"required":["currency","expirationDateDays","fulfillmentTerm","items","name","recipient","recipientAddress"],"type":"object","properties":{"coupon":{"maxLength":255,"minLength":0,"type":"string","examples":["TENOFF"]},"currency":{"maxLength":3,"minLength":3,"type":"string","examples":["USD"]},"expirationDateDays":{"maximum":90,"minimum":1,"type":"integer","format":"int32","examples":[30]},"fulfillmentTerm":{"type":"string"},"items":{"maxItems":**********,"minItems":1,"type":"array","items":{"required":["product"],"type":"object","properties":{"product":{"maxLength":255,"minLength":0,"type":"string"},"unitListPrice":{"minimum":0,"exclusiveMinimum":false,"type":"number"},"quantity":{"minimum":1,"type":"integer","format":"int32","maximum":**********}}}},"name":{"type":"string","examples":["Quote for ABC Company"]},"notes":{"maxLength":5000,"minLength":0,"type":"string","examples":["This is a Note"]},"netTermsDays":{"type":"integer","format":"int32","minimum":-**********,"maximum":**********},"recipientAddress":{"type":"object","properties":{"addressLine1":{"maxLength":255,"minLength":0,"type":"string","examples":["801 Garden St"]},"addressLine2":{"maxLength":255,"minLength":0,"type":"string","examples":["Suite 201"]},"city":{"maxLength":255,"minLength":0,"type":"string","examples":["Santa Barbara"]},"country":{"maxLength":2,"minLength":2,"type":"string","examples":["US"]},"postalCode":{"maxLength":255,"minLength":0,"type":"string","examples":["93101"]},"region":{"maxLength":255,"minLength":0,"type":"string","examples":["California"]}}},"recipient":{"required":["email","first","last"],"type":"object","properties":{"company":{"maxLength":255,"minLength":0,"type":"string","examples":["ABC Company"]},"email":{"maxLength":255,"minLength":0,"type":"string","examples":["<EMAIL>"]},"first":{"maxLength":255,"minLength":0,"type":"string","examples":["Leeroy"]},"last":{"maxLength":255,"minLength":0,"type":"string","examples":["Jenkins"]},"phone":{"maxLength":255,"minLength":0,"type":"string","examples":["+**********"]}}},"status":{"type":"string","enum":["OPEN","CANCELED","AWAITING_PAYMENT","COMPLETED","EXPIRED"],"examples":["OPEN"]},"tags":{"type":"array","items":{"type":"object","properties":{"key":{"maxLength":255,"minLength":0,"type":"string","examples":["tag-key"]},"value":{"maxLength":255,"minLength":0,"type":"string","examples":["Tag Value"]}}}},"taxId":{"maxLength":255,"minLength":0,"type":"string","examples":["BE09999999XX"]},"source":{"maxLength":255,"minLength":0,"type":"string","examples":["MANAGER"]},"sourceIP":{"maxLength":25,"minLength":0,"type":"string","examples":["*************"]},"invoiceId":{"maxLength":128,"minLength":0,"type":"string"}}}},"$schema":"http://json-schema.org/draft-04/schema#"},"metadata":{"allOf":[{"type":"object","properties":{"id":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"A unique identifier for the quote"}},"required":["id"]}]},"response":{"200":{"type":"object","properties":{"id":{"type":"string","description":"The quote id","examples":["QUVJIVYZTQDFBOBEO7IGNXER3VBQ"]},"buyerGenerated":{"type":"boolean"},"coupon":{"type":"string","examples":["TENOFF"]},"created":{"type":"string","format":"date-time","examples":["2021-03-02T19:48:56.395Z"]},"createdBy":{"type":"object","properties":{"company":{"type":"string"},"email":{"type":"string"},"first":{"type":"string"},"last":{"type":"string"},"phone":{"type":"string"},"userId":{"type":"string"}}},"currency":{"type":"string","examples":["USD"]},"discount":{"type":"number"},"discountDisplay":{"type":"string"},"discountInPayoutCurrency":{"type":"number"},"discountInPayoutCurrencyDisplay":{"type":"string"},"expires":{"type":"string","format":"date-time","examples":["2021-04-01T19:48:56.395Z"]},"expirationDateDays":{"type":"integer","format":"int32","examples":[30],"minimum":-**********,"maximum":**********},"fulfillmentTerm":{"type":"string","enum":["ON_PAYMENT","ON_QUOTE_ACCEPTANCE"],"examples":["ON_QUOTE_ACCEPTANCE"],"description":"`ON_PAYMENT` `ON_QUOTE_ACCEPTANCE`"},"items":{"type":"array","items":{"type":"object","properties":{"product":{"type":"string"},"customPrice":{"type":"boolean"},"display":{"type":"string","examples":["Book The Ring"]},"image":{"type":"string","examples":["icon.png"]},"intervalCount":{"type":"string","examples":["2"]},"period":{"type":"string","examples":["Monthly"]},"periodDays":{"type":"string","examples":["0"]},"quantity":{"type":"integer","format":"int32","examples":[1],"minimum":-**********,"maximum":**********},"renewIntoProducts":{"uniqueItems":true,"type":"array","items":{"type":"object","properties":{"renewIntoProductId":{"type":"string","examples":["DG4UH337PVYDC33XEP3ZH3JHD"]},"renewIntoPath":{"type":"string","examples":["video-subscription"]},"renewIntoPrice":{"type":"number","examples":[9.99]},"renewIntoPeriod":{"type":"string","examples":["Weekly"]},"renewIntoLevel":{"type":"integer","format":"int32","examples":[1],"minimum":-**********,"maximum":**********},"renewIntoIntervalCount":{"type":"string","examples":["2"]},"renewIntoUpcomingProduct":{"type":"string","examples":["audio-subscription"]}}}},"taxes":{"type":"array","items":{"type":"object","properties":{"taxValue":{"type":"number","examples":[0]},"totalTaxable":{"type":"number","examples":[0]}}}},"trialDays":{"type":"integer","format":"int32","examples":[30],"minimum":-**********,"maximum":**********},"subscription":{"type":"boolean"},"type":{"type":"string"},"unitCouponDiscount":{"type":"number"},"unitCouponDiscountDisplay":{"type":"string"},"unitCouponDiscountInPayoutCurrency":{"type":"number"},"unitCouponDiscountInPayoutCurrencyDisplay":{"type":"string"},"unitDiscount":{"type":"number"},"unitDiscountDisplay":{"type":"string"},"unitDiscountInPayoutCurrency":{"type":"number"},"unitDiscountInPayoutCurrencyDisplay":{"type":"string"},"unitListPrice":{"type":"number"},"unitListPriceDisplay":{"type":"string"},"unitListPriceInPayoutCurrency":{"type":"number"},"unitListPriceInPayoutCurrencyDisplay":{"type":"string"},"unitPrice":{"type":"number"},"unitPriceDisplay":{"type":"string"},"unitPriceInPayoutCurrency":{"type":"number"},"unitPriceInPayoutCurrencyDisplay":{"type":"string"},"unitPriceWithoutTax":{"type":"number"},"unitPriceWithoutTaxDisplay":{"type":"string"},"unitListPriceWithoutTax":{"type":"number"},"unitListPriceWithoutTaxDisplay":{"type":"string"}}}},"name":{"type":"string","examples":["Quote for ABC Company"]},"notes":{"type":"string","examples":["This is a Note"]},"netTermsDays":{"type":"integer","format":"int32","examples":[30],"minimum":-**********,"maximum":**********},"quoteUrl":{"type":"string","examples":["https://josemunoz.test.qa2.onfastspring.com/popup-defaultB2B/account/order/quote/QUVJIVYZTQDFBOBEO7IGNXER3VBQ"]},"recipient":{"type":"object","properties":{"company":{"type":"string"},"email":{"type":"string"},"first":{"type":"string"},"last":{"type":"string"},"phone":{"type":"string"},"userId":{"type":"string"}}},"recipientAddress":{"type":"object","properties":{"addressLine1":{"type":"string"},"addressLine2":{"type":"string"},"city":{"type":"string"},"country":{"type":"string"},"postalCode":{"type":"string"},"region":{"type":"string"}}},"siteId":{"type":"string","examples":["pOBehkkfTGo"]},"status":{"type":"string","enum":["OPEN","CANCELED","AWAITING_PAYMENT","COMPLETED","EXPIRED"],"examples":["OPEN"],"description":"`OPEN` `CANCELED` `AWAITING_PAYMENT` `COMPLETED` `EXPIRED`"},"statusHistory":{"uniqueItems":true,"type":"array","items":{"type":"object","properties":{"statusUpdatedTo":{"type":"string","enum":["OPEN","CANCELED","AWAITING_PAYMENT","COMPLETED","EXPIRED"],"examples":["OPEN"],"description":"`OPEN` `CANCELED` `AWAITING_PAYMENT` `COMPLETED` `EXPIRED`"},"statusUpdatedByFullName":{"type":"string","examples":["John Smith"]},"statusUpdatedByEmail":{"type":"string","examples":["<EMAIL>"]},"statusUpdatedOn":{"type":"string","format":"date-time","examples":["2021-03-02T19:48:56.395Z"]}}}},"subtotal":{"type":"number"},"subtotalDisplay":{"type":"string"},"subtotalInPayoutCurrency":{"type":"number"},"subtotalInPayoutCurrencyDisplay":{"type":"string"},"tags":{"type":"array","items":{"type":"object","properties":{"key":{"maxLength":255,"minLength":0,"type":"string","examples":["tag-key"]},"value":{"maxLength":255,"minLength":0,"type":"string","examples":["Tag Value"]}}}},"tax":{"type":"number"},"taxType":{"type":"string"},"total":{"type":"number"},"totalDisplay":{"type":"string"},"totalInPayoutCurrency":{"type":"number"},"totalInPayoutCurrencyDisplay":{"type":"string"},"updated":{"type":"string","format":"date-time","examples":["2021-03-02T19:48:56.395Z"]},"taxId":{"type":"string","examples":["BE09999999XX"]},"source":{"type":"string","examples":["MANAGER"]},"sourceIP":{"type":"string","examples":["*************"]},"isGrossTax":{"type":"boolean","examples":[false]},"invoiceId":{"type":"string"},"links":{"type":"array","items":{"type":"object","properties":{"rel":{"type":"string"},"href":{"type":"string"},"hreflang":{"type":"string"},"media":{"type":"string"},"title":{"type":"string"},"type":{"type":"string"},"deprecation":{"type":"string"},"profile":{"type":"string"},"name":{"type":"string"}}}}},"$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Updateasingleevent = {"body":{"title":"UpdateasingleeventRequest","type":"object","properties":{"processed":{"type":"boolean","examples":[true]}},"required":["processed"],"$schema":"http://json-schema.org/draft-04/schema#"},"metadata":{"allOf":[{"type":"object","properties":{"event_id":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Event Id"}},"required":["event_id"]}]},"response":{"200":{"type":"object","properties":{}}}} as const
;
const Updateexistingaccount = {"body":{"title":"CreateOneAccount","type":"object","properties":{"contact":{"type":"object","required":["email","first","last"],"properties":{"company":{"maxLength":255,"minLength":0,"type":"string","examples":["ABC Company"]},"email":{"maxLength":255,"minLength":0,"type":"string","examples":["<EMAIL>"]},"first":{"maxLength":255,"minLength":0,"type":"string","examples":["Leeroy"]},"last":{"maxLength":255,"minLength":0,"type":"string","examples":["Jenkins"]},"phone":{"maxLength":255,"minLength":0,"type":"string","examples":["+**********"]}}}},"$schema":"http://json-schema.org/draft-04/schema#"},"metadata":{"allOf":[{"type":"object","properties":{"account_id":{"type":"string","$schema":"http://json-schema.org/draft-04/schema#","description":"Account Id"}},"required":["account_id"]}]},"response":{"200":{"title":"CreateOneAccountResponse","$schema":"http://json-schema.org/draft-04/schema#"},"500":{"title":"CreateOneAccountError","$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
const Updateordertagsandattributes = {"body":{"title":"UpdateOrderTags","$schema":"http://json-schema.org/draft-04/schema#"},"response":{"200":{"oneOf":[{"title":"UpdateOrderTagsResponse"},{"title":"UpdateOrderTagsResponse2"}],"$schema":"http://json-schema.org/draft-04/schema#"}}} as const
;
export { Addcouponcodestoacoupon, CancelQuote, Cancelsubscriptioninstances, Changetheproductforanactivesubscription, ConvertExpiredTrialWithoutPaymentMethod, CreateQuote, Createanaccount, Createanewcoupon, Createasessionwithoutoverridinganydefaultvalues, Createoneormorenewproducts, Createorupdateproductoffers, DeleteProducts, DeleteQuote, Deleteallcouponcodesfromacoupon, DownloadReport, GenerateRevenueReport, GenerateSubscriptionReport, GetAllQuotes, GetJobById, GetJobs, GetQuoteById, Getalloffersforproductbyoffertype, Getallproductsprice, Getallproductspricewithcountry, Getallproductspricewithcountryandcurrency, Getallsubscriptioninstances, GetauthenticatedaccountmanagementUrl, Getcouponcodesassignedtoacoupon, Getlistofallproductids, Getoneaccount, Getoneormoresubscriptioninstances, Getoneormultiplereturns, GetordersbyId, Getordersbydaterange, Getordersbyenddate, Getordersbyproductdaterange, Getordersbyproductpath, Getordersbyreturn, Getprocessedevents, Getproductsbyid, Getspecificproductprice, Getspecificproductpricecountry, Getspecificproductpricecountrycurrency, Getsubscriptioninstanceentries, GetsubscriptionplanCChangehistory, Getunprocessedevents, LookUpAccountsbyParameters, Pauseasubscription, PostOneMoreOrdersReturns, Rebillmanagedsubscriptioninstance, ResetCache, Resumeapausedsubscription, Retrievecoupondetails, RotateWebhookKey, SubscriptoinProratePreviewEstimate, Uncancelasubscriptionpriortodeactivation, UpdateQuote, Updateasingleevent, Updateexistingaccount, Updateordertagsandattributes }
