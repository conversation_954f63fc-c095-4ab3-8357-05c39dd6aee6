import { bootstrap } from "./system/bootstrap.mjs";
import { microservice } from "./microservices/telemetry.microservice.mjs";
import express = require("express");
import bodyParser = require("body-parser");
import cors = require("cors");
import { config } from "./system/Config.mjs";
import { errorHandler } from "./system/errorHandler.mjs";
import { addTelemetryRoutes } from "./routes/telemetry.routes.mjs";
import { addHealthRoutes } from "./routes/telemetry.health.routes.mjs";
import { ids } from "./system/Strings.mjs";
import { makeError } from "./system/error.mjs";
import { parseUserAgent } from "./system/UserAgent.mjs";

let app = express();

bootstrap()
  .then(() => {
    return microservice.start();
  })
  .then(() => {
    app.set("etag", false);
    app.set("x-powered-by", false);
    app.set("lastModified", false);

    app.use(parseUserAgent);
    app.use(cors());
    app.use(express.json({ limit: "16mb" }));
    addHealthRoutes(app);
    addTelemetryRoutes(app);

    // not found catch all
    app.use((req, res, next) => next(makeError(404, ids.ROUTE_DOES_NOT_EXIST)));

    // global error handler
    app.use(errorHandler);

    app.listen(config.port, () => {
      console.log(
        `Cloud running on port ${config.port
        } started at ${new Date().toUTCString()}`
      );
      console.log(process.pid);
    });
  })
  .catch((err) => {
    console.error(JSON.stringify(err || {}));
    process.exit(666);
  });
