

create sequence a0.account_account_id;

create table a0.account(
    flags int NULL,
	modified_time timestamptz NULL,
	created_time timestamptz NULL,
	account_id int NOT NULL,
	t bytea NULL,
	cipher bytea NULL,
	client_cipher bytea NULL,
	client_cipher_version int NULL,
	min_build int NULL,
	peer_to_peer_key text NULL,
	client_peer_to_peer_key text NULL,
	client_peer_to_peer_key_version int NULL,
	rsa_private_key text NULL,
	x509_cert text NULL,
	tfa boolean NULL,
	idp text NOT NULL,
	sub text NOT NULL,
	email text NULL,
	password_hash text NULL,
	password_hash_version int NULL,
	salt text NULL,
	password_set_time timestamptz NULL,
	plan_id text NULL,
	role text NULL,
	device_limit int NULL,
	photo_limit int NULL,
	cloud_storage_limit int NULL,
	features int NULL,
	next_plan_date timestamptz NULL,
	available_upgrades text NULL,
	license_template_id text NULL,
	license_display_name text NULL,
	License_manager text NULL,
	license_flags int NULL,
	available_upgrades_features int NULL,
	license_id text NULL,
	affiliate_id text NULL
);

alter table a0.account
add primary key (account_id);

 
create unique index ux_account_by_sub_and_idp on a0.account(sub,idp);

 create index ix_account_by_email on a0.account(email);
create index ix_account_by_sub on a0.account(sub);

