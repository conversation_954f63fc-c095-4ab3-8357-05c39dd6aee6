import { query } from "../system/Postgres.mjs";
import { Context } from "../system/Context.mjs";
import { error } from "../system/error.mjs";
import crypto = require("crypto");
import { ids } from "../system/Strings.mjs";

export class LockService {
  public lock(
    context: Context,
    aid: number,
    key: string,
    timeout: number,
    ticket?: string
  ) {
    if (isNaN(timeout)) return error<string>(400, "TIMEOUT_MUST_BE_A_NUMBER");

    if (!ticket) ticket = crypto.randomBytes(20).toString("base64");

    return query<{ ticket: string }>(
      context,
      `insert into a0.lock(account_id, key, expire, ticket)
            values($1, $2, now() + ($4 || ' seconds')::interval, $3)
            on conflict(account_id, key)
            do update set
	            expire = EXCLUDED.expire,
                ticket = EXCLUDED.ticket
            where a0.lock.expire < now() or a0.lock.ticket = $3
            returning ticket`,
      [aid, key, ticket, timeout.toString()]
    ).then((rows) => {
      if (rows.length === 0) {
        return error<string>(400, ids.LOCKED);
      }
      return rows[0].ticket;
    });
  }

  public unlock(context: Context, aid: number, key: string, ticket: string) {
    return query<{ ticket: string }>(
      context,
      `delete from a0.lock
	        where account_id = $1 and key = $2
            and ticket=$3
            returning ticket`,
      [aid, key, ticket]
    ).then((rows) => {
      if (rows.length === 0) {
        return error<string>(400, "RELEASE_FAILED");
      }
      return rows[0].ticket;
    });
  }
}
