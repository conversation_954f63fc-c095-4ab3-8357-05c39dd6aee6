#pragma once

#include "./short_alloc.h"
#include "base64.h"
#include "MYHash.h"
#include "MYTrev.h"
#include <vector>
#include <deque>

class MYFastToStringHandler;

enum class BJsonType
{
    // Max value for this structure is 0xF - must fit into 4 bits as part of id

    null = 0x0,       // no data. Value is implied 0 or 'false'
    true1 = 0x1,      // no data. Value is implied 1 or 'true'
    uint8 = 0x2,      // data is uint8_t
    varint = 0x3,     // data is 7-bit packed integer
    varint_neg = 0x4, // data is ones' complement of 7-bit packed integer. -0 means std::numeric_limits<int64_t>::min().
    string = 0x5,     // data is string - varint prefixed length, followed by char array. Embedded nulls ok.
    float32 = 0x6,    // data is varint up to 4 bytes (IEEE 754 float in little endian)
    double64 = 0x7,   // data is varint up to 8 bytes (IEEE 754 double in little endian)
    array = 0x8,      // data is start of array
    object = 0x9,     // data is start of object
    end = 0xA,        // data is close of last object/array - key is implementation defined, and can't be more than 7
    hash = 0xB,       // data is MYHash (20 bytes)
    trev = 0xC,       // data is MYTRev (33 bytes)
    binary = 0xD,     // data is binary - varint prefixed length, followed by uint8_t array. Embedded nulls ok.
    separator = 0xE,  // jagged array separator - key is implementation defined, and can't be more than 7
    fixed32 = 0xF     // data is uint32_t stored as 4 bytes
};

// #if defined(DEBUG) || defined(_DEBUG)
#define MYBJsonIterator MYBJsonCheckedIterator
#define MYBJsonScopedIterator MYBJsonCheckedScopedIterator
// #else
// #define MYBJsonIterator MYBJsonUncheckedIterator
// #define MYBJsonScopedIterator MYBJsonUncheckedScopedIterator
// #endif

void bjson_bugcheck(const char *message);

#if _WIN32
#define BJSONINLINE __forceinline
#else
#define BJSONINLINE inline
#endif

// Key is of the form
// 0: marker bit (0 is 8-bit key, 1 is 16-bit key)
// For 8 bit key
//   0: marker bit
//   1-3: serialized id - sid (0 to 7)
//   4-7: type (BJsonType)
// For 16 bit key
//   0: marker bit
//   1-12:  serialized id - sid (8 to 2048). 0 to 7 is treated the same, but unused to avoid confusion.
//   13-16: type (BJsonType)
//
// PERFORMANCE NOTE:
// This element has no state. It directly points into the blob. This means it cannot
// cache certain information like type, key, length etc. I have tried a second implementation of
// MYBJsonElement where it DOES cache that information, but as a result it will have to double-deref
// the pointer. Doing that is slower than doing the recomputes all the time.
class MYBJsonElement
{
    // The 'this' pointer has to be pointing directly into the bjson blob so that address offsets
    // relative to this member works correctly. (Also why the copy constructor is deleted).
    uint8_t sidAndType;

private:
    uint8_t varIntLength(const uint8_t *varIntValue) const
    {
        const uint8_t *pos = varIntValue;
        for (int iteration = 0; iteration < 10; iteration++)
        {
            if (*pos < 0b10000000)
            {
                // No more bits
                return iteration + 1;
            }

            pos++;
        }

        bjson_bugcheck("varIntLength: More than 10 bytes in a varInt");
        return 0;
    }

    uint64_t readVarInt(const uint8_t *varIntValue, uint8_t *varIntLength = nullptr) const
    {
        const uint8_t *pos = varIntValue;
        uint64_t value = 0;
        int shiftLeftBy = 0;
        for (int iteration = 0; iteration < 10; iteration++)
        {
            assert(shiftLeftBy < 64);
            uint64_t real7bitValue = (*pos) & 0b01111111;
            if (real7bitValue == 0)
            {
                // not math needed for this one
            }
            else
            {
                uint64_t correctlyPositionedValue = real7bitValue << shiftLeftBy;
                value |= correctlyPositionedValue;
            }

            if (*pos < 0b10000000)
            {
                if (varIntLength)
                {
                    *varIntLength = iteration + 1;
                }
                // No more bits
                return value;
            }

            shiftLeftBy += 7;
            pos++;
        }

        // More than 10 bytes in the integer
        bjson_bugcheck("readVarInt: More than 10 bytes in a varInt");
        if (varIntLength)
        {
            *varIntLength = 10;
        }
        return value;
    }

    uint32_t readVarInt32(const uint8_t *varIntValue, uint8_t *varIntLength = nullptr) const
    {
        const uint8_t *pos = varIntValue;
        uint32_t value = 0;
        int shiftLeftBy = 0;
        for (int iteration = 0; iteration < 5; ++iteration)
        {
            assert(shiftLeftBy < 32);
            uint32_t real7bitValue = (*pos) & 0b01111111;
            if (real7bitValue == 0)
            {
                // not math needed for this one
            }
            else
            {
                uint32_t correctlyPositionedValue = real7bitValue << shiftLeftBy;
                value |= correctlyPositionedValue;
            }

            if (*pos < 0b10000000)
            {
                if (varIntLength)
                {
                    *varIntLength = iteration + 1;
                }
                // No more bits
                return value;
            }

            shiftLeftBy += 7;
            pos++;
        }

        assert(false); // did you mean to read using asUint64 instead?
        return (uint32_t)readVarInt(varIntValue, varIntLength);
    }

    uint64_t readVarIntBigEndian(const uint8_t *varIntValue, uint8_t *varIntLength = nullptr) const
    {
        return readVarInt(varIntValue, varIntLength);

        const uint8_t *pos = varIntValue;
        uint64_t value = 0;
        int shiftLeftBy = 57;
        for (int iteration = 0; iteration < 10; ++iteration)
        {
            uint64_t real7bitValue = (*pos) & 0b01111111;
            if (real7bitValue == 0)
            {
                // not math needed for this one
            }
            else
            {
                if (shiftLeftBy < 0)
                {
                    assert(((real7bitValue >> (0 - shiftLeftBy)) << (0 - shiftLeftBy)) == real7bitValue);
                    uint64_t correctlyPositionedValue = real7bitValue >> (0 - shiftLeftBy);
                    value |= correctlyPositionedValue;
                }
                else
                {
                    uint64_t correctlyPositionedValue = real7bitValue << shiftLeftBy;
                    value |= correctlyPositionedValue;
                }
            }

            if (*pos < 0b10000000)
            {
                if (varIntLength)
                {
                    *varIntLength = iteration + 1;
                }
                // No more bits
                return value;
            }

            shiftLeftBy -= 7;
            pos++;
        }

        // More than 10 bytes in the integer
        bjson_bugcheck("readVarIntBigEndian: More than 10 bytes in a varInt");
        if (varIntLength)
        {
            *varIntLength = 10;
        }
        return value;
    }

    uint32_t readVarIntBigEndian32(const uint8_t *varIntValue, uint8_t *varIntLength = nullptr) const
    {
        return readVarInt32(varIntValue, varIntLength);

        const uint8_t *pos = varIntValue;
        uint32_t value = 0;
        int shiftLeftBy = 25;
        for (int iteration = 0; iteration < 5; ++iteration)
        {
            uint32_t real7bitValue = (*pos) & 0b01111111;
            if (real7bitValue == 0)
            {
                // not math needed for this one
            }
            else
            {
                if (shiftLeftBy < 0)
                {
                    assert(((real7bitValue >> (0 - shiftLeftBy)) << (0 - shiftLeftBy)) == real7bitValue);
                    uint32_t correctlyPositionedValue = real7bitValue >> (0 - shiftLeftBy);
                    value |= correctlyPositionedValue;
                }
                else
                {
                    uint32_t correctlyPositionedValue = real7bitValue << shiftLeftBy;
                    value |= correctlyPositionedValue;
                }
            }

            if (*pos < 0b10000000)
            {
                if (varIntLength)
                {
                    *varIntLength = iteration + 1;
                }
                // No more bits
                return value;
            }

            shiftLeftBy -= 7;
            pos++;
        }

        assert(false); // did you mean to read using asDouble instead?
        return (uint32_t)readVarIntBigEndian(varIntValue, varIntLength);
    }

    bool hasShortKey() const
    {
        return (sidAndType & 0x80) == 0;
    }

    size_t keyExtraBytes() const
    {
        return sidAndType >> 7;
    }

    const uint8_t *offset(size_t n) const
    {
        return &sidAndType + n;
    }

    const uint8_t *data() const
    {
        return &sidAndType + ((sidAndType >> 7) + 1);
    }

public:
    MYBJsonElement(const MYBJsonElement &other) = delete;
    MYBJsonElement &operator==(const MYBJsonElement &other) = delete;

    class MYBJsonCheckedScopedIterator innerChecked(class MYBJsonIteratorBase &end) const;
    class MYBJsonUncheckedScopedIterator innerUnchecked(class MYBJsonIteratorBase &end) const;
    class MYBJsonScopedIterator inner(class MYBJsonIteratorBase &end) const;

    bool isSeparatorOrBegin() const
    {
        auto typ = type();
        return (typ == BJsonType::separator) || (typ == BJsonType::array) || (typ == BJsonType::object);
    }

    bool isSeparatorOrEnd() const
    {
        auto typ = type();
        if (typ == BJsonType::end)
        {
            return true;
        }

        return (typ == BJsonType::separator) || (typ == BJsonType::end);
    }

    bool isScopeEnd() const
    {
        auto typ = type();
        if (typ == BJsonType::end)
        {
            return true;
        }
        return false;
    }

    bool isScopeBegin() const
    {
        auto typ = type();
        if ((typ == BJsonType::array) || (typ == BJsonType::object))
        {
            return true;
        }
        return false;
    }

    int scopeAdjust() const
    {
        auto typ = type();
        if ((typ == BJsonType::array) || (typ == BJsonType::object))
        {
            return +1;
        }
        else if (typ == BJsonType::end)
        {
            return -1;
        }
        return 0;
    }

    BJsonType type() const
    {
        return (BJsonType)(sidAndType & 0x0F);
    }

    uint32_t key() const
    {
        // if (hasShortKey())
        if (sidAndType & 0x80)
        {
            int key = sidAndType & 0x70;
            key <<= 4;

            uint8_t sidAndType2 = ((uint8_t *)&sidAndType)[1];
            key |= sidAndType2;
            return key;
        }

        return (sidAndType & 0x70) >> 4;
    }

    template <typename T>
    T asType() const;

    bool asBool() const
    {
        switch (type())
        {
        case BJsonType::null:
            return false;
        case BJsonType::true1:
            return true;
        default:
            assert(false);
            return false;
        }
    }

    uint8_t asUint8() const
    {
        switch (type())
        {
        case BJsonType::null:
            return 0;
        case BJsonType::true1:
            return 1;
        case BJsonType::uint8:
            return uint8();
        case BJsonType::fixed32:
        {
            uint32_t varint = fixed32();
            assert(varint <= std::numeric_limits<uint8_t>::max());
            return (uint8_t)varint;
        }
        case BJsonType::varint:
        {
            uint32_t varint = uint32();
            assert(varint <= std::numeric_limits<uint8_t>::max());
            return (uint8_t)varint;
        }
        default:
            assert(false);
            return 0;
        }
    }

    uint32_t asUint32() const
    {
        switch (type())
        {
        case BJsonType::null:
            return 0;
        case BJsonType::true1:
            return 1;
        case BJsonType::uint8:
            return uint8();
        case BJsonType::varint:
        {
            uint32_t varint = uint32();
            assert(varint <= std::numeric_limits<uint32_t>::max());
            return varint;
        }
        case BJsonType::fixed32:
            return fixed32();
        default:
            assert(false);
            return 0;
        }
    }

    int32_t asInt32() const
    {
        switch (type())
        {
        case BJsonType::null:
            return 0;
        case BJsonType::true1:
            return 1;
        case BJsonType::uint8:
            return uint8();
        case BJsonType::varint:
        {
            uint32_t varint = uint32();
            return (int32_t)varint;
        }
        case BJsonType::varint_neg:
        {
            int32_t varint = int32();
            return (int32_t)varint;
        }
        case BJsonType::fixed32:
        {
            uint32_t varint = fixed32();
            return (int32_t)varint;
        }
        default:
            assert(false);
            return 0;
        }
    }

    uint64_t asUint64() const
    {
        switch (type())
        {
        case BJsonType::null:
            return 0;
        case BJsonType::true1:
            return 1;
        case BJsonType::uint8:
            return uint8();
        case BJsonType::fixed32:
            return fixed32();
        case BJsonType::varint:
        {
            return uint64();
        }
        default:
            assert(false);
            return 0;
        }
    }

    bool isInteger() const
    {
        switch (type())
        {
        case BJsonType::null:
        case BJsonType::true1:
        case BJsonType::uint8:
        case BJsonType::fixed32:
        case BJsonType::varint:
        case BJsonType::varint_neg:
            return true;
        default:
            return false;
        }
    }

    int64_t asInt64() const
    {
        switch (type())
        {
        case BJsonType::null:
            return 0;
        case BJsonType::varint:
        {
            uint64_t varint = uint64();
            assert(varint <= (uint64_t)std::numeric_limits<int64_t>::max());
            return varint;
        }
        case BJsonType::varint_neg:
        {
            int64_t varint = int64();
            assert((varint >= std::numeric_limits<int64_t>::min()) &&
                   (varint <= std::numeric_limits<int64_t>::max()));
            return varint;
        }
        case BJsonType::true1:
            return 1;
        case BJsonType::uint8:
            return uint8();
        case BJsonType::fixed32:
            return fixed32();
        default:
            assert(false);
            return 0;
        }
    }

    float asFloat() const
    {
        switch (type())
        {
        case BJsonType::null:
            return 0.0f;
        case BJsonType::float32:
        {
            return float32();
        }
        case BJsonType::double64:
        {
            double d = double64();
            assert((d >= std::numeric_limits<float>::min()) &&
                   (d <= std::numeric_limits<float>::max()));
            return (float)d;
        }
        case BJsonType::true1:
            return 1.0f;
        case BJsonType::uint8:
            return uint8();
        case BJsonType::fixed32:
            return (float)fixed32();
        case BJsonType::varint:
        {
            uint64_t varint = uint64();
            assert(varint <= (uint64_t)std::numeric_limits<float>::max());
            return (float)varint;
        }
        case BJsonType::varint_neg:
        {
            int64_t varint = int64_t();
            assert((varint >= std::numeric_limits<float>::min()) &&
                   (varint <= std::numeric_limits<float>::max()));
            return (float)varint;
        }
        default:
            assert(false);
            return 0;
        }
    }

    double asDouble() const
    {
        switch (type())
        {
        case BJsonType::double64:
        {
            return double64();
        }
        case BJsonType::float32:
        {
            return float32();
        }
        case BJsonType::null:
            return 0.0f;
        case BJsonType::true1:
            return 1.0f;
        case BJsonType::uint8:
            return uint8();
        case BJsonType::varint:
        {
            uint64_t varint = uint64();
            assert(varint <= (uint64_t)std::numeric_limits<double>::max());
            return (double)varint;
        }
        case BJsonType::varint_neg:
        {
            int64_t varint = int64_t();
            assert((varint >= std::numeric_limits<double>::min()) &&
                   (varint <= std::numeric_limits<double>::max()));
            return (double)varint;
        }
        case BJsonType::fixed32:
            return fixed32();
        default:
            assert(false);
            return 0;
        }
    }

    MYHash asHash() const
    {
        switch (type())
        {
        case BJsonType::null:
            return ::MYHash();
        case BJsonType::hash:
        {
            return hash();
        }
        default:
            assert(false);
            return ::MYHash();
        }
    }

    MYTRev asTrev() const
    {
        switch (type())
        {
        case BJsonType::null:
            return ::MYTRev();
        case BJsonType::trev:
        {
            return trev();
        }
        default:
            assert(false);
            return MYTRev();
        }
    }

    std::string asString() const
    {
        switch (type())
        {
        case BJsonType::null:
            return std::string();
        case BJsonType::string:
        {
            return string_copy();
        }
        default:
            assert(false);
            return std::string();
        }
    }

    std::string asBinaryBase64() const
    {
        switch (type())
        {
        case BJsonType::null:
            return std::string();
        case BJsonType::binary:
        {
            return binary_base64();
        }
        default:
            assert(false);
            return std::string();
        }
    }

    std::vector<uint8_t> asBinary() const
    {
        switch (type())
        {
        case BJsonType::null:
            return std::vector<uint8_t>();
        case BJsonType::binary:
        {
            return binary_copy();
        }
        default:
            assert(false);
            return std::vector<uint8_t>();
        }
    }

    std::vector<uint8_t> asBinaryFromScoped(const uint8_t *endBuffer) const
    {
        switch (type())
        {
        case BJsonType::null:
            return std::vector<uint8_t>();
        case BJsonType::object:
        case BJsonType::array:
        {
            return object_copy(endBuffer);
        }
        default:
            assert(false);
            return std::vector<uint8_t>();
        }
    }

    uint8_t uint8() const
    {
        assert(type() == BJsonType::uint8);
        return *data();
    }

    uint32_t fixed32() const
    {
        assert(type() == BJsonType::fixed32);

        uint32_t fixedInt32;
        memcpy(&fixedInt32, data(), sizeof(uint32_t));

        return fixedInt32;
    }

    uint32_t uint32() const
    {
        assert(type() == BJsonType::varint);
        return readVarInt32(data());
    }

    uint64_t uint64() const
    {
        assert(type() == BJsonType::varint);
        return readVarInt(data());
    }

    int32_t int32() const
    {
        assert(type() == BJsonType::varint || type() == BJsonType::varint_neg);

        uint32_t n = readVarInt32(data());

        if (type() == BJsonType::varint)
            return n;

        return (int32_t)~n;
    }

    int64_t int64() const
    {
        assert(type() == BJsonType::varint || type() == BJsonType::varint_neg);

        uint64_t n = readVarInt(data());

        if (type() == BJsonType::varint)
            return n;

        return (int64_t)~n;
    }

    std::pair<const uint8_t *, size_t> binary() const
    {
        assert(type() == BJsonType::binary);

        uint8_t prefixLen;
        size_t blobLength = (size_t)readVarInt(data(), &prefixLen);
        return std::make_pair((data() + prefixLen), blobLength);
    }

    std::vector<uint8_t> binary_copy() const
    {
        assert(type() == BJsonType::binary);

        uint8_t prefixLen;
        size_t blobLength = (size_t)readVarInt(data(), &prefixLen);

        std::vector<uint8_t> vec((data() + prefixLen), (data() + prefixLen) + blobLength);
        return vec;
    }

    std::vector<uint8_t> object_copy(const uint8_t *end) const;

    std::string binary_base64() const
    {
        assert(type() == BJsonType::binary);

        uint8_t prefixLen;
        size_t blobLength = (size_t)readVarInt(data(), &prefixLen);

        std::string ret = base64_encode(data() + prefixLen, blobLength);
        return ret;
    }

    std::pair<const char *, size_t> string() const
    {
        assert(type() == BJsonType::string);

        uint8_t prefixLen;
        size_t strlen = (size_t)readVarInt(data(), &prefixLen);
        return std::make_pair((const char *)(data() + prefixLen), strlen);
    }

    std::string string_copy() const
    {
        assert(type() == BJsonType::string);

        auto string_buffer = string();
        return std::string(string_buffer.first, string_buffer.second);
    }

    MYHash hash() const
    {
        assert(type() == BJsonType::hash);
        MYHash hash(data());
        return hash;
    }

    MYTRev trev() const
    {
        assert(type() == BJsonType::trev);
        MYTRev trev;
        memcpy(&trev, data(), sizeof(MYTRev));
        return trev;
    }

    double double64() const
    {
        uint64_t varInt = readVarIntBigEndian(data());
        double d;

        static_assert(sizeof(d) == sizeof(varInt), "Size mismatch");
        memcpy(&d, &varInt, sizeof(d));

        return d;
    }

    float float32() const
    {
        uint32_t varInt = readVarIntBigEndian32(data());
        float f;

        static_assert(sizeof(f) == sizeof(varInt), "Size mismatch");
        memcpy(&f, &varInt, sizeof(f));

        return f;
    }

    BJSONINLINE size_t length() const
    {
        // auto myType = type();
        // size_t type = ;
        switch ((BJsonType)(sidAndType & 0x0F))
        {
        case BJsonType::null:
        case BJsonType::true1:
        case BJsonType::array:
        case BJsonType::object:
            // return hasShortKey() ? 1 : 2;
            return keyExtraBytes() + 1;

        case BJsonType::separator:
        case BJsonType::end:
            return 1;

        case BJsonType::uint8:
            // return hasShortKey() ? 2 : 3;
            return keyExtraBytes() + 2;

        case BJsonType::fixed32:
            // return hasShortKey() ? 2 : 3;
            return keyExtraBytes() + 5;

        case BJsonType::varint:
        case BJsonType::varint_neg:
        case BJsonType::float32:
        case BJsonType::double64:
            // return (hasShortKey() ? 1 : 2) + varIntLength(data());
            return keyExtraBytes() + 1 + varIntLength(data());

        case BJsonType::hash:
            // return hasShortKey() ? 21 : 22;
            return keyExtraBytes() + 21;

        case BJsonType::trev:
            return keyExtraBytes() + 34;

        case BJsonType::string:
        case BJsonType::binary:
        {
            uint8_t prefixLen;
            uint64_t bloblen = readVarInt(data(), &prefixLen);
            // int length = (hasShortKey() ? 1 : 2) + prefixLen + (int)bloblen;
            size_t length = keyExtraBytes() + 1 + prefixLen + (int)bloblen;
            if (bloblen >= (std::numeric_limits<int>::max() - length))
            {
                bjson_bugcheck("length: binary/string array length invalid");
            }

            return length;
        }

        default:
            bjson_bugcheck("length: type invalid");
            return std::numeric_limits<int>::max();
        }
    }

    std::string toString() const;
};

class MYBJsonIteratorBase
    : public std::iterator<std::forward_iterator_tag, const MYBJsonElement>
{
protected:
    typedef const MYBJsonIteratorBase iterator;
    pointer pos_;

public:
    BJSONINLINE uint8_t *ptr() const
    {
        return (uint8_t *)pos_;
    }

    BJSONINLINE MYBJsonIteratorBase(std::nullptr_t) : pos_(nullptr) {}
    BJSONINLINE MYBJsonIteratorBase(pointer pos) : pos_(pos) {}
    BJSONINLINE MYBJsonIteratorBase(uint8_t *pos) : pos_((pointer)pos) {}

    BJSONINLINE reference operator*() const { return *pos_; }
    BJSONINLINE pointer operator->() const { return pos_; }
    BJSONINLINE bool operator==(const iterator &rhs) const
    {
        return pos_ == rhs.pos_;
    }
    BJSONINLINE bool operator!=(const iterator &rhs) const { return pos_ != rhs.pos_; }

    MYBJsonIteratorBase extent_or_end(MYBJsonIteratorBase end) const
    {
        if (pos_ == end.pos_)
        {
            return end;
        }

        uint8_t *nextPtr = (uint8_t *)pos_;
        auto next = (MYBJsonElement *)nextPtr;

        if (!next->isScopeBegin())
        {
            return end;
        }

        int level = 1;
        while (level != 0)
        {
            auto len = next->length();
            nextPtr += len;
            if (nextPtr >= (uint8_t *)end.pos_)
            {
                return end;
            }

            next = (MYBJsonElement *)nextPtr;
            if (next == end.pos_)
            {
                return end;
            }

            if (next->type() == BJsonType::end)
            {
                level--;
                if (level == 0)
                {
                    return MYBJsonIteratorBase(next);
                }
            }
            else if (next->type() == BJsonType::object)
            {
                level++;
            }
            else if (next->type() == BJsonType::array)
            {
                level++;
            }
        }

        return end;
    }
};

class MYBJsonUncheckedIterator
    : public MYBJsonIteratorBase
{
private:
    typedef MYBJsonIteratorBase base;
    typedef const MYBJsonUncheckedIterator iterator;

protected:
    MYBJsonUncheckedIterator(pointer begin) : base(begin) {}
    MYBJsonUncheckedIterator(const uint8_t *begin) : base((pointer)begin) {}

public:
    BJSONINLINE uint8_t *ptr() const
    {
        return (uint8_t *)pos_;
    }

    MYBJsonUncheckedIterator() : base(nullptr) {}
    MYBJsonUncheckedIterator(std::nullptr_t) : base(nullptr) {}
    MYBJsonUncheckedIterator(const iterator &iter) : base(iter.pos_) {}
    MYBJsonUncheckedIterator(pointer begin, pointer end) : base(begin) {}
    MYBJsonUncheckedIterator(const uint8_t *begin, const uint8_t *end) : base((pointer)begin) {}

    class MYBJsonUncheckedScopedIterator inner(MYBJsonUncheckedIterator end) const;

    iterator extent() const
    {
        uint8_t *nextPtr = (uint8_t *)pos_;
        auto next = (MYBJsonElement *)nextPtr;
        assert((next->type() == BJsonType::array) || (next->type() == BJsonType::object));

        int level = 1;
        while (level != 0)
        {
            nextPtr += next->length();

            next = (MYBJsonElement *)nextPtr;

            if (next->type() == BJsonType::end)
            {
                level--;
                if (level == 0)
                {
                    return iterator(next);
                }
            }
            else if (next->type() == BJsonType::object)
            {
                level++;
            }
            else if (next->type() == BJsonType::array)
            {
                level++;
            }
        }

        bjson_bugcheck("extent will run off end");
        return *this;
    }

    BJSONINLINE iterator operator++(int) /* postfix */
    {
        iterator pos(pos_);
        ++(*this);
        return pos;
    }

    BJSONINLINE iterator &operator++() /* prefix */
    {
        static_assert(sizeof(*pos_) == sizeof(uint8_t), "unexpected length");

        size_t len = pos_->length();
        pos_ = pos_ + len;

        return *this;
    }
};

class MYBJsonUncheckedScopedIterator
    : public MYBJsonUncheckedIterator
{
protected:
    typedef MYBJsonUncheckedIterator base;
    typedef const MYBJsonUncheckedScopedIterator iterator;
    pointer end_;

protected:
    MYBJsonUncheckedScopedIterator(pointer begin) : base(begin) {}
    MYBJsonUncheckedScopedIterator(const uint8_t *begin) : base((pointer)begin) {}

public:
    MYBJsonUncheckedScopedIterator() : base(nullptr), end_(nullptr) {}
    MYBJsonUncheckedScopedIterator(std::nullptr_t) : base(nullptr), end_(nullptr) {}
    MYBJsonUncheckedScopedIterator(const iterator &iter) : base(iter.ptr()), end_(iter.end_) {}
    MYBJsonUncheckedScopedIterator(pointer begin, pointer end) : base(begin), end_(end) {}
    MYBJsonUncheckedScopedIterator(const uint8_t *begin, const uint8_t *end) : base((pointer)begin), end_((pointer)end) {}

    class MYBJsonUncheckedIterator unscoped() const;
    MYBJsonUncheckedScopedIterator findKey(uint8_t key, bool nullOnNotFound = false) const;

    class MYBJsonUncheckedScopedIterator begin() const;
    class MYBJsonUncheckedScopedIterator end() const;

    BJSONINLINE iterator operator++(int) /* postfix */
    {
        iterator pos(ptr());
        ++(*this);
        return pos;
    }

    BJSONINLINE iterator &operator++() /* prefix */
    {
        static_assert(sizeof(*pos_) == sizeof(uint8_t), "unexpected length");

        if ((pos_->type() == BJsonType::object) || (pos_->type() == BJsonType::array))
        {
            pos_ = (pointer)extent().ptr();
            if (pos_->type() == BJsonType::end)
            {
                assert(pos_ != end_);
                ++pos_;
            }
            else
            {
                assert(false);
                pos_ = end_;
            }
        }
        else
        {
            size_t len = pos_->length();
            pos_ = pos_ + len;
        }

        if ((pos_ != end_) && (pos_->type() == BJsonType::end))
        {
            pos_ = end_;
        }

        return *this;
    }
};

class MYBJsonCheckedIterator
    : public MYBJsonIteratorBase
{
protected:
    typedef MYBJsonIteratorBase base;
    typedef const MYBJsonCheckedIterator iterator;
    pointer end_;

public:
    MYBJsonCheckedIterator() : base(nullptr), end_(nullptr) {}
    MYBJsonCheckedIterator(std::nullptr_t) : base(nullptr), end_(nullptr) {}
    MYBJsonCheckedIterator(const iterator &iter) : base(iter.pos_), end_(iter.end_) {}
    MYBJsonCheckedIterator(pointer begin, pointer end) : base(begin), end_(end) {}
    MYBJsonCheckedIterator(const uint8_t *begin, const uint8_t *end) : base((pointer)begin), end_((pointer)end) {}

    class MYBJsonCheckedScopedIterator inner(MYBJsonCheckedIterator end) const;

    MYBJsonCheckedIterator extent() const
    {
        if (pos_ == end_)
        {
            bjson_bugcheck("extent: called on end");
            return iterator(end_, end_);
        }

        uint8_t *nextPtr = (uint8_t *)pos_;
        auto next = (MYBJsonElement *)nextPtr;

        if (!next->isScopeBegin())
        {
            bjson_bugcheck("extent: called on something that is not a { or [");
            return iterator(end_, end_);
        }

        int level = 1;
        while (level != 0)
        {
            auto len = next->length();
            nextPtr += len;
            if (nextPtr >= (uint8_t *)end_)
            {
                bjson_bugcheck("extent: length is past end");
                return iterator(end_, end_);
            }

            next = (MYBJsonElement *)nextPtr;
            if (next == end_)
            {
                bjson_bugcheck("extent: unexpected end encountered");
                return iterator(end_, end_);
            }

            if (next->type() == BJsonType::end)
            {
                level--;
                if (level == 0)
                {
                    return iterator(next, end_);
                }
            }
            else if (next->type() == BJsonType::object)
            {
                level++;
            }
            else if (next->type() == BJsonType::array)
            {
                level++;
            }
        }

        bjson_bugcheck("extent: no end found");
        return iterator(end_, end_);
    }

    iterator operator++(int) /* postfix */
    {
        iterator pos(pos_, end_);
        ++(*this);
        return pos;
    }

    BJSONINLINE iterator &operator++() /* prefix */
    {
        if (pos_ >= end_)
        {
            bjson_bugcheck("++: would advance past the end");
        }

        static_assert(sizeof(*pos_) == sizeof(uint8_t), "unexpected length");

        size_t len = pos_->length();
        if (pos_ + len > end_)
        {
            bjson_bugcheck("++: length would advance past end");
        }

        pos_ = pos_ + len;

        if (pos_ != end_)
        {
            size_t len = pos_->length();
            if (pos_ + len > end_)
            {
                bjson_bugcheck("++: element returned will have invalid length");
            }
        }

        return *this;
    }
};

class MYBJsonCheckedScopedIterator
    : public MYBJsonCheckedIterator
{
protected:
    typedef MYBJsonCheckedIterator base;
    typedef const MYBJsonCheckedScopedIterator iterator;

public:
    MYBJsonCheckedScopedIterator() : base(nullptr) {}
    MYBJsonCheckedScopedIterator(const iterator &iter) : base(iter) {}
    MYBJsonCheckedScopedIterator(pointer begin, pointer end) : base(begin, end) {}
    MYBJsonCheckedScopedIterator(const uint8_t *begin, const uint8_t *end) : base(begin, end) {}

    class MYBJsonCheckedIterator unscoped() const;
    MYBJsonCheckedScopedIterator findKey(uint8_t key, bool nullOnNotFound = false) const;

    class MYBJsonCheckedScopedIterator begin() const;
    class MYBJsonCheckedScopedIterator end() const;

    BJSONINLINE iterator operator++(int) /* postfix */
    {
        iterator pos(pos_, end_);
        ++(*this);
        return pos;
    }

    BJSONINLINE iterator &operator++() /* prefix */
    {
        static_assert(sizeof(*pos_) == sizeof(uint8_t), "unexpected length");

        if (pos_ >= end_)
        {
            bjson_bugcheck("++: would advance past the end");
            return *this;
        }

        if ((pos_->type() == BJsonType::object) || (pos_->type() == BJsonType::array))
        {
            pos_ = (pointer)extent().ptr();
            if (pos_->type() == BJsonType::end)
            {
                ++pos_;
            }
            else
            {
                bjson_bugcheck("++: extent didn't find end");

                pos_ = end_;
                return *this;
            }
        }
        else
        {
            size_t len = pos_->length();
            if (pos_ + len > end_)
            {
                bjson_bugcheck("++: length would advance past end");
                pos_ = end_;
                return *this;
            }

            pos_ = pos_ + len;

            if (pos_ != end_)
            {
                size_t len = pos_->length();
                if (pos_ + len > end_)
                {
                    bjson_bugcheck("++: element returned will have invalid length");
                    pos_ = end_;
                    return *this;
                }
            }
        }

        if (pos_->type() == BJsonType::end)
        {
            pos_ = end_;
        }

        return *this;
    }
};

class MYBJson
{
protected:
    MYBJson(){};

    virtual MYBJsonIterator get_begin() const = 0;
    virtual MYBJsonIterator get_end() const = 0;
    virtual bool get_empty() const = 0;

public:
    virtual ~MYBJson() {}

    MYBJson(const MYBJson &other) = delete;
    MYBJson &operator=(const MYBJson &other) = delete;

    MYBJsonIterator begin() const
    {
        return get_begin();
    }

    MYBJsonIterator end() const
    {
        return get_end();
    }

    MYBJsonIterator null_iterator() const;

    uint8_t *pbegin() const
    {
        return get_begin().ptr();
    }

    uint8_t *pend() const
    {
        return get_end().ptr();
    }

    size_t psize() const
    {
        if (empty())
        {
            return 0;
        }
        return (pend() - pbegin());
    }

    bool empty() const
    {
        return get_empty();
    }

    class MYBJsonScopedIterator scoped() const;
    class MYBJsonCheckedScopedIterator scopedChecked() const;
    class MYBJsonUncheckedScopedIterator scopedUnchecked() const;

    template <typename TIter1, typename TIter2>
    static TIter1 findKey(TIter1 begin, TIter2 end, uint32_t key, bool nullOnMissing = false)
    {
        for (auto iter = begin; iter != end; ++iter)
        {
            if (iter->key() == key)
            {
                return iter;
            }
        }

        extern uint16_t zero16;
        return nullOnMissing ? TIter1((uint8_t *)&zero16, (uint8_t *)&zero16 + 1) : TIter1(end.ptr(), end.ptr());
    }

    template <typename TIter1, typename TIter2>
    static TIter1 findKey(TIter1 begin, TIter2 end, uint32_t key, BJsonType bjsonType, bool nullOnMissing = false)
    {
        for (auto iter = begin; iter != end; ++iter)
        {
            if ((iter->key() == key) && (iter->type() == bjsonType))
            {
                return iter;
            }
        }

        extern uint16_t zero16;
        return nullOnMissing ? TIter1((uint8_t *)&zero16, (uint8_t *)&zero16 + 1) : TIter1(end.ptr(), end.ptr());
    }

    MYBJsonIterator findKey(uint32_t key, bool nullOnMissing = false) const
    {
        for (auto iter = begin(); iter != end(); ++iter)
        {
            if (iter->key() == key)
            {
                return iter;
            }
        }

        return nullOnMissing ? null_iterator() : end();
    }

    MYBJsonIterator findKey(uint32_t key, BJsonType bjsonType, bool nullOnMissing = false) const
    {
        for (auto iter = begin(); iter != end(); ++iter)
        {
            if ((iter->key() == key) && (iter->type() == bjsonType))
            {
                return iter;
            }
        }

        return nullOnMissing ? null_iterator() : end();
    }

    bool operator==(const MYBJson &other) const
    {
        if (empty() && other.empty())
        {
            return true;
        }

        if (psize() != other.psize())
            return false;

        return memcmp(begin().ptr(), other.begin().ptr(), psize()) == 0;
    }

    // Separate these into 2 methods due to XCODE debugger that doesn't understand default parameter values
    std::string toJsonString() const
    {
        return toJsonStringInternal(false);
    }

    std::string toJsonStringPretty() const
    {
        return toJsonStringInternal(true);
    }

    // This method using shorct-circuitting method for searching inside the contents of the bjson.
    // The word to search for needs to be lowerCased.
    bool quickSearchUsingShortCircuit(const std::string &loweCasedText) const
    {
        return containsInBjsonUsingShortCircutting(false, loweCasedText);
    }

    std::string getFastToString(bool pretty) const;

private:
    bool containsInBjsonUsingShortCircutting(bool pretty, const std::string &findThisWord) const;

    std::string toJsonStringInternal(bool pretty) const;
};

class MYBJsonView final : public MYBJson
{
private:
    MYBJsonIterator begin_;
    MYBJsonIterator end_;

protected:
    virtual MYBJsonIterator get_begin() const override
    {
        return begin_;
    }

    virtual MYBJsonIterator get_end() const override
    {
        return end_;
    }

    virtual bool get_empty() const override
    {
        return begin_ == end_;
    }

public:
    MYBJsonView &operator=(const MYBJsonView &other)
    {
        begin_ = other.begin();
        end_ = other.end();
        return *this;
    }

    MYBJsonView(const MYBJson &other) : begin_(other.pbegin(), other.pend()), end_(other.pend(), other.pend()) {}
    MYBJsonView(const MYBJsonView &other) : MYBJson(), begin_(other.begin()), end_(other.end()) {}
    MYBJsonView(MYBJsonIterator begin, MYBJsonIterator end) : begin_(MYBJsonIterator(begin.ptr(), end.ptr())), end_(MYBJsonIterator(end.ptr(), end.ptr())) {}
    MYBJsonView(const uint8_t *begin, const uint8_t *end) : begin_(begin, end), end_(end, end)
    {
    }

    // constructor to match to simplify this constructor
    //
    // MYBJsonView view((uint8_t*)inputStdString.c_str(), (uint8_t*)inputStdString.c_str() + inputStdString.length());
    //
    MYBJsonView(const std::string &binaryDataVector) : MYBJsonView((uint8_t *)binaryDataVector.c_str(), (uint8_t *)binaryDataVector.c_str() + binaryDataVector.length())
    {
    }

    MYBJsonIterator begin() const
    {
        return begin_;
    }

    MYBJsonIterator end() const
    {
        return end_;
    }

    static std::shared_ptr<class MYBJson> emptyObjectPtr();
    static MYBJsonView emptyObject();
};

class MYBJsonOwnedView final : public MYBJson
{
private:
    MYBJsonIterator begin_;
    MYBJsonIterator end_;

protected:
    virtual MYBJsonIterator get_begin() const override
    {
        return begin_;
    }

    virtual MYBJsonIterator get_end() const override
    {
        return end_;
    }

    virtual bool get_empty() const override
    {
        return begin_ == end_;
    }

public:
    MYBJsonOwnedView(const MYBJsonOwnedView &other) = delete;
    MYBJsonOwnedView &operator=(const MYBJsonOwnedView &other) = delete;

    MYBJsonOwnedView &operator=(MYBJsonOwnedView &&other)
    {
        std::swap(begin_, other.begin_);
        std::swap(end_, other.end_);
        return *this;
    }

    MYBJsonOwnedView(MYBJsonOwnedView &&other) : begin_(other.begin_), end_(other.end_)
    {
        other.begin_ = MYBJsonIterator();
        other.end_ = MYBJsonIterator();
    }

    MYBJsonOwnedView(const uint8_t *begin, const uint8_t *end) : begin_(begin, end), end_(end, end)
    {
    }

    ~MYBJsonOwnedView()
    {
        auto ptr = begin().ptr();
        if (ptr)
        {
            delete[] ptr;
        }
    }

    MYBJsonIterator begin() const
    {
        return begin_;
    }

    MYBJsonIterator end() const
    {
        return end_;
    }
};

class MYBJsonStringView final : public MYBJson
{
private:
    MYBJsonIterator begin_;
    MYBJsonIterator end_;
    std::string data_;

protected:
    virtual MYBJsonIterator get_begin() const override
    {
        return begin_;
    }

    virtual MYBJsonIterator get_end() const override
    {
        return end_;
    }

    virtual bool get_empty() const override
    {
        return begin_ == end_;
    }

public:
    MYBJsonStringView(const MYBJsonOwnedView &other) = delete;
    MYBJsonStringView &operator=(const MYBJsonOwnedView &other) = delete;

    MYBJsonStringView &operator=(MYBJsonStringView &&other)
    {
        std::swap(begin_, other.begin_);
        std::swap(end_, other.end_);
        std::swap(data_, other.data_);
        return *this;
    }

    MYBJsonStringView(MYBJsonStringView &&other) : begin_(other.begin_), end_(other.end_)
    {
        other.begin_ = MYBJsonIterator();
        other.end_ = MYBJsonIterator();
        data_ = std::move(other.data_);
    }

    MYBJsonStringView(std::string &&s)
    {
        if (!s.empty())
        {
            data_ = std::move(s);
            const uint8_t *begin = (const uint8_t *)data_.c_str();
            const uint8_t *end = (const uint8_t *)data_.c_str() + data_.length();
            begin_ = MYBJsonIterator(begin, end);
            end_ = MYBJsonIterator(end, end);
        }
        else
        {
            begin_ = MYBJson::null_iterator();
            end_ = MYBJson::null_iterator();
        }
    }

    ~MYBJsonStringView()
    {
    }

    MYBJsonIterator begin() const
    {
        return begin_;
    }

    MYBJsonIterator end() const
    {
        return end_;
    }
};

template <typename TBuffer>
class MYBJsonRWBase final : public MYBJson, public TBuffer
{
protected:
    virtual MYBJsonIterator get_begin() const override
    {
        return begin();
    }

    virtual MYBJsonIterator get_end() const override
    {
        return end();
    }

    virtual bool get_empty() const override
    {
        return TBuffer::getEmpty();
    }

public:
    // Initialization
    MYBJsonRWBase() {}
    MYBJsonRWBase(const uint8_t *begin, const uint8_t *end) : TBuffer(begin, end) {}
    MYBJsonRWBase(const MYBJson &other) : TBuffer(other.pbegin(), other.pend()) {}

    // Copy/Move
    MYBJsonRWBase(MYBJsonRWBase &&other) : TBuffer(std::move(other)) {}
    MYBJsonRWBase(const MYBJsonRWBase &other) : TBuffer(other) {}

    // Assignment
    MYBJsonRWBase &operator=(MYBJsonRWBase &&other)
    {
        TBuffer::operator=(std::move(other));
        return *this;
    }

    MYBJsonRWBase &operator=(const MYBJsonRWBase &other)
    {
        TBuffer::operator=(other);
        return *this;
    }

    MYBJsonIterator begin() const
    {
        if (TBuffer::getBuffer().empty())
        {
            return MYBJsonIterator();
        }
        MYBJsonElement *begin = (MYBJsonElement *)&(*TBuffer::getBuffer().begin());
        MYBJsonElement *end = begin + TBuffer::getBuffer().size();
        auto iter = MYBJsonIterator(begin, end);
        return iter;
    }

    MYBJsonIterator end() const
    {
        if (TBuffer::getBuffer().empty())
        {
            return MYBJsonIterator();
        }

        MYBJsonElement *begin = (MYBJsonElement *)&(*TBuffer::getBuffer().begin());
        MYBJsonElement *end = begin + TBuffer::getBuffer().size();
        return MYBJsonIterator(end, end);
    }

    std::string toString(SerializationFlags flags)
    {
        if (flags & SerializationFlags::Base64Hash)
        {
            return base64_encode(begin().ptr(), psize());
        }

        assert(false);
        return "";
    }

    bool Key(const char *str, size_t length, bool copy = false)
    {
        int key = atoi(str);
        return Key(key);
    }

    bool Key(uint16_t shortId)
    {
#if defined(DEBUG) || defined(_DEBUG)
        assert(shortId < 2048); // Keys have to be smaller than 2048
#endif
        TBuffer::setShortId(shortId);
        return true;
    }

    void Type(BJsonType type)
    {
        if (TBuffer::getShortId() < 8)
        {
            // 1 byte format:
            // HIII TTTT
            // H -> Hi bit - 0 if set to 1 byte format
            // I -> ID bits (0b000 to 0b111)
            // T -> Type bits
            uint8_t sidAndType = (uint8_t)(TBuffer::getShortId() << 4) | (uint8_t)type;
            assert((sidAndType & 0x80) == 0);
            TBuffer::getBuffer().push_back(sidAndType);
        }
        else if (TBuffer::getShortId() <= 0x7FF)
        {
            assert(type != BJsonType::end);
            assert(type != BJsonType::separator);

            // 2 byte format:
            // HIII TTTT  LLLLLLLL
            // H -> Hi bit - 1 if set to 2 byte format
            // I -> ID bits (high 3 bits)
            // T -> Type bits
            // L -> ID bits (lower 256 bits)
            uint8_t keyHi = (uint8_t)((TBuffer::getShortId() >> 4) & 0x70);
            assert((keyHi & 0b10001111) == 0);
            uint8_t sidAndType1 = 0x80 | keyHi | (uint8_t)type;
            uint8_t sidAndType2 = (uint8_t)(TBuffer::getShortId() & 0xFF);

            TBuffer::getBuffer().push_back(sidAndType1);
            TBuffer::getBuffer().push_back(sidAndType2);
        }
        else
        {
            assert(false);
        }

        TBuffer::setShortId(0);
    }

    bool addMember(uint16_t key, std::nullptr_t) { return Null(key); }
    bool Null()
    {
        Type(BJsonType::null);
        return true;
    }
    bool Null(uint16_t key)
    {
        Key(key);
        return Null();
    }

    bool Separator()
    {
        Type(BJsonType::separator);
        return true;
    }
    bool Separator(uint16_t key)
    {
        assert(key <= 7);
        Key(key);
        return Separator();
    }

    bool addMember(uint16_t key, bool v) { return Bool(key, v); }
    bool Bool(bool b)
    {
        if (b)
        {
            Type(BJsonType::true1);
        }
        else
        {
            Type(BJsonType::null);
        }

        return true;
    }

    bool Bool(uint16_t key, bool b)
    {
        Key(key);
        return Bool(b);
    }

    bool writeVarIntVal(uint32_t value)
    {
        while (true)
        {
            if (value < 0x80) // fits in 7 bits - just write it
            {
                TBuffer::getBuffer().push_back((uint8_t)value);
                break;
            }
            else // take 7 bits, mark high bit to indicate there is more
            {
                uint8_t charValue = (uint8_t)(value | 0x80);

                TBuffer::getBuffer().push_back(charValue);

                value = value >> 7;
            }
        }
        return true;
    }

    bool writeVarIntVal(uint64_t value)
    {
        while (true)
        {
            if (value < 0x80) // fits in 7 bits - just write it
            {
                TBuffer::getBuffer().push_back((uint8_t)value);
                break;
            }
            else // take 7 bits, mark high bit to indicate there is more
            {
                uint8_t charValue = (uint8_t)(value | 0x80);

                TBuffer::getBuffer().push_back(charValue);

                value = value >> 7;
            }
        }
        return true;
    }

    bool writeVarIntValBigEndian(uint32_t value)
    {
        return writeVarIntVal(value);

        while (true)
        {
            if ((value & 0x01FFFFFF) == 0) // fits in 7 bits - just write it
            {
                TBuffer::getBuffer().push_back((uint8_t)(value >> 25));
                break;
            }
            else // take 7 bits, mark high bit to indicate there is more
            {
                uint8_t charValue = (uint8_t)(0x80 | (uint8_t)(value >> 25));

                TBuffer::getBuffer().push_back(charValue);

                value = value << 7;
            }
        }
        return true;
    }

    bool writeVarIntValBigEndian(uint64_t value)
    {
        return writeVarIntVal(value);

        while (true)
        {
            if ((value & 0x01FFFFFFFFFFFFFFull) == 0) // fits in 7 bits - just write it
            {
                TBuffer::getBuffer().push_back((uint8_t)(value >> 57));
                break;
            }
            else // take 7 bits, mark high bit to indicate there is more
            {
                uint8_t charValue = (uint8_t)(0x80 | (uint8_t)(value >> 57));

                TBuffer::getBuffer().push_back(charValue);

                value = value << 7;
            }
        }
        return true;
    }

    bool writeInteger(uint32_t i)
    {
        if (i <= 0xFF)
        {
            if (i == 0)
            {
                Type(BJsonType::null);
            }
            else if (i == 1)
            {
                Type(BJsonType::true1);
            }
            else
            {
                Type(BJsonType::uint8);
                TBuffer::getBuffer().push_back((uint8_t)i);
            }
        }
        else if (i >= 0x3FFF)
        {
            uint8_t *ptrI = (uint8_t *)&i;
            Type(BJsonType::fixed32);
            static_assert(sizeof(uint32_t) == 4, "Platform not supported");
            TBuffer::getBuffer().insert(TBuffer::getBuffer().end(), ptrI, ptrI + sizeof(uint32_t));
        }
        else
        {
            Type(BJsonType::varint);
            writeVarIntVal(i);
        }

        return true;
    }

    bool writeInteger(uint64_t i)
    {
        if (i <= 0xFF)
        {
            if (i == 0)
            {
                Type(BJsonType::null);
            }
            else if (i == 1)
            {
                Type(BJsonType::true1);
            }
            else
            {
                Type(BJsonType::uint8);
                TBuffer::getBuffer().push_back((uint8_t)i);
            }
        }
        else if ((i >= 0x3FFF) && (i < 0xFFFFFFFF))
        {
            uint32_t i32 = (uint32_t)i;
            uint8_t *ptrI = (uint8_t *)&i32;
            Type(BJsonType::fixed32);
            static_assert(sizeof(uint32_t) == 4, "Platform not supported");
            TBuffer::getBuffer().insert(TBuffer::getBuffer().end(), ptrI, ptrI + sizeof(uint32_t));
        }
        else
        {
            Type(BJsonType::varint);
            writeVarIntVal(i);
        }

        return true;
    }

    bool writeNegativeInteger(uint32_t i) // Caller needs to pass in the ones' complement of the number, this only special-cases the type
    {
        Type(BJsonType::varint_neg);
        writeVarIntVal(i);

        return true;
    }

    bool writeNegativeInteger(uint64_t i) // Caller needs to pass in the ones' complement of the number, this only special-cases the type
    {
        Type(BJsonType::varint_neg);
        writeVarIntVal(i);

        return true;
    }

    bool addMember(uint16_t key, int32_t v) { return Int32(key, v); }
    bool Int32(int32_t i)
    {
        if (i < 0)
        {
            return writeNegativeInteger((uint32_t)~i);
        }
        return writeInteger((uint32_t)i);
    }

    bool Int32(uint16_t key, int32_t i)
    {
        Key(key);
        return Int32(i);
    }

    bool addMember(MYBJsonIterator &iter)
    {
        const auto &element = *iter;
        switch (element.type())
        {
        case BJsonType::null:
            return Null(element.key());
        case BJsonType::separator:
            return Separator(element.key());
        case BJsonType::true1:
            return addMember(element.key(), true);
        case BJsonType::array:
        {
            auto begin = iter;
            auto next = iter.extent();

            auto end = next;
            end++;
            MYBJsonView view(begin, end);

            iter = next;
            return ExistingArray(begin->key(), view);
        }

        case BJsonType::object:
        {
            auto begin = iter;
            auto next = iter.extent();

            auto end = next;
            end++;
            MYBJsonView view(begin, end);

            iter = next;
            return ExistingObject(begin->key(), view);
        }

        case BJsonType::end:
            assert(false);
            return true;

        case BJsonType::uint8:
            return addMember(element.key(), element.uint8());

        case BJsonType::fixed32:
            return addMember(element.key(), element.fixed32());

        case BJsonType::varint:
            return addMember(element.key(), element.asUint64());

        case BJsonType::varint_neg:
            return addMember(element.key(), element.asInt64());

        case BJsonType::float32:
            return addMember(element.key(), element.asFloat());

        case BJsonType::double64:
            return addMember(element.key(), element.asDouble());

        case BJsonType::hash:
            return addMember(element.key(), element.asHash());

        case BJsonType::trev:
            return addMember(element.key(), element.asTrev());

        case BJsonType::binary:
            return addMember(element.key(), element.asBinary());

        case BJsonType::string:
            return addMember(element.key(), element.string_copy());

        default:
            assert(false);
        }
        return false;
    }

    bool Fixed32(uint32_t u)
    {
        Type(BJsonType::fixed32);
        uint8_t *ptrI = (uint8_t *)&u;
        static_assert(sizeof(uint32_t) == 4, "Platform not supported");
        TBuffer::getBuffer().insert(TBuffer::getBuffer().end(), ptrI, ptrI + sizeof(uint32_t));
        return true;
    }

    bool Fixed32(uint16_t key, uint32_t u)
    {
        Key(key);
        return Fixed32(u);
    }

    bool addMember(uint16_t key, uint32_t v) { return Uint32(key, v); }
    bool Uint32(uint32_t u) { return writeInteger(u); }
    bool Uint32(uint16_t key, uint32_t u)
    {
        Key(key);
        return Uint32(u);
    }

    bool addMember(uint16_t key, int64_t v) { return Int64(key, v); }
    bool Int64(int64_t i64)
    {
        if (i64 < 0)
        {
            return writeNegativeInteger(~(uint64_t)i64);
        }

        return writeInteger((uint64_t)i64);
    }

    bool Int64(uint16_t key, int64_t i64)
    {
        Key(key);
        return Int64(i64);
    }

    bool addMember(uint16_t key, uint64_t v) { return Uint64(key, v); }
    bool Uint64(uint64_t u64) { return writeInteger(u64); }
    bool Uint64(uint16_t key, uint64_t u64)
    {
        Key(key);
        return Uint64(u64);
    }

    bool addMember(uint16_t key, float v) { return Double(key, v); }
    bool Double(float d)
    {
        static_assert(sizeof(d) == 4, "float on this platform not 4 bytes");
        if (d == 0.0f)
        {
            Type(BJsonType::null);
        }
        else if (d == 1.0f)
        {
            Type(BJsonType::true1);
        }
        else
        {
            uint32_t n;

            static_assert(sizeof(d) == sizeof(n), "Size mismatch");
            memcpy(&n, &d, sizeof(n));

            Type(BJsonType::float32);
            writeVarIntValBigEndian(n);
        }

        return true;
    }
    bool Double(uint16_t key, float d)
    {
        Key(key);
        return Double(d);
    }

    bool addMember(uint16_t key, double v) { return Double(key, v); }
    bool Double(double d)
    {
        static_assert(sizeof(d) == 8, "double on this platform not 8 bytes");

        float f = (float)d;
        if (f == d)
        {
            return Double(f); // Store as a float32 instead
        }

        uint64_t n;

        static_assert(sizeof(d) == sizeof(n), "Size mismatch");
        memcpy(&n, &d, sizeof(n));

        Type(BJsonType::double64);
        writeVarIntValBigEndian(n);

        return true;
    }
    bool Double(uint16_t key, double d)
    {
        Key(key);
        return Double(d);
    }

    bool Raw(uint8_t *begin, uint8_t *end)
    {
        TBuffer::getBuffer().reserve(TBuffer::getBuffer().size() + (end - begin));
        TBuffer::getBuffer().insert(TBuffer::getBuffer().end(), begin, end);
        return true;
    }

    bool Raw(uint8_t *data, size_t length)
    {
        TBuffer::getBuffer().reserve(TBuffer::getBuffer().size() + length);
        TBuffer::getBuffer().insert(TBuffer::getBuffer().end(), data, data + length);
        return true;
    }

    bool Raw(const uint8_t *begin, const uint8_t *end)
    {
        TBuffer::getBuffer().reserve(TBuffer::getBuffer().size() + (end - begin));
        TBuffer::getBuffer().insert(TBuffer::getBuffer().end(), begin, end);
        return true;
    }

    bool addMember(uint16_t key, const MYHash &hash) { return MYHash(key, hash); }
    bool MYHash(const MYHash &hash)
    {
        if (hash.empty())
        {
            return Null();
        }

        Type(BJsonType::hash);
        TBuffer::getBuffer().insert(TBuffer::getBuffer().end(), hash.raw.begin(), hash.raw.end());

        return true;
    }
    bool MYHash(uint16_t key, const ::MYHash &hash)
    {
        Key(key);
        return MYHash(hash);
    }

    bool addMember(uint16_t key, const MYTRev &hash) { return MYTRev(key, hash); }
    bool MYTRev(const MYTRev &trev)
    {
        if (trev.empty())
        {
            return Null();
        }

        Type(BJsonType::trev);
        TBuffer::getBuffer().insert(TBuffer::getBuffer().end(), (uint8_t *)&trev, ((uint8_t *)&trev) + sizeof(::MYTRev));

        return true;
    }
    bool MYTRev(uint16_t key, const ::MYTRev &hash)
    {
        Key(key);
        return MYTRev(hash);
    }

    bool String(const char *str, size_t length, bool copy = false)
    {
        if (length == 0)
        {
            return Null();
        }

        Type(BJsonType::string);
        writeVarIntVal((uint64_t)length);

        TBuffer::getBuffer().insert(TBuffer::getBuffer().end(), str, str + length);

        return true;
    }
    bool String(uint16_t key, const char *str, size_t length, bool copy = false)
    {
        Key(key);
        return String(str, length, copy);
    }

    bool addMember(uint16_t key, const char *ch) { return String(key, ch); }
    bool addMember(uint16_t key, const std::string &v) { return String(key, v); }

    bool String(const char *str) { return String(str, strlen(str)); }
    bool String(const std::string &str) { return String(str.c_str(), (size_t)str.length()); }

    bool String(uint16_t key, const char *str)
    {
        Key(key);
        return String(str);
    }

    bool String(uint16_t key, const std::string &str)
    {
        Key(key);
        return String(str);
    }

    bool addMember(uint16_t key, const std::vector<uint8_t> &vec, BJsonType binaryType = BJsonType::binary) { return Binary(key, &vec[0], vec.size(), binaryType); }
    bool Binary(const uint8_t *raw, size_t length, BJsonType binaryType = BJsonType::binary)
    {
        Type(binaryType);
        writeVarIntVal((uint64_t)length);

        TBuffer::getBuffer().insert(TBuffer::getBuffer().end(), raw, raw + length);

        return true;
    }
    bool Binary(uint16_t key, const uint8_t *raw, size_t length, BJsonType binaryType = BJsonType::binary)
    {
        Key(key);
        return Binary(raw, length, binaryType);
    }

    bool BinaryBase64(const char *base64, size_t length)
    {
        Type(BJsonType::binary);
        auto binaryLength = binary_length_from_base64(base64, length);
        writeVarIntVal((uint64_t)binaryLength);
        base64_decode(base64, length, TBuffer::getBuffer());
        return true;
    }

    bool BinaryBase64(uint16_t key, const char *base64, size_t length)
    {
        Key(key);
        return BinaryBase64(base64, length);
    }

    bool WrapWithObject(const MYBJson &other)
    {
        MYBJsonView view(other.pbegin(), other.pend());
        auto iter = view.pbegin();
        if (iter == view.pend())
        {
            return Null();
        }

        Type(BJsonType::object);
        TBuffer::getBuffer().insert(TBuffer::getBuffer().end(), iter, view.pend());
        EndObject();
        return true;
    }

    bool WrapWithObject(uint16_t key, const MYBJson &other)
    {
        Key(key);
        return WrapWithObject(other);
    }

    bool ExistingObject(const MYBJson &other)
    {
        MYBJsonView view(other.pbegin(), other.pend());

        auto iter = view.pbegin();
        if (iter == view.pend())
        {
            return Null();
        }

        if (view.begin()->type() == BJsonType::object)
        {
            Type(BJsonType::object);
            if (*iter & 0x80) // Long key
            {
                ++iter;
            }
            ++iter;

            if (iter == view.pend())
            {
                EndObject();
            }
            else
            {
                TBuffer::getBuffer().insert(TBuffer::getBuffer().end(), iter, view.pend());
            }
            return true;
        }

        assert(false);
        return Null();
    }

    bool ExistingObject(uint16_t key, const MYBJson &other)
    {
        Key(key);
        return ExistingObject(other);
    }

    bool StartObject()
    {
        Type(BJsonType::object);
        return true;
    }
    bool StartObject(uint16_t key)
    {
        Key(key);
        return StartObject();
    }

    bool RewindEndObject()
    {
        assert(*pend() == (uint8_t)BJsonType::end);
        assert(TBuffer::getBuffer().size() > 1);
        TBuffer::getBuffer().resize(TBuffer::getBuffer().size() - 1);
        return true;
    }

    bool EndObject(size_t memberCount = 0)
    {
        Type(BJsonType::end);
        return true;
    }

    bool ExistingArray(const MYBJson &other)
    {
        MYBJsonView view(other.pbegin(), other.pend());

        auto iter = view.pbegin();
        if (iter == view.pend())
        {
            return Null();
        }

        if (view.begin()->type() == BJsonType::array)
        {
            Type(BJsonType::array);
            if (*iter & 0x80) // Long key
            {
                ++iter;
            }
            ++iter;

            if (iter == view.pend())
            {
                EndArray();
            }
            else
            {
                TBuffer::getBuffer().insert(TBuffer::getBuffer().end(), iter, view.pend());
            }
            return true;
        }
        assert(false);
        return Null();
    }

    bool ExistingArray(uint16_t key, const MYBJson &other)
    {
        Key(key);
        return ExistingArray(other);
    }

    bool WrapWithArray(const MYBJson &other)
    {
        MYBJsonView view(other.pbegin(), other.pend());

        auto iter = view.pbegin();
        if (iter == view.pend())
        {
            return Null();
        }

        Type(BJsonType::array);
        TBuffer::getBuffer().insert(TBuffer::getBuffer().end(), iter, view.pend());
        EndArray();
        return Null();
    }

    bool WrapWithArray(uint16_t key, const MYBJson &other)
    {
        Key(key);
        return WrapWithArray(other);
    }

    bool StartArray()
    {
        Type(BJsonType::array);
        return true;
    }
    bool StartArray(uint16_t key)
    {
        Key(key);
        return StartArray();
    }
    bool EndArray(size_t memberCount = 0)
    {
        Type(BJsonType::end);
        return true;
    }
};

// This has no move semantics. Use only on the stack.
template <std::size_t bufferSize>
class MYBJsonStackBuffer
{
private:
    uint16_t _currentId = 0;

    typedef std::vector<uint8_t, short_alloc<uint8_t, bufferSize, alignof(size_t)>> smallvec;
    typename smallvec::allocator_type::arena_type _buffer_embed;
    smallvec _buf;

protected:
    BJSONINLINE smallvec &getBuffer() { return _buf; }
    BJSONINLINE const smallvec &getBuffer() const { return _buf; }
    BJSONINLINE uint16_t getShortId() const { return _currentId; }
    BJSONINLINE void setShortId(uint16_t id) { _currentId = id; }
    BJSONINLINE bool getEmpty() const
    {
        return _buf.empty();
    }

    MYBJsonStackBuffer() : _buf(_buffer_embed) { _buf.reserve(bufferSize); }
    MYBJsonStackBuffer(const uint8_t *begin, const uint8_t *end) : _buf(begin, end, _buffer_embed) {}

    MYBJsonStackBuffer(MYBJsonStackBuffer &&other) : _buf(other._buf.begin(), other._buf.end(), _buffer_embed), _currentId(other._currentId) {}
    MYBJsonStackBuffer(const MYBJsonStackBuffer &other) : _buf(other._buf.begin(), other._buf.end(), _buffer_embed), _currentId(other._currentId) {}

    MYBJsonStackBuffer &operator=(MYBJsonStackBuffer &&other)
    {
        _currentId = other._currentId;
        _buf.insert(_buf.begin(), other._buf.begin(), other._buf.end());
        return *this;
    }

    MYBJsonStackBuffer &operator=(const MYBJsonStackBuffer &other)
    {
        _currentId = other._currentId;
        _buf.insert(_buf.begin(), other._buf.begin(), other._buf.end());
        return *this;
    }

public:
    void clear()
    {
        _buf.clear();
    }
};

// Represented as a pointer value.
// Does not allocate any storage until used, but then has to allocate double-indirected storage
class MYBJsonLazyBuffer
{
private:
    struct HeapBuffer
    {
        uint16_t _currentId = 0;
        std::vector<uint8_t> _buf;
    };

    mutable HeapBuffer *_ptr;

    HeapBuffer *getPtr() const
    {
        if (!_ptr)
        {
            _ptr = new HeapBuffer();
        }
        return _ptr;
    }

protected:
    BJSONINLINE std::vector<uint8_t> &getBuffer()
    {
        return getPtr()->_buf;
    }
    BJSONINLINE const std::vector<uint8_t> &getBuffer() const
    {
        return getPtr()->_buf;
    }
    BJSONINLINE uint16_t getShortId() const
    {
        return getPtr()->_currentId;
    }
    BJSONINLINE void setShortId(uint16_t id)
    {
        getPtr()->_currentId = id;
    }
    BJSONINLINE bool getEmpty() const
    {
        return _ptr == nullptr || _ptr->_buf.empty();
    }

    MYBJsonLazyBuffer() : _ptr(nullptr){};
    MYBJsonLazyBuffer(const uint8_t *begin, const uint8_t *end)
    {
        if (begin != end)
        {
            _ptr = new HeapBuffer();
            _ptr->_buf.insert(_ptr->_buf.end(), begin, end);
        }
        else
        {
            _ptr = nullptr;
        }
    }

    MYBJsonLazyBuffer(MYBJsonLazyBuffer &&other)
    {
        std::swap(_ptr, other._ptr);
    }

    MYBJsonLazyBuffer(const MYBJsonLazyBuffer &other)
    {
        if (other._ptr)
        {
            _ptr = new HeapBuffer();
            _ptr->_currentId = other._ptr->_currentId;
            _ptr->_buf.insert(_ptr->_buf.end(), other._ptr->_buf.begin(), other._ptr->_buf.end());
        }
        else
        {
            _ptr = nullptr;
        }
    }

    MYBJsonLazyBuffer &operator=(const MYBJsonLazyBuffer &other)
    {
        clear();

        if (other._ptr)
        {
            getPtr()->_currentId = other._ptr->_currentId;
            _ptr->_buf.insert(_ptr->_buf.end(), other._ptr->_buf.begin(), other._ptr->_buf.end());
        }

        return *this;
    }

    MYBJsonLazyBuffer &operator=(MYBJsonLazyBuffer &&other)
    {
        std::swap(_ptr, other._ptr);
        return *this;
    }

    ~MYBJsonLazyBuffer()
    {
        delete _ptr;
    }

public:
    void clear()
    {
        delete _ptr;
        _ptr = nullptr;
    }
};

template <std::size_t bufferSize>
class MYBJsonVectorBuffer
{
private:
    std::vector<uint8_t> _buf;
    uint16_t _currentId = 0;

protected:
    BJSONINLINE const std::vector<uint8_t> &getBuffer() const
    {
        return _buf;
    }

    BJSONINLINE std::vector<uint8_t> &getBuffer()
    {
        _buf.reserve(bufferSize);
        return _buf;
    }

    BJSONINLINE uint16_t getShortId() const { return _currentId; }
    BJSONINLINE void setShortId(uint16_t id) { _currentId = id; }
    BJSONINLINE bool getEmpty() const
    {
        return _buf.empty();
    }

    MYBJsonVectorBuffer() {}
    MYBJsonVectorBuffer(const uint8_t *begin, const uint8_t *end)
    {
        _buf.reserve(end - begin);
        _buf.insert(_buf.end(), begin, end);
    }

    MYBJsonVectorBuffer(MYBJsonVectorBuffer &&other) : _buf(std::move(other._buf)), _currentId(other._currentId) {}
    MYBJsonVectorBuffer(const MYBJsonVectorBuffer &other) : _buf(other._buf), _currentId(other._currentId) {}
    MYBJsonVectorBuffer &operator=(MYBJsonVectorBuffer &&other)
    {
        _buf = std::move(other._buf);
        _currentId = other._currentId;
        return *this;
    }

    MYBJsonVectorBuffer &operator=(const MYBJsonVectorBuffer &other)
    {
        _buf = other._buf;
        _currentId = other._currentId;
        return *this;
    }

public:
    size_t capacity() const
    {
        return _buf.capacity();
    }

    void reserve(size_t sz)
    {
        _buf.reserve(sz);
    }

    void resize(size_t sz)
    {
        _buf.resize(sz);
    }

    void clear()
    {
        _buf.clear();
    }

    BJSONINLINE const std::vector<uint8_t> &getVectorBuffer() const
    {
        return _buf;
    }

    std::vector<uint8_t> tearOffBuffer()
    {
        std::vector<uint8_t> other;
        std::swap(other, _buf);
        return other;
    }
};

typedef MYBJsonRWBase<MYBJsonStackBuffer<256>> MYBJsonStackRW;

typedef MYBJsonRWBase<MYBJsonVectorBuffer<128>> MYBJsonRW;
typedef MYBJsonRWBase<MYBJsonVectorBuffer<64>> MYBJsonSmallRW;
typedef MYBJsonRWBase<MYBJsonVectorBuffer<4 * 1024 * 1024>> MYBJsonBigRW;

typedef MYBJsonRWBase<MYBJsonLazyBuffer> MYBJsonLazyRW;

class MYBJsonReader
{
public:
    static const size_t kDefaultStackCapacity = 256; //!< Default stack capacity in bytes for storing a single decoded string.

    inline bool next(const uint8_t *&sidAndType, const uint8_t *isEnd)
    {
        sidAndType++;
        if (sidAndType == isEnd)
        {
            assert(false);
            return false;
        }
        return true;
    }

    BJsonType getType(const uint8_t *&sidAndType)
    {
        return (BJsonType)((*sidAndType) & 0x0F);
    }

    template <typename Handler>
    bool Type(const uint8_t *&sidAndType, const uint8_t *isEnd, Handler &handler, BJsonType &type, bool setKey)
    {
        type = (BJsonType)((*sidAndType) & 0x0F);
        if (/*setKey && */ (type != BJsonType::end))
        {
            int key;
            if ((*sidAndType & 0x80) == 0)
            {
                key = (*sidAndType) >> 4;
            }
            else
            {
                key = (*sidAndType) & 0x70;
                key <<= 4;

                if (!next(sidAndType, isEnd))
                {
                    return false;
                }

                key |= *sidAndType;
            }

            handler.Type(type);
            handler.Key(key);
        }
        return true;
    }

    bool readVarInt(const uint8_t *&sidAndType, const uint8_t *isEnd, uint64_t &value)
    {
        value = 0;
        for (int iteration = 0; iteration < 10; iteration++)
        {
            uint64_t real7bitValue = (*sidAndType) & 0b01111111;
            if (real7bitValue == 0)
            {
                // not math needed for this one
            }
            else
            {
                short shiftLeftBy = 7 * iteration;
                uint64_t correctlyPositionedValue = real7bitValue << shiftLeftBy;
                value |= correctlyPositionedValue;
            }

            if (*sidAndType < 0b10000000)
            {
                // No more bits
                return true;
            }

            if (!next(sidAndType, isEnd))
            {
                // expected bit not found
                return false;
            }
        }

        // More than 10 bytes in the integer
        bjson_bugcheck("readVarIntBig: More than 10 bytes in a varInt");
        return false;
    }

    bool readVarIntBigEndian(const uint8_t *&sidAndType, const uint8_t *isEnd, uint64_t &value)
    {
        return readVarInt(sidAndType, isEnd, value);
        value = 0;
        int shiftLeftBy = 57;
        for (int iteration = 0; iteration < 10; ++iteration)
        {
            uint64_t real7bitValue = (*sidAndType) & 0b01111111;
            if (real7bitValue == 0)
            {
                // not math needed for this one
            }
            else
            {
                if (shiftLeftBy < 0)
                {
                    assert(((real7bitValue >> (0 - shiftLeftBy)) << (0 - shiftLeftBy)) == real7bitValue);
                    uint64_t correctlyPositionedValue = real7bitValue >> (0 - shiftLeftBy);
                    value |= correctlyPositionedValue;
                }
                else
                {
                    uint64_t correctlyPositionedValue = real7bitValue << shiftLeftBy;
                    value |= correctlyPositionedValue;
                }
            }

            if (*sidAndType < 0b10000000)
            {
                // No more bits
                return true;
            }

            shiftLeftBy -= 7;

            if (!next(sidAndType, isEnd))
            {
                // expected bit not found
                return false;
            }
        }

        // More than 10 bytes in the integer
        bjson_bugcheck("readVarIntBigEndian: More than 10 bytes in a varInt");
        return false;
    }

    bool readVarIntBigEndian32(const uint8_t *&sidAndType, const uint8_t *isEnd, uint32_t &value)
    {
        uint64_t v;
        auto ret = readVarInt(sidAndType, isEnd, v);
        value = (uint32_t)v;
        return ret;

        value = 0;
        int shiftLeftBy = 25;
        for (int iteration = 0; iteration < 5; ++iteration)
        {
            uint64_t real7bitValue = (*sidAndType) & 0b01111111;
            if (real7bitValue == 0)
            {
                // not math needed for this one
            }
            else
            {
                if (shiftLeftBy < 0)
                {
                    assert(((real7bitValue >> (0 - shiftLeftBy)) << (0 - shiftLeftBy)) == real7bitValue);
                    uint64_t correctlyPositionedValue = real7bitValue >> (0 - shiftLeftBy);
                    value |= correctlyPositionedValue;
                }
                else
                {
                    uint64_t correctlyPositionedValue = real7bitValue << shiftLeftBy;
                    value |= correctlyPositionedValue;
                }
            }

            if (*sidAndType < 0b10000000)
            {
                // No more bits
                return true;
            }

            shiftLeftBy -= 7;

            if (!next(sidAndType, isEnd))
            {
                // expected bit not found
                return false;
            }
        }

        // More than 10 bytes in the integer
        bjson_bugcheck("readVarIntBigEndian: More than 10 bytes in a varInt");
        return false;
    }

    const MYBJson *_is = nullptr;
    const uint8_t *_ptrType = nullptr;
    MYBJsonIterator getTypeParsePosition()
    {
        return MYBJsonIterator(_ptrType, _is->pend());
    }

    MYBJsonIterator getEnd()
    {
        return _is->end();
    }

    const MYBJson *getInputStream()
    {
        return _is;
    }

    template <typename Handler>
    bool Parse(const MYBJson &is, Handler &handler, bool copy = true, bool pretendOuterObject = false)
    {
        _is = &is;

        std::deque<bool> modes; // true for object, false for array

        int currentMemberCount = 0;
        std::deque<int> memberCount;

        if (pretendOuterObject)
        {
            modes.push_back(true);
            memberCount.push_back(0);
        }

        for (const uint8_t *_ptr = is.pbegin(); _ptr != is.pend(); _ptr++)
        {
            _ptrType = _ptr;
            BJsonType type;
            if (!Type(_ptr, is.pend(), handler, type, modes.size() > 0 ? modes.back() : 0))
                return false;

            if (type == BJsonType::null)
            {
                currentMemberCount++;
                if (!handler.Null())
                    return false;
            }
            else if (type == BJsonType::separator)
            {
                currentMemberCount++;
                if (!handler.Separator())
                    return false;
            }
            else if (type == BJsonType::true1)
            {
                currentMemberCount++;
                if (!handler.Int32(1))
                    return false;
            }
            else if (type == BJsonType::array)
            {
                currentMemberCount++;
                memberCount.push_back(currentMemberCount);
                currentMemberCount = 0;

                modes.push_back(false);
                if (!handler.StartArray())
                    return false;
            }
            else if (type == BJsonType::object)
            {
                currentMemberCount++;
                memberCount.push_back(currentMemberCount);
                currentMemberCount = 0;

                modes.push_back(true);
                if (!handler.StartObject())
                    return false;
            }
            else if (type == BJsonType::end)
            {
                if ((modes.size() > 0) && !modes.back())
                {
                    if (!handler.EndArray(currentMemberCount))
                        return false;

                    if (memberCount.size() > 0)
                    {
                        currentMemberCount = memberCount.back();
                        memberCount.pop_back();
                    }
                }
                else
                {
                    if (!handler.EndObject(currentMemberCount))
                        return false;

                    if (memberCount.size() > 0)
                    {
                        currentMemberCount = memberCount.back();
                        memberCount.pop_back();
                    }
                }

                if (modes.size() > 0)
                    modes.pop_back();
            }
            else if (type == BJsonType::uint8)
            {
                currentMemberCount++;

                if (!next(_ptr, is.pend()))
                    return false;

                if (!handler.Uint32(*_ptr))
                    return false;
            }
            else if (type == BJsonType::fixed32)
            {
                currentMemberCount++;

                if (!next(_ptr, is.pend()))
                    return false;

                if (_ptr + 3 >= is.pend())
                    return false;

                uint32_t fixedInt32;
                memcpy(&fixedInt32, _ptr, sizeof(uint32_t));

                if (!handler.Uint32(fixedInt32))
                    return false;

                _ptr += 3;
            }
            else if (type == BJsonType::varint)
            {
                currentMemberCount++;

                if (!next(_ptr, is.pend()))
                    return false;

                uint64_t varInt;
                if (!readVarInt(_ptr, is.pend(), varInt))
                    return false;

                if (!handler.Int64(varInt))
                    return false;
            }
            else if (type == BJsonType::varint_neg)
            {
                currentMemberCount++;

                if (!next(_ptr, is.pend()))
                    return false;

                uint64_t varInt;
                if (!readVarInt(_ptr, is.pend(), varInt))
                    return false;

                varInt = ~varInt;
                if (!handler.Int64(varInt))
                    return false;
            }
            else if (type == BJsonType::string)
            {
                currentMemberCount++;

                if (!next(_ptr, is.pend()))
                    return false;

                uint64_t strlength;
                if (!readVarInt(_ptr, is.pend(), strlength))
                    return false;

                assert(strlength < std::numeric_limits<size_t>::max());
                if (strlength == 0)
                {
                    if (!handler.String("", 0, copy))
                        return false;
                }
                else
                {
                    if (!next(_ptr, is.pend()))
                        return false;

                    if (_ptr + strlength - 1 >= is.pend())
                        return false;

                    if (!handler.String((const char *)_ptr, (size_t)strlength, copy))
                        return false;

                    if (strlength > 1)
                    {
                        _ptr += (strlength - 1);
                    }
                }
            }
            else if (type == BJsonType::binary)
            {
                currentMemberCount++;

                if (!next(_ptr, is.pend()))
                    return false;

                uint64_t strlength;
                if (!readVarInt(_ptr, is.pend(), strlength))
                    return false;

                if (!next(_ptr, is.pend()))
                    return false;

                if (_ptr + strlength - 1 >= is.pend())
                    return false;

                assert(strlength < std::numeric_limits<size_t>::max());
                if (!handler.Binary(_ptr, (size_t)strlength, copy))
                    return false;

                if (strlength > 1)
                {
                    _ptr += (strlength - 1);
                }
            }
            else if (type == BJsonType::hash)
            {
                currentMemberCount++;

                if (!next(_ptr, is.pend()))
                    return false;

                if (_ptr + (sizeof(MYHashStorage) - 1) >= is.pend())
                    return false;

                MYHash hash;
                memcpy(&hash.raw[0], _ptr, sizeof(MYHashStorage));
                hash.resetsetbit();

                if (!handler.MYHash(hash))
                    return false;

                _ptr += (sizeof(MYHashStorage) - 1);
            }
            else if (type == BJsonType::trev)
            {
                currentMemberCount++;

                if (!next(_ptr, is.pend()))
                    return false;

                if (_ptr + (sizeof(MYTRev) - 1) >= is.pend())
                    return false;

                MYTRev trev;
                memcpy(&trev, _ptr, sizeof(MYTRev));

                if (!handler.MYTRev(trev))
                    return false;

                _ptr += (sizeof(MYTRev) - 1);
            }
            else if (type == BJsonType::float32)
            {
                currentMemberCount++;

                if (!next(_ptr, is.pend()))
                    return false;

                uint32_t varInt;
                if (!readVarIntBigEndian32(_ptr, is.pend(), varInt))
                    return false;

                assert(varInt <= std::numeric_limits<uint32_t>::max());

                uint32_t varInt32 = (uint32_t)varInt;
                float f;

                static_assert(sizeof(f) == sizeof(varInt32), "Size mismatch");
                memcpy(&f, &varInt32, sizeof(f));

                if (!handler.Double(f))
                    return false;
            }
            else if (type == BJsonType::double64)
            {
                currentMemberCount++;

                if (!next(_ptr, is.pend()))
                    return false;

                uint64_t varInt;
                if (!readVarIntBigEndian(_ptr, is.pend(), varInt))
                    return false;

                double d;

                static_assert(sizeof(d) == sizeof(varInt), "Size mismatch");
                memcpy(&d, &varInt, sizeof(d));

                if (!handler.Double(d))
                    return false;
            }
        }

        return true;
    }
    // This method searches through the contents of the Bjson ONLY the word you are searching for needs to be provided in lowercase
    bool quickShortCircuitFinder(const MYBJson &is, MYFastToStringHandler &handler, const std::string &lowerCasedWordToFind, bool copy = true, bool pretendOuterObject = false);

    // This method generates the content of the Bjson.
    bool generateQuickString(const MYBJson &is, MYFastToStringHandler &handler, bool copy = true, bool pretendOuterObject = false);

    template <typename Handler>
    MYBJsonIterator ParseWithIter(const MYBJson &is, Handler &handler, bool copy = true)
    {
        _is = &is;

        std::deque<bool> modes; // true for object, false for array

        for (const uint8_t *_ptr = is.pbegin(); _ptr != is.pend(); _ptr++)
        {
            _ptrType = _ptr;
            const uint8_t *ptrToType = _ptr;

            BJsonType type;
            if (!Type(_ptr, is.pend(), handler, type, modes.size() > 0 ? modes.back() : 0))
                return MYBJsonIterator(is.pend(), is.pend());

            if (type == BJsonType::null)
            {
                if (!handler.Null())
                    return MYBJsonIterator(ptrToType, is.pend());
            }
            else if (type == BJsonType::separator)
            {
                // currentMemberCount++;
                if (!handler.Separator())
                    return MYBJsonIterator(ptrToType, is.pend());
            }
            else if (type == BJsonType::true1)
            {
                if (!handler.Int32(1))
                    return MYBJsonIterator(ptrToType, is.pend());
            }
            else if (type == BJsonType::array)
            {
                modes.push_back(false);
                if (!handler.StartArray())
                    return MYBJsonIterator(ptrToType, is.pend());
            }
            else if (type == BJsonType::object)
            {
                modes.push_back(true);
                if (!handler.StartObject())
                    return MYBJsonIterator(ptrToType, is.pend());
            }
            else if (type == BJsonType::end)
            {
                if (modes.size() == 0)
                {
                    return MYBJsonIterator(is.pend(), is.pend());
                }

                if (!modes.back())
                {
                    if (!handler.EndArray(0))
                        return MYBJsonIterator(ptrToType, is.pend());
                }
                else
                {
                    if (!handler.EndObject(0))
                        return MYBJsonIterator(ptrToType, is.pend());
                }
                modes.pop_back();
            }
            else if (type == BJsonType::uint8)
            {
                if (!next(_ptr, is.pend()))
                    return MYBJsonIterator(is.pend(), is.pend());

                if (!handler.Uint32(*_ptr))
                    return MYBJsonIterator(ptrToType, is.pend());
            }
            else if (type == BJsonType::fixed32)
            {
                if (!next(_ptr, is.pend()))
                    return MYBJsonIterator(is.pend(), is.pend());

                if (_ptr + 3 >= is.pend())
                    return MYBJsonIterator(is.pend(), is.pend());

                uint32_t fixedInt32;
                memcpy(&fixedInt32, _ptr, sizeof(uint32_t));

                if (!handler.Uint32(fixedInt32))
                    return MYBJsonIterator(is.pend(), is.pend());

                _ptr += 3;
            }
            else if (type == BJsonType::varint)
            {
                if (!next(_ptr, is.pend()))
                    return MYBJsonIterator(is.pend(), is.pend());

                uint64_t varInt;
                if (!readVarInt(_ptr, is.pend(), varInt))
                    return MYBJsonIterator(is.pend(), is.pend());

                if (!handler.Int64(varInt))
                    return MYBJsonIterator(ptrToType, is.pend());
            }
            else if (type == BJsonType::varint_neg)
            {
                if (!next(_ptr, is.pend()))
                    return MYBJsonIterator(is.pend(), is.pend());

                uint64_t varInt;
                if (!readVarInt(_ptr, is.pend(), varInt))
                    return MYBJsonIterator(is.pend(), is.pend());

                varInt = ~varInt;
                if (!handler.Int64((int64_t)varInt))
                    return MYBJsonIterator(ptrToType, is.pend());
            }
            else if (type == BJsonType::string)
            {
                if (!next(_ptr, is.pend()))
                    return MYBJsonIterator(is.pend(), is.pend());

                uint64_t strlength;
                if (!readVarInt(_ptr, is.pend(), strlength))
                    return MYBJsonIterator(is.pend(), is.pend());

                if (!next(_ptr, is.pend()))
                    return MYBJsonIterator(is.pend(), is.pend());

                if (_ptr + strlength - 1 >= is.pend())
                    return MYBJsonIterator(is.pend(), is.pend());

                assert(strlength < std::numeric_limits<size_t>::max());
                if (!handler.String((const char *)_ptr, (size_t)strlength, copy))
                    return MYBJsonIterator(ptrToType, is.pend());

                if (strlength > 1)
                {
                    _ptr += (strlength - 1);
                }
            }
            else if (type == BJsonType::binary)
            {
                if (!next(_ptr, is.pend()))
                    return MYBJsonIterator(is.pend(), is.pend());

                uint64_t strlength;
                if (!readVarInt(_ptr, is.pend(), strlength))
                    return MYBJsonIterator(is.pend(), is.pend());

                if (!next(_ptr, is.pend()))
                    return MYBJsonIterator(is.pend(), is.pend());

                if (_ptr + strlength - 1 >= is.pend())
                    return MYBJsonIterator(is.pend(), is.pend());

                assert(strlength < std::numeric_limits<size_t>::max());
                if (!handler.Binary(_ptr, (size_t)strlength, copy))
                    return MYBJsonIterator(ptrToType, is.pend());

                if (strlength > 1)
                {
                    _ptr += (strlength - 1);
                }
            }
            else if (type == BJsonType::hash)
            {
                if (!next(_ptr, is.pend()))
                    return MYBJsonIterator(is.pend(), is.pend());

                if (_ptr + (sizeof(MYHashStorage) - 1) >= is.pend())
                    return MYBJsonIterator(is.pend(), is.pend());

                MYHash hash;
                memcpy(&hash.raw[0], _ptr, sizeof(MYHashStorage));
                hash.resetsetbit();

                if (!handler.MYHash(hash))
                    return MYBJsonIterator(ptrToType, is.pend());

                _ptr += (sizeof(MYHashStorage) - 1);
            }
            else if (type == BJsonType::trev)
            {
                if (!next(_ptr, is.pend()))
                    return MYBJsonIterator(is.pend(), is.pend());

                if (_ptr + (sizeof(MYTRev) - 1) >= is.pend())
                    return MYBJsonIterator(is.pend(), is.pend());

                MYTRev trev;
                memcpy(&trev, _ptr, sizeof(MYTRev));

                if (!handler.MYTRev(trev))
                    return MYBJsonIterator(ptrToType, is.pend());

                _ptr += (sizeof(MYTRev) - 1);
            }
            else if (type == BJsonType::float32)
            {
                if (!next(_ptr, is.pend()))
                    return MYBJsonIterator(is.pend(), is.pend());

                uint32_t varInt;
                if (!readVarIntBigEndian32(_ptr, is.pend(), varInt))
                    return MYBJsonIterator(is.pend(), is.pend());

                assert(varInt <= std::numeric_limits<uint32_t>::max());

                uint32_t varInt32 = (uint32_t)varInt;
                float f = *(float *)&varInt32;
                if (!handler.Double(f))
                    return MYBJsonIterator(ptrToType, is.pend());
            }
            else if (type == BJsonType::double64)
            {
                if (!next(_ptr, is.pend()))
                    return MYBJsonIterator(is.pend(), is.pend());

                uint64_t varInt;
                if (!readVarIntBigEndian(_ptr, is.pend(), varInt))
                    return MYBJsonIterator(is.pend(), is.pend());

                double d = *(double *)&varInt;
                if (!handler.Double(d))
                    return MYBJsonIterator(ptrToType, is.pend());
            }
        }

        return MYBJsonIterator(is.pend(), is.pend());
    }

private:
    bool quickParse(const MYBJson &is, MYFastToStringHandler &handler, bool copy, bool pretendOuterObject, const std::string lowerCasedWordToFind = "");
};

namespace dummy
{
    struct BJsonType_dummy
    {
        int c;
    };
};
