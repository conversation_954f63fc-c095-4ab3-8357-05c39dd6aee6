import express = require("express");

export let binaryEncoder = require("../../build/Release/binary_encoder");

export function sendResponse(
  req: express.Request,
  res: express.Response,
  result: any,
  formatter: (r) => Buffer
) {
  if (req.header("Accept") === "application/octet-stream") {
    res.setHeader("Content-Type", "application/octet-stream");
    let blob = formatter(result);
    res.status(200).send(blob);
  } else {
    return res.status(200).json(result);
  }
}
