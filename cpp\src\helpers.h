#pragma once

#include <node.h>
#include "MYTrev.h"
#include "bjson.h"
#include "MYLiterals.h"
#include "base64.h"
#include "MYHash.h"
#include <napi.h>

struct x
{
	x() : v(1000)
	{
	}
	std::vector<uint8_t> v;
	void write(const Napi::Object &obj)
	{
		std::string s = obj.Get("cert").ToString();
	}
};

namespace helpers
{
	std::string t(int id);
	int rt(const std::string &id);
	std::vector<uint8_t> text2bin(const std::string &text);
	std::string bin2text(const std::string &binary);
	std::string bin2text(const char *start, int length);
	std::string bin2text(const uint8_t *start, int length);
	void freeWriter(Napi::Env env, uint8_t *data, void *hint);
	void freeX(Napi::Env env, uint8_t *data, void *hint);
	MYHash hash(const std::string &b64);
	std::string hash64(const MYHash &hash);
	MYTRev trev(const std::string &b64);
	std::string trev64(const MYTRev &v8Trev);
	void writeBinary(MYBJsonBigRW *writer, int key, const std::string &base64);
	void writeBinary(MYBJsonBigRW *writer, const std::string &base64);
	Napi::Object decodeObject(Napi::Env isolate, MYBJsonIterator &current, int sectionSid);
	bool encodeObject(MYBJsonBigRW *writer, int sectionId, const Napi::Object &obj, int key = -1);

	/// Stringify V8 value to JSON
	/// return empty string for empty value
	std::string json_str(Napi::Env isolate, v8::Value value);
}