export function makeError(
  statusCode: number,
  code: string,
  message?: string,
  errors?: Array<string>
) {
  let e = Error(code) as any;

  e.httpStatus = statusCode;
  e.code = code;
  e.message = message;
  e.errors = errors;
  e.source = "mylio";

  return e;
}

export function error<T>(
  statusCode: number,
  code: string,
  message?: string,
  errors?: Array<string>
) {
  return Promise.reject<T>(
    makeError(statusCode, code, message, errors)
  ) as Promise<T>;
}

export function logError(err: any) {
  if (err) {
    console.log(`http status: ${err && err.httpStatus}`);
    console.log(`code: ${err && err.code}`);
    console.log(`message: ${err && err.message}`);
    console.log(`errors: ${err && err.errors}`);
    console.log(`source: ${err && err.source}`);
    console.log(`type: ${err && err.type}`);
    console.log(`stack: ${err && err.stack}`);
  } else {
    console.log("null error");
  }
}
