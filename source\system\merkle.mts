export function lpad(str: string, size: number, padString = "0") {
  while (str.length < size) str = padString + str;
  return str;
}

export function b4096Tob256(theNumber: string | number) {
  let bucketNumber: number;
  if (typeof theNumber === "string") bucketNumber = parseInt(theNumber, 16);
  else bucketNumber = theNumber;
  return {
    b16: `x${lpad((bucketNumber & 0xf).toString(16), 2)}`,
    b256: `x${lpad((bucketNumber & 0x0ff).toString(16), 2)}`,
  };
}

export function h2d(hex: string) {
  if (hex.startsWith("x")) return parseInt(hex.substr(1), 16);
  else return parseInt(hex, 16);
}

export function d2h(decimal: number, leading = 0) {
  return `x${lpad(decimal.toString(16), leading)}`;
}

export class Bucket {
  private _number = 0;

  public schema() {
    return d2h(this.nf00());
  }

  public table() {
    return d2h(this.n0ff(), 2);
  }

  public fqT() {
    return `${this.schema()}.${this.table()}`;
  }

  public sf00(value?: string | number) {
    return this.nf00().toString(10);
  }

  public s0ff(value?: string | number) {
    return this.n0ff().toString(10);
  }

  public nf00(value?: string | number) {
    if (value !== undefined) {
      let temp = 0;
      if (typeof value === "string") {
        temp = h2d(value);
      } else {
        temp = value;
      }
      this._number = (temp << 8) | (this._number & 0xff);
    }
    return (this._number & 0xf00) >> 8;
  }

  public n0ff(value?: string | number) {
    if (value !== undefined) {
      let temp = 0;
      if (typeof value === "string") {
        temp = h2d(value);
      } else {
        temp = value;
      }
      this._number = temp | (this._number & 0x0f00);
    }
    return this._number & 0xff;
  }

  public nfff(value?: string | number) {
    if (value !== undefined) {
      if (typeof value === "string") this._number = h2d(value);
      else this._number = value;
    }
    return this._number;
  }

  constructor(bFFF: number | string);
  constructor(bF00: number | string, b0FF: number | string);
  constructor(bF00?: number | string, b0FF?: number | string) {
    if (b0FF === undefined) {
      // in this case bF === bFFF
      this.nfff(bF00);
    } else {
      this.nf00(bF00);
      this.n0ff(b0FF);
    }
  }
}
