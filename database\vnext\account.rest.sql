

    drop view if exists a0."Account" cascade;

    create or replace view a0."Account" as
    select
    flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		encode(t, 'base64') as "t",
		encode(cipher, 'base64') as "cipher",
		encode(client_cipher, 'base64') as "clientCipher",
		client_cipher_version as "clientCipherVersion",
		min_build as "minBuild",
		peer_to_peer_key as "peerToPeerKey",
		client_peer_to_peer_key as "clientPeerToPeerKey",
		client_peer_to_peer_key_version as "clientPeerToPeerKeyVersion",
		rsa_private_key as "rsaPrivateKey",
		x509_cert as "x509Cert",
		tfa,
		idp,
		sub,
		email,
		password_hash as "passwordHash",
		password_hash_version as "passwordHashVersion",
		salt,
		password_set_time as "passwordSetTime",
		plan_id as "planId",
		role,
		device_limit as "deviceLimit",
		photo_limit as "photoLimit",
		cloud_storage_limit as "cloudStorageLimit",
		features,
		next_plan_date as "nextPlanDate",
		available_upgrades as "availableUpgrades",
		license_template_id as "licenseTemplateId",
		license_display_name as "licenseDisplayName",
		License_manager as "licenseManager",
		license_flags as "licenseFlags",
		available_upgrades_features as "availableUpgradesFeatures",
		license_id as "licenseId",
		affiliate_id as "affiliateId"
    from a0.account;
    

drop function if exists a0.account_create; 
        create function a0.account_create(
            _flags int,
	_modified_time timestamptz,
	_created_time timestamptz,
	_account_id int,
	__t text,
	__cipher text,
	__client_cipher text,
	_client_cipher_version int,
	_min_build int,
	_peer_to_peer_key text,
	_client_peer_to_peer_key text,
	_client_peer_to_peer_key_version int,
	_rsa_private_key text,
	_x509_cert text,
	_tfa boolean,
	_idp text,
	_sub text,
	_email text,
	_password_hash text,
	_password_hash_version int,
	_salt text,
	_password_set_time timestamptz,
	_plan_id text,
	_role text,
	_device_limit int,
	_photo_limit int,
	_cloud_storage_limit int,
	_features int,
	_next_plan_date timestamptz,
	_available_upgrades text,
	_license_template_id text,
	_license_display_name text,
	_License_manager text,
	_license_flags int,
	_available_upgrades_features int,
	_license_id text,
	_affiliate_id text
        )
        returns a0."Account"
        as $$
        
    declare
        result a0."Account";
        _t bytea;
		_cipher bytea;
		_client_cipher bytea;
    begin
        _t := decode(__t, 'base64');
		_cipher := decode(__cipher, 'base64');
		_client_cipher := decode(__client_cipher, 'base64');
        


       
        


        
        _account_id := nextval('a0.account_account_id');
        _t := public.new_trev4(_account_id, 5);
        
        _modified_time := coalesce(_modified_time, now());
        _created_time := coalesce(_created_time, now());
        insert into a0.account (
            flags,
	modified_time,
	created_time,
	account_id,
	t,
	cipher,
	client_cipher,
	client_cipher_version,
	min_build,
	peer_to_peer_key,
	client_peer_to_peer_key,
	client_peer_to_peer_key_version,
	rsa_private_key,
	x509_cert,
	tfa,
	idp,
	sub,
	email,
	password_hash,
	password_hash_version,
	salt,
	password_set_time,
	plan_id,
	role,
	device_limit,
	photo_limit,
	cloud_storage_limit,
	features,
	next_plan_date,
	available_upgrades,
	license_template_id,
	license_display_name,
	License_manager,
	license_flags,
	available_upgrades_features,
	license_id,
	affiliate_id
        )
        values(
            _flags,
			_modified_time,
			_created_time,
			_account_id,
			_t,
			_cipher,
			_client_cipher,
			_client_cipher_version,
			_min_build,
			_peer_to_peer_key,
			_client_peer_to_peer_key,
			_client_peer_to_peer_key_version,
			_rsa_private_key,
			_x509_cert,
			_tfa,
			_idp,
			_sub,
			_email,
			_password_hash,
			_password_hash_version,
			_salt,
			_password_set_time,
			_plan_id,
			_role,
			_device_limit,
			_photo_limit,
			_cloud_storage_limit,
			_features,
			_next_plan_date,
			_available_upgrades,
			_license_template_id,
			_license_display_name,
			_License_manager,
			_license_flags,
			_available_upgrades_features,
			_license_id,
			_affiliate_id
        )
        returning
            flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		encode(t, 'base64') as "t",
		encode(cipher, 'base64') as "cipher",
		encode(client_cipher, 'base64') as "clientCipher",
		client_cipher_version as "clientCipherVersion",
		min_build as "minBuild",
		peer_to_peer_key as "peerToPeerKey",
		client_peer_to_peer_key as "clientPeerToPeerKey",
		client_peer_to_peer_key_version as "clientPeerToPeerKeyVersion",
		rsa_private_key as "rsaPrivateKey",
		x509_cert as "x509Cert",
		tfa,
		idp,
		sub,
		email,
		password_hash as "passwordHash",
		password_hash_version as "passwordHashVersion",
		salt,
		password_set_time as "passwordSetTime",
		plan_id as "planId",
		role,
		device_limit as "deviceLimit",
		photo_limit as "photoLimit",
		cloud_storage_limit as "cloudStorageLimit",
		features,
		next_plan_date as "nextPlanDate",
		available_upgrades as "availableUpgrades",
		license_template_id as "licenseTemplateId",
		license_display_name as "licenseDisplayName",
		License_manager as "licenseManager",
		license_flags as "licenseFlags",
		available_upgrades_features as "availableUpgradesFeatures",
		license_id as "licenseId",
		affiliate_id as "affiliateId"
        into result;

        
/* b::before_merkle */
insert into a0.account_metadata(account_id) values (_account_id);
/* end */


        perform a0.account_merkle(_account_id);

    
        



        return result;
    end;
        $$
        language plpgsql;
        

drop function if exists a0.account_update; 
        create function a0.account_update(
            _flags int,
	_modified_time timestamptz,
	_created_time timestamptz,
	_account_id int,
	__t text,
	__cipher text,
	__client_cipher text,
	_client_cipher_version int,
	_min_build int,
	_peer_to_peer_key text,
	_client_peer_to_peer_key text,
	_client_peer_to_peer_key_version int,
	_rsa_private_key text,
	_x509_cert text,
	_tfa boolean,
	_idp text,
	_sub text,
	_email text,
	_password_hash text,
	_password_hash_version int,
	_salt text,
	_password_set_time timestamptz,
	_plan_id text,
	_role text,
	_device_limit int,
	_photo_limit int,
	_cloud_storage_limit int,
	_features int,
	_next_plan_date timestamptz,
	_available_upgrades text,
	_license_template_id text,
	_license_display_name text,
	_License_manager text,
	_license_flags int,
	_available_upgrades_features int,
	_license_id text,
	_affiliate_id text
        )
        returns a0."Account"
        as $$
        
    declare
        result a0."Account";
        _t bytea;
		_cipher bytea;
		_client_cipher bytea;
    begin
        _t := decode(__t, 'base64');
		_cipher := decode(__cipher, 'base64');
		_client_cipher := decode(__client_cipher, 'base64');
        


       
        


        
        _t := public.next_trev(_t);
        _modified_time := now();
        update a0.account
        set
            flags = _flags,
			modified_time = _modified_time,
			created_time = _created_time,
			t = _t,
			cipher = _cipher,
			client_cipher = _client_cipher,
			client_cipher_version = _client_cipher_version,
			min_build = _min_build,
			peer_to_peer_key = _peer_to_peer_key,
			client_peer_to_peer_key = _client_peer_to_peer_key,
			client_peer_to_peer_key_version = _client_peer_to_peer_key_version,
			rsa_private_key = _rsa_private_key,
			x509_cert = _x509_cert,
			tfa = _tfa,
			idp = _idp,
			sub = _sub,
			email = _email,
			password_hash = _password_hash,
			password_hash_version = _password_hash_version,
			salt = _salt,
			password_set_time = _password_set_time,
			plan_id = _plan_id,
			role = _role,
			device_limit = _device_limit,
			photo_limit = _photo_limit,
			cloud_storage_limit = _cloud_storage_limit,
			features = _features,
			next_plan_date = _next_plan_date,
			available_upgrades = _available_upgrades,
			license_template_id = _license_template_id,
			license_display_name = _license_display_name,
			License_manager = _License_manager,
			license_flags = _license_flags,
			available_upgrades_features = _available_upgrades_features,
			license_id = _license_id,
			affiliate_id = _affiliate_id
        where account_id = _account_id
        returning
            flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		encode(t, 'base64') as "t",
		encode(cipher, 'base64') as "cipher",
		encode(client_cipher, 'base64') as "clientCipher",
		client_cipher_version as "clientCipherVersion",
		min_build as "minBuild",
		peer_to_peer_key as "peerToPeerKey",
		client_peer_to_peer_key as "clientPeerToPeerKey",
		client_peer_to_peer_key_version as "clientPeerToPeerKeyVersion",
		rsa_private_key as "rsaPrivateKey",
		x509_cert as "x509Cert",
		tfa,
		idp,
		sub,
		email,
		password_hash as "passwordHash",
		password_hash_version as "passwordHashVersion",
		salt,
		password_set_time as "passwordSetTime",
		plan_id as "planId",
		role,
		device_limit as "deviceLimit",
		photo_limit as "photoLimit",
		cloud_storage_limit as "cloudStorageLimit",
		features,
		next_plan_date as "nextPlanDate",
		available_upgrades as "availableUpgrades",
		license_template_id as "licenseTemplateId",
		license_display_name as "licenseDisplayName",
		License_manager as "licenseManager",
		license_flags as "licenseFlags",
		available_upgrades_features as "availableUpgradesFeatures",
		license_id as "licenseId",
		affiliate_id as "affiliateId"
        into result;

        perform a0.account_merkle(_account_id);

    
        



        return result;
    end;
        $$
        language plpgsql;
        

drop function if exists a0.account_read_by_account_id; 
        create function a0.account_read_by_account_id(
            _account_id int
        )
        returns a0."Account"
        as $$
        select
        flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		encode(t, 'base64') as "t",
		encode(cipher, 'base64') as "cipher",
		encode(client_cipher, 'base64') as "clientCipher",
		client_cipher_version as "clientCipherVersion",
		min_build as "minBuild",
		peer_to_peer_key as "peerToPeerKey",
		client_peer_to_peer_key as "clientPeerToPeerKey",
		client_peer_to_peer_key_version as "clientPeerToPeerKeyVersion",
		rsa_private_key as "rsaPrivateKey",
		x509_cert as "x509Cert",
		tfa,
		idp,
		sub,
		email,
		password_hash as "passwordHash",
		password_hash_version as "passwordHashVersion",
		salt,
		password_set_time as "passwordSetTime",
		plan_id as "planId",
		role,
		device_limit as "deviceLimit",
		photo_limit as "photoLimit",
		cloud_storage_limit as "cloudStorageLimit",
		features,
		next_plan_date as "nextPlanDate",
		available_upgrades as "availableUpgrades",
		license_template_id as "licenseTemplateId",
		license_display_name as "licenseDisplayName",
		License_manager as "licenseManager",
		license_flags as "licenseFlags",
		available_upgrades_features as "availableUpgradesFeatures",
		license_id as "licenseId",
		affiliate_id as "affiliateId"
        from a0.account
        where account_id = _account_id;
        $$
        language sql;
        

drop function if exists a0.account_read_by_sub_and_idp; 
        create function a0.account_read_by_sub_and_idp(
            _sub text,
	_idp text
        )
        returns a0."Account"
        as $$
        select
        flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		encode(t, 'base64') as "t",
		encode(cipher, 'base64') as "cipher",
		encode(client_cipher, 'base64') as "clientCipher",
		client_cipher_version as "clientCipherVersion",
		min_build as "minBuild",
		peer_to_peer_key as "peerToPeerKey",
		client_peer_to_peer_key as "clientPeerToPeerKey",
		client_peer_to_peer_key_version as "clientPeerToPeerKeyVersion",
		rsa_private_key as "rsaPrivateKey",
		x509_cert as "x509Cert",
		tfa,
		idp,
		sub,
		email,
		password_hash as "passwordHash",
		password_hash_version as "passwordHashVersion",
		salt,
		password_set_time as "passwordSetTime",
		plan_id as "planId",
		role,
		device_limit as "deviceLimit",
		photo_limit as "photoLimit",
		cloud_storage_limit as "cloudStorageLimit",
		features,
		next_plan_date as "nextPlanDate",
		available_upgrades as "availableUpgrades",
		license_template_id as "licenseTemplateId",
		license_display_name as "licenseDisplayName",
		License_manager as "licenseManager",
		license_flags as "licenseFlags",
		available_upgrades_features as "availableUpgradesFeatures",
		license_id as "licenseId",
		affiliate_id as "affiliateId"
        from a0.account
        where sub = _sub and idp = _idp;
        $$
        language sql;
        

drop function if exists a0.account_delete_by_account_id; 
        create function a0.account_delete_by_account_id(
            _account_id int
        )
        returns void
        as $$
        
    declare
        
        
    begin
        
        


       
        


        

    delete from a0.account
    where account_id = _account_id;

    perform a0.account_merkle(_account_id);
    
        
/* b::after_delete_by_account_id */
perform a0.device_delete_by_account_id(_account_id);
  perform a0.account_metadata_delete_by_account_id(_account_id);
  perform a0.message_delete_by_account_id(_account_id);
  perform a0.system_property_delete_by_account_id(_account_id);
  delete from a0.license where account_id = _account_id;
  perform a0.device_data_delete_by_account_id(_account_id);
  perform a0.user_property_delete_by_account_id(_account_id);
  delete from a0.backblaze_info where account_id = _account_id;
  perform a0.refresh_token_delete_by_account_id(_account_id);
/* end */


        
    end;
        $$
        language plpgsql;
        

drop function if exists a0.account_delete_by_sub_and_idp; 
        create function a0.account_delete_by_sub_and_idp(
            _sub text,
	_idp text
        )
        returns void
        as $$
        
    declare
        
        
    begin
        
        


       
        


        

    delete from a0.account
    where sub = _sub and idp = _idp;

    perform a0.account_merkle(_account_id);
    
        



        
    end;
        $$
        language plpgsql;
        

drop function if exists a0.account_find_by_email; 
        create function a0.account_find_by_email(
            _email text
        )
        returns setof a0."Account"
        as $$
        select
        flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		encode(t, 'base64') as "t",
		encode(cipher, 'base64') as "cipher",
		encode(client_cipher, 'base64') as "clientCipher",
		client_cipher_version as "clientCipherVersion",
		min_build as "minBuild",
		peer_to_peer_key as "peerToPeerKey",
		client_peer_to_peer_key as "clientPeerToPeerKey",
		client_peer_to_peer_key_version as "clientPeerToPeerKeyVersion",
		rsa_private_key as "rsaPrivateKey",
		x509_cert as "x509Cert",
		tfa,
		idp,
		sub,
		email,
		password_hash as "passwordHash",
		password_hash_version as "passwordHashVersion",
		salt,
		password_set_time as "passwordSetTime",
		plan_id as "planId",
		role,
		device_limit as "deviceLimit",
		photo_limit as "photoLimit",
		cloud_storage_limit as "cloudStorageLimit",
		features,
		next_plan_date as "nextPlanDate",
		available_upgrades as "availableUpgrades",
		license_template_id as "licenseTemplateId",
		license_display_name as "licenseDisplayName",
		License_manager as "licenseManager",
		license_flags as "licenseFlags",
		available_upgrades_features as "availableUpgradesFeatures",
		license_id as "licenseId",
		affiliate_id as "affiliateId"
        from a0.account
        where email = _email;
        $$
        language sql;
        

drop function if exists a0.account_find_by_sub; 
        create function a0.account_find_by_sub(
            _sub text
        )
        returns setof a0."Account"
        as $$
        select
        flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		encode(t, 'base64') as "t",
		encode(cipher, 'base64') as "cipher",
		encode(client_cipher, 'base64') as "clientCipher",
		client_cipher_version as "clientCipherVersion",
		min_build as "minBuild",
		peer_to_peer_key as "peerToPeerKey",
		client_peer_to_peer_key as "clientPeerToPeerKey",
		client_peer_to_peer_key_version as "clientPeerToPeerKeyVersion",
		rsa_private_key as "rsaPrivateKey",
		x509_cert as "x509Cert",
		tfa,
		idp,
		sub,
		email,
		password_hash as "passwordHash",
		password_hash_version as "passwordHashVersion",
		salt,
		password_set_time as "passwordSetTime",
		plan_id as "planId",
		role,
		device_limit as "deviceLimit",
		photo_limit as "photoLimit",
		cloud_storage_limit as "cloudStorageLimit",
		features,
		next_plan_date as "nextPlanDate",
		available_upgrades as "availableUpgrades",
		license_template_id as "licenseTemplateId",
		license_display_name as "licenseDisplayName",
		License_manager as "licenseManager",
		license_flags as "licenseFlags",
		available_upgrades_features as "availableUpgradesFeatures",
		license_id as "licenseId",
		affiliate_id as "affiliateId"
        from a0.account
        where sub = _sub;
        $$
        language sql;
        
