update a0.license l
	set license_id = 'A.' || s.manager_ref || '.NOKEY.apple-plus.0'
from a0.subscription s
where l.license_id = 'A.' || s.manager_ref || '.NOKEY.0'; 

update a0.license l
	set 
		license_id = 'F.' || k.subscription_id || '.' || k.license_key || '.plus.0',
		activation_key = k.license_key
from a0.fast_spring_license_key k
where l.license_id = 'F.' || k.subscription_id || '.' || k.license_key || '.0'
and l.license_id != 'F.' || k.subscription_id || '.' || k.license_key || '.plus.0'
and not (coalesce(l.deleted, false))

update a0.license l
	set license_id = 'F.' || s.manager_ref || '.NOKEY.plus.0'
from a0.subscription s
where l.license_id = 'F.' || s.manager_ref || '.NOKEY.0'; 



select * from a0.license where license_id like 'F.exGyblemTLS3kv_imOR0LQ%'


update a0.license l
	set deleted = false
from a0.fast_spring_license_key f
where license_id like 'F.' || f.subscription_id || '.' || f.license_key || '.plus.0'
and coalesce(deleted, false)
and exists (select * from a0.license l2 where license_id = 'F.' || f.subscription_id || '.NOKEY.plus.0' )


delete from a0.license 
using a0.fast_spring_license_key f
where license_id like 'F.' || f.subscription_id || '.NOKEY.plus.0'











