// import { SubscriptionService } from "../services/LicenseService.mjs";
// import { AccountService } from "../services/AccountService.mjs";
// import { SubscriptionRestDataService } from "../dataServices/License.rest.dataService.mjs";
// import { AccountRestDataService } from "../dataServices/Account.rest.dataService.mjs";
// import { DeviceService } from "../services/DeviceService.mjs";
// import { DeviceRestDataService } from "../dataServices/Device.rest.dataService.mjs";
// import { DeviceDataRestDataService } from "../dataServices/DeviceData.rest.dataService.mjs";
// import { EmailService } from "../services/EmailService.mjs";
// import { TokenService } from "../services/TokenService.mjs";
// import { tx } from "../system/Postgres.mjs";
// import { FeaturesetRestDataService } from "../dataServices/Featureset.rest.dataService.mjs";
// import { SubscriptionTemplateRestDataService } from "../dataServices/LicenseTemplate.rest.dataService.mjs";
// import { OfferRestDataService } from "../dataServices/Offer.rest.dataService.mjs";
// import { SubscriptionTemplateKeyRestDataService } from "../dataServices/LicenseTemplateKey.rest.dataService.mjs";
// import { Context } from "../system/Context.mjs";
// import { Account } from "../models/Account.model.mjs";

// let accounts = [
//   { sub: "<EMAIL>", role: "admin" },
//   { sub: "<EMAIL>", role: "admin" },
//   { sub: "<EMAIL>", role: "user" },
//   { sub: "<EMAIL>", role: "user" },
// ];

// const deviceService = new DeviceService(
//   new DeviceRestDataService(),
//   new DeviceDataRestDataService(),
//   tx
// );

// const accountService = new AccountService(
//   new AccountRestDataService(),
//   deviceService,
//   new EmailService(new TokenService()),
//   new SubscriptionRestDataService()
// );

// const subscriptionService = new SubscriptionService(
//   new SubscriptionRestDataService(),
//   accountService,
//   new FeaturesetRestDataService(),
//   new SubscriptionTemplateRestDataService(),
//   new SubscriptionTemplateKeyRestDataService(),
//   new OfferRestDataService()
// );

// const context = new Context();
// context.hasAdminRights = () => true;

// for (let state of accounts as any[]) {
//   state = { ...state, idp: "mylio", password: "P@$$w0rd" };
//   try {
//     let existings = await accountService.tryBySub(context, state.sub);
//     for (const existing of existings) {
//       await accountService.delete(context, existing.accountId());
//     }
//     let account = await subscriptionService.createAccountAndSubscribe(
//       context,
//       new Account(state)
//     );
//     console.log(`created account ${account.sub()}`);
//   } catch (e) {
//     console.log(JSON.stringify(e));
//   }
// }
