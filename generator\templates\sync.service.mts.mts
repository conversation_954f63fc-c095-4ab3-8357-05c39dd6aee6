import { Entity } from "../Entity.mjs";
import { jsname, ScriptHelper, blockN } from "../ScriptHelper.mjs";

export function template(e: Entity, h: ScriptHelper) {
  return `

import { Context } from "../system/Context.mjs";
import { query, tx } from "../system/Postgres.mjs";
import { EventType } from "../system/Strings.mjs";
import { telemetry } from "../system/telemetry.mjs";
import { config } from "../system/Config.mjs";
import { makeError } from "../system/error.mjs";

export class ${jsname(e)}SyncService {

    constructor(
        private rootName = "${e.plural}",
        ) { }

    async sync(context: Context, aid: number, input, output?) {



        ${e.name === "system_property" ? "aid = 1;" : ""}

        let root = input[this.rootName] && input[this.rootName]["0"]["0"];
        if (!root) return Promise.resolve(undefined);
        let oroot = {} as any;
        output[this.rootName] = { 0: { 0:  oroot } };
        let have = root.have as string[];
        let rawData = root.data || [];
        let doData = !!root.data;
        let doHave = !!root.have;

        ${blockN(e, "before_sync")}


        return query(
                    context,
                    "select * from a0.${e.name}_sync($1, $2, $3, $4, $5)", 
                    [
                        aid, 
                        JSON.stringify(rawData),
                        doData,
                        have,
                        doHave
                    ]
            )
            .then((results: any[]) => {
                oroot.data = results
                    .filter(r => r.op === 2);
                oroot.want = results
                    .filter(r => r.op === 3)
                    .map(r => r.t);

                ${blockN(e, "on_success")}
            });


    }


}`;
}
