import { Entity, Field, Datatype } from "./Entity.mjs";
import * as changeCase from "change-case";

export class <PERSON>riptHelper {
  public pgParams: Array<Field>;
  public updatable: Array<Field>;
  public all: Array<Field>;
  public pk: Array<Field>;
  public dk: Field;
  public t: Field;
  public view: string;
  public selectList: string;
  public defaults: Array<Field>;
  public diskFields: Array<Field>;
  public modelFields: Array<Field>;

  constructor(e: Entity) {
    this.updatable = e.fields.filter((f) => f.canGo("postgres", "disk"));
    this.pgParams = e.fields.filter(
      (f) => f.canGo("postgres", "cloud") || f.canGo("cloud", "postgres")
    );
    if (e.keys.length > 0) {
      this.pk = e.keys[0];
      this.dk = this.pk[this.pk.length - 1];
    }

    this.t = e.fields.find((f) => f.name === "t");
    this.defaults = e.fields.filter((f) => !!f.default);
    this.view = `"${jsname(e)}"`;
    this.diskFields = e.fields.filter(
      (f) => f.canGo("postgres", "disk") || f.canGo("disk", "postgres")
    );
    this.modelFields = e.fields;
  }
}

export function wrapBlock(blockId: string, expr: string) {
  if (expr) {
    return `/* b:${blockId} */${expr} /* end */`;
  }
  return undefined;
}

export function blockN(e: Entity, blockId: string, expr = "") {
  let isUserBlock = false;
  expr = expr || e.generatedBlocks.get(blockId) || "";
  if (e.blocks.has(blockId)) {
    isUserBlock = true;
    expr = e.blocks.get(blockId);
  }
  if (!(expr.startsWith("\n") || expr.startsWith("\r"))) expr = `\n${expr}`;
  if (!(expr.endsWith("\n") || expr.startsWith("\r"))) expr = `\n${expr}`;
  if (isUserBlock) {
    return `\n/* b::${blockId} */\n${expr.trim()}\n/* end */\n`;
  } else {
    return `\n${expr.trim()}\n`;
  }
}

export function mname(root: string, fields: Array<Field>) {
  if (fields) {
    return `${root}_by_${fields.map((f) => `${f.name}`).join("_and_")}`;
  }
}

export function blockI(e: Entity, blockId: string, expr = "") {
  let isUserBlock = false;
  expr = expr || e.generatedBlocks.get(blockId) || "";
  if (e.blocks.has(blockId)) {
    isUserBlock = true;
    expr = e.blocks.get(blockId);
  }
  if (isUserBlock) {
    return `/* b::${blockId} */${expr.trim()}/* end */`;
  } else {
    return `${expr.trim()}`;
  }
}

export function pname(f: Field) {
  return `_${f.name}`;
}

export function ppdt(f: Field) {
  switch (f.datatype) {
    case Datatype.binary:
      return "text";
    case Datatype.int64:
      return "text";
    default:
      return sqldt(f);
  }
}

export function iif(condition: boolean, trueString: string, falseString = "") {
  if (condition) return trueString;
  else return falseString;
}

export function paramName(f: Field) {
  if (ppdt(f) !== sqldt(f)) {
    return `__${f.name}`;
  } else {
    return pname(f);
  }
}

export function sql2js(f: Field) {
  switch (f.datatype) {
    case Datatype.binary:
      return `encode(${f.name}, 'base64')`;
    case Datatype.int64:
      return `${f.name}::text`;
    default:
      return f.name;
  }
}

export function js2sql(f: Field) {
  switch (f.datatype) {
    case Datatype.binary:
      return `decode(${pname(f)}, 'base64')`;
    case Datatype.int64:
      return `${pname(f)}::int8`;
    default:
      return pname(f);
  }
}

export function sqldt(f: Field) {
  switch (f.datatype) {
    case Datatype.binary:
      return "bytea";
    case Datatype.string:
      return "text";
    case Datatype.date:
      return "timestamptz";
    case Datatype.int32:
      return "int";
    case Datatype.int64:
      return "int8";
    case Datatype.decimal:
      return "numeric";
    case Datatype.boolean:
      return "boolean";
    case Datatype.json:
      return "json";
    default:
      throw Error("Datatype not supported");
  }
}

export function jsdt(f: Field) {
  switch (f.datatype) {
    case Datatype.binary:
    case Datatype.string:
    case Datatype.int64:
      return "string";
    case Datatype.date:
      return "Date";
    case Datatype.int32:
    case Datatype.decimal:
      return "number";
    case Datatype.boolean:
      return "boolean";
    case Datatype.json:
      return "any";
    default:
      throw Error("Datatype not supported");
  }
}

export function jsname(i: Field | Entity) {
  if (i instanceof Entity) return changeCase.pascalCase(i.name);
  else return changeCase.camelCase(i.name);
}
