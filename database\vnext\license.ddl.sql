



create table a0.license(
    flags int NULL,
	modified_time timestamptz NULL,
	created_time timestamptz NULL,
	license_id text NOT NULL,
	account_id int NULL,
	manager text NULL,
	end_date timestamptz NULL,
	template_id text NULL,
	activation_key text NULL,
	device_limit int NULL,
	photo_limit int NULL,
	features int NULL,
	cloud_storage_limit int NULL,
	available_upgrades text NULL,
	status int NULL
);

alter table a0.license
add primary key (license_id);

 

 create index ix_license_by_account_id on a0.license(account_id);
create index ix_license_by_activation_key on a0.license(activation_key);

