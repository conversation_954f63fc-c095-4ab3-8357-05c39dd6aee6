#!/bin/bash

function said_yes() {
    INPUT="$1"

    if [[ $INPUT =~ ^[yY] ]]
    then
        echo "yes"
    else
        echo ""
    fi
}

function prompt() {
    PROMPT="$1"
    DEFAULT="$2"

    while ! [[ $ANSWER ]]
    do
        read -e -p "$PROMPT " ANSWER
        ANSWER="${ANSWER:-$DEFAULT}"
    done

    echo $ANSWER
}

function prompt_password() {
    PROMPT="$1"
    DEFAULT="$2"

    while ! [[ $PASSWORD ]]
    do
        read -s -e -p "$PROMPT " PASSWORD
        PASSWORD="${PASSWORD:-$DEFAULT}"
    done

    echo $PASSWORD
}

function prompt_file() {
    PROMPT="$1"
    EXTENSION="$2"
    FILE="$3"

    while ! [[ $FILE ]] || ! [[ -f $FILE ]]  || [[ "${FILE##*.}" != $EXTENSION ]]
    do
        read -e -p "$PROMPT " FILE
        eval FILE=$FILE
    done

    echo $FILE
}

function spinner() {
    pid=$! # Process Id of the previous running command

    spin='-\|/'

    i=0
    while kill -0 $pid 2>/dev/null
    do
        i=$(( (i+1) %4 ))
        printf "\r${spin:$i:1}"
        sleep .1
    done

    printf "\r"
}
