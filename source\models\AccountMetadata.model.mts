

import Validation from "../system/Validation.mjs";
import { IModel } from "./IModel.mjs";







export interface IAccountMetadata {
    flags?: number;
	modifiedTime?: Date;
	createdTime?: Date;
	accountId?: number;
	accountMerkle?: string;
	deviceMerkle?: string;
	messageMerkle?: string;
	systemPropertyMerkle?: string;
	userPropertyMerkle?: string;
	deviceDataMerkle?: string;
	nextDeviceId?: number;
	nextMessageId?: number;
	bootstrapDeviceId?: string;
	nextSystemPropertyId?: number;
}


export class AccountMetadata 
implements IModel {
    private _state: IAccountMetadata;

    


    
    changed = false;

    constructor(state: IAccountMetadata) {
        this.state(state);
    }

    isUndefined() {
        return this._state !== void 0;
    }

    async validate(skipUndefined?: boolean) {
        let v = new Validation(this, skipUndefined);
        


        return v;
    }

    rtt() {
        return "AccountMetadata"; 
    }

    state (value?: IAccountMetadata) {
        if (value !== undefined) { 
            this._state = value;
            
            


        }
        return this._state;
    }

    flags(value?: number) {
                if (value !== void 0) {
                    if (this.state().flags !== value) {
                        this.state().flags = value;
                        this.changed = true;
                    }
                }
                return this.state().flags;
            };

		modifiedTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().modifiedTime !== value) {
                        this.state().modifiedTime = value;
                        this.changed = true;
                    }
                }
                return this.state().modifiedTime;
            };

		createdTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().createdTime !== value) {
                        this.state().createdTime = value;
                        this.changed = true;
                    }
                }
                return this.state().createdTime;
            };

		accountId(value?: number) {
                if (value !== void 0) {
                    if (this.state().accountId !== value) {
                        this.state().accountId = value;
                        this.changed = true;
                    }
                }
                return this.state().accountId;
            };

		accountMerkle(value?: string) {
                if (value !== void 0) {
                    if (this.state().accountMerkle !== value) {
                        this.state().accountMerkle = value;
                        this.changed = true;
                    }
                }
                return this.state().accountMerkle;
            };

		deviceMerkle(value?: string) {
                if (value !== void 0) {
                    if (this.state().deviceMerkle !== value) {
                        this.state().deviceMerkle = value;
                        this.changed = true;
                    }
                }
                return this.state().deviceMerkle;
            };

		messageMerkle(value?: string) {
                if (value !== void 0) {
                    if (this.state().messageMerkle !== value) {
                        this.state().messageMerkle = value;
                        this.changed = true;
                    }
                }
                return this.state().messageMerkle;
            };

		systemPropertyMerkle(value?: string) {
                if (value !== void 0) {
                    if (this.state().systemPropertyMerkle !== value) {
                        this.state().systemPropertyMerkle = value;
                        this.changed = true;
                    }
                }
                return this.state().systemPropertyMerkle;
            };

		userPropertyMerkle(value?: string) {
                if (value !== void 0) {
                    if (this.state().userPropertyMerkle !== value) {
                        this.state().userPropertyMerkle = value;
                        this.changed = true;
                    }
                }
                return this.state().userPropertyMerkle;
            };

		deviceDataMerkle(value?: string) {
                if (value !== void 0) {
                    if (this.state().deviceDataMerkle !== value) {
                        this.state().deviceDataMerkle = value;
                        this.changed = true;
                    }
                }
                return this.state().deviceDataMerkle;
            };

		nextDeviceId(value?: number) {
                if (value !== void 0) {
                    if (this.state().nextDeviceId !== value) {
                        this.state().nextDeviceId = value;
                        this.changed = true;
                    }
                }
                return this.state().nextDeviceId;
            };

		nextMessageId(value?: number) {
                if (value !== void 0) {
                    if (this.state().nextMessageId !== value) {
                        this.state().nextMessageId = value;
                        this.changed = true;
                    }
                }
                return this.state().nextMessageId;
            };

		bootstrapDeviceId(value?: string) {
                if (value !== void 0) {
                    if (this.state().bootstrapDeviceId !== value) {
                        this.state().bootstrapDeviceId = value;
                        this.changed = true;
                    }
                }
                return this.state().bootstrapDeviceId;
            };

		nextSystemPropertyId(value?: number) {
                if (value !== void 0) {
                    if (this.state().nextSystemPropertyId !== value) {
                        this.state().nextSystemPropertyId = value;
                        this.changed = true;
                    }
                }
                return this.state().nextSystemPropertyId;
            };

    differs(original: AccountMetadata) {
        return (
            (this.flags() !== void 0 && this.flags() !== original.flags())
		 || (this.modifiedTime() !== void 0 && this.modifiedTime() !== original.modifiedTime())
		 || (this.createdTime() !== void 0 && this.createdTime() !== original.createdTime())
		 || (this.accountId() !== void 0 && this.accountId() !== original.accountId())
		 || (this.accountMerkle() !== void 0 && this.accountMerkle() !== original.accountMerkle())
		 || (this.deviceMerkle() !== void 0 && this.deviceMerkle() !== original.deviceMerkle())
		 || (this.messageMerkle() !== void 0 && this.messageMerkle() !== original.messageMerkle())
		 || (this.systemPropertyMerkle() !== void 0 && this.systemPropertyMerkle() !== original.systemPropertyMerkle())
		 || (this.userPropertyMerkle() !== void 0 && this.userPropertyMerkle() !== original.userPropertyMerkle())
		 || (this.deviceDataMerkle() !== void 0 && this.deviceDataMerkle() !== original.deviceDataMerkle())
		 || (this.nextDeviceId() !== void 0 && this.nextDeviceId() !== original.nextDeviceId())
		 || (this.nextMessageId() !== void 0 && this.nextMessageId() !== original.nextMessageId())
		 || (this.bootstrapDeviceId() !== void 0 && this.bootstrapDeviceId() !== original.bootstrapDeviceId())
		 || (this.nextSystemPropertyId() !== void 0 && this.nextSystemPropertyId() !== original.nextSystemPropertyId())
        );
    }







}



export function sanitizeInput(source: AccountMetadata, amdin: boolean, mode: string) : IAccountMetadata;
export function sanitizeInput(source: IAccountMetadata, admin: boolean, mode: string) : IAccountMetadata;
export function sanitizeInput(source: AccountMetadata | IAccountMetadata, admin = false, mode="default"): IAccountMetadata {
    let s: IAccountMetadata;
    if (source instanceof AccountMetadata)
        s = source.state();
    else
        s = source;        
    let t = {} as IAccountMetadata;
    
    
        
            switch(mode) {
                
                
                
            }
            t.flags = s.flags;
		t.accountId = s.accountId;
		t.accountMerkle = s.accountMerkle;
		t.deviceMerkle = s.deviceMerkle;
		t.messageMerkle = s.messageMerkle;
		t.systemPropertyMerkle = s.systemPropertyMerkle;
		t.userPropertyMerkle = s.userPropertyMerkle;
		t.deviceDataMerkle = s.deviceDataMerkle;
		t.nextDeviceId = s.nextDeviceId;
		t.nextMessageId = s.nextMessageId;
		t.bootstrapDeviceId = s.bootstrapDeviceId;
		t.nextSystemPropertyId = s.nextSystemPropertyId;
        
    return t;
}

export function sanitizeOutput(source: AccountMetadata, amdin: boolean) : IAccountMetadata;
export function sanitizeOutput(source: IAccountMetadata, admin: boolean) : IAccountMetadata;
export function sanitizeOutput(source: AccountMetadata | IAccountMetadata, admin = false): IAccountMetadata {
    let s: IAccountMetadata;
    if (source instanceof AccountMetadata)
        s = source.state();
    else
        s = source;        
    let t = {} as IAccountMetadata;
    
    
    
    t.flags = s.flags;	
t.modifiedTime = s.modifiedTime;	
t.createdTime = s.createdTime;	
t.accountId = s.accountId;	
t.accountMerkle = s.accountMerkle;	
t.deviceMerkle = s.deviceMerkle;	
t.messageMerkle = s.messageMerkle;	
t.systemPropertyMerkle = s.systemPropertyMerkle;	
t.userPropertyMerkle = s.userPropertyMerkle;	
t.deviceDataMerkle = s.deviceDataMerkle;	
t.nextDeviceId = s.nextDeviceId;	
t.nextMessageId = s.nextMessageId;	
t.bootstrapDeviceId = s.bootstrapDeviceId;	
t.nextSystemPropertyId = s.nextSystemPropertyId;
    return t;
}

export function mergeState(dbVersion: IAccountMetadata, newVersion: IAccountMetadata) {
    let targetState: IAccountMetadata = {};
    targetState.flags = newVersion.flags === undefined ? dbVersion.flags : newVersion.flags;
	targetState.modifiedTime = newVersion.modifiedTime === undefined ? dbVersion.modifiedTime : newVersion.modifiedTime;
	targetState.createdTime = newVersion.createdTime === undefined ? dbVersion.createdTime : newVersion.createdTime;
	targetState.accountId = newVersion.accountId === undefined ? dbVersion.accountId : newVersion.accountId;
	targetState.accountMerkle = newVersion.accountMerkle === undefined ? dbVersion.accountMerkle : newVersion.accountMerkle;
	targetState.deviceMerkle = newVersion.deviceMerkle === undefined ? dbVersion.deviceMerkle : newVersion.deviceMerkle;
	targetState.messageMerkle = newVersion.messageMerkle === undefined ? dbVersion.messageMerkle : newVersion.messageMerkle;
	targetState.systemPropertyMerkle = newVersion.systemPropertyMerkle === undefined ? dbVersion.systemPropertyMerkle : newVersion.systemPropertyMerkle;
	targetState.userPropertyMerkle = newVersion.userPropertyMerkle === undefined ? dbVersion.userPropertyMerkle : newVersion.userPropertyMerkle;
	targetState.deviceDataMerkle = newVersion.deviceDataMerkle === undefined ? dbVersion.deviceDataMerkle : newVersion.deviceDataMerkle;
	targetState.nextDeviceId = newVersion.nextDeviceId === undefined ? dbVersion.nextDeviceId : newVersion.nextDeviceId;
	targetState.nextMessageId = newVersion.nextMessageId === undefined ? dbVersion.nextMessageId : newVersion.nextMessageId;
	targetState.bootstrapDeviceId = newVersion.bootstrapDeviceId === undefined ? dbVersion.bootstrapDeviceId : newVersion.bootstrapDeviceId;
	targetState.nextSystemPropertyId = newVersion.nextSystemPropertyId === undefined ? dbVersion.nextSystemPropertyId : newVersion.nextSystemPropertyId;
    return targetState;
}

export function merge(dbVersion: AccountMetadata, newVersion: AccountMetadata) {
    return new AccountMetadata(mergeState(dbVersion.state(), newVersion.state()));
}
