
import { makeAutoObservable } from "mobx"
    


    




export interface IDevice {
    flags?: number;
	modifiedTime?: Date;
	createdTime?: Date;
	accountId?: number;
	deleted?: boolean;
	t?: string;
	d?: string;
	deviceId?: number;
	name?: string;
	deviceType?: number;
	nickname?: string;
	encrypt?: boolean;
	creationTime?: Date;
	longId?: string;
	supportTicket?: string;
}

export interface IWireDevice {
    flags?: number;
	modifiedTime?: string;
	createdTime?: string;
	accountId?: number;
	deleted?: boolean;
	t?: string;
	d?: string;
	deviceId?: number;
	name?: string;
	deviceType?: number;
	nickname?: string;
	encrypt?: boolean;
	creationTime?: string;
	longId?: string;
	supportTicket?: string;
}

export class Device implements IDevice {
    flags?: number;
	modifiedTime?: Date;
	createdTime?: Date;
	accountId?: number;
	deleted?: boolean;
	t?: string;
	d?: string;
	deviceId?: number;
	name?: string;
	deviceType?: number;
	nickname?: string;
	encrypt?: boolean;
	creationTime?: Date;
	longId?: string;
	supportTicket?: string;
    setFlags(value: number) { this.changed = true; this.flags = value; }
	setModifiedTime(value: Date) { this.changed = true; this.modifiedTime = value; }
	setCreatedTime(value: Date) { this.changed = true; this.createdTime = value; }
	setAccountId(value: number) { this.changed = true; this.accountId = value; }
	setDeleted(value: boolean) { this.changed = true; this.deleted = value; }
	setT(value: string) { this.changed = true; this.t = value; }
	setD(value: string) { this.changed = true; this.d = value; }
	setDeviceId(value: number) { this.changed = true; this.deviceId = value; }
	setName(value: string) { this.changed = true; this.name = value; }
	setDeviceType(value: number) { this.changed = true; this.deviceType = value; }
	setNickname(value: string) { this.changed = true; this.nickname = value; }
	setEncrypt(value: boolean) { this.changed = true; this.encrypt = value; }
	setCreationTime(value: Date) { this.changed = true; this.creationTime = value; }
	setLongId(value: string) { this.changed = true; this.longId = value; }
	setSupportTicket(value: string) { this.changed = true; this.supportTicket = value; }
    changed = false;
    setChanged() {
        this.changed = true;
    }

    clearChanged() {
        this.changed = false;
    }

    constructor(state? : IWireDevice | IDevice) {
        if (!state)
            throw "An Device must have a valid start state";
        this.flags = state.flags;;
	if (typeof(state.modifiedTime) === "string")
            this.modifiedTime = new Date(state.modifiedTime);
         else
            this.modifiedTime = state.modifiedTime;
	if (typeof(state.createdTime) === "string")
            this.createdTime = new Date(state.createdTime);
         else
            this.createdTime = state.createdTime;
	this.accountId = state.accountId;;
	this.deleted = state.deleted;;
	this.t = state.t;;
	this.d = state.d;;
	this.deviceId = state.deviceId;;
	this.name = state.name;;
	this.deviceType = state.deviceType;;
	this.nickname = state.nickname;;
	this.encrypt = state.encrypt;;
	if (typeof(state.creationTime) === "string")
            this.creationTime = new Date(state.creationTime);
         else
            this.creationTime = state.creationTime;
	this.longId = state.longId;;
	this.supportTicket = state.supportTicket;
        makeAutoObservable(this, {
            flags: true,
			modifiedTime: true,
			createdTime: true,
			accountId: true,
			deleted: true,
			t: true,
			d: true,
			deviceId: true,
			name: true,
			deviceType: true,
			nickname: true,
			encrypt: true,
			creationTime: true,
			longId: true,
			supportTicket: true
        });

    }

    state() : IDevice {
        return {
            flags : this.flags,
		modifiedTime : this.modifiedTime,
		createdTime : this.createdTime,
		accountId : this.accountId,
		deleted : this.deleted,
		t : this.t,
		d : this.d,
		deviceId : this.deviceId,
		name : this.name,
		deviceType : this.deviceType,
		nickname : this.nickname,
		encrypt : this.encrypt,
		creationTime : this.creationTime,
		longId : this.longId,
		supportTicket : this.supportTicket
        };
    }

    asWire() : IWireDevice {
        return {
            flags : this.flags,
		modifiedTime : this.modifiedTime ? this.modifiedTime.toISOString() : undefined,
		createdTime : this.createdTime ? this.createdTime.toISOString() : undefined,
		accountId : this.accountId,
		deleted : this.deleted,
		t : this.t,
		d : this.d,
		deviceId : this.deviceId,
		name : this.name,
		deviceType : this.deviceType,
		nickname : this.nickname,
		encrypt : this.encrypt,
		creationTime : this.creationTime ? this.creationTime.toISOString() : undefined,
		longId : this.longId,
		supportTicket : this.supportTicket
        };
    }

    



    


}


