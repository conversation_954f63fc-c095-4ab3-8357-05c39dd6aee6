
export enum FeatureBitsEnum {
    RemoteControl = 0x1,
    Permissions = 0x2,
    LimitedAccessMode = 0x4,
    EncryptedStorage = 0x8,
    GuestDeviceSetup = 0x10,
    CollaborativeZone = 0x20,
    MultipleCameraRolls = 0x40,
    DeviceSync = 0x80,
    FullDedup = 0x100
}

export class FeatureBits {
    private state_: number;

    constructor(state: number = 0) {
        this.state_ = state;
    }

    onChange: (features: number) => void;

    changeFlag(flag: FeatureBitsEnum, value: boolean | undefined) {
        if (value !== void 0) {
            if (value) this.state_ |= flag;
            else this.state_ &= ~flag;
        }
        this.onChange(this.state_);
        return !!(this.state_ & flag);
    }

    fullDedup(value: boolean | undefined = undefined) {
        return this.changeFlag(FeatureBitsEnum.FullDedup, value);
    }

    deviceSync(value: boolean | undefined = undefined) {
        return this.changeFlag(FeatureBitsEnum.DeviceSync, value);
    }

    multipleCameraRolls(value: boolean | undefined = undefined) {
        return this.changeFlag(FeatureBitsEnum.MultipleCameraRolls, value);
    }

    collaborativeZone(value: boolean | undefined = undefined) {
        return this.changeFlag(FeatureBitsEnum.CollaborativeZone, value);
    }

    guestDeviceSetup(value: boolean | undefined = undefined) {
        return this.changeFlag(FeatureBitsEnum.GuestDeviceSetup, value);
    }

    encryptedStorage(value: boolean | undefined = undefined) {
        return this.changeFlag(FeatureBitsEnum.EncryptedStorage, value);
    }

    limitedAccessMode(value: boolean | undefined = undefined) {
        return this.changeFlag(FeatureBitsEnum.LimitedAccessMode, value);
    }

    permissions(value: boolean | undefined = undefined) {
        return this.changeFlag(FeatureBitsEnum.Permissions, value);
    }

    remoteControl(value: boolean | undefined = undefined) {
        return this.changeFlag(FeatureBitsEnum.RemoteControl, value);
    }
}