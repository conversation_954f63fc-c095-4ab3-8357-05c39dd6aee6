{
  "port": 3000,
  "website": "http://localhost:8080",
  "cloud": "http://localhost:3000",
  "wordpress": "http://mylio.com",
  "refresh_token_lifespan": "5 years",
  "access_token_lifespan": "30m",
  "stripe_trial_duration": "PT5M",
  "postgres_trial_duration": "P30D",
  "mylio_trial_duration": "P60D",
  "access_token_secret": "ACCESS_TOKEN_SECRET",
  "refresh_token_secret": "REFRESH_TOKEN_SECRET",
  "activation_link_lifespan": "2 days",
  "reset_password_link_lifespan": "2 days",
  "binary_encoding": "hex",
  "email_from": "<EMAIL>",
  "email_from_name": "Mylio Support",
  "email_token_secret": "EMAIL_TOKEN_SECRET",
  "magic_link_token_lifespan": "24 hours",
  "create_link_token_lifespan": "24 hours",
  "login_link_token_lifespan": "PT24H",
  "invitation_duration": "P2D",
  "services": [
    {
      "name": "resource",
      "uri": [
        "http://localhost:4000"
      ]
    },
    {
      "name": "location",
      "uri": [
        "https://api.opencagedata.com/geocode/v1/json?q=%f,%f&pretty=0&no_annotations=1&key=74306074d3a5ef1f7c86ea43062367a6"
      ]
    },
    {
      "name": "cloudSignalChannel",
      "uri": [
        "signal.mylio.com"
      ]
    }
  ],
  "minimum_supported_build": 4554,
  "logLevel": 3,
  "location_cache_lifespan": "P30D",
  "amazon": {
    "clientID": "amzn1.application-oa2-client.1a59cd3047874e41940df3c6feb19f94",
    "clientSecret": "beb6e5951e3eafa439b48944eefd32bbf214b12cdc3e060de67a0970a408f98e"
  },
  "apple": {
    "authority": "https://appleid.apple.com",
    "clientIds": [
      "com.mylollc.Mylio",
      "com.mylio.app"
    ],
    "teamId": "AEW565VKHV",
    "requestSignAlg": "ES256",
    "redirectUrl": "https://merkle3accounttest.mylio.com/apple/redirect",
    "appStorePassword": "b2bbca916d88478c9e9a3d7673281926",
    "appStoreSecret": "b2bbca916d88478c9e9a3d7673281926",
    "tokenLifeTimeInSeconds": 600,
    "appstoreEnvironment": "Sandbox"
  },
  "facebook": {
    "clientID": "fakeid",
    "clientSecret": "fakesecret"
  },
  "instagram": {
    "clientID": "fakeid",
    "clientSecret": "fakesecret"
  },
  "google": {
    "clientID": "*************-********************************.apps.googleusercontent.com",
    "clientSecret": "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"
  },
  "microsoft": {
    "clientID": "********-8bb0-4ff2-8116-a38def88292e",
    "clientSecret": "7:K@unEQO[]B2f3geod3Jls27?zK.?_i"
  },
  "s3_support_bucket": "mylo_support",
  "admins": [
    "<EMAIL>"
  ],
  "rendezvous_token_secret": "RENDEZVOUS_TOKEN_SECRET",
  "pg_timeout": 5000,
  "bad_client_timeout": 300000,
  "stun_token_lifetime": 24,
  "stun_secret": "secret",
  "breaking_builds": [
    5301,
    5595
  ],
  "create_plan_change_date": "2021-06-01",
  "mylio_api_key": "3cb00042eb4f9c7384b335dfa396ca831b153d1df43b72facfe6cf005d9a19ef",
  "stripe_version": "2022-11-15",
  "fastspring_template_key": "FASTSPRING_TEMPLATE_KEY",
  "fastspring_webhook_secret": "FASTSPRING_WEBHOOK_SECRET",
  "stripe_template_key": "STRIPE_TEMPLATE_KEY",
  "fastspring_api_secret": "FASTSPRING_API_SECRET",
  "apple_template_key": "APPLE_TEMPLATE_KEY",
  "syncThrottleInterval": "1 minute",
}