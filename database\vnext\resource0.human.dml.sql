
\c /* r::database */ resource0; 

\i plpgsql_trev.sql

SET client_min_messages=WARNING;

create or replace function public.presync()
returns void
as
$$
    create temp table if not exists __data (
        _account_id int,
        _rid bytea,
        _t bytea,
        _d bytea,
        primary key (_account_id, _rid)
    );
    
    create temp table if not exists __have (
        _account_id int,
        _rid bytea,
        _t bytea,
        primary key (_account_id, _rid)
    );
$$
language sql;

select * from public.presync();

create or replace function sync(__account_id int, schema text, commands json) 
returns table(n0ff int, op int, t text, d text)
as
$$
declare
	b json;
begin
    execute 'insert into ' || schema || '.bf00_metadata(account_id,merkle) values($1,E''\\x0000000000000000000000000000000000000000'') on conflict(account_id) do nothing' using __account_id;
    perform presync();
    execute 'select * from ' || schema || '.bf00_metadata where account_id = $1 for update nowait' using __account_id;
    for b IN (select json_array_elements(commands)) loop
        if (b->'data' is not null) then
            return query execute 'select ' || (b->'n0ff')::text || ' n0ff, results.* from ' || (b->>'table') || '_sync_data($1,$2) results'::text using __account_id, b->'data';
        end if;
        if (b->'have' is not null) then
            return query execute 'select ' || (b->'n0ff')::text || ' n0ff, results.* from ' || (b->>'table') || '_sync_have($1,$2) results'::text using __account_id, b->'have';
        end if;
    end loop;
    execute 'select * from ' || schema || '.bf00_merkle($1)' using __account_id;
end;	
$$
language plpgsql
CALLED ON NULL INPUT;


grant execute on function digest(bytea, text) to public;
grant execute on function digest(bytea, text) to public;
grant execute on function gen_random_bytes(int) to public;

/* repeat for each schema */

\i x0.human.dml.sql

/* end schemas */

