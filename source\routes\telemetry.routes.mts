import express = require("express");
import { microservice as g } from "../microservices/telemetry.microservice.mjs";
import { safeAny, secure, safeNone, safeAccount } from "../system/safe.mjs";
import { Context } from "../system/Context.mjs";

export function addTelemetryRoutes(router: express.Router) {
  router.post(
    "/accounts/:aid/devices/:did/counters",
    safeAny,
    (req, res, next) => {
      const context: Context = req.context;

      return g.telemetryService
        .saveCounters(context, context.aid, context.did, context.any)
        .then(() => res.status(200).json({}))
        .catch(next);
    }
  );

  router.post("/devices/:longdid/events", safeAny, (req, res, next) => {
    const context: Context = req.context;

    return g.telemetryService
      .saveClientEvents(context, req.params.longdid, context.any.data)
      .then(() => res.status(200).json({}))
      .catch(next);
  });

  router.post("/accounts/:aid/cloud-events", safeAny, (req, res, next) => {
    const context: Context = req.context;
    return g.telemetryService
      .saveCloudEvent(
        context,
        context.aid,
        context.any.did || 0,
        context.any.eventType,
        context.any.data
      )
      .then(() => res.status(200).json({}))
      .catch(next);
  });

  router.get(
    "/accounts/:aid/last-access-time",
    safeNone,
    secure,
    (req, res, next) => {
      const context: Context = req.context;
      const aid = context.aid;

      return g.telemetryService
        .getLastAccessTimeForAccount(context, aid)
        .then((result) => {
          context.dumpLog();
          return res.status(200).json(result);
        })
        .catch(next);
    }
  );

  router.post("/accounts/:aid/exit-surveys", safeAny, (req, res, next) => {
    const context: Context = req.context;
    const aid = context.aid;
    const data = context.any;

    return g.telemetryService
      .saveExitSurvey(context, aid, data)
      .then(() => {
        return res.sendStatus(200);
      })
      .catch(next);
  });

  return router;
}
