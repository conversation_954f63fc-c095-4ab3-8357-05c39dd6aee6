


create or replace function x0.ping256(_account_id int)
returns table(blob text)
as
$$

select encode
		(string_agg
			(coalesce(merkle, E'\\x0000000000000000000000000000000000000000'
			)
			, null order by _0_to_ff
		)
	, 'base64')  blob
from generate_series(0, 255) as _0_to_ff
left join x0.b0ff_metadata meta on meta.account_id = _account_id and meta.b0ff = _0_to_ff;

$$
language sql; 


create or replace function x0.bf00_merkle(__account_id int)
returns void
as
$$
    insert into x0.bf00_metadata(account_id, merkle)
    select __account_id, 
	digest(
		string_agg(
			coalesce(merkle, E'\\x0000000000000000000000000000000000000000'
			)
			, null order by _0_to_ff
		)
		, 'sha1')
    from generate_series(0, 255) as _0_to_ff
    left join x0.b0ff_metadata meta on meta.account_id = __account_id and meta.b0ff = _0_to_ff
    on conflict(account_id) do update set merkle = EXCLUDED.merkle;
$$
language sql;



/* repeat for each table */

\i x00.human.dml.sql

/* end tables */

