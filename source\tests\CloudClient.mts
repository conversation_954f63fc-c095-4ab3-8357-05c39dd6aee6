import { Agent } from "node:http";
import { IAccount } from "../models/Account.model.mjs";

interface ErrorFunc {
  (err: { code: string; message: string }): void;
}

export class CloudClient {
  private atoken = "";
  private rtoken = "";
  private itoken = "";
  public account?: IAccount;
  public admin?: IAccount;
  private errorCallback?: ErrorFunc;

  constructor(private host: string) { }

  impersonated() {
    return !!this.itoken;
  }


  public async jsonRequest(
    method: string,
    route: string,
    body: any,
    headers: any
  ) {
    if (this.account && this.account.accountId) {
      route = route.replace(":aid", this.account.accountId.toString());
    }
    if (this.account && this.account.sub) {
      route = route.replace(":sub", this.account.sub);
    }
    headers = { ...headers, "Content-Type": "application/json", "User-Agent": "Mylio" };
    let token = this.itoken || this.atoken || this.rtoken;
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
    if (!route.startsWith("/")) route = "/" + route;
    let url = this.host + route;
    let r: RequestInit = {
      method,
      mode: "cors", // no-cors, *cors, same-origin
      cache: "no-cache", // *default, no-cache, reload, force-cache, only-if-cached
      credentials: "same-origin", // include, *same-origin, omit
      headers,
      redirect: "follow", // manual, *follow, error
      referrerPolicy: "no-referrer", // no-referrer, *no-referrer-when-downgrade, origin, origin-when-cross-origin, same-origin, strict-origin, strict-origin-when-cross-origin, unsafe-url
    };
    if (body) r.body = JSON.stringify(body);

    // Default options are marked with *
    const response = await fetch(url, r);
    if (!response.ok) {
      let error = await response.json();
      if (this.errorCallback) this.errorCallback(error);
      throw error;
    }
    let payload;
    try {
      payload = await response.json();
    } catch (error) {
      payload = {};
    }
    return payload; // parses JSON response into native JavaScript objects
  }

  async impersonate(aid: number) {
    if (this.impersonated()) this.stopImpersonating();
    let r = await this.post<{ token: string; account: IAccount }>(
      "admin/impersonate",
      { aid }
    );
    this.admin = this.account;
    this.account = r.account;
    this.itoken = r.token;
    return this.account;
  }

  stopImpersonating() {
    this.account = this.admin;
    this.itoken = "";
  }

  async post<T>(route: string, body?: any, headers?: any) {
    return this.jsonRequest("POST", route, body, headers) as T;
  }

  async get<T>(route: string, headers?: any) {
    return this.jsonRequest("GET", route, undefined, headers) as T;
  }

  async delete<T>(route: string, headers?: any) {
    return this.jsonRequest("DELETE", route, undefined, headers) as T;
  }

  async put<T>(route: string, body?: any, headers?: any) {
    return this.jsonRequest("PUT", route, body, headers) as T;
  }

  async signin(email: string, password: string) {
    this.atoken = "";
    this.account = undefined;
    this.admin = undefined;
    this.rtoken = "";
    const tokenR = await this.post<{ account: IAccount; rtoken: string }>(
      "/v4/accounts/x/rtoken",
      { idp: "mylio", sub: email, password }
    );
    this.rtoken = tokenR.rtoken;
    const tokenA = await this.get<{ account: IAccount; token: string }>(
      "/v2/accounts/x/token"
    );
    this.atoken = tokenA.token;
    this.account = tokenA.account;
    return this.account;
  }

  onError(callback: ErrorFunc) {
    this.errorCallback = callback;
  }

  signedin() {
    return !!this.account;
  }
}
