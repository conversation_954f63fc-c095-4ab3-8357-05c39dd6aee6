{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Launch Account Service", "program": "${workspaceFolder}//debug/source/account.microservice.express", "runtimeArgs": ["--<PERSON><PERSON><PERSON>", "--harmony"], "env": {"NODE_ENV": "development", "HOSTNAME": "localhost.account"}}, {"type": "node", "request": "attach", "name": "Debug Resource", "address": "localhost", "port": 5858, "remoteRoot": "/mnt/c/git/cloudv3", "sourceMaps": true}, {"type": "node", "request": "attach", "name": "Attach by Process ID"}, {"name": "(gdb) Attach", "type": "cppdbg", "request": "attach", "program": "/home/<USER>/.nvm/versions/node/v10.0.0/bin/node", "processId": "${command:pickProcess}", "MIMode": "gdb"}, {"type": "node", "request": "attach", "name": "Attach", "port": 5858, "sourceMaps": true}, {"type": "node", "request": "launch", "name": "aws login", "program": "${workspaceFolder}/aws-login.js", "sourceMaps": true, "stopOnEntry": true}, {"type": "node", "request": "launch", "name": "Account", "program": "${workspaceFolder}//debug/source/account.microservice.express", "sourceMaps": true, "stopOnEntry": true, "env": {"PGCREDS": "cloud:mjPinKQMeJg3zpFTDzEtEANu", "TZ": "UTC", "HOSTNAME": "ebs.account", "PORT": "3000", "NODE_ENV": "test", "STRIPE_PRIVATE_KEY": "sk_test_qoromUDiG2j5BGwlYdZ40VRx", "CLOUD": "http://localhost:3000", "USE_SECRETS_MANAGER": "0", "LOG_LEVEL": "0", "MAGIC_LINK_TOKEN_LIFESPAN": "2 days"}}, {"type": "node", "request": "launch", "name": "Generate", "cwd": "${workspaceFolder}/generator", "program": "${workspaceFolder}/debug/generator/generateAll.mjs", "sourceMaps": true, "stopOnEntry": true, "env": {"PGCREDS": "cloud:mjPinKQMeJg3zpFTDzEtEANu", "TZ": "UTC", "HOSTNAME": "ebs.account", "PORT": "3000", "NODE_ENV": "test", "STRIPE_PRIVATE_KEY": "sk_test_qoromUDiG2j5BGwlYdZ40VRx", "CLOUD": "http://localhost:3000", "USE_SECRETS_MANAGER": "0", "LOG_LEVEL": "0", "MAGIC_LINK_TOKEN_LIFESPAN": "2 days"}}, {"type": "node", "request": "launch", "name": "Test License", "cwd": "${workspaceFolder}", "program": "${workspaceFolder}//debug/source/tests/License.tests.js", "runtimeArgs": ["--test"], "sourceMaps": true, "stopOnEntry": false, "env": {"PGCREDS": "cloud:mjPinKQMeJg3zpFTDzEtEANu", "TZ": "UTC", "HOSTNAME": "ebs.account", "PORT": "3000", "NODE_ENV": "test", "STRIPE_PRIVATE_KEY": "sk_test_qoromUDiG2j5BGwlYdZ40VRx", "CLOUD": "http://localhost:3000", "USE_SECRETS_MANAGER": "0", "LOG_LEVEL": "0", "MAGIC_LINK_TOKEN_LIFESPAN": "2 days"}}]}