import { config, getServices } from "../system/Config.mjs";
import { error, makeError } from "../system/error.mjs";
import { RefreshTokenService } from "./RefreshTokenService.mjs";
import { Context } from "../system/Context.mjs";
import { RefreshToken } from "../models/RefreshToken.model.mjs";
import {
  Device,
  DeviceType,
  sanitizeOutput as sanitizeDevice,
} from "../models/Device.model.mjs";
import { DeviceService } from "./DeviceService.mjs";
import { tx } from "../system/Postgres.mjs";
import { LockService } from "./LockService.mjs";
import { ids } from "../system/Strings.mjs";
import { postForm } from "../system/fetch.mjs";

export class GoogleOAuthService {
  async getRefreshToken(code: string, redirectUrl?: string) {
    let user = await this.autorizationCodeToTokens(code, redirectUrl);
    return user.refresh_token;
  }

  async autorizationCodeToTokens(code: string, redirectUrl: string) {
    let form = {
      client_id: config.google.clientID,
      client_secret: config.google.clientSecret,
      code: code,
      grant_type: "authorization_code",
      redirect_uri: redirectUrl || `${config.cloud}/google/authorize`,
    };
    const tokenResponse = await postForm(
      "https://www.googleapis.com/oauth2/v4/token",
      form
    );
    const tokenResult = await tokenResponse.json();
    return tokenResult;
  }

  async getAccessToken(rtoken: string) {
    let form = {
      client_id: config.google.clientID,
      client_secret: config.google.clientSecret,
      grant_type: "refresh_token",
      refresh_token: rtoken,
    };
    let tokenResponse = await postForm(
      "https://www.googleapis.com/oauth2/v4/token",
      form
    );
    let tokenResult = await tokenResponse.json();
    return tokenResult.access_token;
  }

  public getDriveOAuthUrl(
    context: Context,
    aid: number,
    encrypt: boolean,
    redirect: string
  ) {
    return this.getOAuthUrl(
      context,
      aid,
      "drive",
      encrypt,
      redirect,
      `https://www.googleapis.com/auth/drive.file`
    );
  }

  public getPicasaOAuthUrl(
    context: Context,
    aid: number,
    encrypt: boolean,
    redirect: string
  ) {
    const build = context.userAgent?.build;

    if (
      build &&
      build >= 5622 /* <-- only for test. 5700 <-- for production */
    ) {
      const picasaUrl =
        "https://www.googleapis.com/auth/photoslibrary.readonly";
      return this.getOAuthUrl(
        context,
        aid,
        "import-images",
        encrypt,
        redirect,
        picasaUrl
      );
    } else {
      return "";
    }
  }

  private getOAuthUrl(
    context: Context,
    aid,
    task: string,
    encrypt: boolean,
    redirect: string,
    scope: string
  ) {
    let state = Buffer.from(
      JSON.stringify({ aid, encrypt, task, redirect }),
      "utf8"
    ).toString("base64");
    let uri = `${config.cloud}/google/authorize`;
    // tslint:disable
    return `https://accounts.google.com/o/oauth2/auth?scope=${scope}&state=${state}&response_type=code&include_granted_scopes=true&redirect_uri=${uri}&client_id=${config.google.clientID}&prompt=consent&access_type=offline`;
    // tslint:enable
  }
}

export class GoogleService {
  private _oauth = new GoogleOAuthService();

  constructor(
    private refreshTokenService: RefreshTokenService,
    private deviceService: DeviceService,
    private lockService: LockService
  ) { }

  public getDriveAccessToken(context: Context, aid: number) {
    const idp = "google";
    const task = "drive";

    return this.refreshTokenService
      .read(context, aid, idp, task)
      .then((refreshToken) => {
        if (!refreshToken)
          return error<{ token: string }>(400, ids.NO_REFRESH_TOKEN);

        return this._oauth
          .getAccessToken(refreshToken.token())
          .catch((err) =>
            this.handleGetAccessTokenError(context, err, aid, idp, task)
          );
      });
  }

  public getImportImagesAccessToken(context: Context, aid: number) {
    const idp = "google";
    const task = "import-images";

    return this.refreshTokenService
      .read(context, aid, idp, task)
      .then((refreshToken) => {
        if (!refreshToken)
          return error<{ token: string }>(400, ids.NO_REFRESH_TOKEN);

        return this._oauth
          .getAccessToken(refreshToken.token())
          .catch((err) =>
            this.handleGetAccessTokenError(context, err, aid, idp, task)
          );
      });
  }

  public getDriveOAuthUrl(
    context: Context,
    aid: number,
    encrypt: boolean,
    redirect: string
  ) {
    return this._oauth.getDriveOAuthUrl(context, aid, encrypt, redirect);
  }

  public getImportImagesOAuthUrl(
    context: Context,
    aid: number,
    encrypt: boolean,
    redirect: string
  ) {
    return this._oauth.getPicasaOAuthUrl(context, aid, encrypt, redirect);
  }

  public addRefreshToken(authorizationCode: string, state: any) {
    let rtokenString: string;
    let tokenString: string;
    let context = new Context();
    let task = state.task;
    context.aid = state.aid;
    let aid = context.aid;

    return this._oauth
      .getRefreshToken(authorizationCode)
      .then((result) => {
        rtokenString = result;
        return this._oauth.getAccessToken(rtokenString);
      })
      .then((result) => {
        tokenString = result;
        return this.refreshTokenService.tryRead(context, aid, "google", task);
      })
      .then((refreshToken) => {
        if (!refreshToken)
          return this.refreshTokenService.create(
            context,
            aid,
            new RefreshToken({
              idp: "google",
              accountId: aid,
              task,
              token: rtokenString,
            })
          );

        refreshToken.token(rtokenString);
        return this.refreshTokenService.update(context, aid, refreshToken);
      });
  }

  public revokeToken(context: Context, aid: number, task: string) {
    return this.refreshTokenService.delete(context, aid, "google", task);
  }

  public reserveDrive(context: Context, aid: number) {
    let ticket: string;
    return this.lockService
      .lock(context, aid, "google/drive/reservation", 10)
      .then((result) => {
        ticket = result;
        return this.deviceService.findByType(
          context,
          aid,
          DeviceType.GoogleDrive
        );
      })
      .then((result) => {
        if (result && result.length > 0 && !result[0].deleted) {
          return error<any>(400, ids.DUPLICATE_CLOUD_DRIVE);
        }
        return this.deviceService.newId(context, aid);
      })
      .then((result) => {
        return {
          ticket,
          deviceId: result,
          deviceName: "Google Drive",
        };
      });
  }

  public addDrive(context: Context, aid: number, encrypt: boolean) {
    return tx(context, () => {
      return this.deviceService
        .findByType(context, aid, DeviceType.GoogleDrive)
        .then((drives) => {
          if (!drives || drives.length === 0)
            return this.deviceService.create(
              context,
              aid,
              new Device({
                deviceType: DeviceType.GoogleDrive,
                encrypt: encrypt || false,
                name: "Google Drive",
                nickname: "Google Drive",
                creationTime: new Date(),
              })
            );
          return drives[0];
        });
    }).then((drive) => {
      return sanitizeDevice(drive, context.hasAdminRights());
    });
  }

  private async handleGetAccessTokenError(
    context: Context,
    err: any,
    aid: number,
    idp: string,
    task: string
  ) {
    if (err && err.error) {
      try {
        const parsedError: any = JSON.parse(err.error);

        if (
          parsedError &&
          parsedError.error &&
          parsedError.error === "invalid_grant"
        ) {
          await this.refreshTokenService.delete(context, aid, idp, task);
        }
      } catch (e) {
        throw makeError(404, ids.REFRESHTOKEN_NOT_VALID_FOR_ACCOUNT);
      }
    }

    throw makeError(404, ids.REFRESHTOKEN_NOT_VALID_FOR_ACCOUNT);
  }
}
