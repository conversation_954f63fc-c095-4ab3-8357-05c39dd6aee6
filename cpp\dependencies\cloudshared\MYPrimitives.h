#pragma once

#include <cinttypes>
#include <string>
#include <chrono>
#include "MYEnums.h"
// Do not change these enum values: they are stored in the catalog.
// See MYNetworkNode.
enum class MYDeviceType : unsigned int
{
    Unknown = 0,

    // Bits 0 to 3 -> instance
    // Bits 4 to 7
    // 0001 -> cloud-like device
    // 0010 -> desktop device
    // 0100 -> mobile device
    // 1000 -> attached (non-cpu) device

    Cloud = 0x11,            // 0b0001 0001
    AmazonCloudDrive = 0x12, // 0b0001 0010
    GoogleCloudDrive = 0x13, // 0b0001 0011
    Leica = 0x14,            // 0b0001 0100

    Mac = 0x21,        // 0b0010 0001
    PC = 0x22,         // 0b0010 0010
    PowerShell = 0x23, // 0b0010 0011

    iPad = 0x41,      // 0b0100 0001
    iPhonePod = 0x42, // 0b0100 0010
    Android = 0x43,   // 0b0100 0011

    RemovableDevice = 0x81, // 0b1000 0001
    NAS = 0x82,             // 0b1000 0002

    Cloud_Mask = 0x10,
    Desktop_Mask = 0x20,
    Mobile_Mask = 0x40,
    Attached_Mask = 0x80,
};
ENUM_FLAGS(MYDeviceType)

namespace std
{
    inline std::string to_string(MYDeviceType deviceType)
    {
        switch (deviceType)
        {
        case MYDeviceType::Cloud:
            return "Cloud";
        case MYDeviceType::AmazonCloudDrive:
            return "AmazonCloudDrive";
        case MYDeviceType::GoogleCloudDrive:
            return "GoogleCloudDrive";
        case MYDeviceType::Leica:
            return "Leica";
        case MYDeviceType::Mac:
            return "Mac";
        case MYDeviceType::PC:
            return "PC";
        case MYDeviceType::PowerShell:
            return "PowerShell";
        case MYDeviceType::iPad:
            return "iPad";
        case MYDeviceType::iPhonePod:
            return "iPhonePod";
        case MYDeviceType::Android:
            return "Android";
        case MYDeviceType::RemovableDevice:
            return "RemovableDevice";
        case MYDeviceType::NAS:
            return "NAS";
        default:
            return std::to_string((uint32_t)deviceType);
        }
    }
}

enum class UserData
{
    CannotEstablishIdentity = 0x1111,
    HttpError = 0x2222,
    NonUpgradedDevices = 0x3333,
    UpgradeAlert = 0x4444
};

enum class SerializationFlags
{
    None,

    // Test these
    NonReplicableBits = 0x01,
    Base64Hash = 0x02,
    Short = 0x04,
    SendZeroHash = 0x08,
    JsonBinBase64 = 0x10,

    // Set or test
    FromReplication = 0x100,
    SlipStream = 0x200,
    Cloud = 0x400,
    BulkReplication = 0x800,

    // Set these
    IsForCloud = SerializationFlags::Cloud,
    // ALPHA:
    IsForWire = SerializationFlags::None,
    IsForSQLite = (int)SerializationFlags::NonReplicableBits,
    IsForMirrorInfo = SerializationFlags::Short,
    // Enable for BETA+:
    //    IsForWire = SerializationFlags::Base64Hash,
    //    IsForSQLite = (int)SerializationFlags::NonReplicableBits | (int)SerializationFlags::Base64Hash,
    //    IsForMirrorInfo = SerializationFlags::Short | SerializationFlags::Base64Hash,
    IsForLogging = SerializationFlags::NonReplicableBits,
};
ENUM_FLAGS(SerializationFlags)

namespace dummy
{
    struct hex_dummy_low
    {
        unsigned char c;
    };

    struct hex_dummy_high
    {
        unsigned char c;
    };

    struct hex_dummy_low_lower
    {
        unsigned char c;
    };

    struct hex_dummy_high_lower
    {
        unsigned char c;
    };

    struct hex_struct
    {
        uint8_t e0;
        uint8_t e1;
        uint8_t e2;
        uint8_t e3;
        uint8_t e4;
        uint8_t e5;
        uint8_t e6;
        uint8_t e7;
        uint8_t e8;
        uint8_t e9;
        uint8_t e10;
        uint8_t e11;
        uint8_t e12;
        uint8_t e13;
        uint8_t e14;
        uint8_t e15;
        uint8_t e16;
        uint8_t e17;
        uint8_t e18;
        uint8_t e19;
    };
}

// 32-bit time functions with a range of 2012 to 2069
uint32_t getMylioNow();
uint32_t systemTimeToMylioTime(std::chrono::system_clock::time_point timepoint);
std::chrono::system_clock::time_point mylioTimeToSystemTime(uint32_t mylioTime);
uint32_t mylioTimeToUnixTime(uint32_t mylioTime);

extern std::string g_emptyString;
extern std::string g_jpgFormat;
extern std::string g_xmpFormat;
extern std::string g_dngFormat;
extern std::string g_vidFormat;

struct no_init_t
{
};
extern no_init_t no_init;
