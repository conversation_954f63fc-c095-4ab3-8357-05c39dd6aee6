// export const activatedEvent = {
//   events: [
//     {
//       id: "EVWBOXPY2LZYNNAGZJ6DOQEZ642JMI",
//       processed: false,
//       created: **********482,
//       type: "subscription.activated",
//       live: false,
//       data: {
//         id: "oknetFBsTHmbEgXSt9QtNA",
//         quote: null,
//         subscription: "oknetFBsTHmbEgXSt9QtNA",
//         active: true,
//         state: "active",
//         isSubscriptionEligibleForPauseByBuyer: false,
//         isPauseScheduled: false,
//         changed: **********426,
//         changedValue: **********426,
//         changedInSeconds: **********,
//         changedDisplay: "6/30/23",
//         changedDisplayISO8601: "2023-06-30",
//         changedDisplayEmailEnhancements: "Jun 30, 2023",
//         changedDisplayEmailEnhancementsWithTime: "Jun 30, 2023 10:17:13 PM",
//         live: false,
//         currency: "USD",
//         account: {
//           id: "JVItN6EdTX-oXnN0NXXGlQ",
//           account: "JVItN6EdTX-oXnN0NXXGlQ",
//           contact: {
//             first: "Willem",
//             last: "Mitchell",
//             email: "<EMAIL>",
//             company: null,
//             phone: null,
//             subscribed: true,
//           },
//           address: {
//             "address line 1": null,
//             "address line 2": null,
//             city: "North Pole",
//             country: "US",
//             "postal code": "88888",
//             region: "US-DC",
//             "region custom": null,
//             company: null,
//           },
//           language: "en",
//           country: "US",
//           lookup: { global: "pG3zw3BoR2uNZ6B5bvBsxg" },
//           url: "https://mylio.onfastspring.com/account",
//         },
//         product: {
//           product: "MYLIO-SUB-STANDARD-MONTH",
//           parent: null,
//           productAppReference: "BWxL5dyETu27j_kHJ1wFPA",
//           display: { en: "Mylio Photos (Monthly)" },
//           description: {},
//           image:
//             "https://d8y8nchqlnmka.cloudfront.net/DHRIJKZE4NHGM/ujzwPH3eSIY/m-monthly.png",
//           sku: "MYLIO-SUB-STANDARD-MONTH",
//           offers: [
//             { type: "upsell", display: {}, items: ["MYLIO-SUB-STANDARD-YEAR"] },
//           ],
//           fulfillments: {},
//           format: "digital",
//           taxcode: "SW054000",
//           taxcodeDescription: "Cloud Services - SaaS - Service Agreement",
//           pricing: {
//             interval: "month",
//             intervalLength: 1,
//             intervalCount: null,
//             quantityBehavior: "hide",
//             quantityDefault: 1,
//             price: { USD: 9.99 },
//             dateLimitsEnabled: false,
//             reminderNotification: {
//               enabled: true,
//               interval: "day",
//               intervalLength: 1,
//             },
//             overdueNotification: {
//               enabled: true,
//               interval: "week",
//               intervalLength: 1,
//               amount: 4,
//             },
//             cancellation: { interval: "week", intervalLength: 1 },
//           },
//         },
//         sku: "MYLIO-SUB-STANDARD-MONTH",
//         display: "Mylio Photos (Monthly)",
//         quantity: 1,
//         adhoc: false,
//         autoRenew: true,
//         price: 9.99,
//         priceDisplay: "$9.99",
//         priceInPayoutCurrency: 9.99,
//         priceInPayoutCurrencyDisplay: "$9.99",
//         discount: 0.0,
//         discountDisplay: "$0.00",
//         discountInPayoutCurrency: 0.0,
//         discountInPayoutCurrencyDisplay: "$0.00",
//         subtotal: 9.99,
//         subtotalDisplay: "$9.99",
//         subtotalInPayoutCurrency: 9.99,
//         subtotalInPayoutCurrencyDisplay: "$9.99",
//         next: 1690675200000,
//         nextValue: 1690675200000,
//         nextInSeconds: 1690675200,
//         nextDisplay: "7/30/23",
//         nextDisplayISO8601: "2023-07-30",
//         end: null,
//         endValue: null,
//         endInSeconds: null,
//         endDisplay: null,
//         endDisplayISO8601: null,
//         canceledDate: null,
//         canceledDateValue: null,
//         canceledDateInSeconds: null,
//         canceledDateDisplay: null,
//         canceledDateDisplayISO8601: null,
//         deactivationDate: null,
//         deactivationDateValue: null,
//         deactivationDateInSeconds: null,
//         deactivationDateDisplay: null,
//         deactivationDateDisplayISO8601: null,
//         sequence: 1,
//         periods: null,
//         remainingPeriods: null,
//         begin: **********093,
//         beginValue: **********093,
//         beginInSeconds: **********,
//         beginDisplay: "6/30/23",
//         beginDisplayISO8601: "2023-06-30",
//         beginDisplayEmailEnhancements: "Jun 30, 2023",
//         beginDisplayEmailEnhancementsWithTime: "Jun 30, 2023 10:17:13 PM",
//         nextDisplayEmailEnhancements: "Jul 30, 2023",
//         nextDisplayEmailEnhancementsWithTime: "Jul 30, 2023 12:00:00 AM",
//         intervalUnit: "month",
//         intervalUnitAbbreviation: "mo",
//         intervalLength: 1,
//         intervalLengthGtOne: false,
//         nextChargeCurrency: "USD",
//         nextChargeDate: 1690675200000,
//         nextChargeDateValue: 1690675200000,
//         nextChargeDateInSeconds: 1690675200,
//         nextChargeDateDisplay: "7/30/23",
//         nextChargeDateDisplayISO8601: "2023-07-30",
//         nextChargePreTax: 9.99,
//         nextChargePreTaxDisplay: "$9.99",
//         nextChargePreTaxInPayoutCurrency: 9.99,
//         nextChargePreTaxInPayoutCurrencyDisplay: "$9.99",
//         nextChargeTotal: 10.59,
//         nextChargeTotalDisplay: "$10.59",
//         nextChargeTotalInPayoutCurrency: 10.59,
//         nextChargeTotalInPayoutCurrencyDisplay: "$10.59",
//         nextNotificationType: "PAYMENT_REMINDER",
//         nextNotificationDate: 1690588800000,
//         nextNotificationDateValue: 1690588800000,
//         nextNotificationDateInSeconds: 1690588800,
//         nextNotificationDateDisplay: "7/29/23",
//         nextNotificationDateDisplayISO8601: "2023-07-29",
//         paymentReminder: { intervalUnit: "day", intervalLength: 1 },
//         paymentOverdue: {
//           intervalUnit: "week",
//           intervalLength: 1,
//           total: 4,
//           sent: 0,
//         },
//         cancellationSetting: {
//           cancellation: "AFTER_LAST_NOTIFICATION",
//           intervalUnit: "week",
//           intervalLength: 1,
//         },
//         fulfillments: null,
//         instructions: [
//           {
//             product: "MYLIO-SUB-STANDARD-MONTH",
//             type: "regular",
//             isNotTrial: true,
//             periodStartDate: 1688083200000,
//             periodStartDateValue: 1688083200000,
//             periodStartDateInSeconds: 1688083200,
//             periodStartDateDisplay: "6/30/23",
//             periodStartDateDisplayISO8601: "2023-06-30",
//             periodEndDate: null,
//             periodEndDateValue: null,
//             periodEndDateInSeconds: null,
//             periodEndDateDisplay: null,
//             periodEndDateDisplayISO8601: null,
//             intervalUnit: "month",
//             intervalLength: 1,
//             discountPercent: 0,
//             discountPercentValue: 0,
//             discountPercentDisplay: "0%",
//             discountTotal: 0.0,
//             discountTotalDisplay: "$0.00",
//             discountTotalInPayoutCurrency: 0.0,
//             discountTotalInPayoutCurrencyDisplay: "$0.00",
//             unitDiscount: 0.0,
//             unitDiscountDisplay: "$0.00",
//             unitDiscountInPayoutCurrency: 0.0,
//             unitDiscountInPayoutCurrencyDisplay: "$0.00",
//             price: 9.99,
//             priceDisplay: "$9.99",
//             priceInPayoutCurrency: 9.99,
//             priceInPayoutCurrencyDisplay: "$9.99",
//             priceTotal: 9.99,
//             priceTotalDisplay: "$9.99",
//             priceTotalInPayoutCurrency: 9.99,
//             priceTotalInPayoutCurrencyDisplay: "$9.99",
//             unitPrice: 9.99,
//             unitPriceDisplay: "$9.99",
//             unitPriceInPayoutCurrency: 9.99,
//             unitPriceInPayoutCurrencyDisplay: "$9.99",
//             total: 9.99,
//             totalDisplay: "$9.99",
//             totalInPayoutCurrency: 9.99,
//             totalInPayoutCurrencyDisplay: "$9.99",
//             totalWithTaxes: 10.59,
//             totalWithTaxesDisplay: "$10.59",
//             totalWithTaxesInPayoutCurrency: 10.59,
//             totalWithTaxesInPayoutCurrencyDisplay: "$10.59",
//           },
//         ],
//         initialOrderId: "gBzOuYnRTDSn2qYG6bpJgA",
//         initialOrderReference: "MYLIO230630-8183-63152",
//       },
//     },
//   ],
// };

// export const updatedEvent = {
//   events: [
//     {
//       id: "EVWB4MFBRTHDURB6TLAS6RLGGRRM2I",
//       processed: false,
//       created: **********228,
//       type: "subscription.updated",
//       live: false,
//       data: {
//         id: "oknetFBsTHmbEgXSt9QtNA",
//         quote: null,
//         subscription: "oknetFBsTHmbEgXSt9QtNA",
//         active: true,
//         state: "active",
//         isSubscriptionEligibleForPauseByBuyer: false,
//         isPauseScheduled: false,
//         changed: **********117,
//         changedValue: **********117,
//         changedInSeconds: **********,
//         changedDisplay: "6/30/23",
//         changedDisplayISO8601: "2023-06-30",
//         changedDisplayEmailEnhancements: "Jun 30, 2023",
//         changedDisplayEmailEnhancementsWithTime: "Jun 30, 2023 10:19:17 PM",
//         live: false,
//         currency: "USD",
//         account: {
//           id: "JVItN6EdTX-oXnN0NXXGlQ",
//           account: "JVItN6EdTX-oXnN0NXXGlQ",
//           contact: {
//             first: "Willem",
//             last: "Mitchell",
//             email: "<EMAIL>",
//             company: null,
//             phone: null,
//             subscribed: true,
//           },
//           address: {
//             "address line 1": null,
//             "address line 2": null,
//             city: "North Pole",
//             country: "US",
//             "postal code": "88888",
//             region: "US-DC",
//             "region custom": null,
//             company: null,
//           },
//           language: "en",
//           country: "US",
//           lookup: { global: "pG3zw3BoR2uNZ6B5bvBsxg" },
//           url: "https://mylio.onfastspring.com/account",
//         },
//         product: {
//           product: "MYLIO-SUB-STANDARD-YEAR",
//           parent: null,
//           productAppReference: "eH5v8UkRRgiQnvYB9zvJ7w",
//           display: { en: "Mylio Photos (Yearly)" },
//           description: {},
//           image:
//             "https://d8y8nchqlnmka.cloudfront.net/DHRIJKZE4NHGM/Wdkq6OC6Rds/m-yearly.png",
//           sku: "MYLIO-SUB-STANDARD-YEAR",
//           fulfillments: {},
//           format: "digital",
//           taxcode: "SW054000",
//           taxcodeDescription: "Cloud Services - SaaS - Service Agreement",
//           pricing: {
//             interval: "year",
//             intervalLength: 1,
//             intervalCount: null,
//             quantityBehavior: "hide",
//             quantityDefault: 1,
//             price: { USD: 99.99 },
//             dateLimitsEnabled: false,
//             reminderNotification: {
//               enabled: true,
//               interval: "week",
//               intervalLength: 1,
//             },
//             overdueNotification: {
//               enabled: true,
//               interval: "week",
//               intervalLength: 1,
//               amount: 4,
//             },
//             cancellation: { interval: "week", intervalLength: 1 },
//           },
//         },
//         sku: "MYLIO-SUB-STANDARD-YEAR",
//         display: "Mylio Photos (Yearly)",
//         quantity: 1,
//         adhoc: false,
//         autoRenew: true,
//         price: 99.99,
//         priceDisplay: "$99.99",
//         priceInPayoutCurrency: 99.99,
//         priceInPayoutCurrencyDisplay: "$99.99",
//         discount: 0.0,
//         discountDisplay: "$0.00",
//         discountInPayoutCurrency: 0.0,
//         discountInPayoutCurrencyDisplay: "$0.00",
//         subtotal: 99.99,
//         subtotalDisplay: "$99.99",
//         subtotalInPayoutCurrency: 99.99,
//         subtotalInPayoutCurrencyDisplay: "$99.99",
//         next: 1690675200000,
//         nextValue: 1690675200000,
//         nextInSeconds: 1690675200,
//         nextDisplay: "7/30/23",
//         nextDisplayISO8601: "2023-07-30",
//         end: null,
//         endValue: null,
//         endInSeconds: null,
//         endDisplay: null,
//         endDisplayISO8601: null,
//         canceledDate: null,
//         canceledDateValue: null,
//         canceledDateInSeconds: null,
//         canceledDateDisplay: null,
//         canceledDateDisplayISO8601: null,
//         deactivationDate: null,
//         deactivationDateValue: null,
//         deactivationDateInSeconds: null,
//         deactivationDateDisplay: null,
//         deactivationDateDisplayISO8601: null,
//         sequence: 1,
//         periods: null,
//         remainingPeriods: null,
//         begin: **********093,
//         beginValue: **********093,
//         beginInSeconds: **********,
//         beginDisplay: "6/30/23",
//         beginDisplayISO8601: "2023-06-30",
//         beginDisplayEmailEnhancements: "Jun 30, 2023",
//         beginDisplayEmailEnhancementsWithTime: "Jun 30, 2023 10:17:13 PM",
//         nextDisplayEmailEnhancements: "Jul 30, 2023",
//         nextDisplayEmailEnhancementsWithTime: "Jul 30, 2023 12:00:00 AM",
//         intervalUnit: "year",
//         intervalUnitAbbreviation: "yr",
//         intervalLength: 1,
//         intervalLengthGtOne: false,
//         nextChargeCurrency: "USD",
//         nextChargeDate: 1690675200000,
//         nextChargeDateValue: 1690675200000,
//         nextChargeDateInSeconds: 1690675200,
//         nextChargeDateDisplay: "7/30/23",
//         nextChargeDateDisplayISO8601: "2023-07-30",
//         nextChargePreTax: 99.99,
//         nextChargePreTaxDisplay: "$99.99",
//         nextChargePreTaxInPayoutCurrency: 99.99,
//         nextChargePreTaxInPayoutCurrencyDisplay: "$99.99",
//         nextChargeTotal: 105.99,
//         nextChargeTotalDisplay: "$105.99",
//         nextChargeTotalInPayoutCurrency: 105.99,
//         nextChargeTotalInPayoutCurrencyDisplay: "$105.99",
//         taxExemptionData: "",
//         nextNotificationType: "PAYMENT_REMINDER",
//         nextNotificationDate: 1690588800000,
//         nextNotificationDateValue: 1690588800000,
//         nextNotificationDateInSeconds: 1690588800,
//         nextNotificationDateDisplay: "7/29/23",
//         nextNotificationDateDisplayISO8601: "2023-07-29",
//         paymentReminder: { intervalUnit: "day", intervalLength: 1 },
//         paymentOverdue: {
//           intervalUnit: "week",
//           intervalLength: 1,
//           total: 4,
//           sent: 0,
//         },
//         cancellationSetting: {
//           cancellation: "AFTER_LAST_NOTIFICATION",
//           intervalUnit: "week",
//           intervalLength: 1,
//         },
//         fulfillments: {},
//         instructions: [
//           {
//             product: "MYLIO-SUB-STANDARD-YEAR",
//             type: "regular",
//             isNotTrial: true,
//             periodStartDate: 1688083200000,
//             periodStartDateValue: 1688083200000,
//             periodStartDateInSeconds: 1688083200,
//             periodStartDateDisplay: "6/30/23",
//             periodStartDateDisplayISO8601: "2023-06-30",
//             periodEndDate: null,
//             periodEndDateValue: null,
//             periodEndDateInSeconds: null,
//             periodEndDateDisplay: null,
//             periodEndDateDisplayISO8601: null,
//             intervalUnit: "year",
//             intervalLength: 1,
//             discountPercent: 0,
//             discountPercentValue: 0,
//             discountPercentDisplay: "0%",
//             discountTotal: 0.0,
//             discountTotalDisplay: "$0.00",
//             discountTotalInPayoutCurrency: 0.0,
//             discountTotalInPayoutCurrencyDisplay: "$0.00",
//             unitDiscount: 0.0,
//             unitDiscountDisplay: "$0.00",
//             unitDiscountInPayoutCurrency: 0.0,
//             unitDiscountInPayoutCurrencyDisplay: "$0.00",
//             price: 99.99,
//             priceDisplay: "$99.99",
//             priceInPayoutCurrency: 99.99,
//             priceInPayoutCurrencyDisplay: "$99.99",
//             priceTotal: 99.99,
//             priceTotalDisplay: "$99.99",
//             priceTotalInPayoutCurrency: 99.99,
//             priceTotalInPayoutCurrencyDisplay: "$99.99",
//             unitPrice: 99.99,
//             unitPriceDisplay: "$99.99",
//             unitPriceInPayoutCurrency: 99.99,
//             unitPriceInPayoutCurrencyDisplay: "$99.99",
//             total: 99.99,
//             totalDisplay: "$99.99",
//             totalInPayoutCurrency: 99.99,
//             totalInPayoutCurrencyDisplay: "$99.99",
//             totalWithTaxes: 105.99,
//             totalWithTaxesDisplay: "$105.99",
//             totalWithTaxesInPayoutCurrency: 105.99,
//             totalWithTaxesInPayoutCurrencyDisplay: "$105.99",
//           },
//         ],
//         initialOrderId: "gBzOuYnRTDSn2qYG6bpJgA",
//         initialOrderReference: "MYLIO230630-8183-63152",
//       },
//     },
//   ],
// };

// export const canceledEvent = {
//   events: [
//     {
//       id: "EVWBGQWBSA3H4RDG7DFX5JWRTE7O5Q",
//       processed: false,
//       created: 1688163615055,
//       type: "subscription.canceled",
//       live: false,
//       data: {
//         id: "oknetFBsTHmbEgXSt9QtNA",
//         quote: null,
//         subscription: "oknetFBsTHmbEgXSt9QtNA",
//         active: true,
//         state: "canceled",
//         isSubscriptionEligibleForPauseByBuyer: false,
//         isPauseScheduled: false,
//         changed: **********999,
//         changedValue: **********999,
//         changedInSeconds: **********,
//         changedDisplay: "6/30/23",
//         changedDisplayISO8601: "2023-06-30",
//         changedDisplayEmailEnhancements: "Jun 30, 2023",
//         changedDisplayEmailEnhancementsWithTime: "Jun 30, 2023 10:20:14 PM",
//         live: false,
//         currency: "USD",
//         account: {
//           id: "JVItN6EdTX-oXnN0NXXGlQ",
//           account: "JVItN6EdTX-oXnN0NXXGlQ",
//           contact: {
//             first: "Willem",
//             last: "Mitchell",
//             email: "<EMAIL>",
//             company: null,
//             phone: null,
//             subscribed: true,
//           },
//           address: {
//             "address line 1": null,
//             "address line 2": null,
//             city: "North Pole",
//             country: "US",
//             "postal code": "88888",
//             region: "US-DC",
//             "region custom": null,
//             company: null,
//           },
//           language: "en",
//           country: "US",
//           lookup: { global: "pG3zw3BoR2uNZ6B5bvBsxg" },
//           url: "https://mylio.onfastspring.com/account",
//         },
//         product: {
//           product: "MYLIO-SUB-STANDARD-YEAR",
//           parent: null,
//           productAppReference: "eH5v8UkRRgiQnvYB9zvJ7w",
//           display: { en: "Mylio Photos (Yearly)" },
//           description: {},
//           image:
//             "https://d8y8nchqlnmka.cloudfront.net/DHRIJKZE4NHGM/Wdkq6OC6Rds/m-yearly.png",
//           sku: "MYLIO-SUB-STANDARD-YEAR",
//           fulfillments: {},
//           format: "digital",
//           taxcode: "SW054000",
//           taxcodeDescription: "Cloud Services - SaaS - Service Agreement",
//           pricing: {
//             interval: "year",
//             intervalLength: 1,
//             intervalCount: null,
//             quantityBehavior: "hide",
//             quantityDefault: 1,
//             price: { USD: 99.99 },
//             dateLimitsEnabled: false,
//             reminderNotification: {
//               enabled: true,
//               interval: "week",
//               intervalLength: 1,
//             },
//             overdueNotification: {
//               enabled: true,
//               interval: "week",
//               intervalLength: 1,
//               amount: 4,
//             },
//             cancellation: { interval: "week", intervalLength: 1 },
//           },
//         },
//         sku: "MYLIO-SUB-STANDARD-YEAR",
//         display: "Mylio Photos (Yearly)",
//         quantity: 1,
//         adhoc: false,
//         autoRenew: true,
//         price: 99.99,
//         priceDisplay: "$99.99",
//         priceInPayoutCurrency: 99.99,
//         priceInPayoutCurrencyDisplay: "$99.99",
//         discount: 0.0,
//         discountDisplay: "$0.00",
//         discountInPayoutCurrency: 0.0,
//         discountInPayoutCurrencyDisplay: "$0.00",
//         subtotal: 99.99,
//         subtotalDisplay: "$99.99",
//         subtotalInPayoutCurrency: 99.99,
//         subtotalInPayoutCurrencyDisplay: "$99.99",
//         next: 1690675200000,
//         nextValue: 1690675200000,
//         nextInSeconds: 1690675200,
//         nextDisplay: "7/30/23",
//         nextDisplayISO8601: "2023-07-30",
//         end: null,
//         endValue: null,
//         endInSeconds: null,
//         endDisplay: null,
//         endDisplayISO8601: null,
//         canceledDate: 1688083200000,
//         canceledDateValue: 1688083200000,
//         canceledDateInSeconds: 1688083200,
//         canceledDateDisplay: "6/30/23",
//         canceledDateDisplayISO8601: "2023-06-30",
//         deactivationDate: 1690675200000,
//         deactivationDateValue: 1690675200000,
//         deactivationDateInSeconds: 1690675200,
//         deactivationDateDisplay: "7/30/23",
//         deactivationDateDisplayISO8601: "2023-07-30",
//         sequence: 1,
//         periods: null,
//         remainingPeriods: null,
//         begin: **********093,
//         beginValue: **********093,
//         beginInSeconds: **********,
//         beginDisplay: "6/30/23",
//         beginDisplayISO8601: "2023-06-30",
//         beginDisplayEmailEnhancements: "Jun 30, 2023",
//         beginDisplayEmailEnhancementsWithTime: "Jun 30, 2023 10:17:13 PM",
//         nextDisplayEmailEnhancements: "Jul 30, 2023",
//         nextDisplayEmailEnhancementsWithTime: "Jul 30, 2023 12:00:00 AM",
//         intervalUnit: "year",
//         intervalUnitAbbreviation: "yr",
//         intervalLength: 1,
//         intervalLengthGtOne: false,
//         nextChargeCurrency: "USD",
//         nextChargeDate: 1690675200000,
//         nextChargeDateValue: 1690675200000,
//         nextChargeDateInSeconds: 1690675200,
//         nextChargeDateDisplay: "7/30/23",
//         nextChargeDateDisplayISO8601: "2023-07-30",
//         nextChargePreTax: 99.99,
//         nextChargePreTaxDisplay: "$99.99",
//         nextChargePreTaxInPayoutCurrency: 99.99,
//         nextChargePreTaxInPayoutCurrencyDisplay: "$99.99",
//         nextChargeTotal: 105.99,
//         nextChargeTotalDisplay: "$105.99",
//         nextChargeTotalInPayoutCurrency: 105.99,
//         nextChargeTotalInPayoutCurrencyDisplay: "$105.99",
//         taxExemptionData: "",
//         nextNotificationType: "PAYMENT_REMINDER",
//         nextNotificationDate: 1690588800000,
//         nextNotificationDateValue: 1690588800000,
//         nextNotificationDateInSeconds: 1690588800,
//         nextNotificationDateDisplay: "7/29/23",
//         nextNotificationDateDisplayISO8601: "2023-07-29",
//         paymentReminder: { intervalUnit: "day", intervalLength: 1 },
//         paymentOverdue: {
//           intervalUnit: "week",
//           intervalLength: 1,
//           total: 4,
//           sent: 0,
//         },
//         cancellationSetting: {
//           cancellation: "AFTER_LAST_NOTIFICATION",
//           intervalUnit: "week",
//           intervalLength: 1,
//         },
//         fulfillments: {},
//         instructions: [
//           {
//             product: "MYLIO-SUB-STANDARD-YEAR",
//             type: "regular",
//             isNotTrial: true,
//             periodStartDate: 1688083200000,
//             periodStartDateValue: 1688083200000,
//             periodStartDateInSeconds: 1688083200,
//             periodStartDateDisplay: "6/30/23",
//             periodStartDateDisplayISO8601: "2023-06-30",
//             periodEndDate: null,
//             periodEndDateValue: null,
//             periodEndDateInSeconds: null,
//             periodEndDateDisplay: null,
//             periodEndDateDisplayISO8601: null,
//             intervalUnit: "year",
//             intervalLength: 1,
//             discountPercent: 0,
//             discountPercentValue: 0,
//             discountPercentDisplay: "0%",
//             discountTotal: 0.0,
//             discountTotalDisplay: "$0.00",
//             discountTotalInPayoutCurrency: 0.0,
//             discountTotalInPayoutCurrencyDisplay: "$0.00",
//             unitDiscount: 0.0,
//             unitDiscountDisplay: "$0.00",
//             unitDiscountInPayoutCurrency: 0.0,
//             unitDiscountInPayoutCurrencyDisplay: "$0.00",
//             price: 99.99,
//             priceDisplay: "$99.99",
//             priceInPayoutCurrency: 99.99,
//             priceInPayoutCurrencyDisplay: "$99.99",
//             priceTotal: 99.99,
//             priceTotalDisplay: "$99.99",
//             priceTotalInPayoutCurrency: 99.99,
//             priceTotalInPayoutCurrencyDisplay: "$99.99",
//             unitPrice: 99.99,
//             unitPriceDisplay: "$99.99",
//             unitPriceInPayoutCurrency: 99.99,
//             unitPriceInPayoutCurrencyDisplay: "$99.99",
//             total: 99.99,
//             totalDisplay: "$99.99",
//             totalInPayoutCurrency: 99.99,
//             totalInPayoutCurrencyDisplay: "$99.99",
//             totalWithTaxes: 105.99,
//             totalWithTaxesDisplay: "$105.99",
//             totalWithTaxesInPayoutCurrency: 105.99,
//             totalWithTaxesInPayoutCurrencyDisplay: "$105.99",
//           },
//         ],
//         initialOrderId: "gBzOuYnRTDSn2qYG6bpJgA",
//         initialOrderReference: "MYLIO230630-8183-63152",
//       },
//     },
//   ],
// };

// export const uncanceledEvent = {
//   events: [
//     {
//       id: "EVWBSKV6MM65R5FYJJSW52JDNIKJ3M",
//       processed: false,
//       created: **********668,
//       type: "subscription.updated",
//       live: false,
//       data: {
//         id: "oknetFBsTHmbEgXSt9QtNA",
//         quote: null,
//         subscription: "oknetFBsTHmbEgXSt9QtNA",
//         active: true,
//         state: "active",
//         isSubscriptionEligibleForPauseByBuyer: false,
//         isPauseScheduled: false,
//         changed: **********529,
//         changedValue: **********529,
//         changedInSeconds: **********,
//         changedDisplay: "6/30/23",
//         changedDisplayISO8601: "2023-06-30",
//         changedDisplayEmailEnhancements: "Jun 30, 2023",
//         changedDisplayEmailEnhancementsWithTime: "Jun 30, 2023 10:21:00 PM",
//         live: false,
//         currency: "USD",
//         account: {
//           id: "JVItN6EdTX-oXnN0NXXGlQ",
//           account: "JVItN6EdTX-oXnN0NXXGlQ",
//           contact: {
//             first: "Willem",
//             last: "Mitchell",
//             email: "<EMAIL>",
//             company: null,
//             phone: null,
//             subscribed: true,
//           },
//           address: {
//             "address line 1": null,
//             "address line 2": null,
//             city: "North Pole",
//             country: "US",
//             "postal code": "88888",
//             region: "US-DC",
//             "region custom": null,
//             company: null,
//           },
//           language: "en",
//           country: "US",
//           lookup: { global: "pG3zw3BoR2uNZ6B5bvBsxg" },
//           url: "https://mylio.onfastspring.com/account",
//         },
//         product: {
//           product: "MYLIO-SUB-STANDARD-YEAR",
//           parent: null,
//           productAppReference: "eH5v8UkRRgiQnvYB9zvJ7w",
//           display: { en: "Mylio Photos (Yearly)" },
//           description: {},
//           image:
//             "https://d8y8nchqlnmka.cloudfront.net/DHRIJKZE4NHGM/Wdkq6OC6Rds/m-yearly.png",
//           sku: "MYLIO-SUB-STANDARD-YEAR",
//           fulfillments: {},
//           format: "digital",
//           taxcode: "SW054000",
//           taxcodeDescription: "Cloud Services - SaaS - Service Agreement",
//           pricing: {
//             interval: "year",
//             intervalLength: 1,
//             intervalCount: null,
//             quantityBehavior: "hide",
//             quantityDefault: 1,
//             price: { USD: 99.99 },
//             dateLimitsEnabled: false,
//             reminderNotification: {
//               enabled: true,
//               interval: "week",
//               intervalLength: 1,
//             },
//             overdueNotification: {
//               enabled: true,
//               interval: "week",
//               intervalLength: 1,
//               amount: 4,
//             },
//             cancellation: { interval: "week", intervalLength: 1 },
//           },
//         },
//         sku: "MYLIO-SUB-STANDARD-YEAR",
//         display: "Mylio Photos (Yearly)",
//         quantity: 1,
//         adhoc: false,
//         autoRenew: true,
//         price: 99.99,
//         priceDisplay: "$99.99",
//         priceInPayoutCurrency: 99.99,
//         priceInPayoutCurrencyDisplay: "$99.99",
//         discount: 0.0,
//         discountDisplay: "$0.00",
//         discountInPayoutCurrency: 0.0,
//         discountInPayoutCurrencyDisplay: "$0.00",
//         subtotal: 99.99,
//         subtotalDisplay: "$99.99",
//         subtotalInPayoutCurrency: 99.99,
//         subtotalInPayoutCurrencyDisplay: "$99.99",
//         next: 1690675200000,
//         nextValue: 1690675200000,
//         nextInSeconds: 1690675200,
//         nextDisplay: "7/30/23",
//         nextDisplayISO8601: "2023-07-30",
//         end: null,
//         endValue: null,
//         endInSeconds: null,
//         endDisplay: null,
//         endDisplayISO8601: null,
//         canceledDate: null,
//         canceledDateValue: null,
//         canceledDateInSeconds: null,
//         canceledDateDisplay: null,
//         canceledDateDisplayISO8601: null,
//         deactivationDate: null,
//         deactivationDateValue: null,
//         deactivationDateInSeconds: null,
//         deactivationDateDisplay: null,
//         deactivationDateDisplayISO8601: null,
//         sequence: 1,
//         periods: null,
//         remainingPeriods: null,
//         begin: **********093,
//         beginValue: **********093,
//         beginInSeconds: **********,
//         beginDisplay: "6/30/23",
//         beginDisplayISO8601: "2023-06-30",
//         beginDisplayEmailEnhancements: "Jun 30, 2023",
//         beginDisplayEmailEnhancementsWithTime: "Jun 30, 2023 10:17:13 PM",
//         nextDisplayEmailEnhancements: "Jul 30, 2023",
//         nextDisplayEmailEnhancementsWithTime: "Jul 30, 2023 12:00:00 AM",
//         intervalUnit: "year",
//         intervalUnitAbbreviation: "yr",
//         intervalLength: 1,
//         intervalLengthGtOne: false,
//         nextChargeCurrency: "USD",
//         nextChargeDate: 1690675200000,
//         nextChargeDateValue: 1690675200000,
//         nextChargeDateInSeconds: 1690675200,
//         nextChargeDateDisplay: "7/30/23",
//         nextChargeDateDisplayISO8601: "2023-07-30",
//         nextChargePreTax: 99.99,
//         nextChargePreTaxDisplay: "$99.99",
//         nextChargePreTaxInPayoutCurrency: 99.99,
//         nextChargePreTaxInPayoutCurrencyDisplay: "$99.99",
//         nextChargeTotal: 105.99,
//         nextChargeTotalDisplay: "$105.99",
//         nextChargeTotalInPayoutCurrency: 105.99,
//         nextChargeTotalInPayoutCurrencyDisplay: "$105.99",
//         taxExemptionData: "",
//         nextNotificationType: "PAYMENT_REMINDER",
//         nextNotificationDate: 1690588800000,
//         nextNotificationDateValue: 1690588800000,
//         nextNotificationDateInSeconds: 1690588800,
//         nextNotificationDateDisplay: "7/29/23",
//         nextNotificationDateDisplayISO8601: "2023-07-29",
//         paymentReminder: { intervalUnit: "day", intervalLength: 1 },
//         paymentOverdue: {
//           intervalUnit: "week",
//           intervalLength: 1,
//           total: 4,
//           sent: 0,
//         },
//         cancellationSetting: {
//           cancellation: "AFTER_LAST_NOTIFICATION",
//           intervalUnit: "week",
//           intervalLength: 1,
//         },
//         fulfillments: {},
//         instructions: [
//           {
//             product: "MYLIO-SUB-STANDARD-YEAR",
//             type: "regular",
//             isNotTrial: true,
//             periodStartDate: 1688083200000,
//             periodStartDateValue: 1688083200000,
//             periodStartDateInSeconds: 1688083200,
//             periodStartDateDisplay: "6/30/23",
//             periodStartDateDisplayISO8601: "2023-06-30",
//             periodEndDate: null,
//             periodEndDateValue: null,
//             periodEndDateInSeconds: null,
//             periodEndDateDisplay: null,
//             periodEndDateDisplayISO8601: null,
//             intervalUnit: "year",
//             intervalLength: 1,
//             discountPercent: 0,
//             discountPercentValue: 0,
//             discountPercentDisplay: "0%",
//             discountTotal: 0.0,
//             discountTotalDisplay: "$0.00",
//             discountTotalInPayoutCurrency: 0.0,
//             discountTotalInPayoutCurrencyDisplay: "$0.00",
//             unitDiscount: 0.0,
//             unitDiscountDisplay: "$0.00",
//             unitDiscountInPayoutCurrency: 0.0,
//             unitDiscountInPayoutCurrencyDisplay: "$0.00",
//             price: 99.99,
//             priceDisplay: "$99.99",
//             priceInPayoutCurrency: 99.99,
//             priceInPayoutCurrencyDisplay: "$99.99",
//             priceTotal: 99.99,
//             priceTotalDisplay: "$99.99",
//             priceTotalInPayoutCurrency: 99.99,
//             priceTotalInPayoutCurrencyDisplay: "$99.99",
//             unitPrice: 99.99,
//             unitPriceDisplay: "$99.99",
//             unitPriceInPayoutCurrency: 99.99,
//             unitPriceInPayoutCurrencyDisplay: "$99.99",
//             total: 99.99,
//             totalDisplay: "$99.99",
//             totalInPayoutCurrency: 99.99,
//             totalInPayoutCurrencyDisplay: "$99.99",
//             totalWithTaxes: 105.99,
//             totalWithTaxesDisplay: "$105.99",
//             totalWithTaxesInPayoutCurrency: 105.99,
//             totalWithTaxesInPayoutCurrencyDisplay: "$105.99",
//           },
//         ],
//         initialOrderId: "gBzOuYnRTDSn2qYG6bpJgA",
//         initialOrderReference: "MYLIO230630-8183-63152",
//       },
//     },
//   ],
// };

// export const deactivatedEvent = {
//   events: [
//     {
//       id: "EVWBXQLJ5L53BZAANCT5TY2YW74WY4",
//       processed: false,
//       created: **********552,
//       type: "subscription.deactivated",
//       live: false,
//       data: {
//         id: "oknetFBsTHmbEgXSt9QtNA",
//         quote: null,
//         subscription: "oknetFBsTHmbEgXSt9QtNA",
//         active: false,
//         state: "deactivated",
//         isSubscriptionEligibleForPauseByBuyer: false,
//         isPauseScheduled: false,
//         changed: **********496,
//         changedValue: **********496,
//         changedInSeconds: **********,
//         changedDisplay: "6/30/23",
//         changedDisplayISO8601: "2023-06-30",
//         changedDisplayEmailEnhancements: "Jun 30, 2023",
//         changedDisplayEmailEnhancementsWithTime: "Jun 30, 2023 10:21:55 PM",
//         live: false,
//         currency: "USD",
//         account: {
//           id: "JVItN6EdTX-oXnN0NXXGlQ",
//           account: "JVItN6EdTX-oXnN0NXXGlQ",
//           contact: {
//             first: "Willem",
//             last: "Mitchell",
//             email: "<EMAIL>",
//             company: null,
//             phone: null,
//             subscribed: true,
//           },
//           address: {
//             "address line 1": null,
//             "address line 2": null,
//             city: "North Pole",
//             country: "US",
//             "postal code": "88888",
//             region: "US-DC",
//             "region custom": null,
//             company: null,
//           },
//           language: "en",
//           country: "US",
//           lookup: { global: "pG3zw3BoR2uNZ6B5bvBsxg" },
//           url: "https://mylio.onfastspring.com/account",
//         },
//         product: {
//           product: "MYLIO-SUB-STANDARD-YEAR",
//           parent: null,
//           productAppReference: "eH5v8UkRRgiQnvYB9zvJ7w",
//           display: { en: "Mylio Photos (Yearly)" },
//           description: {},
//           image:
//             "https://d8y8nchqlnmka.cloudfront.net/DHRIJKZE4NHGM/Wdkq6OC6Rds/m-yearly.png",
//           sku: "MYLIO-SUB-STANDARD-YEAR",
//           fulfillments: {},
//           format: "digital",
//           taxcode: "SW054000",
//           taxcodeDescription: "Cloud Services - SaaS - Service Agreement",
//           pricing: {
//             interval: "year",
//             intervalLength: 1,
//             intervalCount: null,
//             quantityBehavior: "hide",
//             quantityDefault: 1,
//             price: { USD: 99.99 },
//             dateLimitsEnabled: false,
//             reminderNotification: {
//               enabled: true,
//               interval: "week",
//               intervalLength: 1,
//             },
//             overdueNotification: {
//               enabled: true,
//               interval: "week",
//               intervalLength: 1,
//               amount: 4,
//             },
//             cancellation: { interval: "week", intervalLength: 1 },
//           },
//         },
//         sku: "MYLIO-SUB-STANDARD-YEAR",
//         display: "Mylio Photos (Yearly)",
//         quantity: 1,
//         adhoc: false,
//         autoRenew: true,
//         price: 99.99,
//         priceDisplay: "$99.99",
//         priceInPayoutCurrency: 99.99,
//         priceInPayoutCurrencyDisplay: "$99.99",
//         discount: 0.0,
//         discountDisplay: "$0.00",
//         discountInPayoutCurrency: 0.0,
//         discountInPayoutCurrencyDisplay: "$0.00",
//         subtotal: 99.99,
//         subtotalDisplay: "$99.99",
//         subtotalInPayoutCurrency: 99.99,
//         subtotalInPayoutCurrencyDisplay: "$99.99",
//         next: 1690675200000,
//         nextValue: 1690675200000,
//         nextInSeconds: 1690675200,
//         nextDisplay: "7/30/23",
//         nextDisplayISO8601: "2023-07-30",
//         end: 1688083200000,
//         endValue: 1688083200000,
//         endInSeconds: 1688083200,
//         endDisplay: "6/30/23",
//         endDisplayISO8601: "2023-06-30",
//         canceledDate: 1688083200000,
//         canceledDateValue: 1688083200000,
//         canceledDateInSeconds: 1688083200,
//         canceledDateDisplay: "6/30/23",
//         canceledDateDisplayISO8601: "2023-06-30",
//         deactivationDate: 1688083200000,
//         deactivationDateValue: 1688083200000,
//         deactivationDateInSeconds: 1688083200,
//         deactivationDateDisplay: "6/30/23",
//         deactivationDateDisplayISO8601: "2023-06-30",
//         sequence: 1,
//         periods: 0,
//         remainingPeriods: 0,
//         begin: **********093,
//         beginValue: **********093,
//         beginInSeconds: **********,
//         beginDisplay: "6/30/23",
//         beginDisplayISO8601: "2023-06-30",
//         beginDisplayEmailEnhancements: "Jun 30, 2023",
//         beginDisplayEmailEnhancementsWithTime: "Jun 30, 2023 10:17:13 PM",
//         nextDisplayEmailEnhancements: "Jul 30, 2023",
//         nextDisplayEmailEnhancementsWithTime: "Jul 30, 2023 12:00:00 AM",
//         intervalUnit: "year",
//         intervalUnitAbbreviation: "yr",
//         intervalLength: 1,
//         intervalLengthGtOne: false,
//         taxExemptionData: "",
//         paymentReminder: { intervalUnit: "day", intervalLength: 1 },
//         paymentOverdue: {
//           intervalUnit: "week",
//           intervalLength: 1,
//           total: 4,
//           sent: 0,
//         },
//         cancellationSetting: {
//           cancellation: "AFTER_LAST_NOTIFICATION",
//           intervalUnit: "week",
//           intervalLength: 1,
//         },
//         fulfillments: {},
//         instructions: [
//           {
//             product: "MYLIO-SUB-STANDARD-YEAR",
//             type: "regular",
//             isNotTrial: true,
//             periodStartDate: 1688083200000,
//             periodStartDateValue: 1688083200000,
//             periodStartDateInSeconds: 1688083200,
//             periodStartDateDisplay: "6/30/23",
//             periodStartDateDisplayISO8601: "2023-06-30",
//             periodEndDate: 1688083200000,
//             periodEndDateValue: 1688083200000,
//             periodEndDateInSeconds: 1688083200,
//             periodEndDateDisplay: "6/30/23",
//             periodEndDateDisplayISO8601: "2023-06-30",
//             intervalUnit: "year",
//             intervalLength: 1,
//             discountPercent: 0,
//             discountPercentValue: 0,
//             discountPercentDisplay: "0%",
//             discountTotal: 0.0,
//             discountTotalDisplay: "$0.00",
//             discountTotalInPayoutCurrency: 0.0,
//             discountTotalInPayoutCurrencyDisplay: "$0.00",
//             unitDiscount: 0.0,
//             unitDiscountDisplay: "$0.00",
//             unitDiscountInPayoutCurrency: 0.0,
//             unitDiscountInPayoutCurrencyDisplay: "$0.00",
//             price: 99.99,
//             priceDisplay: "$99.99",
//             priceInPayoutCurrency: 99.99,
//             priceInPayoutCurrencyDisplay: "$99.99",
//             priceTotal: 99.99,
//             priceTotalDisplay: "$99.99",
//             priceTotalInPayoutCurrency: 99.99,
//             priceTotalInPayoutCurrencyDisplay: "$99.99",
//             unitPrice: 99.99,
//             unitPriceDisplay: "$99.99",
//             unitPriceInPayoutCurrency: 99.99,
//             unitPriceInPayoutCurrencyDisplay: "$99.99",
//             total: 99.99,
//             totalDisplay: "$99.99",
//             totalInPayoutCurrency: 99.99,
//             totalInPayoutCurrencyDisplay: "$99.99",
//             totalWithTaxes: 105.99,
//             totalWithTaxesDisplay: "$105.99",
//             totalWithTaxesInPayoutCurrency: 105.99,
//             totalWithTaxesInPayoutCurrencyDisplay: "$105.99",
//           },
//         ],
//         initialOrderId: "gBzOuYnRTDSn2qYG6bpJgA",
//         initialOrderReference: "MYLIO230630-8183-63152",
//       },
//     },
//   ],
// };
