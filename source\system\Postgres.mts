import { config } from "./Config.mjs";
import { Context } from "./Context.mjs";
import { Bucket } from "../system/merkle.mjs";

import pg = require("pg");
const { Pool } = pg;

const pools: any = {};

export function pgq(value) {
  if (value === void 0 || value === null) return value;
  value = value.replace(/"/g, '""');
  value = value.replace(/'/g, "''");
  return value;
}

export function tx<T>(
  context: Context,
  logic: () => Promise<T>,
  schema?: string
) {
  let root: Promise<any>;
  let client: pg.PoolClient;

  schema = schema || "a0";

  if (context.txLevel === 0) {
    root = getPgPool(cs(context, schema))
      .then((pool) => pool.connect())
      .then((theClient) => {
        client = theClient;
        context.tx = client;

        return context.tx.query(
          "begin transaction isolation level read committed"
        );
      });
  } else {
    root = Promise.resolve<any>(context.tx);
  }
  context.txLevel++;

  return root
    .then(() => {
      return logic();
    })
    .then((result) => {
      context.txLevel--;
      if (context.txLevel === 0) {
        return context.tx.query("commit").then(() => {
          client = client || context.tx;
          if (client) client.release();
          context.tx = undefined;
          return result;
        });
      } else {
        return result;
      }
    })
    .catch((err) => {
      context.error(err, "TX_ERROR");
      if (context.tx) {
        return context.tx.query("rollback").then(() => {
          client = client || context.tx;
          if (client) client.release();
          context.tx = undefined;
          context.txLevel = 0;

          throw err;
        });
      }
      throw err;
    }) as Promise<T>;
}

export function getPgPool(connectionString, schema = ""): Promise<pg.Pool> {
  // if we already have a pool connected to the database
  if (pools[connectionString]) {
    return Promise.resolve(pools[connectionString]);
  }

  //let pool = new pg.Pool({ connectionString } as any);

  // otherwise create a new pool
  let pool = new pg.Pool({
    connectionString,
    ssl: {
      rejectUnAuthorized: false,
    },
  } as any);

  let timeout = config.pg_timeout;
  timeout = (schema && config[schema + "_pg_timeout"]) || timeout;

  (pool as any).on("connect", (client: pg.PoolClient) => {
    // set statement timeout for new connections
    client.query(`SET statement_timeout to ${timeout}`);
  });

  // save the pool for reuse
  pools[connectionString] = pool;

  return Promise.resolve(pool);
}

let _sqCache = new Map<string, Map<string, string>>();
let allF00s = [
  0x0, 0x1, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0xa, 0xb, 0xc, 0xd, 0xe,
  0xf,
];
export function squery<T>(
  name: string,
  context: Context,
  rawSql: string,
  params: Array<any>,
  nf00s?: number[]
) {
  let queries: Map<string, string>;
  if (name) queries = _sqCache.get(name);
  if (!queries) {
    queries = new Map<string, string>();
    if (!nf00s || nf00s.length === 0) nf00s = allF00s;
    else nf00s.sort((a, b) => a - b);

    let prevCs = "";
    let nf00Sql = "";
    let prefix = "";
    for (let nf00 of nf00s) {
      let bucket = new Bucket(nf00, 0);
      let newCs = cs(context, bucket.schema());
      if (prevCs && newCs !== prevCs) {
        queries.set(prevCs, nf00Sql);
        nf00Sql = "";
        prefix = "";
      }
      let clean = rawSql.replace(/<nf00>/g, bucket.sf00());
      clean = clean.replace(/<schema>/g, bucket.schema());
      nf00Sql += `${prefix} ${clean}`;
      prefix = " union all ";
      prevCs = newCs;
    }
    queries.set(prevCs, nf00Sql);
    _sqCache.set(name, queries);
  }
  let runningQueries = new Array<Promise<T[]>>();
  for (let [cstring, sql] of queries) {
    runningQueries.push(query<T>(context, sql, params, cstring));
  }
  let allRows: T[] = [];
  return Promise.all(runningQueries).then((arrayOfArrayOfRows) => {
    for (let arrayOfRows of arrayOfArrayOfRows) {
      for (let row of arrayOfRows) {
        allRows.push(row);
      }
    }
    return allRows;
  });
}

export async function query<T>(
  context: Context,
  text: string,
  params: Array<any>,
  connectionString = cs(context, "a0")
): Promise<Array<T>> {

  let results: any;
  if (context.tx) {
    results = await context.tx.query(text, params);
  } else {
    let pool = await getPgPool(connectionString);
    results = await pool.query(text, params);
  }
  return (results.rows || []) as Array<T>;
}

export async function query2<T>(
  context: Context,
  text: string,
  params: Array<any>,
  schema: string
): Promise<Array<T>> {
  let connectionString = cs(context, schema);

  let results: any;
  let pool = await getPgPool(connectionString, schema);
  results = await pool.query(text, params);
  return (results.rows || []) as Array<T>;
}

export function lpad(str: string, size: number, padString = "0") {
  while (str.length < size) str = padString + str;
  return str;
}

export function cs(context: Context, schema: string) {
  const raw = config.connectionStrings[schema] as string;
  let key = "pgcreds";
  if (schema.startsWith("x"))
    key = "r_pgcreds";
  let creds = config[key] || config.pgcreds;
  return raw.replace(/<user>:<password>/, () => creds);
}
