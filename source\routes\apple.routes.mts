"use strict";

import * as express from "express";
import { config, getServices } from "../system/Config.mjs";
import { safeAccount, safeNone, safeAny, s } from "../system/safe.mjs";
import { Context } from "../system/Context.mjs";
import { microservice as g } from "../microservices/account.microservice.mjs";
import { ids } from "../system/Strings.mjs";
import { makeError } from "../system/error.mjs";
import jsonwebtoken from "jsonwebtoken";
import * as url from "url";
import * as bodyParser from "body-parser";

import { binaryEncoder, sendResponse } from "../system/bjson.cjs";
import { postForm } from "../system/fetch.mjs";

async function obtainJWTFromAccessCode(
  clientId: string,
  redirectUrl: string,
  code: string
): Promise<string> {
  let claims = {
    iss: config.apple.teamId,
    aud: config.apple.authority,
    sub: clientId,
  };

  let options: jsonwebtoken.SignOptions = {
    header: {
      kid: config.apple.privateKeyId,
      alg: config.apple.requestSignAlg,
    },
    algorithm: config.apple.requestSignAlg as jsonwebtoken.Algorithm,
    expiresIn: "5 minutes",
  };

  let privateKey = Buffer.from(config.apple.privateKey, "base64").toString(
    "ascii"
  );

  let outToken = await new Promise<string>((resolve, reject) => {
    jsonwebtoken.sign(claims, privateKey, options, (err, token) => {
      if (err) {
        console.log("error signing JWT:", err);
        reject(makeError(500, ids.SERVER_ERROR));
        return;
      }
      resolve(token);
    });
  });

  const tokenResponse = await postForm(`https://appleid.apple.com/auth/token`, {
    client_id: clientId,
    client_secret: outToken,
    code: code,
    grant_type: "authorization_code",
    redirect_uri: redirectUrl,
  });

  if (tokenResponse.ok)
    throw makeError(
      500,
      ids.SERVER_ERROR,
      "Unable to retrieve token from Apple"
    );

  const tokenResult = await tokenResponse.json();
  if (!tokenResult.id_token) throw makeError(403, ids.INVALID_DATA);

  return tokenResult.id_token;
}

async function obtainJWTFromToken(
  clientId: string,
  redirectUrl: string,
  token: string
): Promise<{ [key: string]: any }> {
  var jwtToken;
  if (!jsonwebtoken.decode(token)) {
    jwtToken = await obtainJWTFromAccessCode(clientId, redirectUrl, token);
  } else {
    jwtToken = token;
  }

  return g.idpService.validateJWT("apple", jwtToken);
}

function validateAppleToken(
  req: express.Request,
  res: express.Response,
  next: express.NextFunction
) {
  let token = s(req.query.access_token);
  if (!token) {
    throw makeError(403, ids.INVALID_DATA);
  }
  let clientId = s(req.query.tokenClientId) || "com.mylio.app";
  let redirectUrl =
    s(req.query.tokenRedirectUrl) || `${config.apple.redirectUrl}`;

  obtainJWTFromToken(clientId, redirectUrl, token)
    .then((jwt) => {
      req.user = {
        id: jwt.sub,
        email: jwt.email,
        email_verified: jwt.email_verified,
      };
      req.context.audience = jwt.aud;
    })
    .then(next, next);
}

function appendQueryParam(query: string, key: any, value: any): string {
  if (query.length > 0) query += "&";
  return query + `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
}

export function addAppleRoutes(router: express.Router) {
  async function getToken(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    const context: Context = req.context;
    const result = await g.idpService.token(context, req.user.id, "apple");
    context.dumpLog();
    return sendResponse(req, res, result, (r) =>
      binaryEncoder.encode_token_responseV2(r)
    );
  }

  async function subscribe(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    const context: Context = req.context;
    const email = req.user && req.user.email_verified && req.user.email;
    if (!email) throw makeError(403, ids.APPLE_EMAIL_MISSING_ON_SUBSCRIBE);
    const result = await g.idpService.subscribe(
      context,
      "apple",
      req.user.id,
      email
    );
    context.dumpLog();
    return sendResponse(req, res, result, (r) =>
      binaryEncoder.encode_rtoken_response(r)
    );
  }

  router.get("/apple/token", safeNone, validateAppleToken, getToken);
  router.get("/v2/apple/token", safeNone, validateAppleToken, getToken);
  router.post("/apple/subscribe", safeAccount, validateAppleToken, subscribe);

  router.post(
    "/apple/redirect",
    express.urlencoded({ extended: true }),
    (req, res, next) => {
      const context: Context = req.context;
      const body = req.body;

      let redirect = "https://account.mylio.com/apple-webview-redirect";

      let query = "";
      for (let key of Object.keys(body)) {
        if (key === "state") {
          let state: any;
          try {
            state = JSON.parse(body.state.replace(/\&quot;/g, '"'));
          } catch (err) {
            console.log("Unable to parse Apple redirect state: " + body.state);
            query = appendQueryParam(query, "state", body.state);
            continue;
          }
          if (Object.prototype.hasOwnProperty.call(state, "stateId")) {
            query = appendQueryParam(query, "stateId", state.stateId);
          }
          if (Object.prototype.hasOwnProperty.call(state, "redirect")) {
            let parsed = url.parse(state.redirect, false);
            if (
              ((parsed.hostname.endsWith(".mylio.com") ||
                parsed.hostname == "mylio.com") &&
                (parsed.protocol === "https:" ||
                  parsed.protocol === "com.mylio:" ||
                  parsed.protocol === "mylio-app:")) ||
              parsed.hostname === "localhost" ||
              parsed.hostname === "127.0.0.1" ||
              parsed.hostname === "::1"
            ) {
              redirect = `${parsed.protocol}//${parsed.host}${parsed.pathname}`;
            }
          }
          continue;
        }
        query = appendQueryParam(query, key, body[key]);
      }
      if (context) context.dumpLog();
      res.status(302).set("location", `${redirect}?${query}`).end();
    }
  );
}
