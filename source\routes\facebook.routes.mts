// @ts-nocheck

import express = require("express");
import passport = require("passport");
import { config, getServices } from "../system/Config.mjs";
import { safeAccount, safeNone, s, safeAny, secure } from "../system/safe.mjs";
import { Context } from "../system/Context.mjs";
import { microservice as g } from "../microservices/account.microservice.mjs";
import { sanitizeOutput } from "../models/Account.model.mjs";
import { ids } from "../system/Strings.mjs";
import { makeError } from "../system/error.mjs";

import { binaryEncoder, sendResponse } from "../system/bjson.cjs";
import { createHmac } from "crypto";

import FacebookTokenStrategy = require("passport-facebook-token");
import FacebookCodeStrategy = require("passport-facebook");

export function addFacebookRoutes(router: express.Router) {
  passport.use(
    "facebook",
    new FacebookTokenStrategy(
      {
        clientID: config.facebook.clientID,
        clientSecret: config.facebook.clientSecret,
      },
      (accessToken, refreshToken, profile, done) => {
        profile.token = accessToken;

        return done(null, profile);
      }
    )
  );

  passport.use(
    "facebook-code",
    new FacebookCodeStrategy(
      {
        callbackURL: `${config.cloud}/facebook/auth-code`,
        clientID: config.facebook.clientID,
        clientSecret: config.facebook.clientSecret,
        profileFields: ["email"],
      },
      (accessToken, refreshToken, profile, done) => {
        profile.token = accessToken;

        return done(null, profile);
      }
    )
  );

  router.get("/facebook/auth-code", (req, res, next) => {
    return passport.authenticate("facebook-code", (err, user, info) => {
      if (err) {
        res.status(401);
        res.contentType("text/html");
        /* tslint:disable */
        res.write(
          `<html>
                <body>
                    ${err}
                </body>
                </html>`
        );
        /* tslint:enable */
        res.end();
      } else {
        req.user = user;
        const token = req.user.token;
        const email =
          req.user.emails && req.user.emails[0] && req.user.emails[0].value;

        res
          .status(302)
          .set(
            "location",
            `${config.website}/child.html?token=${token}&email=${email}`
          )
          .end();
      }
    })(req, res, next);
  });

  router.get("/facebook/authenticate", (req, res, next) => {
    res.status(302).set("location", `com.mylio:${req.url}`).end();
    return next();
  });

  router.post(
    "/facebook/sign-access-token",
    safeAny,
    secure,
    (req, res, next) => {
      try {
        let context = req.context;
        let hmac = createHmac("sha256", config.facebook.clientSecret);
        hmac.update(context.any.token);
        return res.status(200).json({ appsecret_proof: hmac.digest("hex") });
      } catch (err) {
        next(err);
      }
    }
  );
}
