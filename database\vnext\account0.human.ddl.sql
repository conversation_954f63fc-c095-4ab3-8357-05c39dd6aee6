  SET client_min_messages=WARNING;

SELECT pg_terminate_backend(pg_stat_activity.pid)
FROM pg_stat_get_activity(NULL::integer) pg_stat_activity
WHERE datid=(SELECT oid from pg_database where datname = 'account0');


drop database if exists /* r::database */ account0;
create database /* r::database */ account0; -- with owner = mylio;

\c /* r::database */ account0;

create extension pgcrypto;

create schema a0; --  authorization mylio;

\i lock.ddl.sql
\i account.ddl.sql
\i account_metadata.ddl.sql
\i device.ddl.sql
\i device_data.ddl.sql
\i message.ddl.sql
\i refresh_token.ddl.sql
\i system_property.ddl.sql
\i user_property.ddl.sql
\i license.ddl.sql
\i license_template.ddl.sql
\i pin.ddl.sql
\i backblaze_info.ddl.sql
\i invitation.ddl.sql
\i invitation_log_entry.ddl.sql
