#include "encode_functions.h"
#include <node.h>
#include <node_buffer.h>
#include "bjson.h"
#include "helpers.h"
#include "Field.h"
#include "getFieldInfo.h"

Napi::Object decode_resource_ping_request(Napi::Env env, const Napi::Buffer<uint8_t> &bjson)
{
	auto data = bjson.Data();
	auto size = bjson.Length();
	auto endPtr = data + size;

	MYBJsonIterator current = MYBJsonIterator(data, endPtr);
	MYBJsonIterator end(endPtr, endPtr);

	auto root = Napi::Object::New(env);
	auto resources = Napi::Array::New(env);
	root.Set("resources", resources);

	while (++current != end)
	{
		if (current->type() == BJsonType::array && current->key() == MYLiterals::resources)
		{
			size_t i = 0;
			while ((++current)->type() != BJsonType::end)
			{
				resources.Set(i++, helpers::hash64(current->asHash()));
			}
		}
	}
	return root;
}

Napi::Object decode_resource_ping_response(Napi::Env env, const Napi::Buffer<uint8_t> &bjson)
{
	auto data = bjson.Data();
	auto size = bjson.Length();
	auto endPtr = data + size;

	MYBJsonIterator current = MYBJsonIterator(data, endPtr);
	MYBJsonIterator end(endPtr, endPtr);

	auto root = Napi::Object::New(env);

	while ((++current)->type() != BJsonType::end)
	{
		auto sectionName = helpers::t(current->key());
		root.Set(sectionName, current->asInt32());
	}

	return root;
}

Napi::Buffer<uint8_t> encode_resource_ping_response(Napi::Env env, const Napi::Object &jsObj)
{
	// {
	MYBJsonBigRW *writer = new MYBJsonBigRW();
	writer->StartObject();

	writer->Int32(MYLiterals::resources, jsObj.Get("resources").ToNumber());

	writer->EndObject();

	return Napi::Buffer<uint8_t>::New(
			env,
			(uint8_t *)writer->pbegin(),
			writer->psize(),
			helpers::freeWriter,
			writer);
}
