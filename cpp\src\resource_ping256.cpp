#include "encode_functions.h"
#include <node.h>
#include <node_buffer.h>
#include "bjson.h"
#include "helpers.h"
#include "Field.h"
#include "getFieldInfo.h"

Napi::Object decode_ping256(Napi::Env env, const Napi::Buffer<uint8_t> &bjson)
{
	auto data = bjson.Data();
	auto size = bjson.Length();
	auto endPtr = data + size;

	MYBJsonIterator current = MYBJsonIterator(data, endPtr);
	MYBJsonIterator end(endPtr, endPtr);

	auto root = Napi::Object::New(env);
	auto resources = Napi::Object::New(env);

	root.Set("resources", resources);

	while (++current != end)
	{
		if (current->key() == MYLiterals::resources && current->type() == BJsonType::object)
		{
			while ((++current)->type() != BJsonType::end)
			{
				auto nf00 = current->key();
				auto blob = current->asBinary();
				resources.Set(nf00, helpers::bin2text(blob.data(), blob.size()));
			}
		}
	}

	return root;
}

Napi::Buffer<uint8_t> encode_ping256(Napi::Env env, const Napi::Object &jsObj)
{
	// {
	MYBJsonBigRW *writer = new MYBJsonBigRW();
	writer->StartObject();
	Napi::Object resources = jsObj.Get("resources").ToObject();

	writer->StartObject(MYLiterals::resources);
	auto properties = resources.GetPropertyNames();
	for (size_t i = 0; i < properties.Length(); ++i)
	{
		std::string key = properties.Get(i).ToString();
		auto buffer = helpers::text2bin(resources.Get(key).ToString());
		writer->Binary(std::stoi(key), buffer.data(), buffer.size());
	}
	writer->EndObject();

	writer->EndObject();

	return Napi::Buffer<uint8_t>::New(
			env,
			(uint8_t *)writer->pbegin(),
			writer->psize(),
			helpers::freeWriter,
			writer);
}