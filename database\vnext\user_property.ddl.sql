



create table a0.user_property(
    flags int NULL,
	modified_time timestamptz NULL,
	created_time timestamptz NULL,
	account_id int NOT NULL,
	deleted boolean NULL,
	t bytea NULL,
	d bytea NULL,
	user_property_id bytea NOT NULL,
	name text NULL,
	value text NULL
);

alter table a0.user_property
add primary key (account_id,user_property_id);

 

 create index ix_user_property_by_account_id on a0.user_property(account_id);
create index ix_user_property_by_name on a0.user_property(name);

