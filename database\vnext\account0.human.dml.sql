SET client_min_messages=WARNING;

\c account0;

\i plpgsql_trev.sql


create or replace function a0.account_merkle(_account_id int)
        returns void
        as
        $$
            update a0.account_metadata
                set account_merkle = agg.merkle
            from (
                select account_id, digest(string_agg(t, null), 'sha1') as merkle
                from (
                    select account_id, t from a0.account where account_id = _account_id
                    order by t
                ) as x
                group by account_id
            ) as agg
            where a0.account_metadata.account_id = agg.account_id
            and (a0.account_metadata.account_merkle != agg.merkle or a0.account_metadata.account_merkle is null);
        $$
        language sql;


create temp table __snapshot__ as
	select dd.account_id, dd.device_id, dd.protocol_version, coalesce(d.deleted, false) deleted
	from a0.device_data dd
	join a0.device d on dd.account_id = d.account_id and d.device_id = dd.device_id
	where dd.account_id = 0;

create or replace function a0.next_device_id(_account_id int)
returns int
as
$$
  update a0.account_metadata
        set next_device_id = coalesce(next_device_id, -1) + 1
    where account_id = _account_id
    returning next_device_id;
$$
language sql;


create or replace function a0.next_system_property_id(_account_id int)
returns int
as
$$
  update a0.account_metadata
        set next_system_property_id = coalesce(next_system_property_id, 0) + 1
    where account_id = _account_id
    returning next_system_property_id;
$$
language sql;

create or replace function a0.get_modified_accounts(_prev_etl_time timestamptz)
returns table(account_id int, plan_id text, next_plan_date timestamptz)
as
$$
    select account_id, plan_id, next_plan_date
    from a0.account
    where modified_time >= _prev_etl_time;
$$
language sql security definer;

create or replace function a0.get_modified_devices(_prev_etl_time timestamptz)
returns table (
    account_id int,
    device_id int,
    device_type int,
    long_id bytea,
    deleted boolean,
    build int,
    os text,
    last_access_time timestamptz,
    media_count int
)
as
$$
    select
        d.account_id,
        d.device_id,
        d.device_type,
        d.long_id,
        d.deleted,
        dd.build::int,
        dd.os,
        dd.last_access_time,
        dd.media_count
    from a0.device d
    left join a0.device_data dd on d.account_id = dd.account_id and d.device_id = dd.device_id
    where greatest(d.modified_time, dd.modified_time) >= _prev_etl_time;
$$
language sql security definer;


create or replace function a0.active_devices_for_account(_account_id int)
returns table(device_id int, device_name text, build int) as $$
  select d.device_id, coalesce(d.nickname, d.name) as device_name, dd.build::int
  from a0.device d
  join a0.device_data dd on dd.device_id = d.device_id
  where d.account_id = _account_id
    and dd.account_id = _account_id
    and d.device_id > 0
    and (d.deleted != true or d.deleted is null)
    and (dd.deleted != true or dd.deleted is null)
    and d.device_type between 33 and 67; -- exclude external storage devices and cloud drives
$$ language sql;

drop function if exists a0.upgraded_devices_for_account(int, int);

create or replace function a0.upgraded_devices_for_account(_account_id int, _build_number int)
returns table(device_name text) as $$
  select device_name from a0.active_devices_for_account(_account_id)
    where build >= _build_number
    order by device_id asc;
$$ language sql;

drop function if exists a0.outdated_devices_for_account(int, int);

create or replace function a0.outdated_devices_for_account(_account_id int, _build_number int)
returns table (device_name text) as $$
  select device_name from a0.active_devices_for_account(_account_id)
    where build < _build_number
    order by device_id asc;
$$ language sql;

CREATE OR REPLACE FUNCTION a0.hide_upgrade_message(
	_account_id integer,
	_device_name text)
    RETURNS void
    LANGUAGE 'plpgsql'

    COST 100
    
AS $BODY$
declare
	_message_text text;
	_count int;
begin
	_message_text := 'Please upgrade device "' || _device_name || '" to the latest Mylio release.';

  -- remove message for the account
  perform a0.message_update(
    m.flags,
    now(),
    null,
    m.account_id,
    true,
    encode(m.t, 'base64'),
    encode(m.d, 'base64'),
    null::int,
    m.message_id,
    0,
    1,
    m.message,
    m.link
  )
  from a0.message m
  where m.account_id = _account_id
  and m.message = _message_text;
  
end;
$BODY$;


CREATE OR REPLACE FUNCTION a0.show_upgrade_message(
	_account_id integer,
	_device_name text)
    RETURNS void
    LANGUAGE 'plpgsql'

    COST 100
    VOLATILE 
    
AS $BODY$
declare
	_message_text text;
begin
  -- display message if it already exists
	_message_text := 'Please upgrade device "' || _device_name || '" to the latest Mylio release.';
  perform a0.message_update(
    m.flags,
    now(),
    null,
    m.account_id,
    false,
    encode(m.t, 'base64'),
    encode(m.d, 'base64'),
    null::int,
    m.message_id,
    0,
    0,
    _message_text,
    m.link
  )
  from a0.message m
  where m.account_id = _account_id
  and m.message = _message_text;

  if not found then
    -- create new message if none exists
    perform a0.message_create(
      null::int,
      now(),
      now(),
      _account_id,
      false,
      null::text,
      null::text,
      null::int,
      null::int,
      0,
      0,
      _message_text,
      'http://mylio.com/permalink-0000'
    );
  end if;
end;
$BODY$;



\i plpgsql_trev.sql
\i account.human.sql
\i account_metadata.rest.sql
\i account.rest.sql
\i device.rest.sql
\i device.sync.sql
\i device_data.rest.sql
\i device_data.sync.sql
\i message.rest.sql
\i message.sync.sql
\i system_property.rest.sql
\i system_property.sync.sql
\i refresh_token.rest.sql
\i user_property.rest.sql
\i user_property.sync.sql
\i license.rest.sql
\i license_template.rest.sql
\i pin.rest.sql
\i licensing.dml.sql
\i invitation.rest.sql
\i invitation_log_entry.rest.sql
\i show_upgrade_warnings.dml.sql
