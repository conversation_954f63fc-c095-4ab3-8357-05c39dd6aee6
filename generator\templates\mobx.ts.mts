import { TsFile } from "../TsFile.mjs";
import { Entity } from "../Entity.mjs";
import {
  ScriptHelper,
  jsdt,
  jsname,
  blockI,
  blockN,
} from "../ScriptHelper.mjs";
//@ts-ignore
import { pascalCase } from "change-case";

export function template(entity: Entity, h: ScriptHelper) {
  let ts = new TsFile(entity);
  let fields = h.modelFields.filter(
    (f) =>
      f.canGo("cloud", "web") ||
      f.canGo("web", "cloud") ||
      f.canGo("admin", "cloud") ||
      f.canGo("cloud", "admin")
  );

  ts.write(
    (e, b) => `
import { makeAutoObservable } from "mobx"
    ${blockN(e, "mobx_imports")}
    ${blockN(e, "mobx_enums")}


export interface I${jsname(e)} {
    ${fields.map((f) => `${jsname(f)}?: ${jsdt(f)}`).join(";\n\t")};
}

export interface IWire${jsname(e)} {
    ${fields
      .map((f) =>
        jsdt(f) === "Date"
          ? `${jsname(f)}?: string`
          : `${jsname(f)}?: ${jsdt(f)}`
      )
      .join(";\n\t")};
}

export class ${jsname(e)} implements I${jsname(e)} {
    ${fields.map((f) => `${jsname(f)}?: ${jsdt(f)};`).join("\n\t")}
    ${fields
      .map(
        (f) =>
          `set${pascalCase(f.name)}(value: ${jsdt(
            f
          )}) { this.changed = true; this.${jsname(f)} = value; }`
      )
      .join("\n\t")}
    changed = false;
    setChanged() {
        this.changed = true;
    }

    clearChanged() {
        this.changed = false;
    }

    constructor(state? : IWire${jsname(e)} | I${jsname(e)}) {
        if (!state)
            throw "An ${jsname(e)} must have a valid start state";
        ${fields
          .map((f) =>
            jsdt(f) !== "Date"
              ? `this.${jsname(f)} = state.${jsname(f)};`
              : `if (typeof(state.${jsname(f)}) === "string")
            this.${jsname(f)} = new Date(state.${jsname(f)});
         else
            this.${jsname(f)} = state.${jsname(f)}`
          )
          .join(";\n\t")}
        makeAutoObservable(this, {
            ${fields.map((f) => `${jsname(f)}: true`).join(",\n\t\t\t")}
        });

    }

    state() : I${jsname(e)} {
        return {
            ${fields
              .map((f) => `${jsname(f)} : this.${jsname(f)}`)
              .join(",\n\t\t")}
        };
    }

    asWire() : IWire${jsname(e)} {
        return {
            ${fields
              .map((f) =>
                jsdt(f) === "Date"
                  ? `${jsname(f)} : this.${jsname(f)} ? this.${jsname(
                      f
                    )}.toISOString() : undefined`
                  : `${jsname(f)} : this.${jsname(f)}`
              )
              .join(",\n\t\t")}
        };
    }

    ${blockN(e, "mobx_public_members")}

    ${blockN(e, "mobx_private_members")}
}


`
  );

  return ts.toString();
}
