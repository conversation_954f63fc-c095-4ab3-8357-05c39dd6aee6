
import { query } from "../system/Postgres.mjs";
import { License, ILicense} from "../models/License.model.mjs";
import { Context } from "../system/Context.mjs";
import { makeError } from "../system/error.mjs";


export class LicenseRestDataService {

    
/* b::rest_public_members */
async findByPartialId(context: Context, partialKey: string) {
    let sql = `select * from a0."License" where "licenseId" like '${partialKey}%'`;
    let results = await query<ILicense>(context, sql, []);
    if (results.length === 1 && !results[0].licenseId)
      return [];
    return results.filter(o => !!o).map(o => new License(o));
  }

  async findByPartialActivationKey(context: Context, partialKey: string) {
    let sql = `select * from a0."License" where "activationKey" like '${partialKey}%'`;
    let results = await query<ILicense>(context, sql, []);
    if (results.length === 1 && !results[0].licenseId)
      return [];
    return results.filter(o => !!o).map(o => new License(o));
  }
/* end */


  public query(context: Context, sql: string, params: any[]) {
    return query < ILicense> (context, sql, params)
      .then(results => {
        if (results.length === 1) {
          if (!results[0].licenseId) {
      return [];
    }
  }
  return results.filter(o => !!o).map(o => new License(o));
});
    }

		public create (context: Context, entity: License) {
          
/* b::create_on_validate */
if (!entity.status())
      throw makeError(400, "STATUS_REQUIRED", "Status is required");
/* end */

  let params = [
    entity.flags(),
				entity.modifiedTime(),
				entity.createdTime(),
				entity.licenseId(),
				entity.accountId(),
				entity.manager(),
				entity.endDate(),
				entity.templateId(),
				entity.activationKey(),
				entity.deviceLimit(),
				entity.photoLimit(),
				entity.features(),
				entity.cloudStorageLimit(),
				entity.availableUpgrades(),
				entity.status()
  ];
  return this
    .query(context, "select * from a0.license_create ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14,$15) ", params)
  .then(r => r[0]);
        }

		public readByLicenseId (context: Context, licenseId: string) {
  let params = [
    licenseId
  ];
  return this
    .query(context, "select * from a0.license_read_by_license_id  ($1) ", params).then(r => r[0]); 
                
        }

		public update (context: Context, entity: License) {
          
/* b::update_on_validate */
if (!entity.status())
      throw makeError(400, "STATUS_REQUIRED", "Status is required");
/* end */

  let params = [
    entity.flags(),
				entity.modifiedTime(),
				entity.createdTime(),
				entity.licenseId(),
				entity.accountId(),
				entity.manager(),
				entity.endDate(),
				entity.templateId(),
				entity.activationKey(),
				entity.deviceLimit(),
				entity.photoLimit(),
				entity.features(),
				entity.cloudStorageLimit(),
				entity.availableUpgrades(),
				entity.status()
  ];
  return this
    .query(context, "select * from a0.license_update ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14,$15) ", params)
  .then(r => r[0]);
        }

		public deleteByLicenseId (context: Context, licenseId: string) {
  let params = [
    licenseId
  ];
  return this
    .query(context, "select * from a0.license_delete_by_license_id  ($1) ", params).then(r => r[0]); 
                
        }

		public findByAccountId (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.license_find_by_account_id  ($1) ", params); 
                
        }

		public findByActivationKey (context: Context, activationKey: string) {
  let params = [
    activationKey
  ];
  return this
    .query(context, "select * from a0.license_find_by_activation_key  ($1) ", params); 
                
        }

}
