

import Validation from "../system/Validation.mjs";
import { IModel } from "./IModel.mjs";




/* b::enums */

/* end */


export interface IMessage {
    flags?: number;
	modifiedTime?: Date;
	createdTime?: Date;
	accountId?: number;
	deleted?: boolean;
	t?: string;
	d?: string;
	deviceId?: number;
	messageId?: number;
	secondsToDisplay?: number;
	displayed?: number;
	message?: string;
	link?: string;
}


export class Message 
implements IModel {
    private _state: IMessage;

    
/* b::model_public_members */

/* end */

    
    changed = false;

    constructor(state: IMessage) {
        this.state(state);
    }

    isUndefined() {
        return this._state !== void 0;
    }

    async validate(skipUndefined?: boolean) {
        let v = new Validation(this, skipUndefined);
        
/* b::validate */

/* end */

        return v;
    }

    rtt() {
        return "Message"; 
    }

    state (value?: IMessage) {
        if (value !== undefined) { 
            this._state = value;
            if (this._state.deleted === undefined) this._state.deleted = false;
            


        }
        return this._state;
    }

    flags(value?: number) {
                if (value !== void 0) {
                    if (this.state().flags !== value) {
                        this.state().flags = value;
                        this.changed = true;
                    }
                }
                return this.state().flags;
            };

		modifiedTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().modifiedTime !== value) {
                        this.state().modifiedTime = value;
                        this.changed = true;
                    }
                }
                return this.state().modifiedTime;
            };

		createdTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().createdTime !== value) {
                        this.state().createdTime = value;
                        this.changed = true;
                    }
                }
                return this.state().createdTime;
            };

		accountId(value?: number) {
                if (value !== void 0) {
                    if (this.state().accountId !== value) {
                        this.state().accountId = value;
                        this.changed = true;
                    }
                }
                return this.state().accountId;
            };

		deleted(value?: boolean) {
                if (value !== void 0) {
                    if (this.state().deleted !== value) {
                        this.state().deleted = value;
                        this.changed = true;
                    }
                }
                return this.state().deleted;
            };

		t(value?: string) {
                if (value !== void 0) {
                    if (this.state().t !== value) {
                        this.state().t = value;
                        this.changed = true;
                    }
                }
                return this.state().t;
            };

		d(value?: string) {
                if (value !== void 0) {
                    if (this.state().d !== value) {
                        this.state().d = value;
                        this.changed = true;
                    }
                }
                return this.state().d;
            };

		deviceId(value?: number) {
                if (value !== void 0) {
                    if (this.state().deviceId !== value) {
                        this.state().deviceId = value;
                        this.changed = true;
                    }
                }
                return this.state().deviceId;
            };

		messageId(value?: number) {
                if (value !== void 0) {
                    if (this.state().messageId !== value) {
                        this.state().messageId = value;
                        this.changed = true;
                    }
                }
                return this.state().messageId;
            };

		secondsToDisplay(value?: number) {
                if (value !== void 0) {
                    if (this.state().secondsToDisplay !== value) {
                        this.state().secondsToDisplay = value;
                        this.changed = true;
                    }
                }
                return this.state().secondsToDisplay;
            };

		displayed(value?: number) {
                if (value !== void 0) {
                    if (this.state().displayed !== value) {
                        this.state().displayed = value;
                        this.changed = true;
                    }
                }
                return this.state().displayed;
            };

		message(value?: string) {
                if (value !== void 0) {
                    if (this.state().message !== value) {
                        this.state().message = value;
                        this.changed = true;
                    }
                }
                return this.state().message;
            };

		link(value?: string) {
                if (value !== void 0) {
                    if (this.state().link !== value) {
                        this.state().link = value;
                        this.changed = true;
                    }
                }
                return this.state().link;
            };

    differs(original: Message) {
        return (
            (this.flags() !== void 0 && this.flags() !== original.flags())
		 || (this.modifiedTime() !== void 0 && this.modifiedTime() !== original.modifiedTime())
		 || (this.createdTime() !== void 0 && this.createdTime() !== original.createdTime())
		 || (this.accountId() !== void 0 && this.accountId() !== original.accountId())
		 || (this.deleted() !== void 0 && this.deleted() !== original.deleted())
		 || (this.t() !== void 0 && this.t() !== original.t())
		 || (this.d() !== void 0 && this.d() !== original.d())
		 || (this.deviceId() !== void 0 && this.deviceId() !== original.deviceId())
		 || (this.messageId() !== void 0 && this.messageId() !== original.messageId())
		 || (this.secondsToDisplay() !== void 0 && this.secondsToDisplay() !== original.secondsToDisplay())
		 || (this.displayed() !== void 0 && this.displayed() !== original.displayed())
		 || (this.message() !== void 0 && this.message() !== original.message())
		 || (this.link() !== void 0 && this.link() !== original.link())
        );
    }





/* b::private_members */

/* end */

}



export function sanitizeInput(source: Message, amdin: boolean, mode: string) : IMessage;
export function sanitizeInput(source: IMessage, admin: boolean, mode: string) : IMessage;
export function sanitizeInput(source: Message | IMessage, admin = false, mode="default"): IMessage {
    let s: IMessage;
    if (source instanceof Message)
        s = source.state();
    else
        s = source;        
    let t = {} as IMessage;
    
    
        
            switch(mode) {
                
                
                
            }
            t.flags = s.flags;
		t.accountId = s.accountId;
		t.deleted = s.deleted;
		t.t = s.t;
		t.d = s.d;
		t.deviceId = s.deviceId;
		t.messageId = s.messageId;
		t.secondsToDisplay = s.secondsToDisplay;
		t.displayed = s.displayed;
		t.message = s.message;
		t.link = s.link;
        
    return t;
}

export function sanitizeOutput(source: Message, amdin: boolean) : IMessage;
export function sanitizeOutput(source: IMessage, admin: boolean) : IMessage;
export function sanitizeOutput(source: Message | IMessage, admin = false): IMessage {
    let s: IMessage;
    if (source instanceof Message)
        s = source.state();
    else
        s = source;        
    let t = {} as IMessage;
    
    
    
    t.flags = s.flags;	
t.modifiedTime = s.modifiedTime;	
t.createdTime = s.createdTime;	
t.accountId = s.accountId;	
t.deleted = s.deleted;	
t.t = s.t;	
t.d = s.d;	
t.deviceId = s.deviceId;	
t.messageId = s.messageId;	
t.secondsToDisplay = s.secondsToDisplay;	
t.displayed = s.displayed;	
t.message = s.message;	
t.link = s.link;
    return t;
}

export function mergeState(dbVersion: IMessage, newVersion: IMessage) {
    let targetState: IMessage = {};
    targetState.flags = newVersion.flags === undefined ? dbVersion.flags : newVersion.flags;
	targetState.modifiedTime = newVersion.modifiedTime === undefined ? dbVersion.modifiedTime : newVersion.modifiedTime;
	targetState.createdTime = newVersion.createdTime === undefined ? dbVersion.createdTime : newVersion.createdTime;
	targetState.accountId = newVersion.accountId === undefined ? dbVersion.accountId : newVersion.accountId;
	targetState.deleted = newVersion.deleted === undefined ? dbVersion.deleted : newVersion.deleted;
	targetState.t = newVersion.t === undefined ? dbVersion.t : newVersion.t;
	targetState.d = newVersion.d === undefined ? dbVersion.d : newVersion.d;
	targetState.deviceId = newVersion.deviceId === undefined ? dbVersion.deviceId : newVersion.deviceId;
	targetState.messageId = newVersion.messageId === undefined ? dbVersion.messageId : newVersion.messageId;
	targetState.secondsToDisplay = newVersion.secondsToDisplay === undefined ? dbVersion.secondsToDisplay : newVersion.secondsToDisplay;
	targetState.displayed = newVersion.displayed === undefined ? dbVersion.displayed : newVersion.displayed;
	targetState.message = newVersion.message === undefined ? dbVersion.message : newVersion.message;
	targetState.link = newVersion.link === undefined ? dbVersion.link : newVersion.link;
    return targetState;
}

export function merge(dbVersion: Message, newVersion: Message) {
    return new Message(mergeState(dbVersion.state(), newVersion.state()));
}
