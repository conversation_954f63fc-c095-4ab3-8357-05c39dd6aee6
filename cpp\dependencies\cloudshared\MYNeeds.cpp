#include "MYNeeds.h"
#include "MYLiterals.h"

////////////////////////////////////////////////////////////////////
//
// MYDeviceNeeds
//
////////////////////////////////////////////////////////////////////
MYDeviceNeeds::MYDeviceNeeds(MYBJsonIterator &iter, const MYBJsonIterator &end)
{
    assert(iter->isInteger());
    fromStorage(iter->asUint64());

    //++iter;
    // if (iter != end && iter->isScopeBegin())
    //{
    //    assert(iter->type() == BJsonType::array);
    //    for (++iter; iter != end && iter->type() != BJsonType::end; ++iter)
    //    {
    //        // Expecting here an array of string, int, string, int etc.
    //        MYDeviceNeedsForFormats perFormat;
    //        perFormat._format = iter->asString();

    //        ++iter;
    //        assert(iter != end && iter->isInteger());

    //        perFormat._wantsHasCanGenerate = (MYDeviceNeedsForFormatsMask)iter->asUint32();
    //        if (!_perFormat)
    //        {
    //            _perFormat = std::make_unique<std::vector<MYDeviceNeedsForFormats>>();
    //        }
    //        _perFormat->emplace_back(std::move(perFormat));
    //    }
    //    assert(iter->type() == BJsonType::end);
    //}
}

NeedsBits NeedsBits::None = NeedsBits(0);
NeedsBits NeedsBits::RAW = NeedsBits(MYMediaFileType::RAW);
NeedsBits NeedsBits::NonRAW = NeedsBits(MYMediaFileType::NonRAW);
NeedsBits NeedsBits::Video = NeedsBits(MYMediaFileType::Video);
NeedsBits NeedsBits::RAWorVideo = NeedsBits(MYMediaFileType::RAW) | NeedsBits(MYMediaFileType::Video);
NeedsBits NeedsBits::XMP = NeedsBits(MYMediaFileType::XMP);
NeedsBits NeedsBits::DisplayImage = NeedsBits(MYMediaFileType::DisplayImage);
NeedsBits NeedsBits::Thumbnail = NeedsBits(MYMediaFileType::Thumbnail);
NeedsBits NeedsBits::Preview = NeedsBits(MYMediaFileType::Preview);
NeedsBits NeedsBits::OriginalsIgnoringXMP = NeedsBits(MYMediaFileType::RAW) | NeedsBits(MYMediaFileType::NonRAW) | NeedsBits(MYMediaFileType::Video) | NeedsBits(MYMediaFileType::DisplayImage);
NeedsBits NeedsBits::OriginalsAndXMP = NeedsBits(MYMediaFileType::RAW) | NeedsBits(MYMediaFileType::NonRAW) | NeedsBits(MYMediaFileType::Video) | NeedsBits(MYMediaFileType::XMP) | NeedsBits(MYMediaFileType::DisplayImage);
NeedsBits NeedsBits::AnyFiles = NeedsBits(MYMediaFileType::RAW) | NeedsBits(MYMediaFileType::NonRAW) | NeedsBits(MYMediaFileType::Video) | NeedsBits(MYMediaFileType::XMP) | NeedsBits(MYMediaFileType::DisplayImage) | NeedsBits(MYMediaFileType::Preview) | NeedsBits(MYMediaFileType::Thumbnail);

const NeedsBits MYDeviceNeeds::canGenerateLatest(const class MYNeeds &needs) const
{
    auto latestOriginalsOverAllDevices = needs.anyDevice().hasLatest() & NeedsBits::OriginalsAndXMP;
    auto hasAllLatestOriginals = (latestOriginalsOverAllDevices & ~_hasLatest).none();
    auto hasLatestDisplayImage = hasLatest(NeedsBits::DisplayImage);

    if (hasAllLatestOriginals || hasLatestDisplayImage)
    {
        assert((latestOriginalsOverAllDevices & _hasStale).none());
        return _canGenerateLatestOrStale | _hasStale;
    }

    // don't have latest originals, but have preview and XMP, can generate thumbnail
    auto previewsAndAllKnownXmps = NeedsBits::Preview | (needs.anyDevice().hasForDisplay() & NeedsBits::XMP);
    auto latestPreviews = hasLatest() & previewsAndAllKnownXmps;
    if (latestPreviews.any() && (latestPreviews & previewsAndAllKnownXmps) == previewsAndAllKnownXmps)
    {
        return NeedsBits::Thumbnail;
    }

    return NeedsBits(0);
}

// void MYDeviceNeeds::setForFormat(const std::string& format, MYDeviceNeedsForFormatsMask wants)
//{
//     if (!_perFormat)
//     {
//         _perFormat = std::make_unique<std::vector<MYDeviceNeedsForFormats>>();
//     }
//
//     for (auto iter = _perFormat->begin(); iter != _perFormat->end(); ++iter)
//     {
//         if (iter->_format == format)
//         {
//             if (wants == MYDeviceNeedsForFormatsMask::Nothing)
//             {
//                 _perFormat->erase(iter);
//             }
//             else
//             {
//                 iter->_wantsHasCanGenerate = wants;
//             }
//             return;
//         }
//     }
//
//     _perFormat->emplace_back(format, wants);
// }
//
// void MYDeviceNeeds::setForFormat(std::string&& format, MYDeviceNeedsForFormatsMask wants)
//{
//     if (!_perFormat)
//     {
//         _perFormat = std::make_unique<std::vector<MYDeviceNeedsForFormats>>();
//     }
//
//     for (auto iter = _perFormat->begin(); iter != _perFormat->end(); ++iter)
//     {
//         if (iter->_format == format)
//         {
//             if (wants == MYDeviceNeedsForFormatsMask::Nothing)
//             {
//                 _perFormat->erase(iter);
//             }
//             else
//             {
//                 iter->_wantsHasCanGenerate = wants;
//             }
//             return;
//         }
//     }
//
//     _perFormat->emplace_back(std::move(format), wants);
// }
//
// MYDeviceNeedsForFormatsMask MYDeviceNeeds::getForFormat(const std::string& format)
//{
//     if (!_perFormat)
//     {
//         return MYDeviceNeedsForFormatsMask::Nothing;
//     }
//
//     for (auto iter = _perFormat->begin(); iter != _perFormat->end(); ++iter)
//     {
//         if (iter->_format == format)
//         {
//             return iter->_wantsHasCanGenerate;
//         }
//     }
//     return MYDeviceNeedsForFormatsMask::Nothing;
// }

////////////////////////////////////////////////////////////////////
//
// MYNeeds
//
//////////////////////////////////////////////////////////////////
MYDeviceNeeds g_emptyDevice(0);

void MYNeeds::compact(const vector_set<MYDeviceId> &orderedDeletedDevices)
{
    if (orderedDeletedDevices.empty())
        return;

    _devices.erase_at(std::remove_if(_devices.begin(), _devices.end(), [&orderedDeletedDevices](const std::pair<MYDeviceId, MYDeviceNeeds> &entry)
                                     {
        bool erase = entry.second.empty() || orderedDeletedDevices.find(entry.first) != orderedDeletedDevices.end();
        return erase; }),
                      _devices.end());

    // auto iter = _devices.begin();
    // for (iter = _devices.begin(); iter != _devices.end(); )
    //{
    //     if (orderedDeletedDevices.find(iter->first) != orderedDeletedDevices.end())
    //     {
    //         iter = _devices.erase_at(iter);
    //     }
    //     else
    //     {
    //         ++iter;
    //     }
    // }
}

const MYDeviceNeeds &MYNeeds::getDeviceNeedsOrEmpty(MYDeviceId deviceId) const
{
    auto iter = _devices.find(deviceId);
    if (iter == _devices.end())
        return g_emptyDevice;

    return iter->second;
}

const MYDeviceNeeds *MYNeeds::getDeviceNeedsOrNull(MYDeviceId deviceId) const
{
    auto iter = _devices.find(deviceId);
    if (iter == _devices.end())
        return nullptr;

    return &(iter->second);
}

MYDeviceNeeds *MYNeeds::getDeviceNeedsOrNull(MYDeviceId deviceId)
{
    auto iter = _devices.find(deviceId);
    if (iter == _devices.end())
        return nullptr;

    return &(iter->second);
}

void MYNeeds::removeDeviceNeeds(MYDeviceId deviceId)
{
    _devices.erase(deviceId);
}

MYDeviceNeeds *MYNeeds::getOrCreateDeviceNeeds(MYDeviceId deviceId)
{
    auto emplaced = _devices.try_emplace(deviceId, 0);
    return &emplaced.first->second;
}

void MYNeeds::emplace_from_sorted_unchecked(MYDeviceId deviceId)
{
    _devices.emplace_from_sorted_unchecked(deviceId, 0);
}

void MYNeeds::reserve(size_t size)
{
    _devices.reserve(size);
}

MYNeeds::MYNeeds(size_t numdevices, MYBJsonIterator &iter, const MYBJsonIterator &end)
{
    _devices.reserve(numdevices);

    for (; iter != end && iter->type() != BJsonType::end; ++iter)
    {
        assert(iter->isInteger());
        auto deviceId = MYDeviceId::fromInt(iter->asUint32());

        ++iter;
        _devices.emplace_from_sorted(deviceId, iter, end);
    }
}

void MYNeeds::deserializeFromBJson(MYBJsonIterator &iter, const MYBJsonIterator &end)
{
    _devices.clear();

    for (; iter != end && iter->type() != BJsonType::end; ++iter)
    {
        assert(iter->isInteger());
        auto deviceId = MYDeviceId::fromInt(iter->asUint32());

        ++iter;
        _devices.emplace_from_sorted(deviceId, MYDeviceNeeds(iter, end));
    }
}

bool MYNeeds::empty() const
{
    return _devices.empty();
}

MYNeeds::MYNeeds(MYNeeds &&other) : _devices(std::move(other._devices))
{
}

MYNeeds::MYNeeds(const MYNeeds &other) : _devices(other._devices)
{
}

MYNeeds &MYNeeds::operator=(MYNeeds &&other)
{
    _devices = std::move(other._devices);

    return *this;
}

MYNeeds &MYNeeds::operator=(const MYNeeds &other)
{
    _devices = other._devices;

    return *this;
}

bool MYNeeds::operator==(const MYNeeds &other) const
{
    return _devices == other._devices;
}

std::string NeedsBits::toString() const
{
    std::string bits;

    if (test(RAW))
    {
        bits += "R";
    }

    if (test(NonRAW))
    {
        bits += "N";
    }

    if (test(Video))
    {
        bits += "V";
    }

    if (test(XMP))
    {
        bits += "X";
    }

    if (test(DisplayImage))
    {
        bits += "D";
    }

    if (test(Preview))
    {
        bits += "P";
    }

    if (test(Thumbnail))
    {
        bits += "T";
    }

    if (test(8))
    {
        bits += "Q";
    }

    if (bits.empty())
    {
        bits = "--";
    }

    return bits;
}

std::string MYDeviceNeeds::toString() const
{
    std::string s;
    s += "Policy:" + policyWants().toString();
    s += ",SoftWant:" + softWants().toString();
    s += ",HardWant:" + hardWants().toString();
    s += ",HardDontWant:" + hardDontWants().toString();
    s += ",Latest:" + hasLatest().toString();
    s += ",Stale:" + hasStale().toString();
    s += ",CanGenerate:" + canGenerateLatestOrStale().toString();
    s += ",CantParse:" + cantParse().toString();
    return s;
}

std::string MYNeeds::toString() const
{
    std::string s;

    for (const auto &device : *this)
    {
        if (!s.empty())
        {
            s += "; ";
        }

        s += device.first.toString() + "=" + device.second.toString() + "";
    }

    return s;
}
