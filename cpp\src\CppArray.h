#pragma once

#include <node.h>
#include "MYTrev.h"
#include "bjson.h"
#include "MYLiterals.h"
#include "base64.h"
#include "MYHash.h"
#include "helpers.h"
#include "MYTrev.h"
#include "MYHash.h"
#include <string>

using v8::Array;
using v8::Date;
using v8::FunctionCallbackInfo;
using v8::Isolate;
using v8::Local;
using v8::Number;
using v8::Object;
using v8::String;
using v8::Value;

class CppObject;

class CppArray
{
private:
	v8::Local<v8::Array> _v8Array;
	v8::Isolate *_isolate;
	v8::Local<v8::String> s(const std::string &value) const;
	v8::Local<v8::Number> n(double value) const;

public:
	CppArray(v8::Isolate *isolate);
	CppArray(v8::Isolate *isolate, const v8::Local<v8::Value> &v8Object);
	void wrap(const v8::Local<v8::Value> &v8Object);
	v8::Local<v8::Array> unwrap();
	void New();
	void addObject(CppObject &obj);
	void addTRev(const MYTRev &trev);
	void addHash(const MYHash &value);
	void addBinary(uint8_t *data, size_t size);
	void addString(const std::string &string);
	template <typename TFunc>
	void forEach(const TFunc &fnc)
	{
		auto count = _v8Array->Length();
		for (uint32_t i = 0; i < count; ++i)
		{
			fnc(i, _v8Array->Get(i));
		}
	}
};
