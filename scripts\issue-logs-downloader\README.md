# Issue log downloader (requries at least node v8)

## How to use:

You need AWS-CLI installed and configured on your local machine, along with the proper S3 permissions attached to your account.

Details on AWS CLI setup can be found here: https://docs.aws.amazon.com/cli/latest/userguide/cli-chap-welcome.html

You need access to the Github Repository `https://www.github.com/mylollc/mylo`

You need to create a Github OAUTH token with read permissions for repositories connected to your account

- Instructions here: https://help.github.com/en/github/authenticating-to-github/creating-a-personal-access-token-for-the-command-line

You will be provided with a Github OAUTH token. _You will not be able to see it again, so do not lose it_. This token must be set as the environment variable - `GH_TOKEN`. For UNIX systems, `export GH_TOKEN=<YOURTOKENHERE>` (you should put this in your `~/.bashrc` file so that it is persistent)

### Install node packages

```bash
npm i
```

To run the downloader, enter the following comamnd:

```bash
node download-logs.js 45779
```

This will download all logs referenced to Github issue number 45779's issue body (not comments) in the mylollc/mylo repository, into a local `./logs` folder.

### Note: This download can take a long time. In the example case, there are roughly 120 files to download, adding up to about 3GB of log file data
