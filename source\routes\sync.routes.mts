import express = require("express");
import { microservice as g } from "../microservices/account.microservice.mjs";
import { safeAny, secure, validateClientBuild } from "../system/safe.mjs";
import { binaryEncoder, sendResponse } from "../system/bjson.cjs";

export function addSyncRoutes(router: express.Router) {
  router.post(
    "/accounts/:aid/ping",
    validateClientBuild,
    safeAny,
    secure,
    (req, res, next) => {
      let context = req.context;
      g.systemService
        .ping(context, context.aid, context.any)
        .then((result) => {
          context.dumpLog();
          sendResponse(req, res, result, (r) =>
            binaryEncoder.encode_account_ping_response(r)
          );
        })
        .catch(next);
    }
  );

  router.post(
    "/accounts/:aid/sync",
    validateClientBuild,
    safeAny,
    secure,
    (req, res, next) => {
      let context = req.context;
      g.systemService
        .sync(context, context.aid, context.any)
        .then((result) => {
          context.dumpLog();
          sendResponse(req, res, result, (r) => binaryEncoder.encode_sync(r));
        })
        .catch(next);
    }
  );
}
