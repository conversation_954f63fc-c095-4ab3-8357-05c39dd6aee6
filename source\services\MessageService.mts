import { Message, merge } from "../models/Message.model.mjs";
import { error } from "../system/error.mjs";
import { MessageRestDataService } from "../dataServices/Message.rest.dataService.mjs";
import { Context } from "../system/Context.mjs";
import { Itx } from "../system/data.mjs";
import { check } from "../system/check.mjs";

export class MessageService {
  constructor(
    private dataService: MessageRestDataService,
    private tx: Itx<Message>
  ) {}

  public async create(context: Context, aid: number, message: Message) {
    message.accountId(aid);
    await check(message);
    return this.dataService.create(context, message);
  }

  public list(context: Context, aid: number) {
    return this.dataService.findByAccountId(context, aid);
  }

  public read(context: Context, aid: number, mid: number) {
    return this.dataService
      .readByAccountIdAndMessageId(context, aid, mid)
      .then((message) => {
        if (!message) {
          return error(404, "MESSAGE_NOT_VALID_FOR_ACCOUNT");
        }
        return message;
      }) as Promise<Message>;
  }

  public async update(context: Context, aid: number, message: Message) {
    return await this.tx(context, async () => {
      const dbVersion = await this.dataService.readByAccountIdAndMessageId(
        context,
        message.accountId(),
        message.messageId()
      );
      message = merge(dbVersion, message);
      await check(message);
      return this.dataService.update(context, message);
    });
  }

  public delete(context: Context, aid: number, mid: number) {
    return this.dataService.deleteByAccountIdAndMessageId(context, aid, mid);
  }
}
