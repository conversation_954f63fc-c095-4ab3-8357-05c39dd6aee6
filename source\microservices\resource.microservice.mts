import { ResourceSyncService } from "../services/ResourceSyncService.mjs";
import { ResourceService } from "../services/ResourceService.mjs";
import { config, getServices, loadConfig } from "../system/Config.mjs";

export class ResourceMicroservice {
  public syncService: ResourceSyncService;
  public resourceService: ResourceService;
  public badAccounts: number[] = [];

  public test() {
    return new Promise((resolve, reject) => {
      resolve(true);
    });
  }

  public async start() {
    await loadConfig();

    this.syncService = new ResourceSyncService();
    this.resourceService = new ResourceService();

    let badAccountString = config.bad_accounts || "";
    if (badAccountString) {
      for (let accountString of badAccountString.split(",")) {
        this.badAccounts.push(parseInt(accountString, 10));
      }
    }
  }
}

export let microservice = new ResourceMicroservice();
