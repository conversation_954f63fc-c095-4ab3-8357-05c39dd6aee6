import express = require("express");

export class RateLimiter {
    private maxRequests: number;
    private timeWindow: number;
    private requests: Map<string, { count: number, firstRequestTime: number }>;
    constructor(maxRequests, timeWindow) {
        this.maxRequests = maxRequests;
        this.timeWindow = timeWindow;
    }
    shouldLimit(key: string): boolean {
        const currentTime = Date.now();
        const requestData = this.requests.get(key);

        if (!requestData) {
            this.requests.set(key, { count: 1, firstRequestTime: currentTime });
            return false;
        }

        const { count, firstRequestTime } = requestData;

        if (currentTime - firstRequestTime > this.timeWindow) {
            this.requests.set(key, { count: 1, firstRequestTime: currentTime });
            return false;
        }

        if (count >= this.maxRequests) {
            return true;
        }

        this.requests.set(key, { count: count + 1, firstRequestTime });
        return false;
    }
}

export function rateLimiter(maxRequests: number, timeWindow: number) {
    let rateLimiter = new RateLimiter(maxRequests, timeWindow);
    return (req: express.Request, res, next) => {
        const key = req.context.userAgent.accountId + req.context.userAgent.deviceId;
        if (rateLimiter.shouldLimit(key)) {
            return res.status(429).json({ message: "Too many requests" });
        }
        next();
    };
}

