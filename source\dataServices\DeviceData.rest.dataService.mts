
import { query } from "../system/Postgres.mjs";
import { DeviceData, IDeviceData} from "../models/DeviceData.model.mjs";
import { Context } from "../system/Context.mjs";
import { makeError } from "../system/error.mjs";


export class DeviceDataRestDataService {

    



  public query(context: Context, sql: string, params: any[]) {
    return query < IDeviceData> (context, sql, params)
      .then(results => {
        if (results.length === 1) {
          if (!results[0].accountId && !results[0].deviceId) {
      return [];
    }
  }
  return results.filter(o => !!o).map(o => new DeviceData(o));
});
    }

		public create (context: Context, entity: DeviceData) {
          


  let params = [
    entity.flags(),
				entity.modifiedTime(),
				entity.createdTime(),
				entity.accountId(),
				entity.deleted(),
				entity.t(),
				entity.d(),
				entity.deviceId(),
				entity.build(),
				entity.lastAccessTime(),
				entity.os(),
				entity.mediaCount(),
				entity.protocolVersion(),
				entity.version(),
				entity.lastStartupTime(),
				entity.lastHidTime(),
				entity.lastImportTime(),
				entity.originalSize(),
				entity.localOriginalSize()
  ];
  return this
    .query(context, "select * from a0.device_data_create ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14,$15,$16,$17,$18,$19) ", params)
  .then(r => r[0]);
        }

		public readByAccountIdAndDeviceId (context: Context, accountId: number, deviceId: number) {
  let params = [
    accountId,
				deviceId
  ];
  return this
    .query(context, "select * from a0.device_data_read_by_account_id_and_device_id  ($1,$2) ", params).then(r => r[0]); 
                
        }

		public readByAccountId (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.device_data_read_by_account_id  ($1) ", params).then(r => r[0]); 
                
        }

		public update (context: Context, entity: DeviceData) {
          


  let params = [
    entity.flags(),
				entity.modifiedTime(),
				entity.createdTime(),
				entity.accountId(),
				entity.deleted(),
				entity.t(),
				entity.d(),
				entity.deviceId(),
				entity.build(),
				entity.lastAccessTime(),
				entity.os(),
				entity.mediaCount(),
				entity.protocolVersion(),
				entity.version(),
				entity.lastStartupTime(),
				entity.lastHidTime(),
				entity.lastImportTime(),
				entity.originalSize(),
				entity.localOriginalSize()
  ];
  return this
    .query(context, "select * from a0.device_data_update ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14,$15,$16,$17,$18,$19) ", params)
  .then(r => r[0]);
        }

		public deleteByAccountIdAndDeviceId (context: Context, accountId: number, deviceId: number) {
  let params = [
    accountId,
				deviceId
  ];
  return this
    .query(context, "select * from a0.device_data_delete_by_account_id_and_device_id  ($1,$2) ", params).then(r => r[0]); 
                
        }

		public deleteByAccountId (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.device_data_delete_by_account_id  ($1) ", params).then(r => r[0]); 
                
        }

		public findByAccountId (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.device_data_find_by_account_id  ($1) ", params); 
                
        }

		public findByAccountIdAndDeviceId (context: Context, accountId: number, deviceId: number) {
  let params = [
    accountId,
				deviceId
  ];
  return this
    .query(context, "select * from a0.device_data_find_by_account_id_and_device_id  ($1,$2) ", params); 
                
        }

		public merkle (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.device_data_merkle ($1) ", params).then(r => r[0]); 
                
        }

}
