import { Context } from "../system/Context.mjs";
import { config, getServices } from "../system/Config.mjs";
import { post, postForm } from "../system/fetch.mjs";

export class BacktraceService {
  private _token: string;
  constructor() {}

  private async getAccessToken(): Promise<string> {
    if (this._token && this._token.length != 0) {
      return Promise.resolve(this._token);
    }

    const loginResponse = await postForm(
      "https://mylio.sp.backtrace.io/api/login",
      {
        username: config.backtrace.username,
        password: config.backtrace.password,
      }
    );

    let loginResult = await loginResponse.json();
    return loginResult.token;
  }

  public async crashdumpExists(context: Context, filename: string) {
    let token = await this.getAccessToken();
    let crashDumpResult = await post(
      `https://mylio.sp.backtrace.io/api/query?token=${token}&universe=mylio&project=Mylio`,
      {
        filter: [{ upload_file_minidump: [["equal", filename]] }],
      }
    );
    let objects = crashDumpResult.response.objects;
    let size = objects[0][1].length;
    return size > 0;
  }
}
