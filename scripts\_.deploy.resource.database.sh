#!/bin/bash

echo "started $0"
source ./_.postgres.sh
export PGCLIENTENCODING=UTF8

# Check for correct number and type of parameters
if [ $# -ne 5 ] || ! [[ $2 =~ ^[0-9]+$ && $4 =~ ^-?[0-9]+$ && $5 =~ ^-?[0-9]+$ ]]; then
  echo "Usage: $0 <HOST> <PORT:int> <USER> <FROM:int> <TO:int>"
  exit 1
fi

HOST=$1
PORT=$2
USER=$3
FROM=$4
TO=$5

open_pg_connection $HOST $PORT $USER
echo $PGPASSWORD
# Loop COUNT times
for ((i = FROM; i <= TO; i++))
do
run_script "../database/vnext/setup/resource$i.ddl.sql"
run_script "../database/vnext/setup/resource$i.dml.sql"
run_script "../database/vnext/setup/resource$i.secure.sql"
done
close_pg_connection

echo "completed $0"
