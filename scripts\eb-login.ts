const { STSClient, GetSessionTokenCommand } = require("@aws-sdk/client-sts");
const { readFileSync, writeFileSync } = require("fs");
const { join } = require("path");
const readline = require("node:readline/promises").createInterface({
  input: process.stdin,
  output: process.stdout,
  terminal: true,
});

async function main() {
  const username = await readline.question("Enter your username: ");
  const mfaToken = await readline.question("Enter your MFA token code: ");
  const stsClient = new STSClient({ region: "us-west-2" });

  const mfaParams = {
    DurationSeconds: 12 * 3600,
    SerialNumber: `arn:aws:iam::988863557391:mfa/${username}`,
    TokenCode: mfaToken.trim(),
  };

  try {
    console.log(mfaParams);

    const { Credentials } = await stsClient.send(
      new GetSessionTokenCommand(mfaParams)
    );

    const credentials = {
      accessKeyId: Credentials.AccessKeyId,
      secretAccessKey: Credentials.SecretAccessKey,
      sessionToken: Credentials.SessionToken,
    };

    const credentialsFilePath = join(process.env.HOME, ".aws", "config");

    const credentialsFileContent = readFileSync(credentialsFilePath, {
      encoding: "utf-8",
    });

    const profileHeader = "profile eb-cli";
    let updatedCredentialsFileContent;

    if (credentialsFileContent.includes(profileHeader)) {
      updatedCredentialsFileContent = credentialsFileContent.replace(
        /\[profile eb-cli\]\naws_access_key_id\s*=\s*[^\n]*\naws_secret_access_key\s*=\s*[^\n]*(\naws_session_token\s*=\s*[^\n]*)?/,
        `[profile eb-cli]\naws_access_key_id = ${credentials.accessKeyId}\naws_secret_access_key = ${credentials.secretAccessKey}\naws_session_token = ${credentials.sessionToken}\n`
      );
    } else {
      updatedCredentialsFileContent =
        credentialsFileContent.trim() +
        `\n\n[${profileHeader}]\naws_access_key_id = ${credentials.accessKeyId}\naws_secret_access_key = ${credentials.secretAccessKey}\naws_session_token = ${credentials.sessionToken}\n`;
    }

    writeFileSync(credentialsFilePath, updatedCredentialsFileContent);

    console.log(`Credentials written successfully to ${credentialsFilePath}`);
  } catch (error) {
    console.error(error);
  } finally {
    readline.close();
  }
}

main();
