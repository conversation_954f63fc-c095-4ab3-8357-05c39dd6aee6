{"stripe_private_key": "sk_test_qoromUDiG2j5BGwlYdZ40VRx", "stripe_version": "2019-12-03", "wordpress": "http://mylio.com", "cloud": "https://merkle3accounttest.mylio.com", "pgcreds": "postgres:password", "sendgrid_api_key": "*********************************************************************", "connectionStrings": {"a0": "pg://<user>:<password>@localhost:5432/account0"}, "services": [{"name": "resource", "uri": ["http://localhost:4000"]}, {"name": "account", "uri": ["http://localhost:3000"]}, {"name": "location", "uri": ["https://api.opencagedata.com/geocode/v1/json?q=%f,%f&pretty=0&no_annotations=1&key=74306074d3a5ef1f7c86ea43062367a6"]}, {"name": "telemetry", "uri": ["http://localhost:5000"]}, {"name": "cloudSignalChannel", "uri": ["signal-test.mylio.com"]}]}