import { TelemetryService } from "../services/TelemetryService.mjs";
import { TelemetryDataService } from "../dataServices/TelemetryDataService.mjs";
import { loadConfig } from "../system/Config.mjs";

export class TelemetryMicroservice {
  public telemetryService: TelemetryService;

  public test() {
    return new Promise((resolve, reject) => {
      resolve(true);
    });
  }

  public async start() {
    await loadConfig();
    this.telemetryService = new TelemetryService(new TelemetryDataService());
  }
}

export let microservice = new TelemetryMicroservice();
