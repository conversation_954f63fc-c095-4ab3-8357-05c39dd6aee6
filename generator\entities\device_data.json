{"plural": "devicedata", "k": 4, "mixin": ["all", "syncable"], "flow": "web<>cloud<>postgres<>disk", "fields": {"device_id": {"datatype": "int32"}, "build": {"datatype": "string"}, "last_access_time": {"datatype": "date"}, "os": {"datatype": "string"}, "media_count": {"datatype": "int32"}, "protocol_version": {"datatype": "int32"}, "version": {"datatype": "string"}, "last_startup_time": {"datatype": "date"}, "last_hid_time": {"datatype": "date"}, "last_import_time": {"datatype": "date"}, "original_size": {"datatype": "int64"}, "local_original_size": {"datatype": "int64"}}, "directives": {"identify": [["account_id", "device_id"]], "find": [["account_id"], ["account_id", "device_id"]]}}