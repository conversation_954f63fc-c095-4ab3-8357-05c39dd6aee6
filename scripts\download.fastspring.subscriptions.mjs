import fs from 'fs/promises';


let username = "C96DV4ZVRT-QTWPPAV_TMQ";
let password = "-9Nbw8euTX6rk_MCb9AznQ";

const fastspringApiUrl = 'https://api.fastspring.com/subscriptions';

async function getPage(page = 1) {
    try {
        const response = await fetch(`${fastspringApiUrl}?page=${page}`, {
            headers: {
                'Authorization': 'Basic ' + Buffer.from(username + ':' + password).toString('base64'),
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Error fetching subscriptions:', error);
        return null;
    }
}

async function downloadAllSubscriptions() {
    let allSubscriptions = [];
    let page = 1;
    let hasMore = true;

    while (hasMore) {
        const data = await getPage(page);

        if (data && data.subscriptions && data.subscriptions.length > 0) {
            allSubscriptions = allSubscriptions.concat(data.subscriptions);
            page += 1;
        } else {
            hasMore = false;
        }
    }

    try {
        await fs.writeFile('subscriptions.json', JSON.stringify(allSubscriptions, null, 2));
        console.log(`Downloaded ${allSubscriptions.length} subscriptions.`);
    } catch (error) {
        console.error('Error writing to file:', error);
    }
}

downloadAllSubscriptions();



