SET client_min_messages=WARNING;

drop database if exists telemetry;
create database telemetry;

\c telemetry;

create extension pgcrypto;

create schema t0;

create table t0.client_event (
    device_long_id bytea,
    time_on_client_clock timestamptz NOT NULL,
    event_type char(2) NOT NULL,
    uptime int8,
    data text,
    session_id int4,
    modified_time timestamptz not null default now(),
    PRIMARY KEY (device_long_id, event_type, time_on_client_clock)
);

create table t0.cloud_event (
    account_id int,
    time_on_cloud_clock timestamptz not null,
    device_id int,
    event_type int not null,
    data json,
    modified_time timestamptz not null default now(),
    primary key(event_type, time_on_cloud_clock)
);

create table t0.counters (
    account_id int,
    device_id int,
    data json,
    modified_time timestamptz not null default now(),
    primary key (account_id, device_id)
);

create table t0.telemetry_metadata (
    telemetry_metadata_id int primary key,
    last_cloudwatch_event_time timestamptz,
    last_event_handler_time timestamptz
);

create table t0.exit_survey (
    account_id int,
    creation_time timestamptz not null default now(),
    data json,
    primary key(account_id, creation_time)
);
