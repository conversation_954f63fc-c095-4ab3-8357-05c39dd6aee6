import moment = require("moment");
import express = require("express");
import { Device } from "../models/Device.model.mjs";
import { Account } from "../models/Account.model.mjs";
import { Token, TokenType } from "../models/Token.mjs";
import { config } from "./Config.mjs";
import { Context } from "./Context.mjs";
import { Message } from "../models/Message.model.mjs";
import { makeError } from "./error.mjs";
import { ids } from "./Strings.mjs";

export function n(
  value: unknown | string | any,
  defaultValue: number = undefined
) {
  if (typeof value === "number") return value;

  if (typeof value === "string") return parseInt(value, 10);

  if (typeof value === "boolean") {
    if (value) return 1;
    else return 0;
  }

  if (value === null || value === void 0) return defaultValue;

  throw "Value can not be converted to a number";
}

export function s(
  value: unknown | string | any,
  defaultValue: string = undefined
) {
  if (typeof value === "number") return value.toString();

  if (typeof value === "string") return value;

  if (typeof value === "boolean") {
    if (value) return "true";
    else return "false";
  }

  if (value === null || value === void 0) return defaultValue;

  throw "Value can not be converted to a string";
}

export function b(
  value: unknown | string | any,
  defaultValue: boolean = undefined
) {
  if (typeof value === "number") return value !== 0;

  if (typeof value === "string")
    return value !== "false" && value !== "0" && value !== "";

  if (typeof value === "boolean") return value;

  if (value === null || value === void 0) return defaultValue;

  throw "Value can not be converted to a boolean";
}

export function secure(req: express.Request, res, next) {
  try {
    let context = req.context;

    // secure called without safe
    if (!context) {
      throw makeError(401, ids.NOT_AUTHENTICATED);
    }

    const { tokenString, aid } = context;

    let result = Token.tryVerify(tokenString, config.access_token_secret);
    if (!result.success) {
      throw makeError(401, ids.NOT_AUTHENTICATED);
    }

    context.token = result.token;

    if ((context.aid && !context.hasAdminRights()) && result.token?.aid() !== context.aid) {
      throw makeError(403, ids.FORBIDDEN);
    }

    if (!context.token) {
      throw makeError(401, ids.NOT_AUTHENTICATED);
    }

    // ensures that the supplied rest parameters
    // are allowed by the user's security permissions
    if (
      aid &&
      !context.token.canAccessAccount(aid) &&
      !context.token.hasAdminRights()
    ) {
      throw makeError(403, ids.FORBIDDEN);
    }

    return next();
  } catch (err) {
    return next(err);
  }
}

export function safeAccount(
  req,
  res: express.Response,
  next: express.NextFunction
) {
  return safe("Account", req, res, next);
}

export function safeDevice(
  req,
  res: express.Response,
  next: express.NextFunction
) {
  return safe("Device", req, res, next);
}

export function safeMessage(
  req,
  res: express.Response,
  next: express.NextFunction
) {
  return safe("Message", req, res, next);
}

export function safeAny(
  req,
  res: express.Response,
  next: express.NextFunction
) {
  return safe("Any", req, res, next);
}

export function safeNone(
  req,
  res: express.Response,
  next: express.NextFunction
) {
  return safe("None", req, res, next);
}

export function validateClientBuild(req, res, next) {
  let context = req.context;
  if (context && context.userAgent?.minimum_supported_build < config.minimum_supported_build) {
    throw makeError(400, ids.CLIENT_BUILD_NOT_SUPPORTED);
  }
  return next();
}

export function admin(req, res, next) {
  let context = req.context;
  let { token } = context;

  if (!context || !token) {
    throw makeError(401, ids.NOT_AUTHENTICATED);
  }

  if (!token.hasAdminRights()) {
    throw makeError(403, ids.FORBIDDEN);
  }

  return next();
}

export function secureURLTokenImpl(req, res, next) {
  let context = req.context;

  if (!context) {
    throw makeError(401, ids.NOT_AUTHENTICATED);
  }

  const { tokenString } = context;

  if (!tokenString) {
    throw makeError(401, ids.NOT_AUTHENTICATED);
  }

  let result = Token.tryVerify(tokenString, config.email_token_secret);
  if (!result.success) {
    if (result.name === "TokenExpiredError")
      throw makeError(401, ids.TOKEN_EXPIRED, result.error);
    throw makeError(401, ids.NOT_AUTHENTICATED, result.error);
  }

  context.token = result.token;

  if (!context.token) {
    let decoded = Token.tryDecode(tokenString);

    if (!decoded) {
      throw makeError(401, ids.NOT_AUTHENTICATED);
    }

    // expired
    if (moment.unix(decoded.exp()) < moment()) {
      let path = req.route.path;
    }

    throw makeError(401, ids.NOT_AUTHENTICATED);
  }

  if (context.token && context.token.tokenType() !== TokenType.url) {
    throw makeError(403, ids.FORBIDDEN);
  }
}

export function secureURLToken(req, res, next) {
  try {
    secureURLTokenImpl(req, res, next);
    return next();
  } catch (err) {
    return next(err);
  }
}

function safe(verify: string, req, res, next) {
  try {
    let context = req.context || new Context();
    let tokenString = "";
    let authHeader = req.header("Authorization");
    let aid: number;
    let did: number;

    context.origin = req.get("origin");

    if (authHeader) {
      let parts = authHeader.split(" ");
      if (parts.length > 1) {
        tokenString = parts[1];
      }
    } else if (req.params.token) {
      tokenString = req.params.token;
    }



    context.did =
      parseInt(req.params && req.params.did, 10) || did || undefined;
    context.aid =
      parseInt(req.params && req.params.aid, 10) || aid || undefined;
    context.mid = parseInt(req.params && req.params.mid, 10) || undefined;
    context.idp = (req.params && req.params.idp) || undefined;
    context.task = (req.params && req.params.task) || undefined;
    context.couponId = req.params && req.params.couponId;
    context.tokenString = tokenString;
    context.audience = req.header("audience") || "website.mylio.com";
    context.query = req.query || {};

    if (
      context.aid &&
      req.body &&
      req.body.accountId &&
      req.body.accountId !== context.aid
    )
      throw makeError(401, ids.FORBIDDEN);

    if (
      context.did &&
      req.body &&
      req.body.deviceId &&
      req.body.deviceId !== context.did
    )
      throw makeError(401, ids.FORBIDDEN);

    if (req.body) {
      let state = req.body;
      if (state["_state"]) state = state["_state"];
      if (state?.email) state.email = state.email.toLowerCase();
      if (state?.sub) state.sub = state.sub.toLowerCase();
      switch (verify) {
        case "Account":
          context.account = new Account(state);
          context.account.accountId(context.aid);
          break;
        case "Device":
          context.device = new Device(state);
          context.device.accountId(context.aid);
          context.device.deviceId(context.did);
          break;
        case "Message":
          context.message = new Message(state);
          context.message.accountId(context.aid);
          context.message.deviceId(context.did);
          context.message.messageId(context.mid);
          break;
        case "None":
          break;
        case "Any":
          context.any = state;
          break;
        default:
      }
    }


    if (req?.query?.email) req.query.email = req.query.email.toLowerCase();

    if (req?.query?.sub) req.query.sub = req.query.sub.toLowerCase();



    context.info("REQUEST", { method: req.method, path: req.path });

    if (!req.route || !req.route.path.match(/\/sync/g)) {
      context.info("HEADERS", { headers: req.headers });
      context.info("BODY", { body: req.body });
    }

    req.body = undefined;

    context.tokenString = tokenString || undefined;

    req.context = context;
    return next();
  } catch (err) {
    return next(err);
  }
}

export function shouldRedirect(req, res, next) {
  req.shouldRedirect = true;
  next();
}

export function apiKey(req: Request, res, next) {
  if ((req as any).query.mylio_api_key === config.mylio_api_key) return next();
  else return next(makeError(400, "INVALID_API_KEY", "The API Key is invalid"));
}

