import { <PERSON>ti<PERSON>, Field } from "./Entity.mjs";
import { <PERSON>riptHelper } from "./ScriptHelper.mjs";

export class TsFile {
  public b: ScriptHelper;
  private _script = "";

  public pname(f: Field) {
    return `_${f.name}`;
  }

  constructor(private e: Entity) {
    let b = new ScriptHelper(e);
    this.b = b;
  }

  public write(f: (e: Entity, b: ScriptHelper) => string) {
    this._script += f(this.e, this.b);
  }

  public toString() {
    return this._script;
  }
}
