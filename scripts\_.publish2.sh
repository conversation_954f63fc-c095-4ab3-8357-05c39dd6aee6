#!/bin/bash
echo "started $0"
SERVICE=$1
VERSION=$2
ENV=$3
SERVICES=()
VERSIONS=()
ENVS=()


if [[ -z "$SERVICE" ]] 
then
echo 'What service do you want to publish?'
options=("account" "resource" "telemetry" "all")
select SERVICE in "${options[@]}"
do
    if [[ $SERVICE = "all" ]]
    then
        SERVICES+=("account" "resource" "telemetry")
    else
        SERVICES+=($SERVICE)
    fi
    break;
done
fi

if [[ -z "$ENV" ]] 
then
echo 'What environment do you want to publish?'
options=("test" "production" "all")
select ENV in "${options[@]}"
do
    if [[ $ENV = "all" ]]
    then
        ENVS+=("test" "production")
    else
        ENVS+=($ENV)
    fi
    break;
done
fi

if [[ -z "$VER" ]] 
then
echo 'What version (EBS Version) do you want to publish?'
options=("0" "1" "2" "3" "9" "all")
select VER in "${options[@]}"
do
    if [[ $VER = "all" ]]
    then
        VERS+=("0" "1" "2" "3")
    else
        VERS+=($VER)
    fi
    break;
done
fi


for s in ${SERVICES[@]}; do
    for e in ${ENVS[@]}; do
        for v in ${VERS[@]}; do
            ./_.publish.sh ebs $s $e $v
        done
    done
done



#echo "./_.publish.sh ebs $SERVICE $ENV $VER"

#./_.publish.sh ebs $SERVICE $ENV $VER




