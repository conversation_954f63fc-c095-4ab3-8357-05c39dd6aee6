#include <node.h>
#include "MYTrev.h"
#include "bjson.h"
#include "MYLiterals.h"
#include "base64.h"
#include "MYHash.h"
#include "helpers.h"
#include "MYTrev.h"
#include "MYHash.h"
#include "CppArray.h"
#include "CppObject.h"
#include <string>
#include <vector>

using v8::Array;
using v8::Date;
using v8::FunctionCallbackInfo;
using v8::Isolate;
using v8::Local;
using v8::Number;
using v8::Object;
using v8::String;
using v8::Value;

v8::Local<v8::String> CppObject::s(const std::string &value) const
{
	return String::NewFromUtf8(_isolate, value.c_str());
}

v8::Local<v8::Number> CppObject::n(double value) const
{
	return Number::New(_isolate, value);
}

CppObject::CppObject(v8::Isolate *isolate) : _isolate(isolate) {}
CppObject::CppObject(v8::Isolate *isolate, const v8::Local<v8::Object> &v8Object) : _v8Object(v8Object), _isolate(isolate) {}

v8::Local<v8::Object> CppObject::New()
{
	_v8Object = v8::Object::New(_isolate);
	return _v8Object;
}

void CppObject::wrap(const v8::Local<v8::Object> &v8Object)
{
	_v8Object = v8Object;
}

v8::Local<v8::Object> CppObject::unwrap()
{
	return _v8Object;
}

CppArray CppObject::getArray(const std::string &pname)
{
	auto value = _v8Object->Get(s(pname));
	return CppArray(_isolate, v8::Local<v8::Array>::Cast(value));
}

void CppObject::setArray(const std::string &pname, CppArray &array)
{
	_v8Object->Set(s(pname), array.unwrap());
}

CppObject CppObject::getObject(const std::string &pname)
{
	auto obj = _v8Object->Get(s(pname))->ToObject();
	return CppObject(_isolate, obj);
}

void CppObject::setObject(const std::string &pname, CppObject &object)
{
	_v8Object->Set(s(pname), object.unwrap());
}

void CppObject::setObject(int pname, CppObject &object)
{
	_v8Object->Set(n(pname), object.unwrap());
}

void CppObject::setDate(const std::string &pname, uint64_t milliseconds)
{
	_v8Object->Set(s(pname), v8::Date::New(_isolate, static_cast<double>(milliseconds)));
}

bool CppObject::hasValue(const std::string &pname) const
{
	auto value = _v8Object->Get(s(pname));
	return value->IsUndefined() || value->IsNull();
}

int32_t CppObject::getInt32(const std::string &pname) const
{
	auto propertyName = s(pname);
	return (int32_t)_v8Object->Get(propertyName)->NumberValue();
}

bool CppObject::getBool(const std::string &pname) const
{
	auto propertyName = s(pname);
	return (int32_t)_v8Object->Get(propertyName)->BooleanValue();
}

void CppObject::setString(const std::string &pname, const std::string &value)
{
	auto propertyName = s(pname);
	_v8Object->Set(propertyName, s(value));
}

std::string CppObject::getString(const std::string &pname) const
{
	auto propertyName = s(pname);
	auto retVal = _v8Object->Get(propertyName)->ToString();
	return helpers::unwrap(retVal);
}

void CppObject::setHash(const std::string &pname, const MYHash &value)
{
	auto propertyName = s(pname);
	auto value64 = helpers::hash64(value);
	_v8Object->Set(propertyName, s(value64));
}

void CppObject::setNumber(const std::string &pname, double number)
{
	_v8Object->Set(s(pname), n(number));
}

void CppObject::setBool(const std::string &pname, bool value)
{
	_v8Object->Set(s(pname), v8::Boolean::New(_isolate, value));
}

void CppObject::setTRev(const std::string &pname, const MYTRev &value)
{
	auto propertyName = s(pname);
	auto value64 = helpers::trev64(value);
	_v8Object->Set(propertyName, s(value64));
}

void CppObject::setBinary(int pname, const std::vector<uint8_t> &buffer)
{
	auto value64 = base64_encode(&buffer[0], buffer.size());
	_v8Object->Set(n(pname), s(value64));
}

void CppObject::setBinary(const std::string &pname, const std::vector<uint8_t> &buffer)
{
	setBinary(pname, (uint8_t *)&buffer[0], buffer.size());
}

void CppObject::setBinary(const std::string &pname, uint8_t *start, size_t size)
{
	auto value64 = base64_encode((unsigned char *)start, size);
	_v8Object->Set(s(pname), s(value64));
}

MYHash CppObject::getHash(const std::string &pname) const
{
	auto propertyName = s(pname);
	auto retVal = _v8Object->Get(propertyName)->ToString();
	auto value64 = helpers::unwrap(retVal);
	return helpers::hash(value64);
}
