{"stripe_private_key": "sk_test_qoromUDiG2j5BGwlYdZ40VRx", "system": {"encoding": "base64", "account_script": "account0.human", "resource_script": "resource0.human", "telemetry_script": "telemetry.human", "datawarehouse_script": "datawarehouse.human"}, "account": {"shards": [{"database": "account0", "host": "localhost", "password": ":pg_mylio_password", "port": 5432, "foreign_server": "localhost_account0"}]}, "resource": {"shards": [{"firstBucket": 0, "lastBucket": 3, "database": "resource0", "host": "localhost", "port": 5432, "password": ":pg_mylio_password"}, {"firstBucket": 4, "lastBucket": 7, "database": "resource1", "host": "localhost", "port": 5432, "password": ":pg_mylio_password"}, {"firstBucket": 8, "lastBucket": 11, "database": "resource2", "host": "localhost", "port": 5432, "password": ":pg_mylio_password"}, {"firstBucket": 12, "lastBucket": 15, "database": "resource3", "host": "localhost", "port": 5432, "password": ":pg_mylio_password"}]}, "telemetry": {"shards": [{"database": "telemetry", "host": "localhost", "password": ":pg_mylio_password", "port": 5432, "foreign_server": "localhost_telemetry"}]}, "datawarehouse": {"shards": [{"database": "datawarehouse", "host": "localhost", "password": ":pg_mylio_password", "port": 5432, "foreign_server": "localhost_datawarehouse"}]}}