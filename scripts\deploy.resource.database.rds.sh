#!/bin/bash

set -e
echo "started $0"
# Prompt the user for input
read -p "Resource Protocol Version?" PROTOCOL_VERSION
# Check if the input is a number between 0 and 9
if [[ $PROTOCOL_VERSION =~ ^[0-9]+$ ]]; then
    echo "You entered: $PROTOCOL_VERSION"
else
    echo "Invalid input. Please enter a number between 0 and 9."
fi
export PGCLIENTENCODING=UTF8
./_.deploy.resource.database.sh "resource-$PROTOCOL_VERSION-0-rds.mylio.com" 5432 superuser 0 0
./_.deploy.resource.database.sh "resource-$PROTOCOL_VERSION-1-rds.mylio.com" 5432 superuser 1 1
./_.deploy.resource.database.sh "resource-$PROTOCOL_VERSION-2-rds.mylio.com" 5432 superuser 2 2
./_.deploy.resource.database.sh "resource-$PROTOCOL_VERSION-3-rds.mylio.com" 5432 superuser 3 3



echo "completed $0"