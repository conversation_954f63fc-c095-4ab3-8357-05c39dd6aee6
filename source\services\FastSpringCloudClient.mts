import { makeError } from "../system/error.mjs";
import { Subscription } from "./FastSpring.interfaces.mjs";
import { get, post, del } from "../system/fetch.mjs";

class FastSpringCloudClient {
    private username: string;
    private password: string;

    constructor(username: string, password: string) {
        this.username = username;
        this.password = password;
    }

    async getSubscription(subscriptionId: string) {
        return await get(`https://api.fastspring.com/subscriptions/${subscriptionId}`, this.makeHeaders());
    }

    async getOrder(orderId: string) {
        return await get(`https://api.fastspring.com/orders/${orderId}`, this.makeHeaders());
    }

    async updateSubscription(subscriptionId: string, updateData: any) {
        return await post(`https://api.fastspring.com/subscriptions/${subscriptionId}`, updateData, this.makeHeaders());
    }

    async previewSubscription(subscriptionId: string, updateData: any) {
        return await post(`https://api.fastspring.com/subscriptions/estimate/${subscriptionId}`, updateData, this.makeHeaders());
    }

    async getProduct(productId: string) {
        return await get(`https://api.fastspring.com/products/${productId}`, this.makeHeaders());
    }

    async getCustomer(accountId: string) {
        return await get(`https://api.fastspring.com/accounts/${accountId}`, this.makeHeaders());
    }

    async getSubscriptionsForCustomer(customerId: string) {
        return await get(`https://api.fastspring.com/subscriptions?accountId=${customerId}`, this.makeHeaders());
    }

    async getAccountsByEmail(email: string) {
        return await get(`https://api.fastspring.com/accounts?email=${encodeURIComponent(email)}`, this.makeHeaders());
    }

    makeHeaders() {
        const auth = Buffer.from(`${this.username}:${this.password}`).toString('base64');
        return {
            'Authorization': `Basic ${auth}`,
        }
    }
    async getProductPrice(id: string) {
        return await get(`https://api.fastspring.com/products/price/${id}`, this.makeHeaders());
    }

    async getProductPriceByCountry(id: string, country: string) {
        return await get(`https://api.fastspring.com/products/price/${id}?country=${country}`, this.makeHeaders());
    }

    async cancelSubscription(subscriptionId: string, immediate: boolean) {
        return await del(`https://api.fastspring.com/subscriptions/${subscriptionId}${immediate ? "billingPeriod=0" : ""}`, this.makeHeaders());
    }
}

export default FastSpringCloudClient;