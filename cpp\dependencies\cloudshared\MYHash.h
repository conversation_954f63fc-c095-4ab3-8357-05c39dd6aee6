#pragma once

#include <memory>
#include <string>
#include <set>
#include <array>
#include <bitset>
#include <algorithm>
#include <cstring>
#include "MyloAPI.h"
#include "MYStringUtil.h"
#include "base64.h"
#include "MYPrimitives.h"

class MYFSPath;
namespace Platform
{
    class MYFileStreamBase;
}

#define HASH_SHA1
// #define HASH_MD5

#ifdef HASH_SHA1
#define MYHASH_SIZE 20
#endif

#ifdef HASH_MD5
#define MYHASH_SIZE 16
#endif

typedef std::shared_ptr<class MYImageDecoder> MYImageDecoderPtr;
typedef std::array<unsigned char, MYHASH_SIZE> MYHashStorage;

struct MYHash
{
    friend class MYReplicationFileSendItem;
    friend class MYDeviceConnection;

    MYHash();
    MYHash(no_init_t);
    MYHash(const unsigned char *inHash);
    MYHash(const MYHashStorage &inHashStorage);

    static bool isValidHashString(const std::string &inString);
    static MYHash fromBucketAndDeviceId(uint16_t bucketId, uint32_t deviceId);
    static MYHash fromHashString(const std::string &inHashAsString, SerializationFlags flags);
    static MYHash fromHashString(const std::string &inHashAsString, bool base64 = false);
    static MYHash computedFrom(const std::string &inString);
    static MYHash computedFrom(const void *buffer, size_t bufferSize);
#ifdef MYLIO_CLIENT
    /**
     *  Returns the hash calculated for the given path and (optional) extension.
     *
     *  @param filePath         Path for the file to be hashed.
     *  @param nominalExtension (Optional) The true extension for the file pointed to by filePath.
     *                          If this is empty it will use the extension from 'filePath'.
     *
     *  @return A valid MYHash on success, an empty MYHash on error.
     */
    static MYHash computedFromFile(const MYFSPath &filePath, const std::string &nominalExtension = std::string());

    /**
     *  Returns the hash calculated for the given path.
     *
     *  @param filePath         Path for the file to be hashed.
     *  @param imageDecoder     Pointer to an existing or NULL image decoder. If non-NULL
     *                          it will be used to get the hash without decoding the file a 2nd
     *                          time (if possible). If NULL, a new decoder will be created and be
     *                          available for use by the caller on return.
     *
     *  @return A valid MYHash on success, an empty MYHash on error.
     */
    static MYHash computedFromFile(const MYFSPath &filePath, MYImageDecoderPtr &imageDecoder);

    /**
     *  Returns the hash calculated for the given path.
     *
     *  @param filePath         Path for the file to be hashed.
     *  @param imageDecoder     Pointer to an existing or NULL image decoder. If non-NULL
     *                          it will be used to get the hash without decoding the file a 2nd
     *                          time (if possible). If NULL, a new decoder will be created and be
     *                          available for use by the caller on return.
     *  @param quit             Pointer to bool which should cause cancel if true
     *
     *  @return A valid MYHash on success, an empty MYHash on error.
     */
    static MYHash computedFromFile(const MYFSPath &filePath, MYImageDecoderPtr &imageDecoder, bool *quit);

    /**
     *  Computes the hash for the given file and (optional) extension.
     *
     *  @param filePath         Path for the file to be hashed.
     *  @param nominalExtension The true extension for the file pointed to by filePath.
     *                          If this is empty it will use the extension from 'filePath'.
     *  @param imageDecoder     Pointer to an existing or NULL image decoder. If non-NULL
     *                          it will be used to get the hash without decoding the file a 2nd
     *                          time (if possible). If NULL, a new decoder will be created and be
     *                          available for use by the caller on return.
     *
     *  @return true if the hash was successfully calculated, false on error.
     */
    bool computeFromFile(const MYFSPath &filePath, const std::string &nominalExtension, MYImageDecoderPtr &imageDecoder, bool *quit);

    /**
     *  Computes the hash for the given file.
     *
     *  @param filePath         Path for the file to be hashed.
     *  @param imageDecoder     Pointer to an existing or NULL image decoder. If non-NULL
     *                          it will be used to get the hash without decoding the file a 2nd
     *                          time (if possible). If NULL, a new decoder will be created and be
     *                          available for use by the caller on return.
     *
     *  @return true if the hash was successfully calculated, false on error.
     */
    bool computeFromFile(const MYFSPath &filePath, MYImageDecoderPtr &imageDecoder, bool *quit);

    /**
     *  Computes the hash for the given file.
     *
     *  @param filePath         Path for the file to be hashed.
     *
     *  @return true if the hash was successfully calculated, false on error.
     */
    bool computeFromFile(const MYFSPath &filePath, bool *quit);

    bool computeFrom(const std::string &inString);
    bool computeFrom(const void *buffer, size_t bufferSize);
    static MYHash generated();
    void generate();
#endif // MYLIO_CLIENT
    void buildFromHashString(const std::string &inHashAsString, bool base64 = false);
    void buildFromHashString(const std::string &inHashAsString, SerializationFlags flags);
    bool buildFromHashString(const char *hashString, bool base64);
    bool buildFromHashString(const char *hashString, SerializationFlags flags);

    static std::set<MYHash> splitToSet(const std::string &input, char delimiter);
    bool empty() const;
    std::string toSQLString() const;
    std::string toString(bool base64 = false) const;

    // You need to pass a string so it doesnt get created all the time
    std::string toQuickString(bool base64, std::string &outString) const;

    std::string toString(SerializationFlags flags) const;
    void clear();

    uint16_t getBucket() const
    {
        uint16_t bucket = (uint16_t)raw[0] << 4;
        bucket |= (uint16_t)(raw[1]) >> 4;
        return bucket;
    }

    uint32_t getShortHash() const
    {
        uint32_t shortHash;
        memcpy(&shortHash, &raw[0], sizeof(shortHash));
        return shortHash;
    }

    // Include LHS, Include RHS
    static MYHash getRangeStartForBucket(uint16_t bucket);
    static MYHash getRangeEndForBucket(uint16_t bucket);

    bool operator==(const MYHash &otherHash) const;
    bool operator!=(const MYHash &otherHash) const;
    bool operator<(const MYHash &otherHash) const;
    bool operator>(const MYHash &otherHash) const;

    MYHashStorage raw;

    MYHash onesCompliment() const;
    static const MYHash &getCloudHash();
    static const MYHash &emptyHash();

    void alignedPartialResetsetbit();
    void unalignedresetsetbit();

    MYLIO_INLINE void resetsetbit()
    {
#ifdef MYLIO_64BIT
        if ((((size_t)(uint8_t *)this) & 0x7) == 0)
        {
            auto part = (uint64_t *)&raw[0];
            if (*part != 0)
            {
                isSet = true;
                return;
            }

            alignedPartialResetsetbit();
        }
#else
        if ((((size_t)(uint8_t *)this) & 0x3) == 0)
        {
            uint32_t *part = (uint32_t *)&raw[0];
            if (*part != 0)
            {
                isSet = true;
                return;
            }

            alignedPartialResetsetbit();
        }
#endif
        else
        {
            unalignedresetsetbit();
        }
    }

    static unsigned char hex2byte(const char *hex);

    template <typename T>
    static MYHash podToHash(T other)
    {
        static_assert(sizeof(T) < MYHASH_SIZE, "T is too long to fit in hash");
        static_assert(std::is_pod<T>::value, "T is not a pod");

        const int hashOffset = MYHASH_SIZE - sizeof(T);

        MYHash hash;
        memcpy(&hash.raw[hashOffset], &other, sizeof(T));
        hash.resetsetbit();

        return hash;
    }

    //  v---- Deprecated Methods ----v
    //  Should be removed in the future since they are not used:
    static MYHash computedFromFile_Deprecated(const MYFSPath &filePath, Platform::MYFileStreamBase *stream, MYImageDecoderPtr &imageDecoder);

    bool computeFromFile_Deprecated(const MYFSPath &filePath, Platform::MYFileStreamBase *stream, MYImageDecoderPtr &imageDecoder);
    //  ^---- Deprecated Methods ----^

#ifdef MYLIO_CLIENT
protected:
    /**
     *  For true Code Warriors! This will attempt to return the "fast hash" for
     *  legacy RAW files. If that fails it will return 'existingHash'.
     *
     *  Note that this is a special case that should only be used carefully when needed.
     *  That's why it's "protected". If you need to use this method then add yourself as
     *  a "friend" above.
     *
     *  @param url              Path to a potential legacy RAW file.
     *  @param existingHash     The hash that you, the caller, have already calculated for the file.
     *  @param nominalExtension (Optional) The true extension for the file pointed to by 'url'.
     *                          If this is empty it will use the extension from 'url'.
     *
     *  @return The "fast hash" if it was available or 'existingHash' if it was not.
     */
    static MYHash fastHashFromLegacyRawOrExistingHash(const MYFSPath &url, const MYHash &existingHash,
                                                      const std::string &nominalExtension = std::string());

private:
    /**
     *  Internal method for computing the "fast hash" for a legacy RAW file.
     *
     *  @param url              Path to a potential legacy RAW file.
     *  @param nominalExtension (Optional) The true extension for the file pointed to by 'url'.
     *                          If this is empty it will use the extension from 'url'.
     *  @param imageDecoder     Pointer to an existing or NULL image decoder. If non-NULL
     *                          it will be used to get the hash without decoding the file a 2nd
     *                          time (if possible). If NULL, a new decoder will be created and be
     *                          available for use by the caller on return.
     *
     *  @return true if the "fast hash" was calculated, otherwise false.
     */
    bool computeFastHashFromLegacyRaw_internal(const MYFSPath &url,
                                               const std::string &nominalExtension,
                                               MYImageDecoderPtr &imageDecoder);

    /**
     *  Internal method for computing the hash.
     *
     *  @param url              Path for the file to be hashed.
     *  @param nominalExtension (Optional) The true extension for the file pointed to by 'url'.
     *                          If this is empty it will use the extension from 'url'.
     *  @param stream           An open file stream to use to compute the file hash.
     *  @param imageDecoder     Pointer to an existing or NULL image decoder. If non-NULL
     *                          it will be used to get the hash without decoding the file a 2nd
     *                          time (if possible). If NULL, a new decoder will be created and be
     *                          available for use by the caller on return.
     *
     *  @return true if the hash was successfully calculated, false on error.
     */
    bool computeFromFile_internal(const MYFSPath &url, const std::string &nominalExtension,
                                  Platform::MYFileStreamBase *stream, MYImageDecoderPtr &imageDecoder, bool *quit);

#endif //  MYLIO_CLIENT

private:
    bool isSet;
    static unsigned char char2nibble(char hex);
};

namespace std
{
    template <>
    struct hash<MYHash>
    {
        std::size_t operator()(const MYHash &h) const
        {
            return *(std::size_t *)(&h.raw);
        }
    };

    template <>
    struct hash<MYHashStorage>
    {
        std::size_t operator()(const MYHashStorage &h) const
        {
            return *(std::size_t *)(&h);
        }
    };
}

extern MYHash g_cloudHash;
extern MYHash g_emptyHash;
