#!/bin/bash

source ./_.postgres.sh

echo "starting $0"

Black="\033[;30m"
Red="\033[;31m"
Green="\033[;32m"
Orange="\033[;33m"
Blue="\033[;34m"
Purple="\033[;35m"
Cyan="\033[;36m"
Light_Gray="\033[;37m"
Dark_Gray="\033[;30m"
Light_Red="\033[;31m"
Light_Green="\033[;32m"
Yellow="\033[;33m"
Light_Blue="\033[;34m"
Light_Purple="\033[;35m"
Light_Cyan="\033[;36m"
White="\033[;37m"
None="\033[0m"

PYTHON_COMMAND=$(which python)
PYTHON_VERSION=$(python --version 2>&1)
POSTGRES_VERSION=$(postgres --version 2>&1)
PSQL_VERSION=$(psql --version 2>&1)
HOST=localhost
PORT=5432
USER=postgres

echo "starting script ..."
echo ""

echo -e "${Cyan}checking python installation ...${None}"
if ! [[ $PYTHON_COMMAND ]]
then
    echo ""
    echo -e "${Light_Red}no python installation found.${None}"
    echo -e "${Light_Red}you must install python 2.7+: https://www.python.org/downloads/${None}"
    exit 1
fi

if ! [[ $PYTHON_VERSION =~ "Python 2" ]]
then
    echo ""
    echo -e "${Light_Red}no python 2 installation found${None}"
    echo -e "${Light_Red}you must install python 2.7+: https://www.python.org/downloads/${None}"
    exit 1
fi

echo "found python: ${PYTHON_VERSION}"
echo -e "${Cyan}checking postgres installation ...${None}"
export PGPASSWORD=password

if ! [[ $POSTGRES_VERSION ]] || ! [[ $PSQL_VERSION ]]
then
    echo ""
    echo -e "${Light_Red}no postgres installation found: https://www.postgresql.org/download/${None}"
fi

psql -q -h $HOST -p $PORT -U $USER -d postgres -v "ON_ERROR_STOP=1" -c "select 'connection succeeded' as status" > /dev/null 2>&1

if [ $? -ne 0 ]
then
    echo ""
    echo -e "${Light_Red}could not connect to a running postgres database.${None}"
    echo -e "${Light_Red}is postgres running?${None}"
fi

echo "found postgres: ${POSTGRES_VERSION}"
echo "found psql: ${PSQL_VERSION}"
echo -e "${Cyan}installing dependencies (may take a while) ...${None}"

npm install > /dev/null 2>&1

if [ $? -ne 0 ]
then
    echo -e "${Light_Red}there was a problem installing dependencies${None}"
    exit 1
fi

echo -e "${Cyan}building project files (may take a while) ...${None}"
./build.slow.sh > /dev/null 2>&1

if [ $? -ne 0 ]
then
    echo -e "${Light_Red}there was a problem building project files${None}"
    exit 1
fi

echo -e "${Cyan}deploying databases (may take a while) ...${None}"

./deploy.account.database.localhost.sh > /dev/null 2>&1

if [ $? -ne 0 ]
then
    echo -e "${Light_Red}there was a problem deploying the account database${None}"
    exit 1
fi

./deploy.telemetry.database.localhost.sh > /dev/null 2>&1

if [ $? -ne 0 ]
then
    echo -e "${Light_Red}there was a problem deploying the telemetry database${None}"
    exit 1
fi

./deploy.resource.database.localhost.sh > /dev/null 2>&1

if [ $? -ne 0 ]
then
    echo -e "${Light_Red}there was a problem deploying the resource database${None}"
    exit 1
fi

./deploy.datawarehouse.database.localhost.sh > /dev/null 2>&1

if [ $? -ne 0 ]
then
    echo -e "${Light_Red}there was a problem deploying the datawarehouse database${None}"
    exit 1
fi

echo ""
echo -e "${Green}success!${None}"
echo ""
echo "to start the cloud services locally, run the following commands in separate terminals:"
echo ""

echo -e "${Yellow}$ npm run start:account:localhost${None}"
echo -e "${Yellow}$ npm run start:telemetry:localhost${None}"
echo -e "${Yellow}$ npm run start:resource:localhost${None}"
echo ""

echo "to run the cloud tests run:"
echo ""

echo -e "${Yellow}$ npm test${None}"
echo ""

echo "completed $0"
