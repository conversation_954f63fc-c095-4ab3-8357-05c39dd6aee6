#include "MYSmallLocalFilesHashImplementation.h"
#include "MYLiterals.h"

const MYSmallHashRef MYSmallHashRefEmpty = std::numeric_limits<uint32_t>::max();

MYSmallBucketLocalFiles g_emptyBucketLocalFiles(0);
// MYSmallLocalFiles g_emptyMYLocalFiles;

MYSmallLocalFile g_emptyFiles[] =
    {
        MYSmallLocalFile(&g_emptyBucketLocalFiles, MYMediaFileType::NoType),
        MYSmallLocalFile(&g_emptyBucketLocalFiles, MYMediaFileType::RAW),
        MYSmallLocalFile(&g_emptyBucketLocalFiles, MYMediaFileType::NonRAW),
        MYSmallLocalFile(&g_emptyBucketLocalFiles, MYMediaFileType::Video),
        MYSmallLocalFile(&g_emptyBucketLocalFiles, MYMediaFileType::DisplayImage),
        MYSmallLocalFile(&g_emptyBucketLocalFiles, MYMediaFileType::XMP),
        MYSmallLocalFile(&g_emptyBucketLocalFiles, MYMediaFileType::Preview),
        MYSmallLocalFile(&g_emptyBucketLocalFiles, MYMediaFileType::Thumbnail)};

////////////////////////////////////////////////////////////////////
//
// MYSmallLocalFile
//
////////////////////////////////////////////////////////////////////
MYSmallLocalFile::MYSmallLocalFile(MYSmallLocalFile &&other) : _extensionData(std::move(other._extensionData))
{
    _myLocalFiles = other._myLocalFiles;
    _dirty = other._dirty;
    _mediaType = other._mediaType;
    if (_dirty)
    {
        return;
    }

    _format = other._format;

    _cropZoomFactor = other._cropZoomFactor;
    _dataHash = other._dataHash;
    _visualEditHash = other._visualEditHash;

    _basisDataHash = other._basisDataHash;
    _fileNameNoExt = other._fileNameNoExt;
    _alURL = other._alURL;
    _parseHash = other._parseHash;
    _flags = other._flags;

    _modifiedFields = other._modifiedFields;
}

MYSmallLocalFile::MYSmallLocalFile(const MYSmallLocalFile &other)
{
    _myLocalFiles = other._myLocalFiles;
    _dirty = other._dirty;
    _mediaType = other._mediaType;
    if (_dirty)
    {
        return;
    }

    _format = other._format;

    _cropZoomFactor = other._cropZoomFactor;
    _dataHash = other._dataHash;
    _visualEditHash = other._visualEditHash;

    _basisDataHash = other._basisDataHash;
    _fileNameNoExt = other._fileNameNoExt;
    _alURL = other._alURL;
    _parseHash = other._parseHash;
    _flags = other._flags;

    _modifiedFields = other._modifiedFields;

    if (other._extensionData)
    {
        _extensionData = std::make_unique<MYBJsonSmallRW>(other._extensionData->pbegin(), other._extensionData->pend());
    }
}

MYSmallLocalFile &MYSmallLocalFile::operator=(const MYSmallLocalFile &other)
{
    _myLocalFiles = other._myLocalFiles;
    _dirty = other._dirty;
    _mediaType = other._mediaType;
    if (_dirty)
    {
        return *this;
    }

    _format = other._format;

    _cropZoomFactor = other._cropZoomFactor;
    _dataHash = other._dataHash;
    _visualEditHash = other._visualEditHash;

    _basisDataHash = other._basisDataHash;
    _fileNameNoExt = other._fileNameNoExt;
    _alURL = other._alURL;
    _parseHash = other._parseHash;
    _flags = other._flags;

    _modifiedFields = other._modifiedFields;

    if (other._extensionData)
    {
        _extensionData = std::make_unique<MYBJsonSmallRW>(other._extensionData->pbegin(), other._extensionData->pend());
    }

    return *this;
}

MYSmallLocalFile &MYSmallLocalFile::operator=(MYSmallLocalFile &&other)
{
    _myLocalFiles = other._myLocalFiles;
    _dirty = other._dirty;
    _mediaType = other._mediaType;
    if (_dirty)
    {
        return *this;
    }

    _format = other._format;

    _cropZoomFactor = other._cropZoomFactor;
    _dataHash = other._dataHash;
    _visualEditHash = other._visualEditHash;

    _basisDataHash = other._basisDataHash;
    _fileNameNoExt = other._fileNameNoExt;
    _alURL = other._alURL;
    _parseHash = other._parseHash;
    _flags = other._flags;

    _modifiedFields = other._modifiedFields;

    _extensionData = std::move(other._extensionData);
    return *this;
}

void MYSmallLocalFile::clear()
{
    _dirty = true;
}

void MYSmallLocalFile::prepareInternal()
{
    _dirty = false;
    _format = MYStringRefEmpty;

    _cropZoomFactor = 1.0f;
    _dataHash = MYSmallHashRefEmpty;
    _visualEditHash = MYSmallHashRefEmpty;

    _basisDataHash = MYSmallHashRefEmpty;
    _fileNameNoExt = MYStringRefEmpty;
    _alURL = MYStringRefEmpty;
    _parseHash = MYSmallHashRefEmpty;
    _flags = 0;

    _modifiedFields.reset();

    _extensionData.reset();
}

MYSmallLocalFile::MYSmallLocalFile(MYSmallBucketLocalFiles *MYSmallLocalFiles, MYMediaFileType::Enum mediaType, MYBJsonIterator &iter, const MYBJsonIterator &end) : _myLocalFiles(MYSmallLocalFiles)
{
    prepare();
    _mediaType = mediaType;
    deserializeFromBJson(iter, end);
}

void MYSmallLocalFile::deserializeFromBJson(MYBJsonIterator &iter, const MYBJsonIterator &end)
{
    for (; iter != end && !iter->isSeparatorOrEnd(); ++iter)
    {
        auto key = iter->key();
        switch (key)
        {
            // case MYLiterals::LocalFile::fileType:
            //     _mediaType = (MYMediaFileType::Enum)iter->asUint32();
            //     break;

        case MYLiterals::LocalFile::format:
            _format = iter->asUint32();
            break;

        case MYLiterals::LocalFile::dataHash:
            _dataHash = iter->HashRefRead();
            break;

        case MYLiterals::LocalFile::visualEditHash:
            _visualEditHash = iter->HashRefRead();
            break;

        case MYLiterals::LocalFile::basisDataHash:
            _basisDataHash = iter->HashRefRead();
            break;

        case MYLiterals::LocalFile::flags:
            _flags = (iter->asUint32() + 1);
            break;

        case MYLiterals::LocalFile::cropZoomFactor:
            _cropZoomFactor = iter->asFloat();
            break;

        case MYLiterals::LocalFile::alUrl:
            _alURL = iter->asUint32();
            break;

        case MYLiterals::LocalFile::fileNameNoExt:
            _fileNameNoExt = iter->asUint32();
            break;

        case MYLiterals::LocalFile::parseHash:
            _parseHash = iter->HashRefRead();
            break;

        default:
            if (!_extensionData)
            {
                _extensionData = std::make_unique<MYBJsonSmallRW>();
            }
            _extensionData->addMember(iter);
            break;
        }
    }

    assert(iter->isSeparatorOrEnd());
}

const std::string &MYSmallLocalFile::getRenamedFileNameNoExt() const
{
    return _myLocalFiles->getString(_fileNameNoExt);
}

bool MYSmallLocalFile::setRenamedFileNameNoExt(const std::string &newFileNameNoExt)
{
    auto ref = _myLocalFiles->getOrCreateStringRef(newFileNameNoExt);
    if (ref == _fileNameNoExt)
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::fileNameNoExt] = true;
    _fileNameNoExt = ref;
    return true;
}

bool MYSmallLocalFile::setRenamedFileNameNoExt(std::string &&newFileNameNoExt)
{
    auto ref = _myLocalFiles->getOrCreateStringRef(newFileNameNoExt);
    if (ref == _fileNameNoExt)
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::fileNameNoExt] = true;
    _fileNameNoExt = ref;
    return true;
}

const std::string &MYSmallLocalFile::getALURL() const
{
    return _myLocalFiles->getString(_alURL);
}

bool MYSmallLocalFile::setALURL(const std::string &newALUrl)
{
    auto ref = _myLocalFiles->getOrCreateStringRef(newALUrl);
    if (ref == _alURL)
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::alUrl] = true;
    _alURL = ref;
    return true;
}

bool MYSmallLocalFile::setALURL(std::string &&newALUrl)
{
    auto ref = _myLocalFiles->getOrCreateStringRef(newALUrl);
    if (ref == _alURL)
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::alUrl] = true;
    _alURL = ref;
    return true;
}

// const MYHash& MYSmallLocalFile::getDataHash() const
//{
//     return _myLocalFiles->getHash(_dataHash);
// }

bool MYSmallLocalFile::setDataHash(const MYHash &newDataHash)
{
    auto ref = _myLocalFiles->getOrCreateHashRef(newDataHash);
    if (ref == _dataHash)
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::dataHash] = true;
    _dataHash = ref;
    return true;
}

// const MYHash& MYSmallLocalFile::getVisualEditHash() const
//{
//     return _myLocalFiles->getHash(_visualEditHash);
// }

bool MYSmallLocalFile::setVisualEditHash(const MYHash &newVisualEditHash)
{
    auto ref = _myLocalFiles->getOrCreateHashRef(newVisualEditHash);
    if (ref == _visualEditHash)
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::visualEditHash] = true;
    _visualEditHash = ref;
    return true;
}

// const MYHash& MYSmallLocalFile::getBasisDataHash() const
//{
//     return _myLocalFiles->getHash(_basisDataHash);
// }

bool MYSmallLocalFile::setBasisDataHash(const MYHash &newBasisDataHash)
{
    auto ref = _myLocalFiles->getOrCreateHashRef(newBasisDataHash);
    if (ref == _basisDataHash)
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::basisDataHash] = true;
    _basisDataHash = ref;
    return true;
}

bool MYSmallLocalFile::setCropZoomFactor(float newCropZoomFactor)
{
    if (newCropZoomFactor == _cropZoomFactor)
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::cropZoomFactor] = true;
    _cropZoomFactor = newCropZoomFactor;
    return true;
}

// const MYHash& MYSmallLocalFile::getParseHash() const
//{
//     return _myLocalFiles->getHash(_parseHash);
// }

bool MYSmallLocalFile::setParseHash(const MYHash &newParseHash)
{
    auto ref = _myLocalFiles->getOrCreateHashRef(newParseHash);
    if (ref == _parseHash)
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::parseHash] = true;
    _parseHash = ref;
    return true;
}

bool MYSmallLocalFile::setParsability(MYParsability newParsability)
{
    if (newParsability == getParsability())
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::flags] = true;
    if (newParsability == MYParsability::NotAttempted)
    {
        _flags.reset((int)Flags::parseAttempted);
        _flags.reset((int)Flags::parseFailed);
    }
    else if (newParsability == MYParsability::NotParsable)
    {
        _flags.set((int)Flags::parseAttempted);
        _flags.set((int)Flags::parseFailed);
    }
    else if (newParsability == MYParsability::Parsable)
    {
        _flags.set((int)Flags::parseAttempted);
        _flags.reset((int)Flags::parseFailed);
    }
    else
    {
        assert(false);
        _flags.reset((int)Flags::parseAttempted);
        _flags.reset((int)Flags::parseFailed);
    }

    return true;
}

bool MYSmallLocalFile::setIsDraft(bool newIsDraft)
{
    if (newIsDraft == getIsDraft())
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::flags] = true;
    if (newIsDraft)
        _flags.set((int)Flags::isDraft);
    else
        _flags.reset((int)Flags::isDraft);

    return true;
}

bool MYSmallLocalFile::setIsHardWant(bool newHardWant)
{
    if (newHardWant == getIsHardWant())
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::flags] = true;
    if (newHardWant)
        _flags.set((int)Flags::hardWant);
    else
        _flags.reset((int)Flags::hardWant);

    return true;
}

bool MYSmallLocalFile::setInInternalDataStorage(bool newInInternalDataStorage)
{
    if (newInInternalDataStorage == getInInternalDataStorage())
    {
        return false;
    }

    _modifiedFields[MYLiterals::LocalFile::flags] = true;
    if (newInInternalDataStorage)
        _flags.set((int)Flags::inInternalData);
    else
        _flags.reset((int)Flags::inInternalData);

    return true;
}

const std::string &MYSmallLocalFile::getFormat() const
{
    if ((getMediaType() == MYMediaFileType::Thumbnail || getMediaType() == MYMediaFileType::Preview))
    {
        if (_format == MYStringRefEmpty)
        {
            return g_jpgFormat;
        }
    }

    return _myLocalFiles->getString(_format);
}

bool MYSmallLocalFile::setFormat(const std::string &newFormat)
{
    if ((getMediaType() == MYMediaFileType::Thumbnail || getMediaType() == MYMediaFileType::Preview))
    {
        if (newFormat == jpgFormat)
        {
            if (_format != MYStringRefEmpty)
            {
                _format = MYStringRefEmpty;
                return true;
            }

            return false;
        }
    }

    auto ref = _myLocalFiles->getOrCreateStringRef(newFormat);
    if (ref == _format)
    {
        return false;
    }

    _modifiedFields[MYLiterals::File::format] = true;
    _format = ref;
    return true;
}

bool MYSmallLocalFile::setFormat(std::string &&newFormat)
{
    auto ref = _myLocalFiles->getOrCreateStringRef(std::move(newFormat));
    if (ref == _format)
    {
        return false;
    }

    _modifiedFields[MYLiterals::File::format] = true;
    _format = ref;
    return true;
}

////////////////////////////////////////////////////////////////////
//
// MYSmallLocalFiles
//
//////////////////////////////////////////////////////////////////
const MYSmallLocalFile &MYSmallLocalFiles::getMediaFileOrEmpty(MYMediaFileType::Enum mediaFileType) const
{
    assert(mediaFileType != MYMediaFileType::NoType);
    if (_knownMediaFiles[(int)mediaFileType - 1]._dirty)
    {
        return g_emptyFiles[(int)mediaFileType + 1];
    }

    return _knownMediaFiles[(int)mediaFileType - 1];
}

MYSmallLocalFile *MYSmallLocalFiles::getMediaFileOrNull(MYMediaFileType::Enum mediaFileType)
{
    assert(mediaFileType != MYMediaFileType::NoType);
    if (_knownMediaFiles[(int)mediaFileType - 1]._dirty)
    {
        return nullptr;
    }

    return &_knownMediaFiles[(int)mediaFileType - 1];
}

const MYSmallLocalFile *MYSmallLocalFiles::getMediaFileOrNull(MYMediaFileType::Enum mediaFileType) const
{
    assert(mediaFileType != MYMediaFileType::NoType);
    if (_knownMediaFiles[(int)mediaFileType - 1]._dirty)
    {
        return nullptr;
    }

    return &_knownMediaFiles[(int)mediaFileType - 1];
}

MYSmallLocalFile *MYSmallLocalFiles::getOrCreateMediaFile(MYMediaFileType::Enum mediaFileType)
{
    assert(mediaFileType != MYMediaFileType::NoType);
    _knownMediaFiles[(int)mediaFileType - 1].prepare();
    return &_knownMediaFiles[(int)mediaFileType - 1];
}

const NeedsBits MYSmallLocalFiles::getSupportMediaTypes() const
{
    NeedsBits supported(0);

    for (size_t i = 0; i < _knownMediaFiles.size(); i++)
    {
        if (!_knownMediaFiles[i].empty())
        {
            supported |= NeedsBits((uint8_t)i + 1);
        }
    }
    return supported;
}

MYSmallHashRef MYSmallBucketLocalFiles::getOrCreateHashRef(const MYHash &hash)
{
    if (hash.empty())
    {
        return MYSmallHashRefEmpty;
    }

    uint32_t hashEntry = *(uint32_t *)(&hash.raw[0]);
#ifdef WITH_FIXED_32
    return hashEntry;
#endif

    auto iter = std::find(_hashMap.begin(), _hashMap.end(), hashEntry);
    if (iter == _hashMap.end())
    {
        _modifiedFields[MYLiterals::Files::hashMap] = true;
        _hashMap.emplace_back(hashEntry);
        return (MYSmallHashRef)_hashMap.size() - 1;
    }

    return (MYSmallHashRef)(iter - _hashMap.begin());
}

MYStringRef MYSmallBucketLocalFiles::getOrCreateStringRef(const std::string &string)
{
    if (string.empty())
    {
        return MYStringRefEmpty;
    }

    auto iter = std::find(_stringMap.begin(), _stringMap.end(), string);
    if (iter == _stringMap.end())
    {
        _modifiedFields[MYLiterals::Files::stringMap] = true;
        _stringMap.emplace_back(string);
        return (MYStringRef)_stringMap.size() - 1;
    }

    return (MYStringRef)(iter - _stringMap.begin());
}

MYStringRef MYSmallBucketLocalFiles::getOrCreateStringRef(std::string &&string)
{
    if (string.empty())
    {
        return MYStringRefEmpty;
    }

    auto iter = std::find(_stringMap.begin(), _stringMap.end(), string);
    if (iter == _stringMap.end())
    {
        _modifiedFields[MYLiterals::Files::stringMap] = true;
        _stringMap.emplace_back(std::move(string));
        return (MYStringRef)_stringMap.size() - 1;
    }

    return (MYStringRef)(iter - _stringMap.begin());
}

// const MYHash& MYSmallBucketLocalFiles::getHash(MYSmallHashRef hashref) const
//{
//     return MYHash::emptyHash();
//     //if (hashref == MYSmallHashRefEmpty)
//     //{
//     //    return MYHash::emptyHash();
//     //}
//
//     //assert(hashref < (MYSmallHashRef)_hashMap.size());
//     //return _hashMap[hashref];
// }

const std::string &MYSmallBucketLocalFiles::getString(MYStringRef stringRef) const
{
    if (stringRef == MYStringRefEmpty)
    {
        return g_emptyString;
    }

    assert(stringRef < (MYStringRef)_stringMap.size());
    return _stringMap[stringRef];
}

MYSmallLocalFiles::MYSmallLocalFiles(MYSmallBucketLocalFiles *bucketLocal, MYBJsonIterator &iter, const MYBJsonIterator &end, bool reInit)
{
    deserializeFromBJson(iter, end);
}

MYSmallLocalFiles::MYSmallLocalFiles(MYSmallBucketLocalFiles *bucketLocal, MYBJsonIterator &iter, const MYBJsonIterator &end) : _bucketLocal(bucketLocal)
{
#if !defined(_DEBUG) && !defined(DEBUG)
    // We don't reserve in debug, because we want to take re-allocations. Otherwise it will hide bugs if
    // the pointer values always remain the same.
    //_hashMap.reserve(10);
#endif

    initLocalFileArray();
    deserializeFromBJson(iter, end);
}

void MYSmallLocalFiles::initLocalFileArray()
{
    for (int i = 0; i < 7; i++)
    {
        _knownMediaFiles[i].init(_bucketLocal, (MYMediaFileType::Enum)(i + 1));
    }
}

void MYSmallLocalFiles::clear()
{
    for (int i = 0; i < 7; i++)
    {
        _knownMediaFiles[i].clear();
    }

    _extendedMediaFiles.clear();
    //_hashMap.clear();
    //_stringMap.clear();
    //_orientation = 0;
    //_visualEditHash = MYSmallHashRefEmpty;
    //_extensionData.reset();
}

void MYSmallLocalFiles::deserializeFromBJson(MYBJsonIterator &iter, const MYBJsonIterator &end)
{
#if !defined(_DEBUG) && !defined(DEBUG)
    // We don't reserve in debug, because we want to take re-allocations. Otherwise it will hide bugs if
    // the pointer values always remain the same.
    //_hashMap.reserve(7);
    //_mediaFiles.reserve(7);
    //_stringMap.reserve(0);
#endif

    auto mediaType = (MYMediaFileType::Enum)iter->key();
    //    assert(iter->key() == MYLiterals::Files::files);
    assert(iter->isScopeBegin());

    //++iter;
    // assert(iter->type() == BJsonType::separator);

    for (++iter; iter != end && !iter->isScopeEnd(); iter->isScopeEnd() ? iter : ++iter)
    {
        if (mediaType == MYMediaFileType::NoType)
        {
            _extendedMediaFiles.emplace_back(_bucketLocal, mediaType, iter, end);
        }
        else
        {
            _knownMediaFiles[(int)mediaType].deserializeFromBJson(iter, end);
            // new (&_knownMediaFiles[(int)mediaType]) MYSmallLocalFile(_bucketLocal, mediaType, iter, end);
        }

        assert(iter->isSeparatorOrEnd());
    }

    // We can't chew up the last iter inside the loop, otherwise we'll miss separators
    ++iter;
    assert(iter->isSeparatorOrEnd());
}

bool MYSmallLocalFile::isModified() const
{
    return _modifiedFields.any();
}

bool MYSmallLocalFiles::isModified() const
{
    if (_modifiedFields.any())
    {
        return true;
    }

    for (const auto &file : _extendedMediaFiles)
    {
        if (file.isModified())
        {
            return true;
        }
    }

    for (const auto &file : _knownMediaFiles)
    {
        if (file.isModified())
        {
            return true;
        }
    }

    return false;
}

bool MYSmallLocalFiles::empty() const
{
    for (const auto &file : _knownMediaFiles)
    {
        if (!file.empty())
        {
            return false;
        }
    }

    return _extendedMediaFiles.empty();
}

MYSmallLocalFiles::MYSmallLocalFiles(MYSmallLocalFiles &&other)
{
    _knownMediaFiles = other._knownMediaFiles;
    _extendedMediaFiles.insert(_extendedMediaFiles.end(), other._extendedMediaFiles.begin(), other._extendedMediaFiles.end());

    _modifiedFields = other._modifiedFields;
    _bucketLocal = other._bucketLocal;

    for (auto &mediaFile : _knownMediaFiles)
    {
        assert(mediaFile._myLocalFiles == _bucketLocal);
    }
}

MYSmallLocalFiles::MYSmallLocalFiles(const MYSmallLocalFiles &other)
{
    _knownMediaFiles = other._knownMediaFiles;
    _extendedMediaFiles.insert(_extendedMediaFiles.end(), other._extendedMediaFiles.begin(), other._extendedMediaFiles.end());

    _modifiedFields = other._modifiedFields;
    _bucketLocal = other._bucketLocal;

    for (auto &mediaFile : _knownMediaFiles)
    {
        assert(mediaFile._myLocalFiles == _bucketLocal);
    }
}

MYSmallLocalFiles &MYSmallLocalFiles::operator=(MYSmallLocalFiles &&other)
{
    _modifiedFields = other._modifiedFields;

    _knownMediaFiles = other._knownMediaFiles;
    _extendedMediaFiles.insert(_extendedMediaFiles.end(), other._extendedMediaFiles.begin(), other._extendedMediaFiles.end());

    for (auto &mediaFile : _knownMediaFiles)
    {
        assert(mediaFile._myLocalFiles == _bucketLocal);
    }

    return *this;
}

MYSmallLocalFiles &MYSmallLocalFiles::operator=(const MYSmallLocalFiles &other)
{
    _modifiedFields = other._modifiedFields;

    _knownMediaFiles = other._knownMediaFiles;
    _extendedMediaFiles.insert(_extendedMediaFiles.end(), other._extendedMediaFiles.begin(), other._extendedMediaFiles.end());

    for (auto &mediaFile : _knownMediaFiles)
    {
        assert(mediaFile._myLocalFiles == _bucketLocal);
    }

    return *this;
}

bool MYSmallLocalFiles::operator==(const MYSmallLocalFiles &other) const
{
    MYBJsonRW one;
    one.StartObject();

    MYBJsonRW two;
    two.StartObject();

    serializeToBJson(one);
    other.serializeToBJson(two);

    if (one.psize() != two.psize())
        return false;

    return memcmp(one.pbegin(), two.pbegin(), one.psize()) == 0;
}

////////////////////////////////////////////////////////////////////
//
// MYSmallBucketLocalFiles
//
////////////////////////////////////////////////////////////////////

MYSmallBucketLocalFiles::MYSmallBucketLocalFiles(size_t estimatedMediaPerBucket, MYBJsonIterator &begin, const MYBJsonIterator &end) : _estimatedMediaPerBucket(estimatedMediaPerBucket)
{
#if !defined(_DEBUG) && !defined(DEBUG)
    // We don't reserve in debug, because we want to take re-allocations. Otherwise it will hide bugs if
    // the pointer values always remain the same.
    _mediaFiles.reserve(estimatedMediaPerBucket);
#endif

    deserializeFromBJson(begin, end);
}

void MYSmallBucketLocalFiles::clear()
{
    _dirty = true;
    //_mediaFiles.clear();
    //_mediaFiles.reserve(_estimatedMediaPerBucket);

    _hashMap.clear();
    _stringMap.clear();
    _extensionData.reset();
}

void MYSmallBucketLocalFiles::deserializeFromBJson(MYBJsonIterator &iter, const MYBJsonIterator &end)
{
    MYMediaMapType::iterator dirtyIter;
    MYMediaMapType::iterator dirtyEnd = _mediaFiles.end();

    if (_dirty)
    {
        dirtyIter = _mediaFiles.begin();
    }
    else
    {
        dirtyIter = _mediaFiles.end();
    }

    for (; iter != end && iter->type() != BJsonType::end; ++iter)
    {
        auto key = iter->key();
        switch (key)
        {
        case MYLiterals::Files::files:
        {
            assert(iter->type() == BJsonType::object);
            for (++iter; iter != end && !iter->isSeparatorOrEnd(); iter->isScopeEnd() ? iter : ++iter)
            {
                assert(iter->type() == BJsonType::hash);
                MYHash media = iter->asHash();
                ++iter;

                if (dirtyIter != dirtyEnd)
                {
                    dirtyIter->first = media;
                    // new (&dirtyIter->second) MYSmallLocalFiles(this, iter, end);
                    dirtyIter->second.deserializeFromBJson(iter, end);
                    ++dirtyIter;
                }
                else
                {
                    _dirty = false;
                    _mediaFiles.emplace_from_sorted(media, this, iter, end);
                }

                assert(iter->isSeparatorOrEnd());
            }
            assert(iter->type() == BJsonType::end);
        }
        break;

        case MYLiterals::Files::hashMap:
        {
            assert(iter->type() == BJsonType::binary);

            auto vect = iter->binary();
            assert(vect.second % sizeof(uint32_t) == 0);

            uint32_t *begin = (uint32_t *)vect.first;
            uint32_t *end = begin + (vect.second / sizeof(uint32_t));

            _hashMap.insert(_hashMap.end(), begin, end);
        }
        break;

        case MYLiterals::Files::stringMap:
        {
            assert(iter->type() == BJsonType::array);
            for (++iter; iter != end && iter->type() != BJsonType::end; ++iter)
            {
                _stringMap.emplace_back(iter->asString());
            }
            assert(iter->type() == BJsonType::end);
        }
        break;

        default:
            if (!_extensionData)
            {
                _extensionData = std::make_unique<MYBJsonSmallRW>();
            }
            _extensionData->addMember(iter);
            break;
        }
    }

    if (_dirty)
    {
        size_t inserted = dirtyIter - _mediaFiles.begin();
        assert(inserted <= _mediaFiles.size());
        _mediaFiles.resize(inserted);
    }
}

MYSmallLocalFiles *MYSmallBucketLocalFiles::getOrCreateLocalFiles(const MYHash &media)
{
    prepare();
    return &_mediaFiles.emplace(media, this).first->second;
}
