CREATE OR REPLACE FUNCTION a0.show_upgrade_warnings(account_id_ int, device_id_ int, protocol_version_ int)
RETURNS void
AS
$$
DECLARE
    device_rec RECORD;
    message_ text := '';
    message_id_ int;
    this_device_ text := '';
BEGIN
    -- Get the current device name or nickname.
    SELECT COALESCE(name, nickname)
    INTO this_device_
    FROM a0.device d
    WHERE d.account_id = account_id_
      AND d.device_id = device_id_;
    
    FOR device_rec IN
        SELECT COALESCE(name, nickname) AS name, dd.protocol_version
        FROM a0.device d
        JOIN a0.device_data dd ON dd.account_id = d.account_id AND dd.device_id = d.device_id
        WHERE d.account_id = account_id_
          AND d.deleted IS FALSE
          AND d.device_id > 0
    LOOP
        IF device_rec.protocol_version < protocol_version_ THEN
            message_ := device_rec.name || ' is running an earlier version of <PERSON><PERSON> that is incompatible with ' || this_device_ || '. Syncing will be disabled until ' || device_rec.name || ' is updated to the latest version of <PERSON><PERSON>';
        ELSIF device_rec.protocol_version > protocol_version_ THEN
            message_ := device_rec.name || ' is running a later version of Mylio that is incompatible with ' || this_device_ || '. Syncing will be disabled until ' || this_device_ || ' is updated to the latest version of Mylio';
        ELSE
            message_ := '';
        END IF;
        
        IF message_ <> '' THEN
            SELECT message_id
            INTO message_id_
            FROM a0.message 
            WHERE account_id = account_id_
              AND message = message_;
        
            IF message_id_ IS NOT NULL THEN
                UPDATE a0.message 
                SET displayed = 0, t = next_trev(t)
                WHERE message_id = message_id_;
            ELSE
                PERFORM a0.message_create(
                    NULL, 
                    NULL, 
                    NULL, 
                    account_id_, 
                    FALSE, 
                    NULL, 
                    NULL, 
                    device_id_, 
                    NULL, 
                    NULL, 
                    FALSE, 
                    message_, 
                    NULL
                );
            END IF;
            
            PERFORM a0.message_merkle(account_id_);
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;
	