
import { query } from "../system/Postgres.mjs";
import { UserProperty, IUserProperty} from "../models/UserProperty.model.mjs";
import { Context } from "../system/Context.mjs";
import { makeError } from "../system/error.mjs";


export class UserPropertyRestDataService {

    



  public query(context: Context, sql: string, params: any[]) {
    return query < IUserProperty> (context, sql, params)
      .then(results => {
        if (results.length === 1) {
          if (!results[0].accountId && !results[0].userPropertyId) {
      return [];
    }
  }
  return results.filter(o => !!o).map(o => new UserProperty(o));
});
    }

		public create (context: Context, entity: UserProperty) {
          


  let params = [
    entity.flags(),
				entity.modifiedTime(),
				entity.createdTime(),
				entity.accountId(),
				entity.deleted(),
				entity.t(),
				entity.d(),
				entity.userPropertyId(),
				entity.name(),
				entity.value()
  ];
  return this
    .query(context, "select * from a0.user_property_create ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ", params)
  .then(r => r[0]);
        }

		public readByAccountIdAndUserPropertyId (context: Context, accountId: number, userPropertyId: string) {
  let params = [
    accountId,
				userPropertyId
  ];
  return this
    .query(context, "select * from a0.user_property_read_by_account_id_and_user_property_id  ($1,$2) ", params).then(r => r[0]); 
                
        }

		public readByAccountId (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.user_property_read_by_account_id  ($1) ", params).then(r => r[0]); 
                
        }

		public update (context: Context, entity: UserProperty) {
          


  let params = [
    entity.flags(),
				entity.modifiedTime(),
				entity.createdTime(),
				entity.accountId(),
				entity.deleted(),
				entity.t(),
				entity.d(),
				entity.userPropertyId(),
				entity.name(),
				entity.value()
  ];
  return this
    .query(context, "select * from a0.user_property_update ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) ", params)
  .then(r => r[0]);
        }

		public deleteByAccountIdAndUserPropertyId (context: Context, accountId: number, userPropertyId: string) {
  let params = [
    accountId,
				userPropertyId
  ];
  return this
    .query(context, "select * from a0.user_property_delete_by_account_id_and_user_property_id  ($1,$2) ", params).then(r => r[0]); 
                
        }

		public deleteByAccountId (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.user_property_delete_by_account_id  ($1) ", params).then(r => r[0]); 
                
        }

		public findByAccountId (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.user_property_find_by_account_id  ($1) ", params); 
                
        }

		public findByName (context: Context, name: string) {
  let params = [
    name
  ];
  return this
    .query(context, "select * from a0.user_property_find_by_name  ($1) ", params); 
                
        }

		public merkle (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.user_property_merkle ($1) ", params).then(r => r[0]); 
                
        }

}
