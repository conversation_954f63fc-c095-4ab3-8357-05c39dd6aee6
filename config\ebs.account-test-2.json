{"cloud": "https://account-test-2.mylio.com", "connectionStrings": {"a0": "pg://<user>:<password>@test-2-rds.mylio.com:5432/account0?sslmode=disable", "x0": "pg://<user>:<password>@test-2-rds.mylio.com:5432/resource0?sslmode=disable", "x4": "pg://<user>:<password>@test-2-rds.mylio.com:5432/resource1?sslmode=disable", "x8": "pg://<user>:<password>@test-2-rds.mylio.com:5432/resource2?sslmode=disable", "xc": "pg://<user>:<password>@test-2-rds.mylio.com:5432/resource3?sslmode=disable"}, "services": [{"name": "resource", "protocolVersion": 23, "uri": ["https://resource-test-0.mylio.com"]}, {"name": "resource", "protocolVersion": 24, "uri": ["https://resource-test-2.mylio.com"]}, {"name": "account", "uri": ["https://account-test-2.mylio.com"]}, {"name": "reverse_geocoding", "uri": ["https://api.opencagedata.com/geocode/v1/json?q=%f,%f&pretty=0&no_annotations=1&key=74306074d3a5ef1f7c86ea43062367a6"]}, {"name": "geocoding", "uri": ["https://api.opencagedata.com/geocode/v1/json?q=%s&pretty=0&no_annotations=1&key=74306074d3a5ef1f7c86ea43062367a6"]}, {"name": "telemetry", "uri": ["https://telemetry-test-0.mylio.com"]}, {"name": "cloudSignalChannel", "uri": ["signal-test.mylio.com:443"], "protocolVersion": 24}, {"name": "cloudSignalChannel", "protocolVersion": 23, "uri": ["signal-test-23.mylio.com:443"]}]}