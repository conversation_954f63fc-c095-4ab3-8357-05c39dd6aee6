import { Context } from "../system/Context.mjs";
import { TelemetryDataService } from "../dataServices/TelemetryDataService.mjs";
import { EventType } from "../system/Strings.mjs";

export interface IExitSurvey {
  bugsNotRelatedToSyncing?: boolean;
  bugsRelatedToSyncing?: boolean;
  featureDidNotWorkAsExpected?: boolean;
  noSupport?: boolean;
  tooExpensive?: boolean;
  tooDifficultToGetStarted?: boolean;
  didNotIntegreateIntoWorkflow?: boolean;
  missingFeature?: boolean;
  missingFeatureText?: string;
  other?: boolean;
  otherText?: string;
}

export class TelemetryService {
  constructor(private dataService: TelemetryDataService) {}

  public saveCounters(
    context: Context,
    accountId: number,
    deviceId: number,
    counter: any
  ) {
    return this.dataService.saveCounters(
      context,
      accountId,
      deviceId,
      counter.data
    );
  }

  public saveClientEvents(context: Context, longDeviceId: string, events: any) {
    return this.dataService.saveClientEvents(context, longDeviceId, events);
  }

  public saveExitSurvey(
    context: Context,
    accountId: number,
    data: IExitSurvey
  ) {
    return this.dataService.saveExitSurvey(context, accountId, data);
  }

  public getLastAccessTimeForAccount(context: Context, accountId: number) {
    return this.dataService.getLastAccessTimeForAccount(context, accountId);
  }

  public saveCloudEvent(
    context: Context,
    aid: number,
    did: number,
    eventType: EventType,
    data: any
  ) {
    aid = aid || (data && data.aid) || context.aid;
    did = did || (data && data.did) || context.did || 0;
    return this.dataService.saveCloudEvent(context, aid, did, eventType, data);
  }
}
