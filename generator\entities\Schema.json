{"versions": {"major": 5, "minor": 337, "comment1": "********************************************************************", "comment2": "*** If you modify formatting in this file, copy the content to   ***", "comment3": "*** jsonlint.com to validate & reformat, and then copy it back   ***", "comment4": "********************************************************************"}, "types": {"Account": {"custom": 1, "kindCode": 5, "isPersistableObject": 1, "bucketType": "Account", "fields": [{"name": "AccountId", "cloudname": "accountId", "sid": 1, "type": "int", "primaryKey": 1, "shortRid": 1, "id": 1}, {"name": "Account<PERSON><PERSON>", "cloudname": "accountKey", "sid": 2, "type": "string"}, {"name": "Subscriber", "cloudname": "sub", "sid": 3, "type": "string"}, {"name": "Flags", "cloudname": "flags", "sid": 4, "type": "int"}, {"name": "PeerToPeerKey", "cloudname": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sid": 5, "type": "string"}, {"name": "Email", "cloudname": "email", "sid": 6, "type": "string"}, {"name": "Cipher", "cloudname": "cipher", "sid": 8, "type": "blob"}, {"name": "idp", "cloudname": "idp", "sid": 9, "type": "string"}, {"name": "planId", "cloudname": "planId", "sid": 10, "type": "string"}, {"name": "role", "cloudname": "role", "sid": 11, "type": "string"}, {"name": "MinBuild", "cloudname": "minBuild", "sid": 12, "type": "int"}, {"name": "RSAPrivateKey", "cloudname": "rsaPrivateKey", "sid": 13, "type": "string"}, {"name": "RSAPublicKey", "cloudname": "x509Cert", "sid": 14, "type": "string"}, {"name": "MaxDevicesAllowed", "cloudname": "deviceLimit", "sid": 15, "type": "int"}, {"name": "MaxPhotosAllowed", "cloudname": "photoLimit", "sid": 16, "type": "int"}, {"name": "FeatureSet0", "cloudname": "FeatureSet0", "sid": 17, "type": "int"}, {"name": "FeatureSet1", "cloudname": "features", "sid": 18, "type": "int"}, {"name": "NextPlanDate", "cloudname": "nextPlanDate", "sid": 19, "type": "MYDateTime"}, {"name": "CloudStorageLimit", "cloudname": "cloudStorageLimit", "sid": 20, "type": "int"}, {"name": "ClientCipher", "cloudname": "clientCipher", "sid": 21, "type": "blob"}, {"name": "ClientPeerToPeerKey", "cloudname": "clientPeerToPeerKey", "sid": 22, "type": "string"}, {"name": "ClientCipherVersion", "cloudname": "clientCipherVersion", "sid": 23, "type": "int"}, {"name": "ClientPeerToPeerKeyVersion", "cloudname": "clientPeerToPeerKeyVersion", "sid": 24, "type": "int"}, {"name": "AvailableUpgrades", "cloudname": "availableUpgrades", "sid": 25, "type": "string"}, {"name": "LicenseTemplateId", "cloudname": "licenseTemplateId", "sid": 26, "type": "string"}, {"name": "LicenseDisplayName", "cloudname": "licenseDisplayName", "sid": 27, "type": "string"}, {"name": "LicenseFlags", "cloudname": "licenseFlags", "sid": 28, "type": "int"}, {"name": "LicenseManager", "cloudname": "licenseManager", "sid": 29, "type": "string"}, {"name": "AvailableUpgradesFeatures", "cloudname": "availableUpgradesFeatures", "sid": 30, "type": "int"}]}, "Album": {"base": "MediaContainer", "custom": 1, "inheritedHashReferences": ["CoverMedia", "GeneratedCoverMedia", "ImportSession"], "genRandomHash": 1, "fields": [{"name": "SmartFilter", "type": "string", "cloudname": "SF", "sid": 6}, {"name": "ParentAlbum", "type": "Album", "cloudname": "PAL", "sid": 7, "hashReference": 1, "indexed": 1}], "kindCode": 10, "isResource": 1}, "Appointment": {"base": "Event", "noSqlTable": 1, "flatten": 1, "hashCalculator": 1, "fields": [{"name": "IsAllDay", "sid": 22, "type": "bool"}, {"name": "IsRecurring", "sid": 23, "type": "bool"}], "kindCode": 11, "isResource": 1}, "AuditEntry": {"base": "Resource", "custom": 1, "genRandomHash": 1, "fields": [{"name": "AuditType", "type": "int", "cloudname": "ALAT", "sid": 2}, {"name": "DateLogged", "type": "BigMYDateTime", "cloudname": "ALDL", "sid": 3}, {"name": "DateSeen", "type": "BigMYDateTime", "cloudname": "ALDS", "flags": "local"}, {"name": "Description", "type": "string", "cloudname": "ALD", "sid": 4}, {"name": "Count", "type": "int", "cloudname": "ALC", "sid": 5}, {"name": "Misc", "type": "string", "cloudname": "ALM", "sid": 6}, {"name": "ResourceHash", "type": "hash", "cloudname": "ALR", "sid": 7}, {"name": "LoggingDeviceId", "type": "MYDeviceId", "cloudname": "ALDI", "v3upgrade": "(SELECT MYV3DeviceId(cloudAssignedNid, UniqueHash, LoggingNetworkNodeHash) from oldNetworkNode)", "sid": 8}], "kindCode": 12, "isResource": 1}, "DeletedByoc": {"simpleTable": 1, "fields": [{"name": "DeviceType", "type": "MYDeviceType"}, {"name": "ByocId", "type": "string"}, {"name": "MediaHash", "type": "hash"}, {"name": "MediaType", "type": "int"}], "typeConstraints": "PRIMARY KEY (DeviceType, ByocId)"}, "CategoryCount": {"simpleTable": 1, "fields": [{"name": "CategoryName", "type": "string", "sid": 1}, {"name": "Count", "type": "int", "sid": 2}]}, "Channel": {"base": "Resource", "fields": [{"name": "KindCode", "flags": ["nocpp", "local"], "v3upgrade": "ResourceTypeToKind(ResourceKind)", "type": "int"}], "kindCode": 13, "isResource": 1, "hashCalculator": 1, "abstract": 1, "includeTypeInHash": 1}, "Configuration": {"simpleTable": 1, "fields": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "varchar128"}, {"name": "ConfigVal", "type": "varchar128notNull"}], "typeConstraints": "PRIMARY KEY (ConfigKey)", "cppExceptions": "Configuration"}, "Counters": {"simpleTable": 1, "fields": [{"name": "Name", "type": "varchar128", "constraints": "PRIMARY KEY"}, {"name": "Value", "type": "int", "constraints": "not null"}]}, "DeletedResource": {"base": "Resource", "noSqlTable": 1, "custom": 1, "kindCode": 240, "isResource": 1, "genRandomHash": 1, "cppExceptions": "DeletedResource"}, "ForgottenResource": {"base": "Resource", "noSqlTable": 1, "custom": 1, "kindCode": 244, "isResource": 1, "genRandomHash": 1, "cppExceptions": "DeletedResource"}, "GhostedResource": {"base": "Resource", "noSqlTable": 1, "custom": 1, "kindCode": 248, "isResource": 1, "genRandomHash": 1, "cppExceptions": "DeletedResource"}, "UnknownResource": {"base": "Resource", "noSqlTable": 1, "kindCode": 8, "fields": [], "isResource": 1, "genRandomHash": 1}, "Email": {"base": "Channel", "noSqlTable": 1, "flatten": 1, "fields": [{"name": "<PERSON>ail<PERSON><PERSON><PERSON>", "type": "string", "hashComponent": 1, "sid": 2}, {"name": "IsGroup", "type": "bool", "sid": 3}], "kindCode": 30, "isResource": 1, "hashCalculator": 1, "includeTypeInHash": 1}, "Event": {"base": "MediaContainer", "inheritedHashReferences": ["CoverMedia", "GeneratedCoverMedia", "ImportSession"], "custom": 1, "fields": [{"name": "Source", "type": "string", "hashComponent": 1, "cloudname": "SRC", "sid": 6, "indexed": 1}, {"name": "SourceId", "type": "string", "hashComponent": 1, "sid": 7, "cloudname": "SID"}, {"name": "Latitude", "type": "double", "cloudname": "LAT", "sid": 8}, {"name": "Longitude", "type": "double", "cloudname": "LON", "sid": 9}, {"name": "Caption", "type": "string", "cloudname": "CAP", "sid": 10}, {"name": "ModifiedByUser", "type": "bool", "cloudname": "MBU", "sid": 11}, {"name": "StartDateTime", "type": "BigMYDateTime", "cloudname": "SDT", "sid": 12}, {"name": "EndDateTime", "type": "BigMYDateTime", "cloudname": "EDT", "sid": 13}, {"name": "<PERSON><PERSON>", "type": "string", "cloudname": "SCOPE", "sid": 14}, {"name": "DisplayOrder", "type": "int", "cloudname": "DO", "sid": 15}, {"name": "Rating", "type": "int16", "cloudname": "RAT", "indexed": "RatingIdx", "sid": 16}, {"name": "CategoryHash", "type": "hash", "indexed": 1, "cloudname": "CATE", "sid": 17}, {"name": "Filter", "type": "string", "cloudname": "FT", "sid": 18}, {"name": "ShowInCalendar", "type": "bool", "cloudname": "SC", "sid": 19}, {"name": "ParentEvent", "type": "Event", "hashReference": 1, "indexed": 1, "sid": 20}, {"name": "Location", "type": "Place", "hashReference": 1, "indexed": 1, "sid": 21}, {"name": "KindCode", "flags": ["nocpp", "local"], "v3upgrade": "ResourceTypeToKind(ResourceKind)", "type": "int"}], "kindCode": 15, "isResource": 1, "hashCalculator": 1, "abstract": 1}, "FacebookCheckin": {"base": "Event", "noSqlTable": 1, "flatten": 1, "fields": [{"name": "FBMessage", "type": "string", "sid": 22}, {"name": "FBApplicationName", "type": "string", "sid": 23}, {"name": "FBCaption", "type": "string", "sid": 24}, {"name": "FBIsPhotoPost", "type": "bool", "sid": 25}], "kindCode": 17, "isResource": 1, "hashCalculator": 1}, "FacebookEvent": {"base": "Event", "noSqlTable": 1, "flatten": 1, "fields": [{"name": "FBEventDescription", "type": "string", "sid": 22}, {"name": "RsvpStatus", "type": "string", "sid": 23}], "kindCode": 18, "isResource": 1, "hashCalculator": 1}, "FaceRectangle": {"base": "Resource", "fields": [{"name": "TopLeftX", "type": "float", "sid": 2}, {"name": "TopLeftY", "type": "float", "sid": 3}, {"name": "<PERSON><PERSON><PERSON>", "type": "float", "sid": 4}, {"name": "Height", "type": "float", "sid": 5}, {"name": "SourceId", "type": "int", "cloudname": "SID", "sid": 6}, {"name": "Person", "type": "Person", "cloudname": "PER", "sid": 7, "hashReference": 1, "indexed": 1}, {"name": "Media", "type": "Media", "cloudname": "MD", "sid": 8, "hashReference": 1, "indexed": 1}, {"name": "ClusterId", "type": "int", "cloudname": "CLID", "flags": "local"}, {"name": "Descriptor", "type": "blob", "cloudname": "FDSC", "sid": 9}, {"name": "FaceInfo", "type": "blob", "cloudname": "FINF", "sid": 10}, {"name": "Ignore", "type": "int", "cloudname": "FIGN", "sid": 11}, {"name": "Proposed<PERSON><PERSON><PERSON><PERSON>", "type": "hash", "cloudname": "PPHS", "flags": "local", "sid_obsolete": 12}, {"name": "Confidence", "type": "int", "cloudname": "CONF", "flags": "local", "sid_obsolete": 13}, {"name": "Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "blob", "cloudname": "RPHS", "sid": 14}], "kindCode": 19, "custom": 1, "isResource": 1, "genRandomHash": 1}, "FeatureCount": {"simpleTable": 1, "fields": [{"name": "Category", "type": "string"}, {"name": "Feature", "type": "string"}, {"name": "Count", "type": "int"}]}, "FilesReceiving": {"simpleTable": 1, "fields": [{"name": "MediaHash", "type": "hash"}, {"name": "MediaFileType", "type": "int"}, {"name": "URL", "type": "string"}, {"name": "IntendedDataHash", "type": "hash"}], "typeConstraints": "PRIMARY KEY (MediaHash, MediaFileType)"}, "FilterHistoryItem": {"base": "Resource", "fields": [{"name": "TimeStamp", "type": "MYDateTime", "sid": 2}, {"name": "FilterString", "type": "string", "hashComponent": 1, "sid": 3}, {"name": "IsPinned", "type": "bool", "sid": 4}], "kindCode": 20, "isResource": 1, "hashCalculator": 1}, "Folder": {"base": "MediaContainer", "custom": 1, "inheritedHashReferences": ["CoverMedia", "GeneratedCoverMedia", "ImportSession"], "genRandomHash": 1, "fields": [{"name": "LocalRootOrTemporaryPath", "type": "MYFSPath", "flags": "local"}, {"name": "IsMissing", "type": "bool", "flags": ["local", "virtual"]}, {"name": "IsPrivate", "type": "bool", "sid": 6}, {"name": "RootDeviceLabel", "type": "string", "cloudname": "RDL", "sid": 9}, {"name": "HasUnhandledFiles", "type": "bool", "flags": "local"}, {"name": "ALURL", "type": "MYFSPath", "flags": "local"}, {"name": "LocalName", "type": "string", "flags": "local"}, {"name": "ReadOnlyDeviceHash", "type": "hashNoHeader", "sid": 8}, {"name": "ParentFolder", "type": "Folder", "cloudname": "PFO", "hashReference": 1, "indexed": 1, "sid": 7}], "kindCode": 1, "isResource": 1}, "FolderDeviceLink": {"base": "Resource", "custom": 1, "fields": [{"name": "SmallIntention", "type": "MYFilterIntention", "sid": 4}, {"name": "SmallKeywords", "type": "string", "sid": 6}, {"name": "SmallRatingFilter", "type": "string", "sid": 7}, {"name": "SmallStarFilter", "type": "string", "sid": 8}, {"name": "LargeIntention", "type": "MYFilterIntention", "sid": 10}, {"name": "LargeKeywords", "type": "string", "sid": 11}, {"name": "LargeRatingFilter", "type": "string", "sid": 12}, {"name": "LargeStarFilter", "type": "string", "sid": 13}, {"name": "MediumIntention", "type": "MYFilterIntention", "sid": 5}, {"name": "MediumKeywords", "type": "string", "sid": 15}, {"name": "MediumRatingFilter", "type": "string", "sid": 16}, {"name": "MediumStarFilter", "type": "string", "sid": 17}, {"name": "LargeIgnoreParent", "type": "bool", "cloudname": "LIP", "sid": 19}, {"name": "MediumIgnoreParent", "type": "bool", "cloudname": "MIP", "sid": 20}, {"name": "SmallIgnoreParent", "type": "bool", "cloudname": "SIP", "sid": 21}, {"name": "OverrideChildMDLs", "type": "bool", "sid": 22}, {"name": "Folder", "type": "Folder", "hashComponent": 1, "cloudname": "FR", "sid": 2, "hashReference": 1, "indexed": 1}, {"name": "DeviceId", "type": "MYDeviceId", "v3upgrade": "(SELECT MYV3DeviceId(cloudAssignedNid, UniqueHash, NetworkNodeHash) from oldNetworkNode)", "hashComponent": 1, "cloudname": "DV", "sid": 3, "indexed": 1}, {"name": "LastOverwriteTime", "type": "MYDateTime", "v3upgrade": "null", "sid": 23}], "kindCode": 3, "isResource": 1, "hashCalculator": 1}, "ImportSession": {"base": "Resource", "fields": [{"name": "StartDateTime", "type": "MYDateTime", "cloudname": "SDT", "sid": 2}, {"name": "EndDateTime", "type": "MYDateTime", "cloudname": "EDT", "sid": 3}, {"name": "Source", "type": "string", "cloudname": "SRC", "sid": 4}], "kindCode": 23, "isResource": 1, "genRandomHash": 1}, "ImportableResource": {"base": "Resource", "abstract": 1, "noSqlTable": 1, "fields": [{"name": "ImportSession", "type": "ImportSession", "cloudname": "IS", "sid": 2, "hashReference": 1, "indexed": 1}], "kindCode": 24, "isResource": 1}, "IngestIgnoreDeviceLink": {"base": "Resource", "fields": [{"name": "DeviceId", "type": "MYDeviceId", "hashComponent": 1, "sid": 1}, {"name": "Url", "type": "string", "hashComponent": 1, "sid": 2}], "kindCode": 249, "isResource": 1, "hashCalculator": 1}, "Internet": {"base": "Channel", "includeTypeInHash": 1, "flatten": 1, "noSqlTable": 1, "fields": [{"name": "<PERSON><PERSON><PERSON>", "type": "string", "sid": 2}, {"name": "URL", "type": "string", "sid": 3}], "kindCode": 25, "isResource": 1, "hashCalculator": 1}, "Link": {"base": "Resource", "link": 1, "custom": 1, "isResource": 1, "hashCalculator": 1, "fields": [{"name": "FromDateTime", "type": "MYDateTime", "cloudname": "FDT", "sid": 4}, {"name": "ToDateTime", "type": "MYDateTime", "cloudname": "TDT", "sid": 5}, {"name": "SourceResource", "type": "Resource", "hashComponent": 1, "cloudname": "SRS", "sid": 2, "hashReference": 1, "indexed": 1}, {"name": "TargetResource", "type": "Resource", "hashComponent": 1, "cloudname": "TRS", "sid": 3, "hashReference": 1, "indexed": 1}, {"name": "SourceResourceKind", "type": "int", "v3upgrade": "null", "sid": 6}, {"name": "TargetResourceKind", "type": "int", "v3upgrade": "null", "sid": 7}, {"name": "KindCode", "flags": ["nocpp", "local"], "v3upgrade": "ResourceTypeToKind(ResourceKind)", "type": "int"}], "kindCode": 64}, "Media": {"base": "ImportableResource", "inheritedHashReferences": ["ImportSession"], "custom": 1, "cppExceptions": "Media", "genRandomHash": 1, "fields": [{"name": "DateCreated", "type": "MYDateTime", "cloudname": "DC", "flags": "virtual", "sid": 8}, {"name": "StarRating", "type": "int", "cloudname": "SR", "sid": 9, "indexed": 1, "indexedOnUSB": 0}, {"name": "KeywordsStr", "type": "string", "cloudname": "KW", "sid": 10}, {"name": "F<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool", "flags": "local"}, {"name": "MonthNumber", "type": "int", "cloudname": "MN", "sid": 11, "indexed": 1, "indexedOnUSB": 0}, {"name": "YearNumber", "type": "int", "cloudname": "YN", "sid": 12}, {"name": "Caption", "type": "string", "cloudname": "CAP", "sid": 13}, {"name": "DayNumber", "type": "int", "cloudname": "DN", "sid": 14}, {"name": "FaceDetectionVersion", "type": "int", "cloudname": "FD", "sid": 15, "indexed": "FaceDetectV", "indexedOnUSB": 0}, {"name": "Orientation", "type": "int", "cloudname": "OT", "sid": 16}, {"name": "SourceId", "type": "string", "cloudname": "SID", "sid": 17}, {"name": "Title", "type": "string", "cloudname": "TL", "sid": 18}, {"name": "Label", "type": "string", "cloudname": "LB", "sid": 19, "indexed": 1, "indexedOnUSB": 0}, {"name": "Copyright", "type": "string", "cloudname": "CY", "sid": 20}, {"name": "CameraMake", "type": "string", "cloudname": "CMK", "sid": 21}, {"name": "CameraModel", "type": "string", "cloudname": "CMO", "sid": 22}, {"name": "CameraSerialNumber", "type": "string", "cloudname": "CSN", "sid": 23}, {"name": "GpsLat", "type": "double", "cloudname": "GLA", "sid": 24, "indexed": "PhotoGeo", "indexedOnUSB": 0}, {"name": "GpsLong", "type": "double", "cloudname": "GLO", "sid": 25, "indexed": "PhotoGeo", "indexedOnUSB": 0}, {"name": "FileNameNoExt", "type": "string", "cloudname": "FN", "flags": "virtual", "sid": 3}, {"name": "Aperture", "type": "double", "cloudname": "AP", "sid": 27}, {"name": "Cropped", "type": "bool", "cloudname": "CR", "sid": 28}, {"name": "Flash", "type": "bool", "cloudname": "FL", "sid": 29}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "double", "cloudname": "FO", "sid": 30}, {"name": "Is<PERSON>", "type": "float", "cloudname": "ISO", "sid": 31}, {"name": "Lens", "type": "string", "cloudname": "LE", "sid": 32}, {"name": "HasVideo", "type": "bool", "cloudname": "RV", "flags": "virtual", "sid": 33}, {"name": "ShutterSpeed", "type": "double", "cloudname": "SS", "sid": 34}, {"name": "Author", "type": "string", "cloudname": "AU", "sid": 35}, {"name": "IsFlagged", "type": "bool", "cloudname": "IF", "indexed": 1, "indexedOnUSB": 0, "sid": 36}, {"name": "IsEdited", "type": "bool", "cloudname": "IE", "sid": 37}, {"name": "OfflineEditingSupport", "type": "string", "cloudname": "OES", "sid": 38}, {"name": "EditingMaster", "type": "EditingMasterType", "cloudname": "EM", "sid": 39}, {"name": "WhiteBalanceAsShotTemp", "type": "float", "cloudname": "WTE", "sid": 40}, {"name": "WhiteBalanceAsShotTint", "type": "float", "cloudname": "WTI", "sid": 41}, {"name": "DateCreatedConfidence", "type": "DateConfidenceType", "cloudname": "DCC", "sid": 42}, {"name": "NormalizedFileNameNoExt", "type": "string", "cloudname": "NFN", "sid": 43}, {"name": "CropZoomFactor", "type": "float", "cloudname": "CZF", "sid": 44, "default": "1", "sqlDefault": "1"}, {"name": "ExposureBias", "type": "float", "cloudname": "EB", "sid": 45}, {"name": "MeteringMode", "type": "int", "cloudname": "MM", "sid": 46}, {"name": "WhiteBalanceMode", "type": "int", "cloudname": "WM", "sid": 47}, {"name": "LocalFileNameNoExt", "type": "string", "flags": ["local", "virtual"]}, {"name": "LocalTempContainingFolderURL", "type": "MYFSPath", "flags": "local"}, {"name": "IsInternal", "type": "bool", "cloudname": "II", "sid": 48}, {"name": "InvolvedInDuplication", "type": "bool", "cloudname": "IID", "sid": 49, "indexed": 1, "indexedOnUSB": 0}, {"name": "AddToCacheTime", "type": "MYDateTime", "flags": "local", "indexed": 1}, {"name": "SearchAttributes", "type": "int", "flags": "local"}, {"name": "SearchPartitions", "type": "int", "v3upgrade": "null", "flags": "local"}, {"name": "AddToCacheTimeRemovable", "type": "MYDateTime", "flags": "local", "indexed": 1}, {"name": "PixelChecksumStorage", "type": "blob", "cloudname": "PCS", "sid": 50, "indexed": "PixelChecksumIndex"}, {"name": "StampAttributes", "type": "int", "flags": "local"}, {"name": "ContainingFolder", "type": "Folder", "cloudname": "CF", "sid": 4, "hashReference": 1, "indexed": 1}, {"name": "Place", "type": "Place", "cloudname": "PL", "sid": 52, "hashReference": 1}, {"name": "PlaceExtraInfo", "type": "int", "cloudname": "PEI", "sid": 53}, {"name": "RootFolder", "type": "Folder", "cloudname": "RF", "sid": 5, "hashReference": 1, "indexed": 1}, {"name": "ContainedInAlbum", "type": "bool", "flags": "local"}, {"name": "BYOA", "type": "string", "cloudname": "BA", "sid": 55}, {"name": "Files", "type": "MYFiles", "sid": 6, "v3upgrade": "UpgradeFiles(RAWorVideoALURL, RAWorVideoCantParse, RAWorVideoFileDateTime, RAWorVideoFileSize, RAWorVideoFormat, RAWorVideoHeight, RAWorVideoInInternalData, RAWorVideoLength, RAWorVideoLocalCantParse, RAWorVideoLocalDataHash, RAWorVideoOrientation, RAWorVideoOriginalDataHash, RAWorVideoWidth, NonRAWALURL, NonRAWCantParse, NonRAWFileDateTime, NonRAWFileSize, NonRAWFormat, NonRAWHeight, NonRAWInInternalData, NonRAWIsGenerated, NonRAWLocalCantParse, NonRAWLocalDataHash, NonRAWOrientation, NonRAWOriginalDataHash, NonRAWWidth, PreviewFileSize, PreviewFormat, PreviewGenVersion, PreviewHeight, PreviewIsDraft, PreviewLocalBasisDataHash, PreviewLocalCropZoomFactor, PreviewLocalDataHash, PreviewWidth, DisplayImageCantParse, DisplayImageFileDateTime, DisplayImageFileSize, DisplayImageFormat, DisplayImageHeight, DisplayImageInInternalData, DisplayImageLocalCantParse, DisplayImageLocalDataHash, DisplayImageLocalVisualEditHash, DisplayImageOriginalDataHash, DisplayImageWidth, ThumbnailFileSize, ThumbnailGenVersion, ThumbnailHeight, ThumbnailIsDraft, ThumbnailLocalBasisDataHash, ThumbnailLocalDataHash, ThumbnailLocalVisualEditHash, ThumbnailWidth, XMPFileDateTime, XMPFileSize, XMPFormat, XMPInInternalData, XMPLocalDataHash, XMPOriginalDataHash, HasVideo)"}, {"name": "LocalFiles", "type": "MYLocalFiles", "flags": "local", "v3upgrade": "UpgradeLocalFiles(RAWorVideoALURL, RAWorVideoCantParse, RAWorVideoFileDateTime, RAWorVideoFileSize, RAWorVideoFormat, RAWorVideoHeight, RAWorVideoInInternalData, RAWorVideoLength, RAWorVideoLocalCantParse, RAWorVideoLocalDataHash, RAWorVideoOrientation, RAWorVideoOriginalDataHash, RAWorVideoWidth, NonRAWALURL, NonRAWCantParse, NonRAWFileDateTime, NonRAWFileSize, NonRAWFormat, NonRAWHeight, NonRAWInInternalData, NonRAWIsGenerated, NonRAWLocalCantParse, NonRAWLocalDataHash, NonRAWOrientation, NonRAWOriginalDataHash, NonRAWWidth, PreviewFileSize, PreviewFormat, PreviewGenVersion, PreviewHeight, PreviewIsDraft, PreviewLocalBasisDataHash, PreviewLocalCropZoomFactor, PreviewLocalDataHash, PreviewWidth, DisplayImageCantParse, DisplayImageFileDateTime, DisplayImageFileSize, DisplayImageFormat, DisplayImageHeight, DisplayImageInInternalData, DisplayImageLocalCantParse, DisplayImageLocalDataHash, DisplayImageLocalVisualEditHash, DisplayImageOriginalDataHash, DisplayImageWidth, ThumbnailFileSize, ThumbnailGenVersion, ThumbnailHeight, ThumbnailIsDraft, ThumbnailLocalBasisDataHash, ThumbnailLocalDataHash, ThumbnailLocalVisualEditHash, ThumbnailWidth, XMPFileDateTime, XMPFileSize, XMPFormat, XMPInInternalData, XMPLocalDataHash, XMPOriginalDataHash, HasVideo, NeedsHas1)"}, {"name": "NeedsId", "type": "int", "flags": "local", "v3upgrade": "0"}, {"name": "SerializedNeeds", "type": "bj<PERSON>", "flags": ["nosql", "nosqlField"], "sid": 57}, {"name": "BYOG", "type": "string", "cloudname": "BG", "sid": 56}, {"name": "ReuploadBYOA", "type": "bool", "flags": "local"}, {"name": "SoftFetchPreview", "type": "MYSoftFetch", "flags": "local"}, {"name": "SoftFetchOriginal", "type": "MYSoftFetch", "flags": "local"}, {"name": "LastViewMinute", "type": "int", "flags": "local"}, {"name": "AutoPreviewPriority", "type": "MYAutoPreviewPriority", "flags": "local"}, {"name": "AutoOriginalPriority", "type": "MYAutoOriginalPriority", "flags": "local"}], "kindCode": 2, "isResource": 1}, "MediaAlbumLink": {"base": "Resource", "custom": 1, "fields": [{"name": "DisplayOrder", "type": "int", "cloudname": "DO", "sid": 4}, {"name": "SourceResource", "type": "Media", "hashComponent": 1, "cloudname": "SRS", "sid": 2, "hashReference": 1, "indexed": 1}, {"name": "TargetResource", "type": "Album", "hashComponent": 1, "cloudname": "TRS", "sid": 3, "hashReference": 1, "indexed": 1}], "kindCode": 27, "isResource": 1, "hashCalculator": 1}, "BucketDeviceLink": {"base": "Resource", "custom": 1, "fields": [{"name": "LocalFiles", "type": "bj<PERSON>", "sid": 2}], "kindCode": 6, "isResource": 1}, "MediaContainer": {"base": "ImportableResource", "abstract": 1, "noSqlTable": 1, "hashCalculator": 1, "includeTypeInHash": 1, "custom": 1, "fields": [{"name": "DirectChildMedia", "type": "int", "flags": ["local", "virtual"]}, {"name": "Name", "type": "string", "cloudname": "NAM", "flags": "virtual", "sid": 3}, {"name": "RecursiveChildMedia", "type": "int", "flags": ["local", "virtual"]}, {"name": "RecursiveChildMediaStartDateTime", "type": "BigMYDateTime", "flags": ["local", "virtual"]}, {"name": "RecursiveChildMediaEndDateTime", "type": "BigMYDateTime", "flags": ["local", "virtual"]}, {"name": "F<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool", "flags": "local"}, {"name": "DirectMediaEndDate", "type": "MYDateTime", "flags": ["local", "virtual"]}, {"name": "DirectMediaStartDate", "type": "MYDateTime", "flags": ["local", "virtual"]}, {"name": "NormalizedName", "type": "string", "cloudname": "NNM", "sid": 4}, {"name": "DirectChildContainers", "type": "int", "flags": ["local", "virtual"]}, {"name": "GeneratedCoverDirty", "type": "bool", "flags": "local"}, {"name": "InitialCoverMedia", "type": "int", "flags": "local", "cloudname": "CICM"}, {"name": "CoverMedia", "type": "Media", "cloudname": "CM", "sid": 5, "hashReference": 1}, {"name": "GeneratedCoverMedia", "type": "Media", "flags": "local", "hashReference": 1, "indexed": 1}], "kindCode": 28, "isResource": 1}, "Needs": {"simpleTable": 1, "fields": [{"name": "Id", "type": "int"}, {"name": "Need", "type": "bj<PERSON>"}], "typeConstraints": "PRIMARY KEY (Id)"}, "NAS": {"base": "Resource", "fields": [{"name": "CatalogPath", "type": "MYFSPath", "cloudname": "NASC", "flags": ["local"]}, {"name": "NetworkPath", "type": "string", "sid": 3}], "kindCode": 29, "isResource": 1, "genRandomHash": 1}, "DeviceData": {"isPersistableObject": 1, "bucketType": "DeviceData", "custom": 1, "cloudname": "DeviceData", "fields": [{"name": "DeviceId", "type": "MYDeviceId", "cloudname": "deviceId", "sid": 1, "id": 1, "primaryKey": 1, "shortRid": 1}, {"name": "Build", "type": "string", "cloudname": "build", "sid": 3}, {"name": "LastAccessTime", "type": "MYDateTime", "cloudname": "lastAccessTime", "sid": 4}, {"name": "Paths", "type": "bj<PERSON>", "sid": 5}, {"name": "OS", "type": "string", "cloudname": "os", "sid": 6}, {"name": "OwningNode", "type": "MYDeviceId", "cloudname": "NNO", "sid": 7}, {"name": "<PERSON><PERSON><PERSON>", "type": "string", "sid": 8}, {"name": "ResourceCount", "type": "int", "sid": 9}, {"name": "MediaCount", "cloudname": "mediaCount", "type": "int", "sid": 10}, {"name": "Address", "type": "string", "sid": 11}, {"name": "ThumbnailSize", "type": "int64", "sid": 12}, {"name": "PreviewSize", "type": "int64", "sid": 13}, {"name": "OriginalSize", "type": "int64", "cloudname": "originalSize", "sid": 14}, {"name": "DisabledDevices", "type": "bj<PERSON>", "sid": 15}, {"name": "LastSupportTicketProcessed", "type": "hash", "sid": 16}, {"name": "LastFilesInSyncTime", "type": "MYDateTime", "flags": "local"}, {"name": "ResourceKinds", "type": "bj<PERSON>", "sid": 17}, {"name": "ProtocolVersion", "type": "int", "cloudname": "protocolVersion", "sid": 18}, {"name": "LocalThumbnailSize", "type": "int64", "sid": 19}, {"name": "LocalPreviewSize", "type": "int64", "sid": 20}, {"name": "LocalOriginalSize", "type": "int64", "sid": 21}, {"name": "Version", "type": "string", "cloudname": "version", "sid": 22}, {"name": "LastStartupTime", "type": "MYDateTime", "cloudname": "lastStartupTime", "sid": 23}, {"name": "LastHidTime", "type": "MYDateTime", "cloudname": "lastHidTime", "sid": 24}, {"name": "LastImportTime", "type": "MYDateTime", "cloudname": "lastImportTime", "sid": 25}, {"name": "<PERSON>up<PERSON><PERSON><PERSON>", "type": "string", "cloudname": "<PERSON>up<PERSON><PERSON><PERSON>", "sid": 26}], "kindCode": 7}, "NetworkNode": {"isPersistableObject": 1, "bucketType": "<PERSON><PERSON>", "custom": 1, "cloudname": "<PERSON><PERSON>", "fields": [{"name": "DeviceId", "type": "MYDeviceId", "cloudname": "deviceId", "sid": 1, "id": 1, "primaryKey": 1, "shortRid": 1, "v3upgrade": "(SELECT MYV3DeviceId(cloudAssignedNid, UniqueHash, oldNetworkNode.uniqueHash) from oldNetworkNode ONN)"}, {"name": "NodeName", "type": "string", "cloudname": "name", "sid": 2}, {"name": "DeviceType", "type": "MYDeviceType", "cloudname": "deviceType", "sid": 3, "v3upgrade": "UpgradeDeviceType(deviceType)"}, {"name": "DeviceCreationTime", "type": "MYDateTime", "cloudname": "creationTime", "v3upgrade": "cast(strftime('%s','now') as int) * 1000", "sid": 4}, {"name": "Nickname", "type": "string", "cloudname": "nickname", "sid": 5}, {"name": "LongId", "type": "hash", "cloudname": "longId", "sid": 7, "v3upgrade": "UniqueHash"}, {"name": "Deleted", "type": "bool", "cloudname": "deleted", "v3upgrade": "0", "sid": 16}, {"name": "DeletedVersion", "type": "int", "cloudname": "deletedVersion", "v3upgrade": "null", "sid": 17}, {"name": "IsThisDevice", "type": "bool", "flags": "local"}, {"name": "ReservedSpaceGB", "type": "int", "sid": 24, "v3upgrade": "0"}, {"name": "NASHash", "type": "hashNoHeader", "sid": 14}, {"name": "InitialBuild", "type": "string", "v3upgrade": "InitialVersion", "sid": 6}, {"name": "DeviceLocation", "type": "hash", "indexed": 1, "sid": 12, "v3upgrade": "DeviceLocationHash"}, {"name": "FDLs", "type": "bj<PERSON>", "v3upgrade": "null", "sid": 21}, {"name": "Shuttle", "type": "bj<PERSON>", "v3upgrade": "null", "sid": 23}, {"name": "IsEncrypted", "type": "bool", "default": true, "sid": 25}, {"name": "ByocRootFolderId", "type": "string", "default": "", "sid": 26}, {"name": "SupportTicketRequest", "type": "bj<PERSON>", "cloudname": "supportTicket", "v3upgrade": "null", "sid": 27}, {"name": "PrioritizeLocalReplication", "type": "int", "sid": 28, "v3upgrade": "0"}, {"name": "DeviceRole", "cloudname": "role", "type": "int", "sid": 33}], "kindCode": 4}, "Party": {"base": "ImportableResource", "inheritedHashReferences": ["ImportSession"], "fields": [{"name": "Source", "type": "string", "hashComponent": 1, "cloudname": "SRC", "sid": 3}, {"name": "SourceId", "type": "string", "hashComponent": 1, "cloudname": "SID", "sid": 4}, {"name": "NormalizedName", "type": "string", "cloudname": "NNM", "sid": 5}, {"name": "SecondaryHomeLocation", "type": "Place", "hashReference": 1, "indexed": 1, "sid": 6}, {"name": "PrimaryHomeLocation", "type": "Place", "hashReference": 1, "indexed": 1, "sid": 7}, {"name": "WorkLocation", "type": "Place", "hashReference": 1, "indexed": 1, "sid": 8}, {"name": "WorkPhone", "type": "Telephone", "hashReference": 1, "indexed": 1, "sid": 9}, {"name": "HomePhone", "type": "Telephone", "hashReference": 1, "indexed": 1, "sid": 10}, {"name": "MobilePhone", "type": "Telephone", "hashReference": 1, "indexed": 1, "sid": 11}, {"name": "WorkEmail", "type": "Email", "hashReference": 1, "indexed": 1, "sid": 12}, {"name": "PrimaryEmail", "type": "Email", "hashReference": 1, "indexed": 1, "sid": 13}, {"name": "SecondaryEmail", "type": "Email", "hashReference": 1, "indexed": 1, "sid": 14}, {"name": "PrimaryWebsite", "type": "Internet", "hashReference": 1, "indexed": 1, "sid": 15}, {"name": "SecondaryWebsite", "type": "Internet", "hashReference": 1, "indexed": 1, "sid": 16}, {"name": "OtherLocation", "type": "Place", "hashReference": 1, "indexed": 1, "sid": 17}, {"name": "ProfileMedia", "type": "Media", "cloudname": "PMD", "hashReference": 1, "indexed": 1, "sid": 18}, {"name": "OriginalProfileMedia", "type": "Media", "cloudname": "OPM", "hashReference": 1, "indexed": 1, "sid": 19}, {"name": "KindCode", "flags": ["nocpp", "local"], "v3upgrade": "ResourceTypeToKind(ResourceKind)", "type": "int"}], "kindCode": 21, "abstract": 1, "isResource": 1, "hashCalculator": 1}, "PersistedTasks": {"simpleTable": 1, "fields": [{"name": "Id", "type": "integer", "constraints": "PRIMARY KEY"}, {"name": "Data", "type": "string"}, {"name": "Action", "type": "int"}, {"name": "Category", "type": "int"}, {"name": "JobClassName", "type": "string"}, {"name": "JobParams", "type": "string"}, {"name": "Priority", "type": "int"}]}, "PersistentTasks": {"simpleTable": 1, "fields": [{"name": "Id", "type": "integer", "constraints": "PRIMARY KEY"}, {"name": "TaskData", "type": "string"}, {"name": "TaskType", "type": "int"}]}, "Person": {"base": "Party", "custom": 1, "noSqlTable": 1, "flatten": 1, "fields": [{"name": "Gender", "type": "string", "hashComponent": 1, "sid": 20}, {"name": "BirthDateYear", "type": "int", "hashComponent": 1, "sid": 21}, {"name": "FirstName", "type": "string", "hashComponent": 1, "sid": 22}, {"name": "LastName", "type": "string", "hashComponent": 1, "sid": 23}, {"name": "BirthDateMonth", "type": "int", "hashComponent": 1, "sid": 24}, {"name": "BirthDateDayOfMonth", "type": "int", "hashComponent": 1, "sid": 25}], "kindCode": 31, "isResource": 1, "hashCalculator": 1}, "Place": {"base": "Resource", "custom": 1, "v3upgrade": "Location", "isResource": 1, "hashCalculator": 1, "fields": [{"name": "LocationName", "type": "string", "sid": 3}, {"name": "Source", "type": "string", "cloudname": "SRC", "sid": 4}, {"name": "CoverMedia", "type": "Media", "cloudname": "CM", "hashReference": 1, "sid": 5, "indexed": 1}, {"name": "City", "type": "string", "sid": 7}, {"name": "Country", "type": "string", "sid": 8}, {"name": "State", "type": "string", "sid": 9}, {"name": "County", "type": "string", "sid": 10}, {"name": "Latitude", "type": "double", "cloudname": "LAT", "sid": 13}, {"name": "Longitude", "type": "double", "cloudname": "LON", "sid": 14}], "kindCode": 26}, "Resource": {"abstract": 1, "bucketType": "Resource", "kindCode": 0, "constraints": {"UNIQUE": "UniqueHash"}, "custom": 1, "fields": [{"name": "UniqueHash", "flags": ["nosqlUpdate", "resourceTable", "local"], "type": "hashNoHeader", "cloudname": "r"}, {"name": "Id", "flags": ["nosql", "local", "resourceTable", "override"], "type": "int"}], "isResource": 1, "cppExceptions": "ResourceBase"}, "ResourceKinds": {"simpleTable": 1, "fields": [{"name": "KindCode", "type": "integer", "constraints": "PRIMARY KEY"}, {"name": "Title", "type": "varchar128"}, {"name": "IsLink", "type": "int"}, {"name": "InTable", "type": "varchar128"}]}, "ResourceDeviceAlert": {"base": "Resource", "custom": 1, "kindCode": 37, "isResource": 1, "hashCalculator": 1, "fields": [{"name": "ResourceHash", "type": "hash", "hashComponent": 1, "cloudname": "RH", "sid": 2, "indexed": 1}, {"name": "DeviceId", "type": "MYDeviceId", "hashComponent": 1, "cloudname": "DV", "sid": 3, "indexed": 1}, {"name": "NormalizedGlobalPath", "type": "string", "sid": 6}, {"name": "<PERSON><PERSON>", "type": "int", "sid": 4}, {"name": "Description", "type": "string", "sid": 5}]}, "Sequences": {"simpleTable": 1, "fields": [{"name": "SequenceId", "type": "integer", "constraints": "PRIMARY KEY DEFAULT 1"}, {"name": "LastKeyUsed", "type": "int", "constraints": "not null"}], "cppExceptions": "Sequences"}, "SharedConfiguration": {"base": "Resource", "custom": 1, "fields": [{"name": "Key", "type": "string", "hashComponent": 1, "indexed": "KeyIndex", "sid": 2}, {"name": "Value", "type": "string", "sid": 3}], "kindCode": 35, "isResource": 1, "hashCalculator": 1, "includeTypeInHash": 1}, "Telephone": {"base": "Channel", "noSqlTable": 1, "flatten": 1, "fields": [{"name": "TelephoneNumber", "type": "string", "sid": 2}, {"name": "Extension", "type": "string", "sid": 3}, {"name": "CountryCode", "type": "string", "sid": 4}], "kindCode": 36, "isResource": 1, "genRandomHash": 1}, "Message": {"custom": 0, "kindCode": 33, "isPersistableObject": 1, "bucketType": "Messages", "fields": [{"name": "MessageId", "cloudname": "messageId", "type": "int", "primaryKey": 1, "shortRid": 1, "id": 1, "sid": 1}, {"name": "Message", "type": "string", "cloudname": "message", "sid": 2}, {"name": "Value", "type": "string", "cloudname": "link", "sid": 3}, {"name": "DeviceId", "type": "MYDeviceId", "cloudname": "deviceId", "sid": 4}, {"name": "SecondsToDisplay", "type": "int", "cloudname": "secondsToDisplay", "sid": 5}, {"name": "Displayed", "type": "int", "cloudname": "displayed", "sid": 6}]}, "User": {"custom": 1, "kindCode": 34, "isPersistableObject": 1, "bucketType": "User", "fields": [{"name": "UserId", "cloudname": "userPropertyId", "type": "hash", "sid": 1}, {"name": "Deleted", "type": "bool", "cloudname": "deleted", "sid": 4}, {"name": "Name", "cloudname": "name", "type": "string", "sid": 2}, {"name": "Value", "cloudname": "value", "type": "string", "sid": 3}]}, "System": {"custom": 1, "kindCode": 9, "isPersistableObject": 1, "bucketType": "System", "fields": [{"name": "SystemId", "cloudname": "systemPropertyId", "type": "int", "primaryKey": 1, "shortRid": 1, "id": 1, "sid": 1}, {"name": "Deleted", "type": "bool", "cloudname": "deleted", "sid": 4}, {"name": "DeletedVersion", "type": "int", "cloudname": "deletedVersion", "sid": 5}, {"name": "Name", "type": "string", "cloudname": "name", "sid": 2}, {"name": "Value", "type": "string", "cloudname": "value", "sid": 3}]}, "TelemetryEvent": {"simpleTable": 1, "fields": [{"name": "Id", "type": "hash", "constraints": "PRIMARY KEY"}, {"name": "Time", "type": "MYDateTime"}, {"name": "UpTime", "type": "int64"}, {"name": "SessionId", "type": "int"}, {"name": "Name", "type": "varchar128"}, {"name": "Value", "type": "string"}]}, "TransferDeviceLink": {"link": 1, "kindCode": 66, "targetName": "TransferDeviceNode", "sourceName": "TransferTargetNode", "linkGroup": 0}, "PartyEventParticipation": {"link": 1, "kindCode": 83, "targetName": "Event", "sourceName": "Party", "linkGroup": 72}, "InvitedPersonsLinks": {"link": 1, "kindCode": 74, "targetName": "Appointment", "sourceName": "Person", "linkGroup": 72}, "EventOrganizingPartyLinks": {"link": 1, "kindCode": 73, "targetName": "Appointment", "sourceName": "Party", "linkGroup": 72}, "BirthdayPersonLinks": {"link": 1, "kindCode": 72, "targetName": "Birthday", "sourceName": "Person", "linkGroup": 30}, "AttendedPersonLinks": {"link": 1, "kindCode": 67, "targetName": "Appointment", "sourceName": "Person", "linkGroup": 72}, "RespondedPersonLinks": {"link": 1, "kindCode": 68, "targetName": "Appointment", "sourceName": "Person", "linkGroup": 72}, "FriendShipLink": {"link": 1, "kindCode": 70, "targetName": "HasFriends", "sourceName": "<PERSON><PERSON><PERSON>", "linkGroup": 72}, "GroupMembershipLink": {"link": 1, "kindCode": 71, "targetName": "PartyGroup", "sourceName": "Person", "linkGroup": 71}, "ResourceSupercessionLinks": {"link": 1, "kindCode": 65, "targetName": "SupercededBy", "sourceName": "Superceded", "linkGroup": 30}, "PersonChildrenLinks": {"link": 1, "kindCode": 76, "targetName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceName": "<PERSON><PERSON><PERSON><PERSON>", "linkGroup": 30}, "PersonSiblingLinks": {"link": 1, "kindCode": 77, "targetName": "<PERSON><PERSON><PERSON><PERSON>", "sourceName": "<PERSON><PERSON><PERSON>", "linkGroup": 30}, "PersonParentLinks": {"link": 1, "kindCode": 78, "targetName": "HasParents", "sourceName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "linkGroup": 30}, "PersonUncleLinks": {"link": 1, "kindCode": 79, "targetName": "<PERSON><PERSON><PERSON><PERSON>", "sourceName": "<PERSON><PERSON><PERSON><PERSON>", "linkGroup": 30}, "PersonAuntLinks": {"link": 1, "kindCode": 75, "targetName": "HasAunts", "sourceName": "<PERSON><PERSON><PERSON><PERSON>", "linkGroup": 30}, "PersonGrandfatherLinks": {"link": 1, "kindCode": 80, "targetName": "<PERSON><PERSON><PERSON><PERSON>s", "sourceName": "Grandfather<PERSON><PERSON><PERSON>", "linkGroup": 30}, "PersonGrandmotherLinks": {"link": 1, "kindCode": 81, "targetName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceName": "Grandmothered<PERSON><PERSON>", "linkGroup": 30}, "PersonCousinLinks": {"link": 1, "kindCode": 82, "targetName": "<PERSON><PERSON><PERSON><PERSON>", "sourceName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "linkGroup": 30}, "hash": {"cpp": "MYHash", "sql": "blob", "cppPassAsReference": "true", "header": "\"MYHash.h\"", "BJsonType": "hash"}, "trev": {"cpp": "<PERSON><PERSON><PERSON><PERSON>", "sql": "blob", "cppPassAsReference": "true", "header": "\"MYHash.h\"", "BJsonType": "trev"}, "hashNoHeader": {"cpp": "MYHash", "sql": "blob", "cppPassAsReference": "true", "BJsonType": "hash"}, "blob": {"cpp": "MYBlob", "sql": "text", "cppPassAsReference": "true", "BJsonType": "binary"}, "string": {"cpp": "std::string", "sql": "text", "header": "<string>", "cppPassAsReference": "true", "defaultValue": "\"\"", "BJsonType": "string"}, "mediatype": {"cpp": "MYMediaFileType::Enum", "sql": "int", "header": "\"MYMediaFileType.h\"", "defaultValue": "MYMediaFileType::NoType"}, "bjson": {"cpp": "MYBJsonRW", "cppPassAsReference": "true", "sql": "blob", "header": "\"../cloudshared/bjson.h\"", "BJsonType": "object"}, "char4": {"cpp": "std::string", "sql": "char(4)", "header": "<string>", "cppPassAsReference": "true", "defaultValue": "\"\"", "BJsonType": "string"}, "varchar128": {"cpp": "std::string", "sql": "<PERSON><PERSON><PERSON>(128)", "header": "<string>", "cppPassAsReference": "true", "defaultValue": "\"\"", "BJsonType": "string"}, "varchar128notNull": {"cpp": "std::string", "sql": "<PERSON><PERSON><PERSON>(128) not null", "header": "<string>", "cppPassAsReference": "true", "defaultValue": "\"\"", "BJsonType": "string"}, "int": {"cpp": "uint32_t", "sql": "int", "defaultValue": "0", "ctorInitDefault": true, "BJsonType": "varint"}, "MYDeviceId": {"cpp": "MYDeviceId", "sql": "int", "defaultValue": "MYDeviceId::Empty", "ctorInitDefault": true, "BJsonType": "varint"}, "MYFiles": {"cpp": "MYFiles", "sql": "text", "cppPassAsReference": "true", "header": "\"MYFiles.h\"", "BJsonType": "object"}, "MYLocalFiles": {"cpp": "MYLocalFiles", "sql": "text", "cppPassAsReference": "true", "header": "\"MYLocalFiles.h\"", "BJsonType": "object"}, "int16": {"cpp": "unsigned int", "sql": "int16", "defaultValue": "0", "ctorInitDefault": true, "BJsonType": "varint"}, "int64": {"cpp": "int64_t", "sql": "int", "defaultValue": "0", "ctorInitDefault": true, "cppPassAsReference": "true", "BJsonType": "varint"}, "integer": {"cpp": "unsigned int", "sql": "Integer", "defaultValue": "0", "ctorInitDefault": true, "BJsonType": "varint"}, "double": {"cpp": "double", "sql": "double", "defaultValue": "0", "ctorInitDefault": true, "BJsonType": "double64"}, "float": {"cpp": "float", "sql": "real", "defaultValue": "0", "ctorInitDefault": true, "BJsonType": "float32"}, "bool": {"cpp": "bool", "sql": "int", "defaultValue": "false", "ctorInitDefault": true, "BJsonType": "true1"}, "DateConfidenceType": {"cpp": "DateConfidenceType", "sql": "int", "cppForwardDeclare": "enum class DateConfidenceType : unsigned int;", "defaultValue": "0", "isEnum": "true", "ctorInitDefault": true, "BJsonType": "uint8"}, "EditingMasterType": {"cpp": "EditingMasterType", "sql": "int", "cppForwardDeclare": "enum class EditingMasterType : unsigned int;", "defaultValue": "0", "isEnum": "true", "ctorInitDefault": true, "BJsonType": "uint8"}, "MYDeviceType": {"cpp": "MYDeviceType", "sql": "int", "cppForwardDeclare": "enum class MYDeviceType : unsigned int;", "defaultValue": "0", "isEnum": "true", "ctorInitDefault": true, "BJsonType": "varint"}, "MYFilterIntention": {"cpp": "MYFilterIntention", "sql": "int", "defaultValue": "0", "cppForwardDeclare": "enum class MYFilterIntention : unsigned int;", "isEnum": "true", "ctorInitDefault": true, "BJsonType": "uint8"}, "MYFSPath": {"cpp": "MYFSPath", "sql": "text", "cppPassAsReference": "true", "header": "\"MYPathUtil.h\"", "BJsonType": "string"}, "MYDateTime": {"cpp": "MYDateTime", "sql": "int", "header": "\"MYDateTime.h\"", "BJsonType": "varint"}, "BigMYDateTime": {"cpp": "MYDateTime", "sql": "bigint", "header": "\"MYDateTime.h\"", "BJsonType": "varint"}, "MYSoftFetch": {"cpp": "MYSoftFetch", "sql": "int", "defaultValue": "0", "cppForwardDeclare": "enum class MYSoftFetch : unsigned int;", "isEnum": "true", "ctorInitDefault": true}, "MYAutoPreviewPriority": {"cpp": "MYAutoPreviewPriority", "sql": "int", "defaultValue": "0", "cppForwardDeclare": "enum class MYAutoPreviewPriority : unsigned int;", "isEnum": "true", "ctorInitDefault": true}, "MYAutoOriginalPriority": {"cpp": "MYAutoOriginalPriority", "sql": "int", "defaultValue": "0", "cppForwardDeclare": "enum class MYAutoOriginalPriority : unsigned int;", "isEnum": "true", "ctorInitDefault": true}}, "customSortOrders": {"types": ["ResourceSupercessionLinks", "FriendShipLink", "PersonChildrenLinks", "PersonSiblingLinks", "PersonParentLinks", "PersonUncleLinks", "PersonAuntLinks", "PersonGrandfatherLinks", "PersonGrandmotherLinks", "PersonCousinLinks", "GroupMembershipLink", "InvitedPersonsLinks", "RespondedPersonLinks", "AttendedPersonLinks", "BirthdayPersonLinks", "PartyEventParticipation", "EventOrganizingPartyLinks", "Folder", "Media", "Person", "Party", "Channel", "Telephone", "Email", "Internet", "Event", "FacebookCheckin", "FacebookEvent", "Link", "SequenceId", "LastKeyUsed", "Appointment", "Album", "NetworkNode", "EyeFiCard", "MediaContainer", "FaceRectangle", "PersistedTasks", "SharedConfiguration", "ImportSession", "ImportableResource", "CategoryCount", "FeatureCount", "DeletedResource", "ForgottenResource", "FilterHistoryItem", "PersistentTasks", "MediaAlbumLink", "FolderDeviceLink", "AuditEntry", "NAS", "MediaDeviceFieldStorage", "HalfResource", "NetworkNodeData", "IngestIgnoreDeviceLink", "FilesReceiving", "Configuration", "Sequences", "KindCode", "Counters", "Resource"], "hashReferences": ["Folder_Folder_ParentFolder", "Media_Media_ContainingFolder", "Media_Media_Place", "Media_Media_RootFolder", "Party_Party_SecondaryHomeLocation", "Party_Party_PrimaryHomeLocation", "Party_Party_WorkLocation", "Party_Party_WorkPhone", "Party_Party_HomePhone", "Party_Party_MobilePhone", "Party_Party_WorkEmail", "Party_Party_PrimaryEmail", "Party_Party_SecondaryEmail", "Party_Party_PrimaryWebsite", "Party_Party_SecondaryWebsite", "Party_Party_OtherLocation", "Party_Party_ProfileMedia", "Party_Party_OriginalProfileMedia", "Event_Event_ParentEvent", "Event_Event_Location", "Place_Place_CoverMedia", "Link_Link_SourceResource", "Link_Link_TargetResource", "NetworkNode_NetworkNode_DeviceLocation", "MediaContainer_Folder_CoverMedia", "MediaContainer_Event_CoverMedia", "MediaContainer_Album_CoverMedia", "MediaContainer_Folder_GeneratedCoverMedia", "MediaContainer_Event_GeneratedCoverMedia", "MediaContainer_Album_GeneratedCoverMedia", "FaceRectangle_FaceRectangle_Person", "FaceRectangle_FaceRectangle_Media", "ImportableResource_Folder_ImportSession", "ImportableResource_Media_ImportSession", "ImportableResource_Party_ImportSession", "ImportableResource_Event_ImportSession", "ImportableResource_Album_ImportSession", "MediaAlbumLink_MediaAlbumLink_SourceResource", "MediaAlbumLink_MediaAlbumLink_TargetResource", "FolderDeviceLink_FolderDeviceLink_Folder", "FolderDeviceLink_FolderDeviceLink_NetworkNode"], "referencedHeaders": ["<string>", "\"MYHash.h\"", "\"MYResourceReference.h\"", "\"MYPathUtil.h\"", "\"MYDateTime.h\""], "sqlFieldOrder": ["Id", "RevisionSourceHash", "KindCode", "Feature", "MediaHash", "MediaFileType", "AddToCacheTime", "AddToCacheTimeRemovable", "Category", "ContainingFolder", "DateCreated", "DayNumber", "Data", "DateLogged", "DeviceLocation", "DisplayOrder", "EndDateTime", "Extension", "FaceDetectionVersion", "FileNameNoExt", "Folder", "F<PERSON><PERSON><PERSON><PERSON><PERSON>", "GpsLat", "GpsLong", "GeneratedCoverMedia", "HomePhone", "ImportSession", "InvolvedInDuplication", "IsFlagged", "Key", "MobilePhone", "Label", "LocalFileNameNoExt", "LocalRootOrTemporaryPath", "LoggingDeviceId", "Media", "MonthNumber", "MustRecluster", "NonRAWFileDateTime", "NonRAWFileSize", "NonRAWLocalDataHash", "NonRAWOriginalDataHash", "NormalizedFileNameNoExt", "Name", "NetworkNode", "DeviceId", "OriginalProfileMedia", "OtherLocation", "ParentEvent", "ParentFolder", "Person", "PixelChecksumStorage", "PrimaryEmail", "PrimaryHomeLocation", "PrimaryWebsite", "ProfileMedia", "Rating", "RAWorVideoFileDateTime", "RAWorVideoFileSize", "RAWorVideoLocalDataHash", "RAWorVideoOriginalDataHash", "ResourceHash", "KindCode", "RevisionSource", "RootFolder", "SearchAttributes", "SecondaryEmail", "SecondaryHomeLocation", "SecondaryWebsite", "Source", "SourceResource", "StartDateTime", "StarRating", "TargetResource", "ToDo", "UniqueHash", "Value", "WorkEmail", "WorkLocation", "WorkPhone", "XMPFileDateTime", "XMPFileSize", "XMPLocalDataHash", "XMPOriginalDataHash", "YearNumber", "Action"]}}