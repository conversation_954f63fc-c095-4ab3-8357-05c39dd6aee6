#include <cstdio>
#include <iostream>

#include <openssl/pem.h>
#include <openssl/x509.h>
#include "X509.h"

X509Certificate::X509Certificate(std::string keyPEM, std::string certPEM)
		: x509_(pem_to_x509(certPEM)), pk_(pem_to_pk(keyPEM))

{
}

X509Certificate::X509Certificate() : pk_(makeKey()), x509_(makeX509(pk_.get()))
{
}

bool X509Certificate::sign()
{
	return X509_sign(x509_.get(), pk_.get(), EVP_sha256()) != 0;
}

bool X509Certificate::sign(X509Certificate &ca)
{
	issuer(ca.subject());
	return X509_sign(x509_.get(), ca.pk_.get(), EVP_sha256());
}

X509Name X509Certificate::issuer()
{
	auto name = X509_get_issuer_name(x509_.get());
	return X509Name(name);
}

X509Name X509Certificate::subject()
{
	auto name = X509_get_subject_name(x509_.get());
	return X509Name(name);
}

void X509Certificate::subject(const X509Name &value)
{
	X509_set_subject_name(x509_.get(), value.wrapped_);
}

void X509Certificate::issuer(const X509Name &value)
{
	X509_set_issuer_name(x509_.get(), value.wrapped_);
}

std::string X509Certificate::certificatePEM()
{
	return x509_to_pem(x509_.get());
}

std::string X509Certificate::keyPEM()
{
	return pk_to_pem(pk_.get());
}

/* Generates a 2048-bit RSA key. */
EVP_PKEY_ptr X509Certificate::makeKey()
{

	/* Allocate memory for the EVP_PKEY structure. */
	EVP_PKEY_ptr pkey(EVP_RSA_gen(2048), ::EVP_PKEY_free);

	/* The key has been generated, return it. */
	return pkey;
}

X509_ptr X509Certificate::makeX509(EVP_PKEY *pk)
{
	if (!pk)
		return X509_ptr(nullptr, ::X509_free);

	X509_ptr x509(X509_new(), ::X509_free);
	if (!x509)
		return x509;

	X509_set_version(x509.get(), 0);
	X509_set_pubkey(x509.get(), pk);
	ASN1_INTEGER_set(X509_get_serialNumber(x509.get()), 1);
	auto oneYear = 31536000L;
	X509_gmtime_adj(X509_get_notBefore(x509.get()), -(60 * 60 * 24 * 26));
	X509_gmtime_adj(X509_get_notAfter(x509.get()), 10 * oneYear);
	return x509;
}

std::string X509Certificate::bio_to_string(BIO *bio_out)
{
	BUF_MEM *bio_buf;
	BIO_get_mem_ptr(bio_out, &bio_buf);
	return std::string(bio_buf->data, bio_buf->length);
}

BIO_ptr X509Certificate::string_to_bio(const std::string &value)
{
	BIO_ptr bio(BIO_new(BIO_s_mem()), ::BIO_free);
	BIO_puts(bio.get(), value.c_str());
	return bio;
}

std::string X509Certificate::pk_to_pem(EVP_PKEY *pk)
{
	BIO_ptr bio(BIO_new(BIO_s_mem()), ::BIO_free);
	PEM_write_bio_PrivateKey(bio.get(), pk, NULL, NULL, 0, NULL, NULL);
	return bio_to_string(bio.get());
}

std::string X509Certificate::x509_to_pem(X509 *x509)
{
	BIO_ptr bio(BIO_new(BIO_s_mem()), ::BIO_free);
	PEM_write_bio_X509(bio.get(), x509);
	return bio_to_string(bio.get());
}

X509_ptr X509Certificate::pem_to_x509(const std::string &pem)
{
	auto bio = string_to_bio(pem);
	return X509_ptr(PEM_read_bio_X509(bio.get(), NULL, NULL, NULL), ::X509_free);
}

EVP_PKEY_ptr X509Certificate::pem_to_pk(const std::string &pem)
{
	auto bio = string_to_bio(pem);
	return EVP_PKEY_ptr(PEM_read_bio_PrivateKey(bio.get(), NULL, NULL, NULL), ::EVP_PKEY_free);
}

X509Name::X509Name(X509_NAME *wrapped) : wrapped_(wrapped)
{
}

std::string X509Name::commonName()
{
	return getText(NID_commonName);
}

std::string X509Name::countryName()
{
	return getText(NID_countryName);
}

std::string X509Name::org()
{
	return getText(NID_org);
}

void X509Name::commonName(const std::string &value)
{
	setText(NID_commonName, value);
}

void X509Name::setText(int nid, const std::string &value)
{
	X509_NAME_add_entry_by_NID(wrapped_, nid, MBSTRING_ASC, (unsigned char *)value.c_str(), -1, -1, 0);
}

void X509Name::countryName(const std::string &value)
{
	setText(NID_countryName, value);
}

void X509Name::org(const std::string &value)
{
	setText(NID_org, value);
}

std::string X509Name::getText(int nid)
{
	char buffer[1000];
	if (X509_NAME_get_text_by_NID(wrapped_, nid, buffer, sizeof(buffer)))
		return std::string(buffer);
	else
		return std::string();
}
