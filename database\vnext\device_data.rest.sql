

    drop view if exists a0."DeviceData" cascade;

    create or replace view a0."DeviceData" as
    select
    flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		deleted,
		encode(t, 'base64') as "t",
		encode(d, 'base64') as "d",
		device_id as "deviceId",
		build,
		last_access_time as "lastAccessTime",
		os,
		media_count as "mediaCount",
		protocol_version as "protocolVersion",
		version,
		last_startup_time as "lastStartupTime",
		last_hid_time as "lastHidTime",
		last_import_time as "lastImportTime",
		original_size::text as "originalSize",
		local_original_size::text as "localOriginalSize"
    from a0.device_data;
    

drop function if exists a0.device_data_create; 
        create function a0.device_data_create(
            _flags int,
	_modified_time timestamptz,
	_created_time timestamptz,
	_account_id int,
	_deleted boolean,
	__t text,
	__d text,
	_device_id int,
	_build text,
	_last_access_time timestamptz,
	_os text,
	_media_count int,
	_protocol_version int,
	_version text,
	_last_startup_time timestamptz,
	_last_hid_time timestamptz,
	_last_import_time timestamptz,
	__original_size text,
	__local_original_size text
        )
        returns a0."DeviceData"
        as $$
        
    declare
        result a0."DeviceData";
        _t bytea;
		_d bytea;
		_original_size int8;
		_local_original_size int8;
    begin
        _t := decode(__t, 'base64');
		_d := decode(__d, 'base64');
		_original_size := __original_size::int8;
		_local_original_size := __local_original_size::int8;
        


       
        


        
        
        _t := public.new_trev4(_device_id, 4);
        
        _modified_time := coalesce(_modified_time, now());
        _created_time := coalesce(_created_time, now());
        insert into a0.device_data (
            flags,
	modified_time,
	created_time,
	account_id,
	deleted,
	t,
	d,
	device_id,
	build,
	last_access_time,
	os,
	media_count,
	protocol_version,
	version,
	last_startup_time,
	last_hid_time,
	last_import_time,
	original_size,
	local_original_size
        )
        values(
            _flags,
			_modified_time,
			_created_time,
			_account_id,
			_deleted,
			_t,
			_d,
			_device_id,
			_build,
			_last_access_time,
			_os,
			_media_count,
			_protocol_version,
			_version,
			_last_startup_time,
			_last_hid_time,
			_last_import_time,
			_original_size,
			_local_original_size
        )
        returning
            flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		deleted,
		encode(t, 'base64') as "t",
		encode(d, 'base64') as "d",
		device_id as "deviceId",
		build,
		last_access_time as "lastAccessTime",
		os,
		media_count as "mediaCount",
		protocol_version as "protocolVersion",
		version,
		last_startup_time as "lastStartupTime",
		last_hid_time as "lastHidTime",
		last_import_time as "lastImportTime",
		original_size::text as "originalSize",
		local_original_size::text as "localOriginalSize"
        into result;

        



        perform a0.device_data_merkle(_account_id);

    
        



        return result;
    end;
        $$
        language plpgsql;
        

drop function if exists a0.device_data_update; 
        create function a0.device_data_update(
            _flags int,
	_modified_time timestamptz,
	_created_time timestamptz,
	_account_id int,
	_deleted boolean,
	__t text,
	__d text,
	_device_id int,
	_build text,
	_last_access_time timestamptz,
	_os text,
	_media_count int,
	_protocol_version int,
	_version text,
	_last_startup_time timestamptz,
	_last_hid_time timestamptz,
	_last_import_time timestamptz,
	__original_size text,
	__local_original_size text
        )
        returns a0."DeviceData"
        as $$
        
    declare
        result a0."DeviceData";
        _t bytea;
		_d bytea;
		_original_size int8;
		_local_original_size int8;
    begin
        _t := decode(__t, 'base64');
		_d := decode(__d, 'base64');
		_original_size := __original_size::int8;
		_local_original_size := __local_original_size::int8;
        


       
        


        
        _t := public.next_trev(_t);
        _modified_time := now();
        update a0.device_data
        set
            flags = _flags,
			modified_time = _modified_time,
			created_time = _created_time,
			deleted = _deleted,
			t = _t,
			d = _d,
			build = _build,
			last_access_time = _last_access_time,
			os = _os,
			media_count = _media_count,
			protocol_version = _protocol_version,
			version = _version,
			last_startup_time = _last_startup_time,
			last_hid_time = _last_hid_time,
			last_import_time = _last_import_time,
			original_size = _original_size,
			local_original_size = _local_original_size
        where account_id = _account_id and device_id = _device_id
        returning
            flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		deleted,
		encode(t, 'base64') as "t",
		encode(d, 'base64') as "d",
		device_id as "deviceId",
		build,
		last_access_time as "lastAccessTime",
		os,
		media_count as "mediaCount",
		protocol_version as "protocolVersion",
		version,
		last_startup_time as "lastStartupTime",
		last_hid_time as "lastHidTime",
		last_import_time as "lastImportTime",
		original_size::text as "originalSize",
		local_original_size::text as "localOriginalSize"
        into result;

        perform a0.device_data_merkle(_account_id);

    
        



        return result;
    end;
        $$
        language plpgsql;
        

drop function if exists a0.device_data_read_by_account_id_and_device_id; 
        create function a0.device_data_read_by_account_id_and_device_id(
            _account_id int,
	_device_id int
        )
        returns a0."DeviceData"
        as $$
        select
        flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		deleted,
		encode(t, 'base64') as "t",
		encode(d, 'base64') as "d",
		device_id as "deviceId",
		build,
		last_access_time as "lastAccessTime",
		os,
		media_count as "mediaCount",
		protocol_version as "protocolVersion",
		version,
		last_startup_time as "lastStartupTime",
		last_hid_time as "lastHidTime",
		last_import_time as "lastImportTime",
		original_size::text as "originalSize",
		local_original_size::text as "localOriginalSize"
        from a0.device_data
        where account_id = _account_id and device_id = _device_id;
        $$
        language sql;
        

drop function if exists a0.device_data_delete_by_account_id_and_device_id; 
        create function a0.device_data_delete_by_account_id_and_device_id(
            _account_id int,
	_device_id int
        )
        returns void
        as $$
        
    declare
        
        
    begin
        
        


       
        


        

    delete from a0.device_data
    where account_id = _account_id and device_id = _device_id;

    perform a0.device_data_merkle(_account_id);
    
        



        
    end;
        $$
        language plpgsql;
        

drop function if exists a0.device_data_delete_by_account_id; 
        create function a0.device_data_delete_by_account_id(
            _account_id int
        )
        returns void
        as $$
        
    declare
        
        
    begin
        
        


       
        


        

    delete from a0.device_data
    where account_id = _account_id;

    perform a0.device_data_merkle(_account_id);
    
        



        
    end;
        $$
        language plpgsql;
        

drop function if exists a0.device_data_find_by_account_id; 
        create function a0.device_data_find_by_account_id(
            _account_id int
        )
        returns setof a0."DeviceData"
        as $$
        select
        flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		deleted,
		encode(t, 'base64') as "t",
		encode(d, 'base64') as "d",
		device_id as "deviceId",
		build,
		last_access_time as "lastAccessTime",
		os,
		media_count as "mediaCount",
		protocol_version as "protocolVersion",
		version,
		last_startup_time as "lastStartupTime",
		last_hid_time as "lastHidTime",
		last_import_time as "lastImportTime",
		original_size::text as "originalSize",
		local_original_size::text as "localOriginalSize"
        from a0.device_data
        where account_id = _account_id;
        $$
        language sql;
        

drop function if exists a0.device_data_find_by_account_id_and_device_id; 
        create function a0.device_data_find_by_account_id_and_device_id(
            _account_id int,
	_device_id int
        )
        returns setof a0."DeviceData"
        as $$
        select
        flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		deleted,
		encode(t, 'base64') as "t",
		encode(d, 'base64') as "d",
		device_id as "deviceId",
		build,
		last_access_time as "lastAccessTime",
		os,
		media_count as "mediaCount",
		protocol_version as "protocolVersion",
		version,
		last_startup_time as "lastStartupTime",
		last_hid_time as "lastHidTime",
		last_import_time as "lastImportTime",
		original_size::text as "originalSize",
		local_original_size::text as "localOriginalSize"
        from a0.device_data
        where account_id = _account_id and device_id = _device_id;
        $$
        language sql;
        
