import crypto = require("crypto");
import { resolve } from "url";
import { config } from "./Config.mjs";

function randomBytes(size: number) {
  return new Promise<Buffer>((resolve, reject) => {
    crypto.randomBytes(size, (err, buffer) => {
      if (err) return reject(err);
      return resolve(buffer);
    });
  });
}

function pbkdf2(
  password: string,
  salt: string,
  size1: number,
  size2: number,
  algorithm: string
) {
  return new Promise<Buffer>((resolve, reject) => {
    crypto.pbkdf2(
      Buffer.from(password, "utf8"),
      Buffer.from(salt, "base64"),
      size1,
      size2,
      algorithm,
      (err, result) => {
        if (err) return reject(err);
        return resolve(result);
      }
    );
  });
}

export interface IPasswordHasher {
  (password: string, salt?: string): Promise<{
    salt: string;
    passwordHash: string;
  }>;
}

export function makeSalt(salt: string) {
  let p: Promise<string>;
  if (salt) {
    p = Promise.resolve(salt);
  } else {
    p = randomBytes(128).then((buffer) => {
      salt = buffer.toString("base64");
      return salt;
    });
  }
  return p;
}

export function v1(password: string, salt?: string) {
  if (!salt) {
    throw Error(
      "500|Password version 1 hasher is obsolete and may not be used to create new passwords"
    );
  }

  let passwordHash = crypto
    .createHash("sha512")
    .update(salt + password)
    .digest("hex");

  return Promise.resolve({ salt, passwordHash });
}

export function v2(password: string, salt?: string) {
  return makeSalt(salt)
    .then((result) => {
      salt = result;
      return pbkdf2(password, salt, 7000, 256, "sha512");
    })
    .then((buffer) => {
      return { salt, passwordHash: buffer.toString("base64") };
    });
}

export function chooseHasher(version: number) {
  if (version === 1) return v1;
  if (version === 2) return v2;
  throw Error("400|NO_PASSWORD_HASHER");
}
