\c account0;

CREATE OR REPLACE FUNCTION a0.mav(VA<PERSON>AD<PERSON> args TEXT[])
RETURNS TEXT AS $$
BEGIN
    RETURN array_to_string(args, '|');
END;
$$ 
LANGUAGE plpgsql;


truncate table a0.account;

ALTER SEQUENCE a0.account_account_id RESTART WITH 1;

truncate table a0.account;

ALTER SEQUENCE a0.account_account_id RESTART WITH 1;

insert into a0.account(account_id, idp, sub, email, password_hash, modified_time, created_time, password_hash_version, role)
values 
(nextval('a0.account_account_id'),'mylio','system','system','TTZlVKinNHEqKVx+xPJ2BYzNRTxCbL3cjBwfB6VK25ZUtcnKOfFd+zNfJ48bGJrkDL2CiSly03AKYzDjJr59R+451ppQt+waQu998yv8Ye+JX8yoxWnYw3NsN9CmRHzSksIgArFm8HWaYzetZ45m43xosOaFfp4OT2V67qEJDKgvaE+aa6u1+pfTP2haU2FNLVVlxGQif66se1cHT2L/t0lFSyx7iFDYegXrDsvrHPyy3OG2B+Pvj8Dij1uWytP7didMxu2+olJvWC/KbI62z0V4mP2kTKYShdxco0lRWRKkjyfzAjH3zz/cd9toLmwAKL4TXN3F6XAiUKX1BxWqww==',now(),now(), 2, 'user'),
(nextval('a0.account_account_id'),'mylio', '<EMAIL>','<EMAIL>','L5haLMh790v356t1I+a0SwYtTQxJo/j1BTXzNms+on/a8H4eEzXzOZFiroA+zjQaf/yuEn+2mV9EPzdZPcYP1nIu9vZOL4J1bDeQvogKrloBo4Fve0sBkdI7TdGqmPA8CARH0GfxjxnoHd55pnGR2h9Vjmj/64JRezgmCCuw1gkZ0Us8jZIwVQtGY1RsWSjCzjCw1ByVG5KjfLuYIQIOK55QDM6GnoRQQh3Lh3NKvZppVVQD3SQA+Zc3FtLgKoPHWyKIlW9MEBUuhXCqD9doSwOADwDbV0GUmykMxuNabVbqy5O4Ph8V2epDdKxWh9aP5YdVY2uagqri5h8T+lxW5g==',now(),now(), 2, 'admin'),
(nextval('a0.account_account_id'),'mylio','<EMAIL>','<EMAIL>','TTZlVKinNHEqKVx+xPJ2BYzNRTxCbL3cjBwfB6VK25ZUtcnKOfFd+zNfJ48bGJrkDL2CiSly03AKYzDjJr59R+451ppQt+waQu998yv8Ye+JX8yoxWnYw3NsN9CmRHzSksIgArFm8HWaYzetZ45m43xosOaFfp4OT2V67qEJDKgvaE+aa6u1+pfTP2haU2FNLVVlxGQif66se1cHT2L/t0lFSyx7iFDYegXrDsvrHPyy3OG2B+Pvj8Dij1uWytP7didMxu2+olJvWC/KbI62z0V4mP2kTKYShdxco0lRWRKkjyfzAjH3zz/cd9toLmwAKL4TXN3F6XAiUKX1BxWqww==',now(),now(), 2, 'user'),
(nextval('a0.account_account_id'),'mylio','<EMAIL>','<EMAIL>','eSaYRr6aK8b35Zyml8dhq94hD0i+g00YJ/6zNLR+jcKJ3h8gaciBlNI1JJZoHnMA2cH5WpNl2Kdv6VVx+noDKX1Q4p73cWqyfDXPj16beszwZHyXBKkc4oaSizAT0N/uyZx8rVUqybAdRzb9ckK+eKLfsPZmBdfM+7iASPI8awWqQGgnB6dqPyTP5FHMb4B3Pt+3EU1d8Qc6hjuNUkfn3lO8pzGVYvqc7RY275knM08MxQ7vRpRCl7isWe6GmEd5awNDiKq+sojUosy+G862S7ruVzoPgttcBnD6LwdP+TNMnfylnnz+ZCc+goUGaON4b7qEzkIA0wyGjo134HDhBw==',now(),now(), 2, 'user'),
(nextval('a0.account_account_id'),'mylio','<EMAIL>','<EMAIL>','13ea/UHn5Qh37rbmsPnQ1bYo/rBTw2q936QYB3CNCXU97ODg97aS7n5klr4Kz3khDGp6+VpcWUFRxIU1NBRtbKrO0WHXKArrYXMYOxu/CEA079ZS4/kKIU0iYj9tkNLgGqlvl5DS/XXHr6kDU3aadW+1sD9LaXX5JvySt4RyrW9VwejlGjOw5m2HnACSxnOjRwbQS/7bdcak8xI43K8TfH8dftkrz67eO3nlYjnoVCAQOerNfac3ppWGUsQ4kq9O5sHhTsimrT+iIZ070o7JWRvAhA+nYeyqUAQpNCNlcdObyeBb402P0wkPx1wgcVAw3qZFfh3QYPhF96M80TdZtg==', now(),now(), 2, 'admin'),
(nextval('a0.account_account_id'),'mylio','<EMAIL>','<EMAIL>','k4eCfGJCGi26jAyJ4abC99cea51NSKEi/dvYrDtfROe65trtLhv0KqF8ar+xeNxOGEi/xC6mQv9RiBW3nSDeJlm3Fotp92zu4uCosVoJ3kD9zLOZgwm2VcgBbzVPLcLJm1EPB4WuBR1cbUUiuybh5mlFPOFGv7KX/Vq2CAcoUzWiRGhMgypZszdabkaBNGWrUFDOrsoIS476YP4epN/khYn2ODuavA01H8yKUBe8i/qY6nu6KCVl6AKLpJ2IQqp1o39zAI3cwf/m/emXJeJhKEZJ02veSm9ZMPwQ0KuLIWcpPZ4SzcSXVyEYx4nPDkA5dwXS+8i9Y8l5bCBgiNKxPQ==', now(),now(), 2,  'user'),
(nextval('a0.account_account_id'),'mylio','<EMAIL>','<EMAIL>','6brZI9/nFrj2qiP5ao0D+17OIEF3paK+O9LtMLE/eNt7DrlcD0ByMqCjIBa54EicAjRSpAOEkRCltN8e6Hc2gMSH/lY/A3xAYVj0bO9co1SmcpqxN1EF2nQcHo2UzIeoMWyuFc5zmJ20dx1kDSvHa+aj/j0TlMr46qQAQaWUvr4nufvsMaa60T1GJTASHDNdQgfI71lYfUxmepZx5rlrqKz6qpEsnWdYtpxHaeMbtqHikt3zM5eJX2OVPJzbZ/Gk6OOO31AdFGQGqOmCQOTYLkAvvW3UcsNogqSE4n6Tus0RyS9Br2yjxeqstbYTCXX03N+30AJhlzDlFWCYLtUbqA==', now(),now(), 2, 'user'),
(nextval('a0.account_account_id'),'mylio','<EMAIL>','<EMAIL>','6brZI9/nFrj2qiP5ao0D+17OIEF3paK+O9LtMLE/eNt7DrlcD0ByMqCjIBa54EicAjRSpAOEkRCltN8e6Hc2gMSH/lY/A3xAYVj0bO9co1SmcpqxN1EF2nQcHo2UzIeoMWyuFc5zmJ20dx1kDSvHa+aj/j0TlMr46qQAQaWUvr4nufvsMaa60T1GJTASHDNdQgfI71lYfUxmepZx5rlrqKz6qpEsnWdYtpxHaeMbtqHikt3zM5eJX2OVPJzbZ/Gk6OOO31AdFGQGqOmCQOTYLkAvvW3UcsNogqSE4n6Tus0RyS9Br2yjxeqstbYTCXX03N+30AJhlzDlFWCYLtUbqA==', now(),now(), 2, 'admin');



truncate table a0.account_metadata;
insert into a0.account_metadata(account_id)
select account_id from a0.account;


do
$$
declare
	f_remote_control int4 = 1;
	f_permissions int4 = 2;
	f_limited_access_mode int4 = 4;
	f_encrypted_storage int4 = 8;
	f_guest_device_setup int4 = 16;
	f_collaborative_zone int4 = 32;
	f_multiple_camera_rolls int4 = 64;
	f_device_sync int4 = 128;
	f_fulldedup int4 = 256;
	f_shared_folders int4 = 512;
	f_paid int4 := f_shared_folders | f_fulldedup | f_device_sync;
	f_multi_user int4 = f_remote_control | f_permissions | f_limited_access_mode | f_encrypted_storage | f_guest_device_setup | f_collaborative_zone | f_multiple_camera_rolls;	


	s_storage text := '+storage-2tb|+storage-5tb';
	s_free text := '=free';
	s_personal text := '=personal';
	s_business text := '=business';
	s_business_group text := '=business-group';
	s_personal_group text := '=personal-group';
	s_free_av text := a0.mav(s_personal, s_business, s_personal_group, s_business_group);
	s_trial_av text := a0.mav(s_personal, s_business, s_personal_group, s_business_group);
	s_personal_av text := a0.mav(s_business, s_business_group, s_personal_group, s_storage);
	s_personal_group_av text := a0.mav(s_business_group, s_storage);
	s_business_av text :=  a0.mav(s_business_group,s_storage);
	s_business_group_av text := s_storage;

	fl_trial int4 = 1;
	fl_addon int4 = 2;
	fl_storage int4 = 4;
	fl_multi_user int4 = 8;
	fl_business int4 = 16;
begin
	truncate table a0.license_template;
	INSERT INTO a0.license_template(
		flags, 
		template_id, 
		duration, 
		uses, 
		public, 
		available_upgrades,
		display_name,
		weight,
		features,
		device_limit,
		photo_limit,
		cloud_storage_limit)
		VALUES 
(0  					, 'free'			, 'P100Y'	, 1		, true	,  s_free_av			, 'Mylio Photos'	, 100	, 0			, 1	, 2000000, 0),
(0						, 'personal'		, null		, 1		, false	,  s_personal_av		, 'Mylio Photos+'			, 400	, f_paid	, 64, 2000000, 0),
(fl_multi_user			, 'personal-group'	, null		, 1		, false  , s_personal_group_av	, 'Mylio Photos+ (Family)'	, 500	, f_paid | f_multi_user, 64, 2000000, 0),
(fl_business 			, 'business'		, null		, 1		, false,  s_business_av		, 'Mylio Photos+ (Pro)'		, 700	, f_paid	, 64, 2000000, 0),
(fl_multi_user | fl_business, 'business-group', null	, 1		, false, s_business_group_av	, 'Mylio Photos+ (Team)', 800	, f_paid | f_multi_user, 64, 2000000, 0),
(fl_storage | fl_addon, 'storage-2tb'		, null		, 1000	, false,  null					, 'Mylio Storage (2TB)', 0, 0, 0, 0, 2),
(fl_storage | fl_addon, 'storage-5tb'		, null		, 1000	, false,  null					, 'Mylio Storage (5TB)', 0, 0, 0, 0, 5);

update a0.license_template set created_time = now(), modified_time = now();


end;
$$
language plpgsql;



truncate table a0.system_property;
insert into a0.system_property(account_id, deleted, t, system_property_id, name, value, modified_time, created_time)
values
(1,false,decode('0200000000000000000000000000000000000000000000170a94e8aa0000000009', 'hex'),2,'build-2','{"deviceType":"2","buildNumber":2000,"uri":"https://s3/binary/2000/windows/setupMylio.zip"}', now(), now()),
(1,false,decode('0300000000000000000000000000000000000000000000170a94e8aa0000000009', 'hex'),3,'flickr','{"key": "x", "secret": "y"}', now(), now()),
(1,false,decode('ea000000000000000000000000000000000000000000003915b871850000000009', 'hex'),234,'buildOs34-64bit','{"filename":"Mylio_x64.msix","filesize":*********,"latestBuild":7640,"minOSVersionRequired":"10.0.18363","updatescript":"taskkill /IM Mylio* /F\npowershell \"Get-AppxPackage -Name Mylio -Publisher ''*OID.*******.4.1.311.********=Delaware, OID.*******.4.1.311.********=US, OID.*********'' | Remove-AppPackage\"\npowershell Add-AppPackage Mylio_x64.msix\nstart mylio:","uri":"https://downloads.mylio.com/Mylio_x64.msix"}', now(), now()),
(1,false,decode('35010000000000000000000000000000000000000000002715b871850000000009', 'hex'),309,'buildOs34-ARM64','{"filename":"Mylio_arm64.msix","filesize":70530185"latestBuild":7640,"minOSVersionRequired":"10.0.18363","updatescript":"taskkill /IM Mylio* /F\npowershell \"Get-AppxPackage -Name Mylio -Publisher ''*OID.*******.4.1.311.********=Delaware, OID.*******.4.1.311.********=US, OID.*********'' | Remove-AppPackage\"\npowershell Add-AppPackage Mylio_arm64.msix\nstart mylio:","uri":"https://downloads.mylio.com/Mylio_arm64.msix"}', now(), now()),
(1,false,decode('04000000000000000000000000000000000000000000007815b871850000000009', 'hex'),4,'buildOs33','{"latestBuild":7640,"uri":"https://d1bbrl7eztvwo3.cloudfront.net/osx/7640/Mylio.app.zip","filesize":178508456,"filename":"Mylio.app.zip","minOSVersionRequired":"10.14"}', now(), now()),
(1,false,decode('06000000000000000000000000000000000000000000003e0e3fb36f0000000009', 'hex'),6,'buildOs65','{"latestBuild":5812}', now(), now()),
(1,false,decode('07000000000000000000000000000000000000000000003e0e3fb36f0000000009', 'hex'),7,'buildOs66','{"latestBuild":5812}', now(), now()),
(1,false,decode('0500000000000000000000000000000000000000000000410db560210000000009', 'hex'),5,'buildOs34','{"latestBuild":5635,"uri":"http://mylio-builds.s3.amazonaws.com/windows/5635/SetupMylio.exe","filesize":275569992,"filename":"SetupMylio.exe","minOSVersionRequired":6.1}', now(), now()),
(1,false,decode('0b0000000000000000000000000000000000000000000006159cfa3f0000000009', 'hex'),11,'ImageTaggerModelVersion-Win','[{"Major":0,"Minor":1},{"Major":1,"Minor":3},{"Major":2,"Minor":0}]', now(), now()),
(1,false,decode('0c0000000000000000000000000000000000000000000006159cfa3f0000000009', 'hex'),12,'ImageTaggerModelVersion-Mac','[{"Major":0,"Minor":1},{"Major":1,"Minor":3},{"Major":2,"Minor":0}]', now(), now()),
(1,false,decode('0100000000000000000000000000000000000000000000170a94e8aa0000000009', 'hex'),1,'build-1','{"latestBuild":7370,"uri":"https://myliodownloads.s3.amazonaws.com/Mylio_Setup_Windows.exe","filesize":208696,"filename":"Mylio_Setup_Windows.exe","minOSVersionRequired":"10.0.18363"}', now(), now()),
(1,false,decode('0a0000000000000000000000000000000000000000000006159cfa3f0000000009', 'hex'),10,'ImageTaggerModelVersion-Android','[{"Major":0,"Minor":1},{"Major":1,"Minor":3},{"Major":2,"Minor":0}]', now(), now()),
(1,false,decode('98000000000000000000000000000000000000000000000b0cd94f240000000009', 'hex'),152,'interimBuildOs33','{"latestBuild":5509}', now(), now()),
(1,false,decode('9a000000000000000000000000000000000000000000000b0cd94f250000000009', 'hex'),154,'interimBuildOs65','{"latestBuild":5509}', now(), now()),
(1,false,decode('9c000000000000000000000000000000000000000000000b0cd94f250000000009', 'hex'),156,'interimBuildOs67','{"latestBuild":5509}', now(), now()),
(1,false,decode('eb00000000000000000000000000000000000000000000131034e7d60000000009', 'hex'),235,'buildOs34-32bit','{"latestBuild":6941,"uri":"http://mylio-builds.s3.amazonaws.com/windows/6941/SetupMylio32.exe","filesize":150687384,"filename":"SetupMylio32.exe","minOSVersionRequired":6.1}', now(), now()),
(1,false,decode('0d0000000000000000000000000000000000000000000006159cfa3f0000000009', 'hex'),13,'ImageTaggerModelVersion-iOS','[{"Major":0,"Minor":1},{"Major":1,"Minor":3},{"Major":2,"Minor":0}]', now(), now()),
(1,false,decode('08000000000000000000000000000000000000000000003e0e3fb3700000000009', 'hex'),8,'buildOs67','{"latestBuild":5812}', now(), now()),
(1,false,decode('99000000000000000000000000000000000000000000000b0cd94f240000000009', 'hex'),153,'interimBuildOs34','{"latestBuild":5509}', now(), now()),
(1,false,decode('9b000000000000000000000000000000000000000000000b0cd94f250000000009', 'hex'),155,'interimBuildOs66','{"latestBuild":5509}', now(), now());
select * from a0.system_property_merkle(1);

select * from a0.license_template;