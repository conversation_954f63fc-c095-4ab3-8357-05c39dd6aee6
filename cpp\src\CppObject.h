#pragma once

#include <node.h>
#include "MYTrev.h"
#include "bjson.h"
#include "MYLiterals.h"
#include "base64.h"
#include "MYHash.h"
#include "MYTrev.h"
#include "MYHash.h"
#include "helpers.h"

using v8::Array;
using v8::Date;
using v8::FunctionCallbackInfo;
using v8::Isolate;
using v8::Local;
using v8::Number;
using v8::Object;
using v8::String;
using v8::Value;

class CppArray;

class CppObject
{
private:
	v8::Local<v8::Object> _v8Object;
	v8::Isolate *_isolate;
	v8::Local<v8::String> s(const std::string &value) const;
	v8::Local<v8::Number> n(double value) const;

public:
	CppObject(v8::Isolate *isolate);
	CppObject(v8::Isolate *isolate, const v8::Local<v8::Object> &v8Object);
	v8::Local<v8::Object> New();
	void wrap(const v8::Local<v8::Object> &v8Object);
	v8::Local<v8::Object> unwrap();
	CppArray getArray(const std::string &pname);
	void setArray(const std::string &pname, CppArray &array);
	CppObject getObject(const std::string &pname);
	void setObject(const std::string &pname, CppObject &object);
	void setObject(int pname, CppObject &object);
	void setDate(const std::string &pname, uint64_t microseconds);
	bool hasValue(const std::string &pname) const;
	int32_t getInt32(const std::string &pname) const;
	void setString(const std::string &pname, const std::string &value);
	std::string getString(const std::string &pname) const;
	void setHash(const std::string &pname, const MYHash &value);
	void setNumber(const std::string &pname, double number);
	void setBool(const std::string &pname, bool value);
	bool getBool(const std::string &pname) const;
	void setTRev(const std::string &pname, const MYTRev &value);
	MYHash getHash(const std::string &pname) const;
	void setBinary(int pname, const std::vector<uint8_t> &buffer);
	void setBinary(const std::string &pname, const std::vector<uint8_t> &buffer);
	void setBinary(const std::string &pname, uint8_t *start, size_t size);

	template <typename TFunc>
	void forEachProperty(const TFunc &fnc)
	{
		auto arrKeys = _v8Object->GetPropertyNames();
		for (uint32_t i = 0; i < arrKeys->Length(); ++i)
		{
			auto v8Key = arrKeys->Get(i)->ToString();
			auto key = helpers::unwrap(v8Key);
			auto value = _v8Object->Get(v8Key);
			bool hasValue = !(value->IsNull() || value->IsUndefined());
			fnc(key, hasValue, value);
		}
	}

	template <typename TFunc>
	void forEachPropertyN(const TFunc &fnc)
	{
		auto arrKeys = _v8Object->GetPropertyNames();
		for (uint32_t i = 0; i < arrKeys->Length(); ++i)
		{
			auto v8Key = arrKeys->Get(i)->ToString();
			auto key = v8Key->NumberValue();
			auto value = _v8Object->Get(v8Key);
			bool hasValue = !(value->IsNull() || value->IsUndefined());
			fnc((int)key, hasValue, value);
		}
	}
};
