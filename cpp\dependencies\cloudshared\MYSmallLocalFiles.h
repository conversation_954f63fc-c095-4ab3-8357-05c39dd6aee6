#pragma once
#include "bjson.h"
#include <vector>
#include <map>
#include "MYHash.h"
#include "MYTRev.h"
#include "vector_map.h"
#include "MYMediaFileType.h"
#include "MYNeeds.h"
#include "MYLocalFiles.h"

#define WITH_FIXED_32
#ifdef WITH_FIXED_32
#define HashRefWrite Fixed32
#define HashRefRead fixed32
#else
#define HashRefWrite Uint32
#define HashRefRead asUint32
#endif

class MYSmallLocalFile;
class MYSmallLocalFiles;
class MYSmallBucketLocalFiles;

typedef uint32_t MYSmallHashRef;
typedef uint16_t MYStringRef;

extern const MYSmallHashRef MYSmallHashRefEmpty;
extern const MYStringRef MYStringRefEmpty;
extern std::string jpgFormat;

// For one media, one media type
class MYSmallLocalFile
{
public:
    MYSmallLocalFile()
    {
    }
    MYSmallLocalFile(const MYSmallLocalFile &);
    MYSmallLocalFile(MYSmallLocalFile &&other);

    MYSmallLocalFile &operator=(MYSmallLocalFile &&);
    MYSmallLocalFile &operator=(const MYSmallLocalFile &);

    MYSmallLocalFile(MYSmallBucketLocalFiles *MYSmallLocalFiles, MYMediaFileType::Enum mediaType) : _myLocalFiles(MYSmallLocalFiles), _mediaType(mediaType)
    {
        prepare();
    }
    MYSmallLocalFile(MYSmallBucketLocalFiles *MYSmallLocalFiles, MYMediaFileType::Enum mediaType, MYBJsonIterator &begin, const MYBJsonIterator &end);

    void initFromLocalFile(const MYLocalFiles &localFiles, const MYLocalFile &localFile);

    void deserializeFromBJson(MYBJsonIterator &begin, const MYBJsonIterator &end);

    const std::string &getFormat() const;
    bool setFormat(const std::string &newFormat);
    bool setFormat(std::string &&newFormat);

    MYMediaFileType::Enum getMediaType() const { return _mediaType; }

    const std::string &getRenamedFileNameNoExt() const;
    bool setRenamedFileNameNoExt(const std::string &newFileNameNoExt);
    bool setRenamedFileNameNoExt(std::string &&newFileNameNoExt);

    bool hasDataHash() const { return _dataHash != MYSmallHashRefEmpty; }
    const MYSmallHashRef getRefDataHash() const { return _dataHash; }
    bool setDataHash(const MYHash &newDataHash);

    bool hasVisualEditHashHash() const { return _visualEditHash != MYSmallHashRefEmpty; }
    const MYSmallHashRef getRefVisualEditHashHash() const { return _visualEditHash; }
    bool setVisualEditHash(const MYHash &newVisualEditHash);

    bool hasBasisDataHash() const { return _basisDataHash != MYSmallHashRefEmpty; }
    const MYSmallHashRef getRefBasisDataHash() const { return _basisDataHash; }
    bool setBasisDataHash(const MYHash &newBasisDataHash);

    float getCropZoomFactor() const { return _cropZoomFactor; }
    bool setCropZoomFactor(float newCropZoomFactor);

    const MYSmallHashRef getRefParseHash() const { return _parseHash; }
    bool setParseHash(const MYHash &newParseHash);

    bool reallyCantParse() const
    {
        return (_parseHash == _dataHash) && (getParsability() == MYParsability::NotParsable);
    }
    const MYParsability getParsability() const
    {
        if (_flags.test((int)MYLocalFileFlags::parseAttempted))
        {
            if (_flags.test((int)MYLocalFileFlags::parseFailed))
            {
                return MYParsability::NotParsable;
            }

            return MYParsability::Parsable;
        }

        return MYParsability::NotAttempted;
    }
    bool setParsability(MYParsability newParsability);

    bool getIsDraft() const { return _flags.test((int)MYLocalFileFlags::isDraft); }
    bool setIsDraft(bool newCantParse);

    bool getIsHardWant() const { return _flags.test((int)MYLocalFileFlags::hardWant); }
    bool setIsHardWant(bool newHardWant);

    bool getIsHardDontWant() const { return _flags.test((int)MYLocalFileFlags::hardDontWant); }
    bool setIsHardDontWant(bool newHardDontWant);

    const std::string &getALURL() const;
    bool setALURL(const std::string &newALUrl);
    bool setALURL(std::string &&newALUrl);

    bool getInInternalDataStorage() const { return _flags.test((int)MYLocalFileFlags::inInternalData); }
    bool setInInternalDataStorage(bool newInInternalDataStorage);

    bool isModified() const;
    std::bitset<16> _modifiedFields;

private:
    MYSmallHashRef getHashRef(const MYHash &hash)
    {
        uint32_t hashEntry;

        uint8_t *p = (uint8_t *)&hashEntry;
        p[0] = hash.raw[3];
        p[1] = hash.raw[2];
        p[2] = hash.raw[1];
        p[3] = hash.raw[0];

        return hashEntry;
    }

    MYSmallBucketLocalFiles *_myLocalFiles;

    friend MYSmallLocalFiles;

    MYSmallHashRef _dataHash;
    MYSmallHashRef _visualEditHash;
    MYSmallHashRef _basisDataHash;
    MYSmallHashRef _parseHash;

    float _cropZoomFactor;

    MYStringRef _fileNameNoExt;
    MYStringRef _alURL;
    MYStringRef _format;

    std::bitset<8> _flags; // Of type MYLocalFileFlags
    bool _dirty = true;
    MYMediaFileType::Enum _mediaType = MYMediaFileType::Enum::NoType;

    BJSONINLINE void init(MYSmallBucketLocalFiles *local, MYMediaFileType::Enum type)
    {
        _myLocalFiles = local;
        _mediaType = type;
    }

    void clear();
    void prepareInternal();
    BJSONINLINE void prepare()
    {
        if (_dirty)
        {
            prepareInternal();
        }
    }

    bool empty() const
    {
        if (_dirty)
        {
            return true;
        }

        if (_format != MYStringRefEmpty)
        {
            return false;
        }

        if (_dataHash != MYSmallHashRefEmpty)
        {
            return false;
        }

        if (_visualEditHash != MYSmallHashRefEmpty)
        {
            return false;
        }

        if (_basisDataHash != MYSmallHashRefEmpty)
        {
            return false;
        }

        if (!_flags.any())
        {
            return false;
        }

        if (_cropZoomFactor != 1.0f)
        {
            return false;
        }

        if (_alURL != MYStringRefEmpty)
        {
            return false;
        }

        if (_fileNameNoExt != MYStringRefEmpty)
        {
            return false;
        }

        if (_parseHash != MYSmallHashRefEmpty)
        {
            return false;
        }

        return true;
    }

    template <typename TBJson>
    void serializeToBJson(TBJson &writer) const
    {
        assert(!_dirty);

        if (_format != MYStringRefEmpty)
        {
            writer.Uint32(MYLiterals::LocalFile::format, _format);
        }

        if (_dataHash != MYSmallHashRefEmpty)
        {
            writer.HashRefWrite(MYLiterals::LocalFile::dataHash, _dataHash);
        }

        if (_visualEditHash != MYSmallHashRefEmpty)
        {
            writer.HashRefWrite(MYLiterals::LocalFile::visualEditHash, _visualEditHash);
        }

        if (_basisDataHash != MYSmallHashRefEmpty)
        {
            writer.HashRefWrite(MYLiterals::LocalFile::basisDataHash, _basisDataHash);
        }

        if ((uint32_t)_flags.to_ulong() > 0)
        {
            writer.Uint32(MYLiterals::LocalFile::flags, (uint32_t)_flags.to_ulong() - 1);
        }

        if (_cropZoomFactor != 1.0f)
        {
            writer.Double(MYLiterals::LocalFile::cropZoomFactor, _cropZoomFactor);
        }

        if (_alURL != MYStringRefEmpty)
        {
            writer.Uint32(MYLiterals::LocalFile::alUrl, _alURL);
        }

        if (_fileNameNoExt != MYStringRefEmpty)
        {
            writer.Uint32(MYLiterals::LocalFile::fileNameNoExt, _fileNameNoExt);
        }

        if (_parseHash != MYSmallHashRefEmpty)
        {
            writer.HashRefWrite(MYLiterals::LocalFile::parseHash, _parseHash);
        }
    }
};

// For one media, across all media type
class MYSmallLocalFiles
{
public:
    MYSmallLocalFiles() {}
    MYSmallLocalFiles(MYSmallBucketLocalFiles *bucketLocal) : _bucketLocal(bucketLocal)
    {
        initLocalFileArray();
    }
    MYSmallLocalFiles(MYSmallBucketLocalFiles *bucketLocal, MYBJsonIterator &iter, const MYBJsonIterator &end, bool reInit);
    MYSmallLocalFiles(MYSmallBucketLocalFiles *bucketLocal, MYBJsonIterator &iter, const MYBJsonIterator &end);
    MYSmallLocalFiles(const MYSmallLocalFiles &);
    MYSmallLocalFiles(MYSmallLocalFiles &&other);

    void deserializeFromBJson(MYBJsonIterator &begin, const MYBJsonIterator &end);
    void clear();
    void initLocalFileArray();
    void initFromLocalFiles(const MYLocalFiles &localFiles);

    MYSmallLocalFiles &operator=(const MYSmallLocalFiles &);
    MYSmallLocalFiles &operator=(MYSmallLocalFiles &&other);

    bool operator==(const MYSmallLocalFiles &other) const;

    template <typename TBJson>
    void serializeToBJson(TBJson &writer) const
    {
        bool first = true;

        for (const auto &file : _knownMediaFiles)
        {
            if (!file.empty())
            {
                if (first)
                {
                    writer.StartObject(file.getMediaType());
                    first = false;
                }
                else
                {
                    writer.Separator(file.getMediaType());
                }

                file.serializeToBJson(writer);
            }
        }

        for (const auto &file : _extendedMediaFiles)
        {
            if (!file.empty())
            {
                if (first)
                {
                    writer.StartObject(file.getMediaType());
                    first = false;
                }
                else
                {
                    writer.Separator(file.getMediaType());
                }

                file.serializeToBJson(writer);
            }
        }

        if (!first)
        {
            writer.EndObject();
        }
    }

    const MYSmallLocalFile &getMediaFileOrEmpty(MYMediaFileType::Enum mediaFileType) const;
    MYSmallLocalFile *getMediaFileOrNull(MYMediaFileType::Enum mediaFileType);
    const MYSmallLocalFile *getMediaFileOrNull(MYMediaFileType::Enum mediaFileType) const;
    MYSmallLocalFile *getOrCreateMediaFile(MYMediaFileType::Enum mediaFileType);

    const NeedsBits getSupportMediaTypes() const;

    bool isModified() const;
    std::bitset<16> _modifiedFields;

    bool empty() const;

private:
    friend MYSmallLocalFile;
    friend MYSmallLocalFile;
    friend class Test;

    MYSmallBucketLocalFiles *_bucketLocal;
    std::vector<MYSmallLocalFile> _extendedMediaFiles;
    std::array<MYSmallLocalFile, 7> _knownMediaFiles;
};

class MYSmallBucketLocalFiles
{
public:
    typedef vector_map<MYHash, MYSmallLocalFiles> MYMediaMapType;

    MYSmallBucketLocalFiles(size_t estimatedMediaPerBucket) : _estimatedMediaPerBucket(estimatedMediaPerBucket)
    {
#if !defined(_DEBUG) && !defined(DEBUG)
        // We don't reserve in debug, because we want to take re-allocations. Otherwise it will hide bugs if
        // the pointer values always remain the same.
        _media.reserve(estimatedMediaPerBucket);
#endif
    }

    MYSmallBucketLocalFiles(size_t estimatedMediaPerBucket, MYBJsonIterator &begin, const MYBJsonIterator &end);
    MYSmallBucketLocalFiles(const MYSmallBucketLocalFiles &) = delete;
    MYSmallBucketLocalFiles(MYSmallBucketLocalFiles &&other);
    MYSmallBucketLocalFiles &operator=(const MYSmallBucketLocalFiles &) = delete;
    MYSmallBucketLocalFiles &operator=(MYSmallBucketLocalFiles &&other) = delete;

    bool operator==(const MYSmallBucketLocalFiles &other) const = delete;

    void deserializeFromBJson(MYBJsonIterator &begin, const MYBJsonIterator &end);
    void clear();

    template <typename TBJson>
    void serializeToBJson(TBJson &writer) const
    {
        prepare();

        if (!_media.empty())
        {
            bool first = true;
            writer.StartObject(MYLiterals::BucketLocalFiles::media);
            for (const auto &mediaFiles : _media)
            {
                if (!mediaFiles.second.empty())
                {
                    if (!first)
                    {
                        writer.Separator();
                    }
                    first = false;

                    writer.MYHash(mediaFiles.first);
                    mediaFiles.second.serializeToBJson(writer);
                }
            }
            writer.EndObject();
        }

        if (!_stringMap.empty())
        {
            writer.StartArray(MYLiterals::BucketLocalFiles::stringMap);
            for (const auto &stringEntry : _stringMap)
            {
                writer.String(0, stringEntry);
            }
            writer.EndArray();
        }
    }

    MYSmallLocalFiles *getOrCreateFilesForMedia(const MYHash &media);

    MYMediaMapType &getFiles()
    {
        prepare();
        return _media;
    }

    const MYMediaMapType &getFiles() const
    {
        prepare();
        return _media;
    }

    bool isModified() const;
    std::bitset<16> _modifiedFields;

    bool empty() const;

private:
    void prepareInternal() const
    {
        _media.clear();
        _dirty = false;
    }

    BJSONINLINE void prepare() const
    {
        if (_dirty)
        {
            prepareInternal();
        }
    }

    MYStringRef getOrCreateStringRef(const std::string &string);
    MYStringRef getOrCreateStringRef(std::string &&string);
    const std::string &getString(MYStringRef stringRef) const;
    std::vector<std::string> _stringMap;

    friend class MYSmallLocalFile;
    friend class Test;

    mutable bool _dirty = false;
    mutable MYMediaMapType _media;

    size_t _estimatedMediaPerBucket = 0;
};
