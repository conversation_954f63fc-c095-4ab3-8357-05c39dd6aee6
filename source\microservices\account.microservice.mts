import { AccountService } from "../services/AccountService.mjs";
import { DeviceService } from "../services/DeviceService.mjs";
import { MessageService } from "../services/MessageService.mjs";
import { SystemService } from "../services/SystemService.mjs";
import { AccountRestDataService } from "../dataServices/Account.rest.dataService.mjs";
import { AccountMetadataRestDataService } from "../dataServices/AccountMetadata.rest.dataService.mjs";
import { DeviceRestDataService } from "../dataServices/Device.rest.dataService.mjs";
import { MessageRestDataService } from "../dataServices/Message.rest.dataService.mjs";
import { SystemPropertyRestDataService } from "../dataServices/SystemProperty.rest.dataService.mjs";
import { EmailService } from "../services/EmailService.mjs";
import { tx } from "../system/Postgres.mjs";
import { TokenService } from "../services/TokenService.mjs";
import { DeviceSyncService } from "../services/Device.sync.service.mjs";
import { DeviceDataSyncService } from "../services/DeviceData.sync.service.mjs";
import { MessageSyncService } from "../services/Message.sync.service.mjs";
import { SystemPropertySyncService } from "../services/SystemProperty.sync.service.mjs";
import { config, getServices, loadConfig } from "../system/Config.mjs";
import { UserPropertySyncService } from "../services/UserProperty.sync.service.mjs";
import { BacktraceService } from "../services/BacktraceService.mjs";
import { AmazonService } from "../services/AmazonService.mjs";
import { GoogleService } from "../services/GoogleService.mjs";
import { InstagramService } from "../services/InstagramService.mjs";
import { MicrosoftService } from "../services/MicrosoftService.mjs";
import { RefreshTokenService } from "../services/RefreshTokenService.mjs";
import { RefreshTokenRestDataService } from "../dataServices/RefreshToken.rest.dataService.mjs";
import { LockService } from "../services/LockService.mjs";
import { TelemetryService } from "../services/TelemetryService.mjs";
import { TelemetryDataService } from "../dataServices/TelemetryDataService.mjs";
import { DeviceDataRestDataService } from "../dataServices/DeviceData.rest.dataService.mjs";
import { LicenseService } from "../services/LicenseService.mjs";
import { LicenseRestDataService } from "../dataServices/License.rest.dataService.mjs";
import { LicenseTemplateRestDataService } from "../dataServices/LicenseTemplate.rest.dataService.mjs";
import { AuthenticationService } from "../services/AuthenticationService.mjs";
import { PinRestDataService } from "../dataServices/Pin.rest.dataService.mjs";
import FastSpringService from "../services/FastSpringService.mjs";
import AppStoreService from "../services/AppStoreService.mjs";
import { IdpService } from "../services/IdpService.mjs";
import { BackblazeService } from "../services/BackblazeService.mjs";
import FastSpringCloudClient from "../services/FastSpringCloudClient.mjs";
import { ResourceService } from "../services/ResourceService.mjs";
import { MultiuserService } from "../services/MultiuserService.mjs";
import { InvitationRestDataService } from "../dataServices/Invitation.rest.dataService.mjs";
import { InvitationLogEntryRestDataService } from "../dataServices/InvitationLogEntry.rest.dataService.mjs";

export class AccountMicroService {
  public accountService: AccountService;
  public deviceService: DeviceService;
  public messageService: MessageService;
  public systemService: SystemService;
  public emailService: EmailService;
  public tokenService: TokenService;
  public amazonService: AmazonService;
  public backtraceService: BacktraceService;
  public refreshTokenService: RefreshTokenService;
  public lockService: LockService;
  public googleService: GoogleService;
  public instagramService: InstagramService;
  public microsoftService: MicrosoftService;
  public telemetryService: TelemetryService;
  public maintenanceMode: boolean;
  public licenseService: LicenseService;
  public multiuserService: MultiuserService;
  backblazeService: BackblazeService;
  authenticationService: AuthenticationService;
  fastSpringService: FastSpringService;
  appStoreService: AppStoreService;
  idpService: IdpService;
  fsCloud: FastSpringCloudClient;
  resourceService: ResourceService;

  public test() {
    return new Promise((resolve, reject) => {
      resolve(true);
    });
  }

  public async start() {
    await loadConfig();
    this.maintenanceMode = false;
    this.lockService = new LockService();
    this.tokenService = new TokenService();
    this.emailService = new EmailService(this.tokenService);
    this.deviceService = new DeviceService(
      new DeviceRestDataService(),
      new DeviceDataRestDataService(),
      tx
    );
    this.messageService = new MessageService(new MessageRestDataService(), tx);
    this.accountService = new AccountService(
      new AccountRestDataService(),
      this.deviceService,
      this.emailService,
      new LicenseRestDataService()
    );

    this.systemService = new SystemService(
      new SystemPropertyRestDataService(),
      new AccountMetadataRestDataService(),
      tx,
      [
        this.accountService,
        new DeviceSyncService(),
        new DeviceDataSyncService(),
        new MessageSyncService(),
        new SystemPropertySyncService(),
        new UserPropertySyncService(),
      ]
    );
    this.backtraceService = new BacktraceService();
    this.refreshTokenService = new RefreshTokenService(
      new RefreshTokenRestDataService(),
      tx
    );
    this.amazonService = new AmazonService(
      this.refreshTokenService,
      this.deviceService,
      this.lockService
    );
    this.googleService = new GoogleService(
      this.refreshTokenService,
      this.deviceService,
      this.lockService
    );
    this.instagramService = new InstagramService(this.refreshTokenService);
    this.microsoftService = new MicrosoftService(
      this.refreshTokenService,
      this.deviceService,
      this.lockService
    );
    this.telemetryService = new TelemetryService(new TelemetryDataService());

    this.licenseService = new LicenseService(
      new LicenseRestDataService(),
      this.accountService,
      new LicenseTemplateRestDataService()
    );

    this.authenticationService = new AuthenticationService(
      this.accountService,
      this.emailService,
      this.tokenService,
      this.deviceService,
      new PinRestDataService(),
      this.licenseService
    );
    this.fsCloud = new FastSpringCloudClient(config.fastspring.api_username, config.fastspring.api_password);
    this.fastSpringService = new FastSpringService(
      this.accountService,
      this.licenseService,
      this.fsCloud
    );

    this.appStoreService = new AppStoreService(this.licenseService);
    this.idpService = new IdpService(
      this.accountService,
      this.authenticationService,
      this.tokenService,
      this.licenseService
    );

    this.backblazeService = new BackblazeService();


    this.resourceService = new ResourceService();

    this.multiuserService = new MultiuserService(
      new InvitationRestDataService(),
      new InvitationLogEntryRestDataService(),
      this.accountService,
      this.emailService,
      this.tokenService
    );

  }
}

export let microservice = new AccountMicroService();
