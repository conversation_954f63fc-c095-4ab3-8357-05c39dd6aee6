
create extension if not exists postgres_fdw;
create schema old_a0;
CREATE SERVER account_server
        FOREIGN DATA WRAPPER postgres_fdw
        OPTIONS (host 'account0.c04zdcgu39tj.us-west-2.rds.amazonaws.com', port '5432', dbname 'account0');
CREATE USER MAPPING FOR superuser
        SERVER account_server
        OPTIONS (user 'superuser', password 'dFnrG8pe2B03iY6sVGqJ' );
ALTER SERVER account_server OPTIONS (ADD use_remote_estimate 'true');
IMPORT FOREIGN SCHEMA a0
    FROM SERVER account_server
    INTO old_a0;





truncate table a0.account_metadata;
INSERT INTO a0.account_metadata(
	flags, modified_time, created_time, account_id, account_merkle, 
	device_merkle, message_merkle, system_property_merkle, 
	user_property_merkle, device_data_merkle, next_device_id, 
	next_message_id, bootstrap_device_id, next_license_id, next_system_property_id
)
SELECT flags, modified_time, modified_time, account_id, account_merkle, device_merkle, message_merkle, 
	system_property_merkle, user_property_merkle, device_data_merkle, next_device_id, 
	next_message_id, bootstrap_device_id, 0, 0
FROM old_a0.account_metadata
where account_id > 1;

truncate table a0.device;
INSERT INTO a0.device(
	flags, modified_time, created_time, account_id, deleted, t, d, device_id, name, device_type, nickname, encrypt, creation_time, long_id, support_ticket)
SELECT flags, modified_time, modified_time, account_id, deleted, t, d, device_id, name, device_type, nickname, encrypt, creation_time, long_id, support_ticket
	FROM old_a0.device;	

truncate table a0.device_data;
INSERT INTO a0.device_data(
	flags, modified_time, created_time, account_id, deleted, t, d, device_id, build, last_access_time, os, media_count, protocol_version, 
	version, last_startup_time, last_hid_time, last_import_time)
SELECT flags, modified_time, modified_time, account_id, deleted, t, d, device_id, build, last_access_time, os, media_count, protocol_version, 
	version, last_startup_time, last_hid_time, last_import_time
	FROM old_a0.device_data;

truncate table a0.lock;
INSERT INTO a0.lock(
	account_id, key, ticket, expire)
SELECT account_id, key, ticket, expire
	FROM old_a0.lock;
	
truncate table a0.message;
INSERT INTO a0.message(
	flags, modified_time, created_time, account_id, deleted, t, d, device_id, message_id, seconds_to_display, displayed, message, link)
SELECT flags, modified_time, modified_time, account_id, deleted, t, d, device_id, message_id, seconds_to_display, displayed, message, link
	FROM old_a0.message;

truncate table a0.pin;
INSERT INTO a0.pin(
	flags, modified_time, created_time, code_challenge, email, pin, expires_at)
SELECT flags, modified_time, modified_time, code_challenge, email, pin, expires_at
	FROM old_a0.pin;	

truncate table a0.refresh_token;
INSERT INTO a0.refresh_token(
	flags, modified_time, created_time, account_id, idp, task, sub, token, email)
SELECT flags, modified_time, modified_time, account_id, idp, task, sub, token, email
	FROM old_a0.refresh_token;
	
	
truncate table a0.user_property;
INSERT INTO a0.user_property(
	flags, modified_time, created_time, account_id, deleted, t, d, user_property_id, name, value)
SELECT flags, modified_time, modified_time, account_id, deleted, t, d, user_property_id, name, value
	FROM old_a0.user_property;
	
	
select * 
into temp table old_account
from old_a0.account;
create index ix_old_account_by_account_id on old_account(account_id);

truncate table a0.account;
INSERT INTO a0.account(
	flags, modified_time, created_time, account_id, t, cipher, min_build, peer_to_peer_key, rsa_private_key, x509_cert, tfa, 
	idp, sub, email, password_hash, password_hash_version, salt, password_set_time, plan_id, role, device_limit, photo_limit, 
	features, next_plan_date, affiliate_id)
SELECT flags, modified_time, modified_time, account_id, t, cipher, min_build, peer_to_peer_key, rsa_private_key, x509_cert, 
		case when flags & 32 = 32 then true else false end, idp, sub, email, password_hash, password_hash_version, salt, password_set_time, plan_id, role, 64, 
		2000000, case plan_id when '1000' then 1 else 3 end, next_plan_date, affiliate_id
FROM old_account;

select a0.account_merkle(account_id)
from a0.account;


select *
into temp table old_apple_purchase
from old_a0.apple_purchase;
create index ix_old_apple_purchase_by_account_id on old_apple_purchase(account_id);

drop table if exists a0.active_stripe_accounts;
CREATE TABLE IF NOT EXISTS a0.active_stripe_accounts
(
    customer_id text COLLATE pg_catalog."default" NOT NULL,
    license_id text COLLATE pg_catalog."default" NOT NULL,
    price_id text COLLATE pg_catalog."default",
    current_period_end timestamp with time zone,
	cancel_at_period_end boolean,
    CONSTRAINT active_stripe_accounts_pkey PRIMARY KEY (customer_id, license_id)
);

alter user cloud with password 'MTkvBLytQjz8EPUyVWvXLQHZ';
alter user datawarehouse with password '4r7W37RBq6W7xEMPXYiN8KUK'

truncate table a0.subscription;
INSERT INTO a0.subscription(
	flags, 
    modified_time, 
    created_time, 
    license_id, 
    account_id, 
    manager, 
    featureset_id, 
    end_date, 
    manager_ref, 
    template_id, 
    template_key, 
    device_limit, 
    photo_limit, 
    features, 
    deleted, 
    canceled
)
select 
    0,
    now(),
    now(),
    a0.next_license_id(account_id),
    account_id,
    null,
    '1000',
    now() + '100 years'::interval,
    null,
    '1000',
    null,
    null,
    null,
    null,
    false,
    false
from old_account
where account_id > 1;

INSERT INTO a0.subscription(
	flags, 
    modified_time, 
    created_time, 
    license_id, 
    account_id, 
    manager, 
    featureset_id, 
    end_date, 
    manager_ref, 
    template_id, 
    template_key, 
    device_limit, 
    photo_limit, 
    features, 
    deleted, 
    canceled
)
select 
    0,
    now(),
    now(),
    a0.next_license_id(account_id),
    account_id,
    'S',
    split_part(s.price_id, '_', 1),
    s.current_period_end,
    s.customer_id || '.' || s.license_id,
    s.price_id,
    null,
    null,
    null,
    null,
    s.cancel_at_period_end,
    false
from old_account a
join a0.active_stripe_accounts s on s.customer_id = a.stripe
where account_id > 1;

INSERT INTO a0.subscription(
	flags, 
    modified_time, 
    created_time, 
    license_id, 
    account_id, 
    manager, 
    featureset_id, 
    end_date, 
    manager_ref, 
    template_id, 
    template_key, 
    device_limit, 
    photo_limit, 
    features, 
    deleted, 
    canceled
)
select 
    0,
    now(),
    now(),
    a0.next_license_id(account_id),
    account_id,
    null,
    case plan_id
		when '1020' then '1015'
		when '1021' then '2013'
		when '2021' then '2013'
	end,
    next_plan_date,
    null,
	case plan_id
		when '1020' then 'TRIAL'
		when '1021' then 'CREATE_3_YEAR'
		when '2021' then 'CREATE_3_YEAR'
    end,
    null,
    null,
    null,
    null,
    case when next_plan_date < now() then true else false end,
    false
from old_account
where account_id > 1
and plan_id in ('1020', '1021', '2021');


INSERT INTO a0.subscription(
	flags, 
    modified_time, 
    created_time, 
    license_id, 
    account_id, 
    manager, 
    featureset_id, 
    end_date, 
    manager_ref, 
    template_id, 
    template_key, 
    device_limit, 
    photo_limit, 
    features, 
    deleted, 
    canceled
)
select 
    0,
    now(),
    now(),
    a0.next_license_id(a.account_id),
    a.account_id,
    'A',
    '1015',
    s.expiration_date,
    s.original_transaction_id,
    s.product_id,
    null,
    null,
    null,
    null,
    false,
    case when expiration_date < now() then true else false end
from old_account a
join old_apple_purchase s on s.account_id = a.account_id
where a.account_id > 1
and plan_id = '1022';


select a0.account_delete_by_account_id(account_id)
from a0.account where email = '<EMAIL>';




	





