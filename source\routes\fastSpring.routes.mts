import express from "express";
import { microservice as g } from "../microservices/account.microservice.mjs";
import { b, safeAny, secure } from "../system/safe.mjs";

export default function addFastSpringRoutes(router: express.Router) {
  router.post(
    "/accounts/:aid/fastspring/encrypt-contact",
    safeAny,
    secure,
    async (req, res, next) => {
      let context = req.context;
      const payload = await g.fastSpringService.enryptContact(
        context,
        context.aid,
        context.any.firstName,
        context.any.lastName
      );
      res.status(200).json(payload);
    }
  );


  router.post("/accounts/:aid/cloud-storage", safeAny, secure, async (req, res, next) => {
    let context = req.context;
    let payload = await g.fastSpringService.buyCloudStorage(context, context.aid, context.any.qty, context.any.preview);
    res.status(200).json(payload);
  });


  router.get("/fastspring/products/:id", safeAny, async (req, res, next) => {
    let context = req.context;
    let payload = await g.fastSpringService.getProduct(context, req.params.id);
    res.status(200).json(payload);
  });

  router.get("/accounts/:aid/fastspring/subscriptions", safeAny, secure, async (req, res, next) => {
    let context = req.context;
    let payload = await g.fastSpringService.getSubscriptions(context, context.aid);
    res.status(200).json(payload);
  });

  router.get("/fastspring/products/price/:id", safeAny, secure, async (req, res, next) => {
    let context = req.context;
    let payload = await g.fastSpringService.getProductPrice(context, req.params.id);
    res.status(200).json(payload);
  });

  router.get("/fastspring/products/price/:id/:country", safeAny, secure, async (req, res, next) => {
    let context = req.context;
    let payload = await g.fastSpringService.getProductPriceByCountry(context, req.params.id, req.params.country);
    res.status(200).json(payload);
  });

  router.get("/accounts/:aid/fastspring/customer-data", safeAny, secure, async (req, res, next) => {
    let context = req.context;
    let payload = await g.fastSpringService.getFastSpringCustomerData(context, context.aid);
    res.status(200).json(payload);
  });


  router.delete("accounts/:aid/fastspring/:subscriptionId", safeAny, secure, async (req, res, next) => {
    let context = req.context;
    const immediate = b(req.query.immediate);
    let payload = await g.fastSpringService.cancelSubscription(context, context.aid, req.params.subscriptionId, immediate);
    res.status(200).json(payload);
  });

  router.post("/accounts/:aid/fastspring/change-product", safeAny, secure, async (req, res, next) => {
    let context = req.context;
    let payload = await g.fastSpringService.changeProduct(
      context,
      context.aid,
      context.any.fromProduct,
      context.any.toProduct,
      context.any.prorate || false,
      context.any.preview || false
    );
    res.status(200).json(payload);
  });

}
