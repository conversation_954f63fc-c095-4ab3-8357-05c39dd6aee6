
#include "getFieldInfo.h"
#include "MYLiterals.h"


bool getFieldInfo(int sectionSid, int sid, Field& f)  {
    switch (sectionSid) {
    case MYLiterals::resources:
        switch(sid) {
            case MYLiterals::trev:
                f.name = "t";
                f.isDate = false;
                f.type = BJsonType::trev;
                return true;
        }
        break;

    case MYLiterals::accounts:
                        switch(sid) {
                            case MYLiterals::trev:
                                f.name = "t";
                                f.isDate = false;
                                f.type = BJsonType::trev;
                                return true;
                            
                        case MYLiterals::Account::accountId:
                                                f.name = "accountId";
                                                f.type = BJsonType::varint;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			
			case MYLiterals::Account::sub:
                                                f.name = "sub";
                                                f.type = BJsonType::string;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Account::flags:
                                                f.name = "flags";
                                                f.type = BJsonType::varint;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Account::peerToPeerKey:
                                                f.name = "peerToPeerKey";
                                                f.type = BJsonType::string;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Account::email:
                                                f.name = "email";
                                                f.type = BJsonType::string;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Account::cipher:
                                                f.name = "cipher";
                                                f.type = BJsonType::binary;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Account::idp:
                                                f.name = "idp";
                                                f.type = BJsonType::string;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Account::planId:
                                                f.name = "planId";
                                                f.type = BJsonType::string;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Account::role:
                                                f.name = "role";
                                                f.type = BJsonType::string;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Account::minBuild:
                                                f.name = "minBuild";
                                                f.type = BJsonType::varint;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Account::rsaPrivateKey:
                                                f.name = "rsaPrivateKey";
                                                f.type = BJsonType::string;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Account::x509Cert:
                                                f.name = "x509Cert";
                                                f.type = BJsonType::string;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Account::deviceLimit:
                                                f.name = "deviceLimit";
                                                f.type = BJsonType::varint;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Account::photoLimit:
                                                f.name = "photoLimit";
                                                f.type = BJsonType::varint;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			
			case MYLiterals::Account::features:
                                                f.name = "features";
                                                f.type = BJsonType::varint;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Account::nextPlanDate:
                                                f.name = "nextPlanDate";
                                                f.type = BJsonType::varint;
                                                f.isDate = true;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Account::cloudStorageLimit:
                                                f.name = "cloudStorageLimit";
                                                f.type = BJsonType::varint;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Account::clientCipher:
                                                f.name = "clientCipher";
                                                f.type = BJsonType::binary;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Account::clientPeerToPeerKey:
                                                f.name = "clientPeerToPeerKey";
                                                f.type = BJsonType::string;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Account::clientCipherVersion:
                                                f.name = "clientCipherVersion";
                                                f.type = BJsonType::varint;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Account::clientPeerToPeerKeyVersion:
                                                f.name = "clientPeerToPeerKeyVersion";
                                                f.type = BJsonType::varint;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Account::availableUpgrades:
                                                f.name = "availableUpgrades";
                                                f.type = BJsonType::string;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Account::licenseTemplateId:
                                                f.name = "licenseTemplateId";
                                                f.type = BJsonType::string;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Account::licenseDisplayName:
                                                f.name = "licenseDisplayName";
                                                f.type = BJsonType::string;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Account::licenseFlags:
                                                f.name = "licenseFlags";
                                                f.type = BJsonType::varint;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Account::licenseManager:
                                                f.name = "licenseManager";
                                                f.type = BJsonType::string;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Account::availableUpgradesFeatures:
                                                f.name = "availableUpgradesFeatures";
                                                f.type = BJsonType::varint;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;

                            //switch sid
                        }
                        break;
                    
		case MYLiterals::devicedata:
                        switch(sid) {
                            case MYLiterals::trev:
                                f.name = "t";
                                f.isDate = false;
                                f.type = BJsonType::trev;
                                return true;
                            
                        case MYLiterals::DeviceData::deviceId:
                                                f.name = "deviceId";
                                                f.type = BJsonType::varint;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::DeviceData::build:
                                                f.name = "build";
                                                f.type = BJsonType::string;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::DeviceData::lastAccessTime:
                                                f.name = "lastAccessTime";
                                                f.type = BJsonType::varint;
                                                f.isDate = true;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::DeviceData::os:
                                                f.name = "os";
                                                f.type = BJsonType::string;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			
			case MYLiterals::DeviceData::mediaCount:
                                                f.name = "mediaCount";
                                                f.type = BJsonType::varint;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::DeviceData::originalSize:
                                                f.name = "originalSize";
                                                f.type = BJsonType::varint;
                                                f.isDate = false;
                  f.isInt64 = true;
                                                return true;
			case MYLiterals::DeviceData::protocolVersion:
                                                f.name = "protocolVersion";
                                                f.type = BJsonType::varint;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::DeviceData::version:
                                                f.name = "version";
                                                f.type = BJsonType::string;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::DeviceData::lastStartupTime:
                                                f.name = "lastStartupTime";
                                                f.type = BJsonType::varint;
                                                f.isDate = true;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::DeviceData::lastHidTime:
                                                f.name = "lastHidTime";
                                                f.type = BJsonType::varint;
                                                f.isDate = true;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::DeviceData::lastImportTime:
                                                f.name = "lastImportTime";
                                                f.type = BJsonType::varint;
                                                f.isDate = true;
                  f.isInt64 = false;
                                                return true;
			

                            //switch sid
                        }
                        break;
                    
		case MYLiterals::devices:
                        switch(sid) {
                            case MYLiterals::trev:
                                f.name = "t";
                                f.isDate = false;
                                f.type = BJsonType::trev;
                                return true;
                            
                        case MYLiterals::Device::deviceId:
                                                f.name = "deviceId";
                                                f.type = BJsonType::varint;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Device::name:
                                                f.name = "name";
                                                f.type = BJsonType::string;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Device::deviceType:
                                                f.name = "deviceType";
                                                f.type = BJsonType::varint;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Device::creationTime:
                                                f.name = "creationTime";
                                                f.type = BJsonType::varint;
                                                f.isDate = true;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Device::nickname:
                                                f.name = "nickname";
                                                f.type = BJsonType::string;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Device::longId:
                                                f.name = "longId";
                                                f.type = BJsonType::hash;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Device::deleted:
                                                f.name = "deleted";
                                                f.type = BJsonType::true1;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			
			case MYLiterals::Device::supportTicket:
                                                f.name = "supportTicket";
                                                f.type = BJsonType::object;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			

                            //switch sid
                        }
                        break;
                    
		case MYLiterals::messages:
                        switch(sid) {
                            case MYLiterals::trev:
                                f.name = "t";
                                f.isDate = false;
                                f.type = BJsonType::trev;
                                return true;
                            
                        case MYLiterals::Message::messageId:
                                                f.name = "messageId";
                                                f.type = BJsonType::varint;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Message::message:
                                                f.name = "message";
                                                f.type = BJsonType::string;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Message::link:
                                                f.name = "link";
                                                f.type = BJsonType::string;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Message::deviceId:
                                                f.name = "deviceId";
                                                f.type = BJsonType::varint;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Message::secondsToDisplay:
                                                f.name = "secondsToDisplay";
                                                f.type = BJsonType::varint;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::Message::displayed:
                                                f.name = "displayed";
                                                f.type = BJsonType::varint;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;

                            //switch sid
                        }
                        break;
                    
		case MYLiterals::userProperties:
                        switch(sid) {
                            case MYLiterals::trev:
                                f.name = "t";
                                f.isDate = false;
                                f.type = BJsonType::trev;
                                return true;
                            
                        case MYLiterals::User::userPropertyId:
                                                f.name = "userPropertyId";
                                                f.type = BJsonType::hash;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::User::deleted:
                                                f.name = "deleted";
                                                f.type = BJsonType::true1;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::User::name:
                                                f.name = "name";
                                                f.type = BJsonType::string;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::User::value:
                                                f.name = "value";
                                                f.type = BJsonType::string;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;

                            //switch sid
                        }
                        break;
                    
		case MYLiterals::systemProperties:
                        switch(sid) {
                            case MYLiterals::trev:
                                f.name = "t";
                                f.isDate = false;
                                f.type = BJsonType::trev;
                                return true;
                            
                        case MYLiterals::System::systemPropertyId:
                                                f.name = "systemPropertyId";
                                                f.type = BJsonType::varint;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::System::deleted:
                                                f.name = "deleted";
                                                f.type = BJsonType::true1;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			
			case MYLiterals::System::name:
                                                f.name = "name";
                                                f.type = BJsonType::string;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;
			case MYLiterals::System::value:
                                                f.name = "value";
                                                f.type = BJsonType::string;
                                                f.isDate = false;
                  f.isInt64 = false;
                                                return true;

                            //switch sid
                        }
                        break;
                    
    // switch sectionSid
    }
    return false;

// bool getFieldInfo
}

bool getFieldInfo(int sectionSid, const std::string& name, Field& f) {
    switch (sectionSid) {
    case MYLiterals::resources:
        if (name == "t") {
            f.sid = MYLiterals::trev;
            f.type = BJsonType::trev;
            f.isDate = false;
            return true;
        }
        break;

    case MYLiterals::accounts:
        if (name == "t") {
            f.sid = MYLiterals::trev;
            f.type = BJsonType::trev;
            f.isDate = false;
            return true;
        }
    
        if (name == "accountId") {
                                f.sid = 1;
                                f.isDate = false;
                                f.type = BJsonType::varint;
                                return true;
                            }
		
		if (name == "sub") {
                                f.sid = 3;
                                f.isDate = false;
                                f.type = BJsonType::string;
                                return true;
                            }
		if (name == "flags") {
                                f.sid = 4;
                                f.isDate = false;
                                f.type = BJsonType::varint;
                                return true;
                            }
		if (name == "peerToPeerKey") {
                                f.sid = 5;
                                f.isDate = false;
                                f.type = BJsonType::string;
                                return true;
                            }
		if (name == "email") {
                                f.sid = 6;
                                f.isDate = false;
                                f.type = BJsonType::string;
                                return true;
                            }
		if (name == "cipher") {
                                f.sid = 8;
                                f.isDate = false;
                                f.type = BJsonType::binary;
                                return true;
                            }
		if (name == "idp") {
                                f.sid = 9;
                                f.isDate = false;
                                f.type = BJsonType::string;
                                return true;
                            }
		if (name == "planId") {
                                f.sid = 10;
                                f.isDate = false;
                                f.type = BJsonType::string;
                                return true;
                            }
		if (name == "role") {
                                f.sid = 11;
                                f.isDate = false;
                                f.type = BJsonType::string;
                                return true;
                            }
		if (name == "minBuild") {
                                f.sid = 12;
                                f.isDate = false;
                                f.type = BJsonType::varint;
                                return true;
                            }
		if (name == "rsaPrivateKey") {
                                f.sid = 13;
                                f.isDate = false;
                                f.type = BJsonType::string;
                                return true;
                            }
		if (name == "x509Cert") {
                                f.sid = 14;
                                f.isDate = false;
                                f.type = BJsonType::string;
                                return true;
                            }
		if (name == "deviceLimit") {
                                f.sid = 15;
                                f.isDate = false;
                                f.type = BJsonType::varint;
                                return true;
                            }
		if (name == "photoLimit") {
                                f.sid = 16;
                                f.isDate = false;
                                f.type = BJsonType::varint;
                                return true;
                            }
		
		if (name == "features") {
                                f.sid = 18;
                                f.isDate = false;
                                f.type = BJsonType::varint;
                                return true;
                            }
		if (name == "nextPlanDate") {
                                f.sid = 19;
                                f.isDate = true;
                                f.type = BJsonType::varint;
                                return true;
                            }
		if (name == "cloudStorageLimit") {
                                f.sid = 20;
                                f.isDate = false;
                                f.type = BJsonType::varint;
                                return true;
                            }
		if (name == "clientCipher") {
                                f.sid = 21;
                                f.isDate = false;
                                f.type = BJsonType::binary;
                                return true;
                            }
		if (name == "clientPeerToPeerKey") {
                                f.sid = 22;
                                f.isDate = false;
                                f.type = BJsonType::string;
                                return true;
                            }
		if (name == "clientCipherVersion") {
                                f.sid = 23;
                                f.isDate = false;
                                f.type = BJsonType::varint;
                                return true;
                            }
		if (name == "clientPeerToPeerKeyVersion") {
                                f.sid = 24;
                                f.isDate = false;
                                f.type = BJsonType::varint;
                                return true;
                            }
		if (name == "availableUpgrades") {
                                f.sid = 25;
                                f.isDate = false;
                                f.type = BJsonType::string;
                                return true;
                            }
		if (name == "licenseTemplateId") {
                                f.sid = 26;
                                f.isDate = false;
                                f.type = BJsonType::string;
                                return true;
                            }
		if (name == "licenseDisplayName") {
                                f.sid = 27;
                                f.isDate = false;
                                f.type = BJsonType::string;
                                return true;
                            }
		if (name == "licenseFlags") {
                                f.sid = 28;
                                f.isDate = false;
                                f.type = BJsonType::varint;
                                return true;
                            }
		if (name == "licenseManager") {
                                f.sid = 29;
                                f.isDate = false;
                                f.type = BJsonType::string;
                                return true;
                            }
		if (name == "availableUpgradesFeatures") {
                                f.sid = 30;
                                f.isDate = false;
                                f.type = BJsonType::varint;
                                return true;
                            }
        break;
    
		case MYLiterals::devicedata:
        if (name == "t") {
            f.sid = MYLiterals::trev;
            f.type = BJsonType::trev;
            f.isDate = false;
            return true;
        }
    
        if (name == "deviceId") {
                                f.sid = 1;
                                f.isDate = false;
                                f.type = BJsonType::varint;
                                return true;
                            }
		if (name == "build") {
                                f.sid = 3;
                                f.isDate = false;
                                f.type = BJsonType::string;
                                return true;
                            }
		if (name == "lastAccessTime") {
                                f.sid = 4;
                                f.isDate = true;
                                f.type = BJsonType::varint;
                                return true;
                            }
		if (name == "os") {
                                f.sid = 6;
                                f.isDate = false;
                                f.type = BJsonType::string;
                                return true;
                            }
		
		if (name == "mediaCount") {
                                f.sid = 10;
                                f.isDate = false;
                                f.type = BJsonType::varint;
                                return true;
                            }
		if (name == "originalSize") {
                                f.sid = 14;
                                f.isDate = false;
                                f.type = BJsonType::varint;
                                return true;
                            }
		if (name == "protocolVersion") {
                                f.sid = 18;
                                f.isDate = false;
                                f.type = BJsonType::varint;
                                return true;
                            }
		if (name == "version") {
                                f.sid = 22;
                                f.isDate = false;
                                f.type = BJsonType::string;
                                return true;
                            }
		if (name == "lastStartupTime") {
                                f.sid = 23;
                                f.isDate = true;
                                f.type = BJsonType::varint;
                                return true;
                            }
		if (name == "lastHidTime") {
                                f.sid = 24;
                                f.isDate = true;
                                f.type = BJsonType::varint;
                                return true;
                            }
		if (name == "lastImportTime") {
                                f.sid = 25;
                                f.isDate = true;
                                f.type = BJsonType::varint;
                                return true;
                            }
		
        break;
    
		case MYLiterals::devices:
        if (name == "t") {
            f.sid = MYLiterals::trev;
            f.type = BJsonType::trev;
            f.isDate = false;
            return true;
        }
    
        if (name == "deviceId") {
                                f.sid = 1;
                                f.isDate = false;
                                f.type = BJsonType::varint;
                                return true;
                            }
		if (name == "name") {
                                f.sid = 2;
                                f.isDate = false;
                                f.type = BJsonType::string;
                                return true;
                            }
		if (name == "deviceType") {
                                f.sid = 3;
                                f.isDate = false;
                                f.type = BJsonType::varint;
                                return true;
                            }
		if (name == "creationTime") {
                                f.sid = 4;
                                f.isDate = true;
                                f.type = BJsonType::varint;
                                return true;
                            }
		if (name == "nickname") {
                                f.sid = 5;
                                f.isDate = false;
                                f.type = BJsonType::string;
                                return true;
                            }
		if (name == "longId") {
                                f.sid = 7;
                                f.isDate = false;
                                f.type = BJsonType::hash;
                                return true;
                            }
		if (name == "deleted") {
                                f.sid = 16;
                                f.isDate = false;
                                f.type = BJsonType::true1;
                                return true;
                            }
		
		if (name == "supportTicket") {
                                f.sid = 27;
                                f.isDate = false;
                                f.type = BJsonType::object;
                                return true;
                            }
		
        break;
    
		case MYLiterals::messages:
        if (name == "t") {
            f.sid = MYLiterals::trev;
            f.type = BJsonType::trev;
            f.isDate = false;
            return true;
        }
    
        if (name == "messageId") {
                                f.sid = 1;
                                f.isDate = false;
                                f.type = BJsonType::varint;
                                return true;
                            }
		if (name == "message") {
                                f.sid = 2;
                                f.isDate = false;
                                f.type = BJsonType::string;
                                return true;
                            }
		if (name == "link") {
                                f.sid = 3;
                                f.isDate = false;
                                f.type = BJsonType::string;
                                return true;
                            }
		if (name == "deviceId") {
                                f.sid = 4;
                                f.isDate = false;
                                f.type = BJsonType::varint;
                                return true;
                            }
		if (name == "secondsToDisplay") {
                                f.sid = 5;
                                f.isDate = false;
                                f.type = BJsonType::varint;
                                return true;
                            }
		if (name == "displayed") {
                                f.sid = 6;
                                f.isDate = false;
                                f.type = BJsonType::varint;
                                return true;
                            }
        break;
    
		case MYLiterals::userProperties:
        if (name == "t") {
            f.sid = MYLiterals::trev;
            f.type = BJsonType::trev;
            f.isDate = false;
            return true;
        }
    
        if (name == "userPropertyId") {
                                f.sid = 1;
                                f.isDate = false;
                                f.type = BJsonType::hash;
                                return true;
                            }
		if (name == "deleted") {
                                f.sid = 4;
                                f.isDate = false;
                                f.type = BJsonType::true1;
                                return true;
                            }
		if (name == "name") {
                                f.sid = 2;
                                f.isDate = false;
                                f.type = BJsonType::string;
                                return true;
                            }
		if (name == "value") {
                                f.sid = 3;
                                f.isDate = false;
                                f.type = BJsonType::string;
                                return true;
                            }
        break;
    
		case MYLiterals::systemProperties:
        if (name == "t") {
            f.sid = MYLiterals::trev;
            f.type = BJsonType::trev;
            f.isDate = false;
            return true;
        }
    
        if (name == "systemPropertyId") {
                                f.sid = 1;
                                f.isDate = false;
                                f.type = BJsonType::varint;
                                return true;
                            }
		if (name == "deleted") {
                                f.sid = 4;
                                f.isDate = false;
                                f.type = BJsonType::true1;
                                return true;
                            }
		
		if (name == "name") {
                                f.sid = 2;
                                f.isDate = false;
                                f.type = BJsonType::string;
                                return true;
                            }
		if (name == "value") {
                                f.sid = 3;
                                f.isDate = false;
                                f.type = BJsonType::string;
                                return true;
                            }
        break;
    
    // switch sectionSid
    }
    return false;
}