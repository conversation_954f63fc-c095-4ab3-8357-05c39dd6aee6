export const ids = {
  CLIENT_BUILD_NOT_SUPPORTED: "CLIENT_BUILD_NOT_SUPPORTED",
  ACCOUNT_NOT_FOUND: "ACCOUNT_NOT_FOUND",
  ACCOUNT_DOES_NOT_EXIST_OR_ACCESS_DENIED:
    "ACCOUNT_DOES_NOT_EXIST_OR_ACCESS_DENIED",
  AUTHENTICATION_FAILED: "AUTHENTICATION_FAILED",
  EMAIL_TOKEN_USED_ILLEGALLY: "EMAIL_TOKEN_USED_ILLEGALLY",
  SAFE_NOT_CALLED_BEFORE_SECURE: "SAFE_NOT_CALLED_BEFORE_SECURE",
  VALIDATION_ERRORS: "VALIDATION_ERRORS",
  APPSTORE_ERROR: "APPSTORE_ERROR",
  INVALID_APPSTORE_RECEIPT: "INVALID_APPSTORE_RECEIPT",
  INVALID_DATA: "INVALID_DATA",
  ROUTE_DOES_NOT_EXIST: "ROUTE_DOES_NOT_EXIST",
  FORBIDDEN: "FORBIDDEN",
  NOT_AUTHENTICATED: "NOT_AUTHENTICATED",
  DEVICE_NOT_VALID_FOR_ACCOUNT: "DEVICE_NOT_VALID_FOR_ACCOUNT",
  DUPLICATE_SUB_AND_IDP: "DUPLICATE_SUB_AND_IDP",
  DUPLICATE_CLOUD_DRIVE: "DUPLICATE_CLOUD_DRIVE",
  ACCOUNT_SERVICE_UNAVAILABLE: "ACCOUNT_SERVICE_UNAVAILABLE",
  SEND_EMAIL_FAILED: "SEND_EMAIL_FAILED",
  UNKNOWN_ERROR: "UNKNOWN_ERROR",
  INVALID_EMAIL_TOKEN: "INVALID_EMAIL_TOKEN",
  MAINTENANCE_MODE: "MAINTENANCE_MODE",
  SERVER_ERROR: "SERVER_ERROR",
  EMAIL_ALREADY_IN_USE: "EMAIL_ALREADY_IN_USE",
  EXTERNAL_EMAIL_ALREADY_IN_USE: "EXTERNAL_EMAIL_ALREADY_IN_USE",
  FACEBOOK_ACCOUNT_ALREADY_EXISTS: "FACEBOOK_ACCOUNT_ALREADY_EXISTS",
  APPLE_ACCOUNT_ALREADY_EXISTS: "APPLE_ACCOUNT_ALREADY_EXISTS",
  GOOGLE_ACCOUNT_ALREADY_EXISTS: "GOOGLE_ACCOUNT_ALREADY_EXISTS",
  MICROSOFT_ACCOUNT_ALREADY_EXISTS: "MICROSOFT_ACCOUNT_ALREADY_EXISTS",
  LOCKED: "LOCKED",
  MISSING_DEVICE_ID: "MISSING_DEVICE_ID",
  KEY_OR_CERT_DOES_NOT_EXIST: "KEY_OR_CERT_DOES_NOT_EXIST",
  COULD_NOT_GENERATE_KEY_AND_CERT: "COULD_NOT_GENERATING_KEY_AND_CERT",
  REFRESHTOKEN_NOT_VALID_FOR_ACCOUNT: "REFRESHTOKEN_NOT_VALID_FOR_ACCOUNT",
  NO_REFRESH_TOKEN: "NO_REFRESH_TOKEN",
  INVALID_GRANT: "INVALID_GRANT",
  BACKTRACE_UNKNOWN_ERROR: "BACKTRACE_UNKNOWN_ERROR",
  APPLE_EMAIL_MISSING_ON_SUBSCRIBE: "APPLE_EMAIL_MISSING_ON_SUBSCRIBE",
  FACEBOOK_EMAIL_MISSING_ON_SUBSCRIBE: "FACEBOOK_EMAIL_MISSING_ON_SUBSCRIBE",
  INVALID_PROMO_CODE: "INVALID_PROMO_CODE",
  PROMO_CODE_NOT_APPLICABLE: "PROMO_CODE_NOT_APPLICABLE",
  INVALID_PLAN_CHANGE: "INVALID_PLAN_CHANGE",
  INVALID_PROMO_CODE_TYPE: "INVALID_PROMO_CODE_TYPE",
  BAD_ACCOUNT: "BAD_ACCOUNT",
  ACCOUNT_ALREADY_TRIALED: "ACCOUNT_ALREADY_TRIALED",
  THIS_IS_A_MICROSOFT_ACCOUNT: "THIS_IS_A_MICROSOFT_ACCOUNT",
  THIS_IS_A_FACEBOOK_ACCOUNT: "THIS_IS_A_FACEBOOK_ACCOUNT",
  THIS_IS_A_GOOGLE_ACCOUNT: "THIS_IS_A_GOOGLE_ACCOUNT",
  THIS_IS_AN_APPLE_ACCOUNT: "THIS_IS_AN_APPLE_ACCOUNT",
  TOKEN_EXPIRED: "TOKEN_EXPIRED",
  LINK_EXPIRED: "LINK_EXPIRED",
  ACCOUNT_ALREADY_A_MYLIO_ACCOUNT: "ACCOUNT_ALREADY_A_MYLIO_ACCOUNT",
  TFA_MISSING_STOKEN: "TFA_MISSING_STOKEN",
  TFA_MISSING_PASSWORD: "TFA_MISSING_PASSWORD",
  MISSING_PASSWORD_AND_PIN: "MISSING_CODE_VERIFIER_AND_PIN_OR_PASSWORD",
  CODE_VERIFIER_NOT_FOUND: "CODE_VERIFIER_NOT_FOUND",
  EMAIL_DOES_MATCH_CODE_VERIFIER: "INVALID_CODE_VERIFIER_OR_EMAIL",
  PIN_DOES_NOT_MATCH_CODE_VERIFIER: "INVALID_CODE_VERIFIER_OR_PIN",
  LOGIN_LINK_EXPIRED: "LOGIN_LINK_EXPIRED",
  TFA_MISSING_CODE_VERIFIER_OR_PIN: "TFA_MISSING_CODE_VERIFIER_OR_PIN",
  INVALID_PIN: "INVALID_PIN",
  SUBSCRIPTION_NOT_FOUND: "SUBSCRIPTION_NOT_FOUND",
  MULTIPLE_PAID_SUBS: "MULTIPLE_PAID_SUBS",
  NOT_AUTHORIZED: "NOT_AUTHORIZED",
};

export enum EventType {
  ACCOUNT_CREATED = 0,
  ACCOUNT_RESTARTED = 2,
  ACCOUNT_DELETED = 3,
  EMAIL_CHANGED = 4,
  PASSWORD_CHANGED = 5,
  SUBSCRIPTION_CHANGED = 6,
  SUBSCRIPTION_CANCELED = 7,
  SUBSCRIPTION_DELETED = 8,
  DEVICE_DELETED = 9,
  ACCOUNT_MIGRATED = 10,
  DEVICE_CREATED = 11,
}

export function humanReadable(code: string, message?: string) {
  switch (code) {
    case ids.ACCOUNT_NOT_FOUND:
    case ids.ACCOUNT_DOES_NOT_EXIST_OR_ACCESS_DENIED:
    case ids.AUTHENTICATION_FAILED:
      return "No account found or incorrect password.";
    case ids.DUPLICATE_SUB_AND_IDP:
      return "Oops! It looks like an account with that email already exists.";
    case ids.INVALID_EMAIL_TOKEN:
      return "This link is no longer valid. Please request another link.";
    case ids.MAINTENANCE_MODE:
      return "Our server is currently undergoing maintenance. Please try again later.";
    case ids.EMAIL_ALREADY_IN_USE:
      return "This account is already associated with an e-mail address that was used to sign-up with Mylio. Please login with the Sign In using email option";
    case ids.APPSTORE_ERROR:
      return "Communication with Apple AppStore failed. Please contact support at '<EMAIL>'.";
    case ids.INVALID_APPSTORE_RECEIPT:
      return "Apple AppStore receipt supplied receipt we couldn't parse. Please contact support at '<EMAIL>'.";
    case ids.NOT_AUTHENTICATED:
      return "You must be signed in to do that.";
    case ids.EXTERNAL_EMAIL_ALREADY_IN_USE:
      return "Oops! You need to use email and password to login to this Mylio account.";
    case ids.FACEBOOK_ACCOUNT_ALREADY_EXISTS:
      return "Oops! You need to use Facebook to login to this Mylio account.";
    case ids.APPLE_ACCOUNT_ALREADY_EXISTS:
      return "Oops! You need to use Apple to login to this Mylio account.";
    case ids.GOOGLE_ACCOUNT_ALREADY_EXISTS:
      return "Oops! You need to use Google to login to this Mylio account.";
    case ids.MICROSOFT_ACCOUNT_ALREADY_EXISTS:
      return "Oops! You need to use Microsoft to login to this Mylio account.";
    case ids.MISSING_DEVICE_ID:
      return "Device id missing in header or request body.";
    case ids.KEY_OR_CERT_DOES_NOT_EXIST:
      return "A security certificate was not found for your account. Please contact support at '<EMAIL>'.";
    case ids.COULD_NOT_GENERATE_KEY_AND_CERT:
      return "There was a problem generating your account security certificate. Please contact support at '<EMAIL>'.";
    case ids.INVALID_GRANT:
      return "Could not grant access to identity provider.";
    case ids.APPLE_EMAIL_MISSING_ON_SUBSCRIBE:
      return "There was an issue setting up your account. To try again, visit http://mylio.com/permalink-0041.";
    case ids.FACEBOOK_EMAIL_MISSING_ON_SUBSCRIBE:
      return (
        "Facebook did not provide us with a valid email address for you. Mylio needs an email to communicate with you in case " +
        "there is an issue with your account. Please make sure you have email configured on your Facebook account and grant Mylio " +
        "permission to access it."
      );
    case ids.INVALID_PROMO_CODE:
      return "The promo code you have used is either expired, invalid or already claimed.";
    case ids.PROMO_CODE_NOT_APPLICABLE:
      return "The promo code you have used cannot be applied to your account. Please contact support at '<EMAIL>'.";
    case ids.INVALID_PLAN_CHANGE:
      return "Your subscription type cannot be changed at this time. Please contact support at '<EMAIL>'.";
    case ids.BAD_ACCOUNT:
      return "Your account has issues that need to be resolved, please contact support to help us resolve this issue.";
    case ids.ACCOUNT_ALREADY_TRIALED:
      return "You have used a free trial of Mylio before. If you believe this is not the case, please contact support at '<EMAIL>'.";
    case ids.ROUTE_DOES_NOT_EXIST:
      return "This route doesn't exist in the API. Check your code for types, e.g. using /account (incorrect) instead of /accounts (correct)";
    case ids.THIS_IS_A_FACEBOOK_ACCOUNT:
      return "Oops! You need to use Facebook to login to this Mylio account.";
    case ids.THIS_IS_AN_APPLE_ACCOUNT:
      return "Oops! You need to use Apple to login to this Mylio account.";
    case ids.THIS_IS_A_GOOGLE_ACCOUNT:
      return "Oops! You need to use Google to login to this Mylio account.";
    case ids.THIS_IS_A_MICROSOFT_ACCOUNT:
      return "Oops! You need to use Microsoft to login to this Mylio account.";
    case ids.TOKEN_EXPIRED:
      return "This token has expired. Request a new token and try again";
    case ids.LINK_EXPIRED:
      return "This link has expired. Request a new link and try again";
    case ids.ACCOUNT_ALREADY_A_MYLIO_ACCOUNT:
      return "This account is already a Mylio account";
    default:
      if (message) {
        return message;
      }
      return code;
  }
}
