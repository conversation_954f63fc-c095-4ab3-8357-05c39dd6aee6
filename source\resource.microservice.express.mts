import { bootstrap } from "./system/bootstrap.mjs";
import { microservice } from "./microservices/resource.microservice.mjs";
import express = require("express");
import cors = require("cors");
import { addHealthRoutes } from "./routes/resource.health.routes.mjs";
import { config } from "./system/Config.mjs";
import { errorHandler } from "./system/errorHandler.mjs";
import { addResourceRoutes } from "./routes/resource.routes.mjs";
import { addBinaryRoutes } from "./routes/resource.binary.routes.mjs";
import https from "https";
import fs from "fs";
import { parseUserAgent } from "./system/UserAgent.mjs";

let app = express();

bootstrap()
  .then(() => {
    return microservice.start();
  })
  .then(() => {
    /*
        app.use((req, res, next) => {
            console.log(req.originalUrl);
            next();
        });
        */



    app.set("etag", false);
    app.set("x-powered-by", false);
    app.set("lastModified", false);

    app.use((req, res, next) => {
      const maxContentLength = 256 * 1024 * 1024; // 10MB in bytes
      if (req.headers['content-length'] && Number(req.headers['content-length']) > maxContentLength) {
        return res.status(500).json({ error: 'Payload Too Large' });
      }
      next();
    });

    app.use(parseUserAgent);
    app.use(cors({ maxAge: Number.MAX_SAFE_INTEGER }));
    app.use(express.raw({ inflate: false, limit: "64mb" }));
    app.use(express.json({ limit: "16mb" }));

    addBinaryRoutes(app);

    addHealthRoutes(app);
    addResourceRoutes(app);

    // global error handler
    app.use(errorHandler);

    if (process.env.IS_WILLEM === "1")
      https
        .createServer(
          // Provide the private and public key to the server by reading each
          // file's content with the readFileSync() method.
          {
            key: fs.readFileSync(
              "C:/certbot/live/willem.mylio.com/privkey.pem"
            ),
            cert: fs.readFileSync(
              "C:/certbot/live/willem.mylio.com/fullchain.pem"
            ),
          },
          app
        )
        .listen(443, "192.168.0.160", () => {
          console.log(config);
        });
    else
      app.listen(config.port, () => {
        console.log(
          `Cloud running on port ${config.port
          } started at ${new Date().toUTCString()}`
        );
        console.log(config);
      });
  })
  .catch((err) => {
    console.error(JSON.stringify(err || {}));
    process.exit(666);
  });
