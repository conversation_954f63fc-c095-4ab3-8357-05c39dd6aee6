#pragma once
#include "bjson.h"
#include <vector>
#include <map>
#include "MYHash.h"
#include "MYTrev.h"
#include "vector_map.h"
#include "MYMediaFileType.h"
#include "MYNeeds.h"

#define WITH_FIXED_32
#ifdef WITH_FIXED_32
#define HashRefWrite Fixed32
#define HashRefRead fixed32
#else
#define HashRefWrite Uint32
#define HashRefRead asUint32
#endif

class MYLocalFile;
class MYLocalFiles;
class MYBucketLocalFiles;

// We store this as flags - 1 (since 0 marshals as 'not exists'). So add 1 to read it.
enum class MYLocalFileFlags : uint8_t
{
    parseAttempted = 0,
    inInternalData = 1,
    parseFailed = 2,
    isDraft = 3,
    hardWant = 4,
    hardDontWant = 5
};

// For one media, one media type
class MYLocalFile final
{
public:
    MYLocalFile()
    {
    }
    MYLocalFile(const MYLocalFile &);
    MYLocalFile(MYLocalFile &&other);

    MYLocalFile &operator=(MYLocalFile &&);
    MYLocalFile &operator=(const MYLocalFile &);

    MYLocalFile(MYMediaFileType::Enum mediaType) : _mediaType(mediaType)
    {
        prepare();
    }
    MYLocalFile(MYMediaFileType::Enum mediaType, MYBJsonIterator &begin, const MYBJsonIterator &end);

    void deserializeFromBJson(MYBJsonIterator &begin, const MYBJsonIterator &end);

    MYLocalFile &operator+=(const MYLocalFile &other);

    const std::string &getFormat() const;
    bool setFormat(const std::string &newFormat);
    bool setFormat(std::string &&newFormat);

    MYMediaFileType::Enum getMediaType() const { return _mediaType; }

    bool hasDataHash() const { return _dataHash != MYHashRefEmpty; }
    const MYHashRef getRefDataHash() const { return _dataHash; }
    const MYHash &getDataHash() const;
    bool setDataHash(const MYHash &newDataHash);

    bool hasVisualEditHashHash() const { return _visualEditHash != MYHashRefEmpty; }
    const MYHashRef getRefVisualEditHashHash() const { return _visualEditHash; }
    const MYHash &getVisualEditHash() const;
    bool setVisualEditHash(const MYHash &newVisualEditHash);

    bool hasBasisDataHash() const { return _basisDataHash != MYHashRefEmpty; }
    const MYHashRef getRefBasisDataHash() const { return _basisDataHash; }
    const MYHash &getBasisDataHash() const;
    bool setBasisDataHash(const MYHash &newBasisDataHash);

    bool hasParseHash() const { return _parseHash != MYHashRefEmpty; }
    const MYHashRef getRefParseHash() const { return _parseHash; }
    const MYHash &getParseHash() const;
    bool setParseHash(const MYHash &newParseHash);

    float getCropZoomFactor() const { return _cropZoomFactor; }
    bool setCropZoomFactor(float newCropZoomFactor);

    bool reallyCantParse() const
    {
        return (_parseHash == _dataHash) && (getParsability() == MYParsability::NotParsable);
    }
    const MYParsability getParsability() const
    {
        if (_flags.test((int)MYLocalFileFlags::parseAttempted))
        {
            if (_flags.test((int)MYLocalFileFlags::parseFailed))
            {
                return MYParsability::NotParsable;
            }

            return MYParsability::Parsable;
        }

        return MYParsability::NotAttempted;
    }
    bool setParsability(MYParsability newParsability);

    bool getIsDraft() const { return _flags.test((int)MYLocalFileFlags::isDraft); }
    bool setIsDraft(bool newCantParse);

    bool getIsHardWant() const { return _flags.test((int)MYLocalFileFlags::hardWant); }
    bool setIsHardWant(bool newHardWant);

    bool getIsHardDontWant() const { return _flags.test((int)MYLocalFileFlags::hardDontWant); }
    bool setIsHardDontWant(bool newHardDontWant);

    const std::string &getALURL() const;
    bool setALURL(const std::string &newALUrl);
    bool setALURL(std::string &&newALUrl);

    const std::string &getByocId() const;
    bool setByocId(const std::string &newByocId);
    bool setByocId(std::string &&newByocId);

    bool getInInternalData() const;
    bool setInInternalData(bool newInInternalDataStorage);

    bool getInInternalDataStorage() const { return _flags.test((int)MYLocalFileFlags::inInternalData); }
    bool setInInternalDataStorage(bool newInInternalDataStorage);

    bool isModified() const;
    std::bitset<16> _modifiedFields;

    std::string toString() const;

    uint64_t getFileSize() const { return _fileSize; }
    bool setFileSize(uint64_t newFileSize);

    unsigned int getWidth() const { return _width; }
    bool setWidth(unsigned int newWidth);

    unsigned int getHeight() const { return _height; }
    bool setHeight(unsigned int newHeight);

    unsigned int getGenVersion() const { return _genVersion; }
    bool setGenVersion(unsigned int newGenVersion);

private:
    MYHashRef getHashRef(const MYHash &hash);

    class MYLocalFiles *getMyLocalFiles();
    const class MYLocalFiles *getMyLocalFiles() const;

    friend MYLocalFiles;

    // Remember to modify localFiles.compact if you modify these
    MYHashRef _dataHash;
    MYHashRef _visualEditHash;
    MYHashRef _basisDataHash;
    MYHashRef _parseHash;

    // Remember to modify localFiles.compact if you modify these
    MYStringRef _alURL;
    MYStringRef _format;
    MYStringRef _byocId;

    uint64_t _fileSize = 0;
    int _width = 0;
    int _height = 0;
    int _genVersion = 0;

    float _cropZoomFactor;

    std::bitset<8> _flags; // Of type MYLocalFileFlags
    bool _dirty = true;
    MYMediaFileType::Enum _mediaType = MYMediaFileType::Enum::NoType;

    MYLIO_INLINE void init(MYMediaFileType::Enum type)
    {
        _mediaType = type;
    }

    void clear();
    void prepareInternal();
    MYLIO_INLINE void prepare()
    {
        if (_dirty)
        {
            prepareInternal();
        }
    }

    bool empty() const
    {
        if (_dirty)
        {
            return true;
        }

        if (_format != MYStringRefEmpty)
        {
            return false;
        }

        if (_dataHash != MYHashRefEmpty)
        {
            return false;
        }

        if (_visualEditHash != MYHashRefEmpty)
        {
            return false;
        }

        if (_basisDataHash != MYHashRefEmpty)
        {
            return false;
        }

        if (_flags.any())
        {
            return false;
        }

        if (_cropZoomFactor != 1.0f)
        {
            return false;
        }

        if (_alURL != MYStringRefEmpty)
        {
            return false;
        }

        if (_byocId != MYStringRefEmpty)
        {
            return false;
        }

        if (_parseHash != MYHashRefEmpty)
        {
            return false;
        }

        if (_fileSize != 0)
        {
            return false;
        }

        if (_width != 0)
        {
            return false;
        }

        if (_height != 0)
        {
            return false;
        }

        return true;
    }

    template <typename TBJson>
    void serializeToBJson(TBJson &writer) const
    {
        assert(!_dirty);

        if (_format != MYStringRefEmpty)
        {
            writer.Uint32(MYLiterals::LocalFile::format, _format);
        }

        if (_dataHash != MYHashRefEmpty)
        {
            writer.Uint32(MYLiterals::LocalFile::dataHash, _dataHash);
        }

        if (_visualEditHash != MYHashRefEmpty)
        {
            writer.Uint32(MYLiterals::LocalFile::visualEditHash, _visualEditHash);
        }

        if (_basisDataHash != MYHashRefEmpty)
        {
            writer.Uint32(MYLiterals::LocalFile::basisDataHash, _basisDataHash);
        }

        if ((uint32_t)_flags.to_ulong() > 0)
        {
            writer.Uint32(MYLiterals::LocalFile::flags, (uint32_t)_flags.to_ulong() - 1);
        }

        if (_cropZoomFactor != 1.0f)
        {
            writer.Double(MYLiterals::LocalFile::cropZoomFactor, _cropZoomFactor);
        }

        if (_alURL != MYStringRefEmpty)
        {
            writer.Uint32(MYLiterals::LocalFile::alUrl, _alURL);
        }

        if (_byocId != MYStringRefEmpty)
        {
            writer.Uint32(MYLiterals::LocalFile::byocId, _byocId);
        }

        if (_parseHash != MYHashRefEmpty)
        {
            writer.Uint32(MYLiterals::LocalFile::parseHash, _parseHash);
        }

        if (_fileSize != 0)
        {
            writer.Uint64(MYLiterals::LocalFile::fileSize, _fileSize);
        }

        if (_width != 0)
        {
            writer.Uint32(MYLiterals::LocalFile::width, _width);
        }

        if (_height != 0)
        {
            writer.Uint32(MYLiterals::LocalFile::height, _height);
        }

        if (_genVersion != 0)
        {
            writer.Uint32(MYLiterals::LocalFile::genVersion, _genVersion);
        }
    }
};

// For one media, across all media type
class MYKnownMediaForLocalFiles
{
protected:
    friend class MYLocalFile;
    std::array<MYLocalFile, 7> _knownMediaFiles;
};

class MYLocalFiles final : public MYKnownMediaForLocalFiles
{
public:
    MYLocalFiles()
    {
        _hashMap.reserve(16);
        initLocalFileArray();
    }
    MYLocalFiles(bool emptyInitializedStructure);

    MYLocalFiles(MYBJsonIterator &iter, const MYBJsonIterator &end /*, bool useShortHash = false*/);
    MYLocalFiles(const MYLocalFiles &);
    MYLocalFiles(MYLocalFiles &&other);

    void deserializeFromBJson(MYBJsonIterator &begin, const MYBJsonIterator &end /*, bool useShortHash = false*/);
    void clear();
    void initLocalFileArray();

    void compact();

    MYLocalFiles &operator=(const MYLocalFiles &);
    MYLocalFiles &operator=(MYLocalFiles &&other);

    MYLocalFiles &operator+=(const MYBJsonView &view);

    bool operator==(const MYLocalFiles &other) const;

    template <typename TBJson>
    void serializeToBJson(TBJson &writer /*, bool useShortHash = false*/) const
    {
        bool wroteStartObject = false;

        for (const auto &file : _knownMediaFiles)
        {
            if (!file.empty())
            {
                if (!wroteStartObject)
                {
                    writer.StartObject(MYLiterals::Files::files);
                    wroteStartObject = true;
                }

                writer.Separator(file.getMediaType());
                file.serializeToBJson(writer);
            }
        }

        for (const auto &file : _extendedMediaFiles)
        {
            if (!wroteStartObject)
            {
                writer.StartObject(MYLiterals::Files::files);
                wroteStartObject = true;
            }

            writer.Separator(file.getMediaType());
            file.serializeToBJson(writer);
        }

        if (wroteStartObject)
        {
            writer.EndObject();

            if (!_hashMap.empty())
            {
                writer.StartArray(MYLiterals::Files::hashMap);
                for (const auto &hashEntry : _hashMap)
                {
                    // if (useShortHash)
                    //{
                    //     uint32_t shortHash;
                    //     memcpy(&shortHash, &hashEntry.raw[0], 4);
                    //     writer.Fixed32(0, shortHash);
                    // }
                    // else
                    //{
                    writer.MYHash(0, hashEntry);
                    //}
                }
                writer.EndArray();
            }

            if (!_stringMap.empty())
            {
                writer.StartArray(MYLiterals::Files::stringMap);
                for (const auto &stringEntry : _stringMap)
                {
                    writer.String(0, stringEntry);
                }
                writer.EndArray();
            }
        }
    }

    const MYLocalFile &getMediaFileOrEmpty(MYMediaFileType::Enum mediaFileType) const;
    MYLocalFile *getMediaFileOrNull(MYMediaFileType::Enum mediaFileType);
    const MYLocalFile *getMediaFileOrNull(MYMediaFileType::Enum mediaFileType) const;
    MYLocalFile *getOrCreateMediaFile(MYMediaFileType::Enum mediaFileType);
    bool removeMediaFileForType(MYMediaFileType::Enum mediaFileType);

    const NeedsBits getSupportMediaTypes() const;

    bool isModified() const;
    std::bitset<16> _modifiedFields;

    bool empty() const;

    bool _isConstructed = 1;

    std::string toString() const;

public:
    // Iterators
    struct const_iterator : std::iterator<std::forward_iterator_tag, const MYLocalFile>
    {
        typedef const_iterator iterator;
        typedef std::array<MYLocalFile, 7>::const_iterator underlying_iterator;
        underlying_iterator pos_;
        underlying_iterator end_;

    public:
        MYLIO_INLINE const_iterator(underlying_iterator pos, underlying_iterator end) : pos_(pos), end_(end) {}
        MYLIO_INLINE reference operator*() const { return *pos_; }
        MYLIO_INLINE pointer operator->() const { return &(*pos_); }
        MYLIO_INLINE bool operator==(const const_iterator &rhs) const
        {
            return pos_ == rhs.pos_;
        }
        MYLIO_INLINE bool operator!=(const const_iterator &rhs) const { return pos_ != rhs.pos_; }

        MYLIO_INLINE iterator operator++(int) /* postfix */
        {
            iterator pos(pos_, end_);
            ++(*this);
            return pos;
        }

        MYLIO_INLINE iterator &operator++() /* prefix */
        {
            do
            {
                ++pos_;
            } while ((pos_ != end_) && (pos_->_dirty));
            return *this;
        }
    };

    struct iterator : std::iterator<std::forward_iterator_tag, MYLocalFile>
    {
        typedef std::array<MYLocalFile, 7>::iterator underlying_iterator;
        underlying_iterator pos_;
        underlying_iterator end_;

    public:
        MYLIO_INLINE iterator(underlying_iterator pos, underlying_iterator end) : pos_(pos), end_(end) {}
        MYLIO_INLINE reference operator*() const { return *pos_; }
        MYLIO_INLINE pointer operator->() const { return &(*pos_); }
        MYLIO_INLINE bool operator==(const iterator &rhs) const
        {
            return pos_ == rhs.pos_;
        }
        MYLIO_INLINE bool operator!=(const iterator &rhs) const { return pos_ != rhs.pos_; }

        MYLIO_INLINE iterator operator++(int) /* postfix */
        {
            iterator pos(pos_, end_);
            ++(*this);
            return pos;
        }

        MYLIO_INLINE iterator &operator++() /* prefix */
        {
            do
            {
                ++pos_;
            } while ((pos_ != end_) && (pos_->_dirty));
            return *this;
        }
    };

    const_iterator begin() const
    {
        auto iter = const_iterator(_knownMediaFiles.cbegin(), _knownMediaFiles.cend());
        if (iter->_dirty)
        {
            ++iter;
        }
        return iter;
    }

    const_iterator end() const
    {
        return const_iterator(_knownMediaFiles.cend(), _knownMediaFiles.cend());
    }

    iterator begin()
    {
        auto iter = iterator(_knownMediaFiles.begin(), _knownMediaFiles.end());
        if (iter->_dirty)
        {
            ++iter;
        }
        return iter;
    }

    iterator end()
    {
        return iterator(_knownMediaFiles.end(), _knownMediaFiles.end());
    }

private:
    friend MYLocalFile;
    friend MYLocalFile;
    friend class Test;

    MYHashRef getOrCreateHashRef(const MYHash &hash);
    MYStringRef getOrCreateStringRef(const std::string &string, MYMediaFileType::Enum fileType);
    MYStringRef getOrCreateStringRef(std::string &&string, MYMediaFileType::Enum fileType);
    const std::string &getString(MYStringRef stringRef, MYMediaFileType::Enum fileType) const;
    const MYHash &getHash(MYHashRef stringRef) const;

    std::vector<MYHash> _hashMap;
    std::vector<std::string> _stringMap;
    std::vector<MYLocalFile> _extendedMediaFiles;
};

class MYBucketLocalFiles final
{
public:
    typedef vector_map<MYHash, MYLocalFiles> MYMediaMapType;

    MYBucketLocalFiles(size_t estimatedMediaPerBucket) : _estimatedMediaPerBucket(estimatedMediaPerBucket)
    {
        // We don't reserve in debug, because we want to take re-allocations. Otherwise it will hide bugs if
        // the pointer values always remain the same.
        _media.reserve(estimatedMediaPerBucket);
    }

    MYBucketLocalFiles(size_t estimatedMediaPerBucket, MYBJsonIterator &begin, const MYBJsonIterator &end);
    MYBucketLocalFiles(const MYBucketLocalFiles &) = delete;
    MYBucketLocalFiles(MYBucketLocalFiles &&other);
    MYBucketLocalFiles &operator=(const MYBucketLocalFiles &) = delete;
    MYBucketLocalFiles &operator=(MYBucketLocalFiles &&other);

    bool operator==(const MYBucketLocalFiles &other) const = delete;

    void deserializeFromBJson(MYBJsonIterator &begin, const MYBJsonIterator &end /*, bool useShortHash=false*/);
    void clear();

    template <typename TBJson>
    void serializeToBJson(TBJson &writer /*, bool useShortHash=false*/) const
    {
        //_useShortHash = useShortHash;
        prepare();

        if (!_media.empty())
        {
            bool first = true;
            writer.StartObject(MYLiterals::BucketLocalFiles::mediaMap);
            for (const auto &mediaFiles : _media)
            {
                if (!mediaFiles.second.empty())
                {
                    if (!first)
                    {
                        writer.Separator();
                    }
                    first = false;

                    writer.MYHash(MYLiterals::Files::media, mediaFiles.first);
                    mediaFiles.second.serializeToBJson(writer /*, useShortHash*/);
                }
            }
            writer.EndObject();
        }
    }

    MYLocalFiles *getFilesForMediaOrNull(const MYHash &media) const;
    MYLocalFiles *getOrCreateFilesForMedia(const MYHash &media);
    bool removeFilesForMedia(const MYHash &media);

    MYMediaMapType &getFiles()
    {
        prepare();
        return _media;
    }

    const MYMediaMapType &getFiles() const
    {
        prepare();
        return _media;
    }

    bool isModified() const;
    std::bitset<16> _modifiedFields;

    bool empty() const;

    std::string toString() const;

private:
    void prepareInternal() const
    {
        _media.clear();
        _dirty = false;
    }

    MYLIO_INLINE void prepare() const
    {
        if (_dirty)
        {
            prepareInternal();
        }
    }

    friend class MYLocalFile;
    friend class MYLocalFiles;
    friend class Test;

    // mutable bool _useShortHash = false;

    mutable bool _dirty = false;
    mutable MYMediaMapType _media;

    size_t _estimatedMediaPerBucket = 0;
};

extern MYLocalFiles g_emptyMYLocalFiles;
