{"plural": "licenses", "flow": "web<>cloud<>postgres<>disk", "mixin": ["all"], "fields": {"license_id": {"datatype": "string"}, "account_id": {"datatype": "int32"}, "manager": {"datatype": "string"}, "end_date": {"datatype": "date"}, "template_id": {"datatype": "string"}, "activation_key": {"datatype": "string"}, "device_limit": {"datatype": "int32"}, "photo_limit": {"datatype": "int32"}, "features": {"datatype": "int32"}, "cloud_storage_limit": {"datatype": "int32"}, "available_upgrades": {"datatype": "string"}, "status": {"datatype": "int32"}}, "directives": {"identify": [["license_id"]], "find": [["account_id"], ["activation_key"]]}}