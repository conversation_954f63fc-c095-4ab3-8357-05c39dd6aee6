import { Entity } from "./Entity.mjs";
import { ScriptHelper, jsname } from "./ScriptHelper.mjs";
import fs = require("fs");
import path = require("path");
export interface ICompileUnit {
  template: string;
  location: string;
}

let pascalCasedExtensions = [".mts", ".ts", ".cpp", ".h"];
function makeFileName(template: string, e: Entity) {
  if (pascalCasedExtensions.find((ext) => ext === path.parse(template).ext))
    return jsname(e);
  else return e.name;
}

export async function generate(
  entityName: string,
  compileUnits: ICompileUnit[]
) {
  let e = Entity.fromFile(entityName);
  let h = new ScriptHelper(e);

  for (let cu of compileUnits) {
    let block = /\/\*\s*b::(\w+)\s*\*\/([^]*?)\/\*\s*end\s*\*\//gm;
    let filename = makeFileName(cu.template, e);
    let filePath = path.join(cu.location, `${filename}.${cu.template}`);
    if (!fs.existsSync(filePath)) continue;
    let source = fs.readFileSync(filePath, "utf8");
    let match = block.exec(source);
    while (match) {
      if (e.blocks.has(match[1]))
        throw Error("Block is defined twice for file " + filePath);
      e.blocks.set(match[1], match[2]);
      match = block.exec(source);
    }
  }

  for (let cu of compileUnits) {
    let filename = entityName;
    if (cu.template.endsWith(".mts") || cu.template.endsWith(".ts")) {
      filename = jsname(e);
    }
    let { template } = await import(`./templates/${cu.template}.mjs`);
    let source = template(e, h);
    fs.writeFileSync(
      path.join(cu.location, `${filename}.${cu.template}`),
      source,
      { encoding: "utf8" }
    );
  }
}

export async function mgenerate(cu: ICompileUnit, ...entityNames: string[]) {
  let entities = entityNames.map((eName) => Entity.fromFile(eName));
  let filename = cu.template;
  let { template } = await import(`./templates/${cu.template}.mjs`);
  let source = template(entities);
  fs.writeFileSync(path.join(cu.location, filename), source, {
    encoding: "utf8",
  });
}
