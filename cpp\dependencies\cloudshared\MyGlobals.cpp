#include "MYPrimitives.h"
#include "MYHash.h"
#include "MYTrev.h"
#include "MYDeviceId.h"
#include <chrono>
#include "util/date.h"

using namespace date;

// From MYPrimitives.h
std::string g_emptyString = "";
std::string g_jpgFormat = "jpg";
std::string g_xmpFormat = "xmp";
std::string g_dngFormat = "dng";
std::string g_vidFormat = "m4v";

dummy::hex_dummy_low low = {1};
dummy::hex_dummy_high high = {2};
dummy::hex_dummy_low_lower low_lower = {1};
dummy::hex_dummy_high_lower high_lower = {2};
dummy::hex_struct hex = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19};
no_init_t no_init;

// From MYHash.h
MYHash g_cloudHash((unsigned char *)"0000000000000000000000000000000000000000");
MYHash g_emptyHash;

// From MYTrev.h
MYTRev g_emptyTRev;
MYTRevKindAndVersion g_emptyKindAndVersion = {{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0}, 0};

// From MYDeviceId.h
MYDeviceId MYDeviceId::BootStrap = MYDeviceId::fromInt(0x7FFF);   // Fake ID that is given to a device during first boot until it has a real ID from another source.
const MYDeviceId MYDeviceId::Empty = MYDeviceId::fromInt(0xFFFF); // Empty ID - indicates "no specific device" or "all devices" to APIs
const MYDeviceId MYDeviceId::Cloud = MYDeviceId::fromInt(0x00000000);
const MYDeviceId MYDeviceId::LikelyFirstDevice = MYDeviceId::fromInt(0x00000001);
const MYDeviceId MYDeviceId::DupDeviceIdParent = MYDeviceId::fromInt(0x7FEE);

auto g_unix_epoch = sys_days{1_d / 1 / 1970};
auto g_mylio_epoch = sys_days{1_d / 1 / 2012};
auto g_mylio_cutoff = sys_days{31_d / 12 / 2069};

static uint32_t millisecond_since_midnight()
{
    uint64_t millisecondsSinceEpoch = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now() - g_mylio_epoch).count();
    uint32_t millisecondsSinceMidnight = (uint32_t)(millisecondsSinceEpoch / 86400000);
    return millisecondsSinceMidnight;
}

static uint32_t millisecond_since_yesterday_midnight()
{
    uint64_t millisecondsSinceEpoch = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now() - g_mylio_epoch).count();
    uint32_t millisecondsSinceMidnight = ((uint32_t)(millisecondsSinceEpoch / 86400000)) + 86400000;
    return millisecondsSinceMidnight;
}

uint32_t systemTimeToMylioTime(std::chrono::system_clock::time_point timepoint)
{
    if (timepoint < g_mylio_epoch)
    {
        return millisecond_since_midnight();
    }
    else if (timepoint > g_mylio_cutoff)
    {
        return millisecond_since_yesterday_midnight();
    }

    return (uint32_t)std::chrono::duration_cast<std::chrono::seconds>(timepoint - g_mylio_epoch).count();
}

std::chrono::system_clock::time_point mylioTimeToSystemTime(uint32_t mylioTime)
{
    auto mylioTimeSeconds = std::chrono::seconds(mylioTime);
    auto systemTime = g_mylio_epoch + mylioTimeSeconds;
    return systemTime;
}

uint32_t getMylioNow()
{
    auto now = std::chrono::system_clock::now();
#ifdef MYLIO_CLIENT
    if (now < g_mylio_epoch)
    {
        return millisecond_since_midnight();
    }
    else if (now > g_mylio_cutoff)
    {
        return millisecond_since_yesterday_midnight();
    }
#endif // MYLIO_CLIENT
    return (uint32_t)std::chrono::duration_cast<std::chrono::seconds>(now - g_mylio_epoch).count();
}

uint32_t mylioTimeToUnixTime(uint32_t mylioTime)
{
    auto mylio_unix_diff = g_mylio_epoch - g_unix_epoch;

    auto unixTime = mylioTime + std::chrono::duration_cast<std::chrono::seconds>(mylio_unix_diff).count();
    return (uint32_t)unixTime;
}
