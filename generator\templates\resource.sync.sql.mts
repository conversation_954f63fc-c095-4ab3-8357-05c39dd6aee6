import { Entity, Datatype } from "../Entity.mjs";
import {
  jsname,
  pname,
  sqldt,
  ppdt,
  ScriptHelper,
  blockI,
} from "../ScriptHelper.mjs";

export function template(e: Entity, h: ScriptHelper) {
  let dataFields = h.diskFields.filter((f) => f.name !== "account_id");
  let where = `${h.pk.map((f) => `${f.name} = ${pname(f)}`).join(" and ")}`;
  let __data = `__${e.name}_sync_data`;
  return `

create or replace function x0.${e.name}_ensure_sync_temp_tables()
returns void
as
$$
    create temp table if not exists ${__data} (
        ${dataFields.map((f) => `${pname(f)} ${sqldt(f)}`)}
    );

    truncate table __${e.name}_sync_data;

    create temp table if not exists __have (
        _t bytea
    );

    truncate table __have;
$$
language sql;


select * from a0.${e.name}_ensure_sync_temp_tables();

create or replace function a0.${e.name}_merkle(_account_id int)
returns void
as
$$
    update a0.account_metadata
        set ${e.name}_merkle = agg.merkle
    from (
        select account_id, digest(string_agg(t, null), 'sha1') as merkle
        from (
            select account_id, t from a0.${
              e.name
            } where account_id = _account_id
            order by t
        ) as x
        group by account_id
    ) as agg
    where a0.account_metadata.account_id = agg.account_id
    and (a0.account_metadata.${
      e.name
    }_merkle != agg.merkle or a0.account_metadata.${e.name}_merkle is null);
$$
language sql;


create or replace function a0.${
    e.name
  }_sync(_account_id int, _data_json json, _have_text text[])
returns table (
    op int,
    ${h.pgParams.map((f) => `"${jsname(f)}" ${ppdt(f)}`).join(",\n")}
)
as
$$

insert into ${__data}
select ${dataFields
    .map((f) => {
      switch (f.datatype) {
        case Datatype.binary:
          return `decode("${jsname(f)}", 'base64') as ${pname(f)}`;
        default:
          if (jsname(f) !== f.name) return `"${jsname(f)}" as ${pname(f)}`;
          else return `${f.name} as ${pname(f)}`;
      }
    })
    .join(",\n\t")}
from json_to_recordset(_data_json) 
as t(
    ${h.pgParams.map((f) => `"${jsname(f)}" ${ppdt(f)}`).join(",\n\t")}
);

insert into __have
select decode(t, 'base64') as _t from unnest(_have_text) as t;

select * from a0.account_metadata where account_id = _account_id for update NOWAIT;

update a0.${e.name} cloud
    set 
        deleted = true,
        t = next_trev(greatest(_t, t))
from ${__data}
where ${where}
and (deleted or _deleted) and deleted != _deleted;

update a0.${e.name} cloud
    set ${dataFields.map((f) => `${f.name} = ${pname(f)}`).join(",\n\t")}
from ${__data}
where ${where}
and _t > t;

update ${__data}
    set _t = least(E'\\\\x0000000000000000000000000000000000000000000000000000', decode((a0.${
      e.name
    }_create(
        ${h.pgParams
          .map((f) => {
            if (f.name === "account_id") return "_account_id";
            if (f.name === "t") return "null";
            if (f.datatype === Datatype.binary)
              return `encode(${pname(f)}, 'base64')`;
            return pname(f);
          })
          .join(",\n\t\t")})).t, 'base64'))
where ${pname(h.dk)} is null;

update ${__data}
    set ${pname(h.dk)} = trev_id4(_t)
where ${pname(h.dk)} is null;

insert into a0.${e.name} (
    ${h.diskFields.map((f) => f.name).join(",\n\t")}
)
select
    ${h.diskFields.map((f) => pname(f)).join(",\n\t")}
from ${__data}
where not exists (select * from a0.${e.name} where ${where});

select from a0.${e.name}_merkle(_account_id);

select
    2 as op,
    ${h.pgParams.map((f) => blockI(e, `__s_${f.name}`)).join(",\n\t\t")}
from a0.${e.name} cloud
join ${__data} on ${h.dk.name} = _${h.dk.name}
where account_id = _account_id
and _t < t

union all

select
    2 as op,
    ${h.pgParams.map((f) => blockI(e, `__s_${f.name}`)).join(",\n\t\t")}
from a0.${e.name} cloud
join __have on trev_id4(t) = trev_id4(_t)
where account_id = _account_id
and _t < t or _t is null

union all

select 
    3 as op,
    ${h.diskFields
      .map((f) => {
        if (f.name === "t") return "encode(t, 'base64')";
        return "null";
      })
      .join(",\n\t")}
from __have
left join a0.${
    e.name
  } cloud on account_id = _account_id and trev_id4(t) = trev_id4(_t)
where account_id is null or t < _t;  

$$
language sql;


`;
}
