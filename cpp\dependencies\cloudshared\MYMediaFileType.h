#pragma once

#include <assert.h>
#include <string>
#include <algorithm>
#include "MYPrimitives.h"

enum class MDLHas : unsigned int;
enum class MDLNeeds : unsigned int;

enum class MYParsability : uint8_t
{
    NotParsable = 0,
    Parsable = 1,
    NotAttempted = 2
};

class MYMediaFileType
{
    MYMediaFileType(void) {}

public:
    enum Enum : int8_t
    {
        All = -2,
        Unknown = -1,
        NoType = 0,
        RAW = 1,
        NonRAW,
        Video,
        DisplayImage,
        XMP,
        Preview,
        Thumbnail,

        // Update typeList & typeListSize in MYMediaFileType.cpp if you add to this enum
    };

    static const std::array<Enum, 7> typeList;
    static const int typeListSize = Enum::Thumbnail;

    enum class SimpleEnum
    {
        Preview,
        Thumbnail,
        Original
    };

    static bool isLocalOnlyType(Enum type)
    {
        return (type == Preview) || (type == Thumbnail);
    }

    static bool isGenerated(Enum value)
    {
        return value == Preview || value == Thumbnail;
    }
    static std::string getColName(Enum type)
    {
        if (type == RAW)
            return "RAW";
        if (type == Video)
            return "Video";
        if (type == NonRAW)
            return "NonRAW";
        if (type == XMP)
            return "XMP";
        if (type == DisplayImage)
            return "DisplayImage";
        if (type == Preview)
            return "Preview";
        if (type == Thumbnail)
            return "Thumbnail";

        assert(false);
        return "RAW";
    }
    static std::string getName(Enum type)
    {
        if (type == RAW)
            return "RAW";
        if (type == NonRAW)
            return "NonRAW";
        if (type == Video)
            return "Video";
        if (type == XMP)
            return "XMP";
        if (type == DisplayImage)
            return "DisplayImage";
        if (type == Preview)
            return "Preview";
        if (type == Thumbnail)
            return "Thumbnail";

        assert(false);
        return "RAW";
    }

    static const char *getTypeCode(Enum type)
    {
        if (type == RAW)
            return "r";
        if (type == NonRAW)
            return "n";
        if (type == Video)
            return "v";
        if (type == XMP)
            return "x";
        if (type == DisplayImage)
            return "d";
        if (type == Preview)
            return "p";
        if (type == Thumbnail)
            return "t";

        assert(false);
        return "r";
    }

    static Enum getTypeFromTypeCode(const char *type)
    {
        if (type == nullptr)
        {
            assert(false);
            return Unknown;
        }

        assert(type[1] == '\0');

        if (type[0] == 'r')
            return RAW;
        if (type[0] == 'n')
            return NonRAW;
        if (type[0] == 'v')
            return Video;
        if (type[0] == 'x')
            return XMP;
        if (type[0] == 'd')
            return DisplayImage;
        if (type[0] == 'p')
            return Preview;
        if (type[0] == 't')
            return Thumbnail;

        assert(false);
        return Unknown;
    }

    // static int getHasMask(Enum type);
    // static int getNeedsMask(Enum type);
    // static MDLHas getHasMaskFromBits(Enum type, int value);
    // static MDLNeeds getNeedsMaskFromBits(Enum type, int value);

    static Enum getTypeFromExtension(const std::string &rawext);
    static bool isSpecialDecoderRAW(const std::string &extension);
#ifdef MYLIO_CLIENT
    static bool isPDF(const std::string &extension);
    static bool isPSD(const std::string &extension);
    static bool isPSB(const std::string &extension);
    static bool isNote(const std::string &extension);
    static bool isJpg(const std::string &extension);
    static bool isMov(const std::string &extension);
#endif // MYLIO_CLIENT

    //
    // display image logic
    //
public:
    static bool isDisplayImage(const std::string &filenameNoExt);
    static std::string stripDisplayImagePostfix(const std::string &filenameNoExt);
    static std::string appendDisplayImagePostfix(const std::string &filenameNoExt);
    static std::string getDisplayImagePostfix();

private:
    static const char *const DisplayImagePostfix;
};

typedef uint16_t MYHashRef;
typedef uint16_t MYStringRef;

extern const MYHashRef MYHashRefEmpty;
extern const MYStringRef MYStringRefEmpty;

class MYFileFormatInterner
{
public:
    static bool isInternedId(uint16_t id);
    static const std::string &getFormatFromId(MYStringRef id, MYMediaFileType::Enum type);
    static MYStringRef getIdFromFormat(const std::string &format, MYMediaFileType::Enum type);
};

enum class MYSoftFetch : unsigned int
{
    No = 0,
    CanFetch = 1,
    CanDelete = 2
};

enum class MYAutoPreviewPriority : unsigned int
{
    ImportSession,
    Red,
    InAlbum,
    Edited,
    FourStar,
    FiveStar,
    Flagged,

    Max = Flagged
};

enum class MYAutoOriginalPriority : unsigned int
{
    None,
    SmallVideo,
    SmallNonPhoto,

    Max = SmallNonPhoto
};
