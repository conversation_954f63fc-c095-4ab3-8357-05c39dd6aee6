

    drop view if exists a0."AccountMetadata" cascade;

    create or replace view a0."AccountMetadata" as
    select
    flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		encode(account_merkle, 'base64') as "accountMerkle",
		encode(device_merkle, 'base64') as "deviceMerkle",
		encode(message_merkle, 'base64') as "messageMerkle",
		encode(system_property_merkle, 'base64') as "systemPropertyMerkle",
		encode(user_property_merkle, 'base64') as "userPropertyMerkle",
		encode(device_data_merkle, 'base64') as "deviceDataMerkle",
		next_device_id as "nextDeviceId",
		next_message_id as "nextMessageId",
		encode(bootstrap_device_id, 'base64') as "bootstrapDeviceId",
		next_system_property_id as "nextSystemPropertyId"
    from a0.account_metadata;
    

drop function if exists a0.account_metadata_create; 
        create function a0.account_metadata_create(
            _flags int,
	_modified_time timestamptz,
	_created_time timestamptz,
	_account_id int,
	__account_merkle text,
	__device_merkle text,
	__message_merkle text,
	__system_property_merkle text,
	__user_property_merkle text,
	__device_data_merkle text,
	_next_device_id int,
	_next_message_id int,
	__bootstrap_device_id text,
	_next_system_property_id int
        )
        returns a0."AccountMetadata"
        as $$
        
    declare
        result a0."AccountMetadata";
        _account_merkle bytea;
		_device_merkle bytea;
		_message_merkle bytea;
		_system_property_merkle bytea;
		_user_property_merkle bytea;
		_device_data_merkle bytea;
		_bootstrap_device_id bytea;
    begin
        _account_merkle := decode(__account_merkle, 'base64');
		_device_merkle := decode(__device_merkle, 'base64');
		_message_merkle := decode(__message_merkle, 'base64');
		_system_property_merkle := decode(__system_property_merkle, 'base64');
		_user_property_merkle := decode(__user_property_merkle, 'base64');
		_device_data_merkle := decode(__device_data_merkle, 'base64');
		_bootstrap_device_id := decode(__bootstrap_device_id, 'base64');
        


       
        


        
        
        
        
        _modified_time := coalesce(_modified_time, now());
        _created_time := coalesce(_created_time, now());
        insert into a0.account_metadata (
            flags,
	modified_time,
	created_time,
	account_id,
	account_merkle,
	device_merkle,
	message_merkle,
	system_property_merkle,
	user_property_merkle,
	device_data_merkle,
	next_device_id,
	next_message_id,
	bootstrap_device_id,
	next_system_property_id
        )
        values(
            _flags,
			_modified_time,
			_created_time,
			_account_id,
			_account_merkle,
			_device_merkle,
			_message_merkle,
			_system_property_merkle,
			_user_property_merkle,
			_device_data_merkle,
			_next_device_id,
			_next_message_id,
			_bootstrap_device_id,
			_next_system_property_id
        )
        returning
            flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		encode(account_merkle, 'base64') as "accountMerkle",
		encode(device_merkle, 'base64') as "deviceMerkle",
		encode(message_merkle, 'base64') as "messageMerkle",
		encode(system_property_merkle, 'base64') as "systemPropertyMerkle",
		encode(user_property_merkle, 'base64') as "userPropertyMerkle",
		encode(device_data_merkle, 'base64') as "deviceDataMerkle",
		next_device_id as "nextDeviceId",
		next_message_id as "nextMessageId",
		encode(bootstrap_device_id, 'base64') as "bootstrapDeviceId",
		next_system_property_id as "nextSystemPropertyId"
        into result;

        



        

    
        



        return result;
    end;
        $$
        language plpgsql;
        

drop function if exists a0.account_metadata_update; 
        create function a0.account_metadata_update(
            _flags int,
	_modified_time timestamptz,
	_created_time timestamptz,
	_account_id int,
	__account_merkle text,
	__device_merkle text,
	__message_merkle text,
	__system_property_merkle text,
	__user_property_merkle text,
	__device_data_merkle text,
	_next_device_id int,
	_next_message_id int,
	__bootstrap_device_id text,
	_next_system_property_id int
        )
        returns a0."AccountMetadata"
        as $$
        
    declare
        result a0."AccountMetadata";
        _account_merkle bytea;
		_device_merkle bytea;
		_message_merkle bytea;
		_system_property_merkle bytea;
		_user_property_merkle bytea;
		_device_data_merkle bytea;
		_bootstrap_device_id bytea;
    begin
        _account_merkle := decode(__account_merkle, 'base64');
		_device_merkle := decode(__device_merkle, 'base64');
		_message_merkle := decode(__message_merkle, 'base64');
		_system_property_merkle := decode(__system_property_merkle, 'base64');
		_user_property_merkle := decode(__user_property_merkle, 'base64');
		_device_data_merkle := decode(__device_data_merkle, 'base64');
		_bootstrap_device_id := decode(__bootstrap_device_id, 'base64');
        


       
        


        
        
        _modified_time := now();
        update a0.account_metadata
        set
            flags = _flags,
			modified_time = _modified_time,
			created_time = _created_time,
			account_merkle = _account_merkle,
			device_merkle = _device_merkle,
			message_merkle = _message_merkle,
			system_property_merkle = _system_property_merkle,
			user_property_merkle = _user_property_merkle,
			device_data_merkle = _device_data_merkle,
			next_device_id = _next_device_id,
			next_message_id = _next_message_id,
			bootstrap_device_id = _bootstrap_device_id,
			next_system_property_id = _next_system_property_id
        where account_id = _account_id
        returning
            flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		encode(account_merkle, 'base64') as "accountMerkle",
		encode(device_merkle, 'base64') as "deviceMerkle",
		encode(message_merkle, 'base64') as "messageMerkle",
		encode(system_property_merkle, 'base64') as "systemPropertyMerkle",
		encode(user_property_merkle, 'base64') as "userPropertyMerkle",
		encode(device_data_merkle, 'base64') as "deviceDataMerkle",
		next_device_id as "nextDeviceId",
		next_message_id as "nextMessageId",
		encode(bootstrap_device_id, 'base64') as "bootstrapDeviceId",
		next_system_property_id as "nextSystemPropertyId"
        into result;

        

    
        



        return result;
    end;
        $$
        language plpgsql;
        

drop function if exists a0.account_metadata_read_by_account_id; 
        create function a0.account_metadata_read_by_account_id(
            _account_id int
        )
        returns a0."AccountMetadata"
        as $$
        select
        flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		encode(account_merkle, 'base64') as "accountMerkle",
		encode(device_merkle, 'base64') as "deviceMerkle",
		encode(message_merkle, 'base64') as "messageMerkle",
		encode(system_property_merkle, 'base64') as "systemPropertyMerkle",
		encode(user_property_merkle, 'base64') as "userPropertyMerkle",
		encode(device_data_merkle, 'base64') as "deviceDataMerkle",
		next_device_id as "nextDeviceId",
		next_message_id as "nextMessageId",
		encode(bootstrap_device_id, 'base64') as "bootstrapDeviceId",
		next_system_property_id as "nextSystemPropertyId"
        from a0.account_metadata
        where account_id = _account_id;
        $$
        language sql;
        

drop function if exists a0.account_metadata_delete_by_account_id; 
        create function a0.account_metadata_delete_by_account_id(
            _account_id int
        )
        returns void
        as $$
        
    declare
        
        
    begin
        
        


       
        


        

    delete from a0.account_metadata
    where account_id = _account_id;

    
    
        



        
    end;
        $$
        language plpgsql;
        
