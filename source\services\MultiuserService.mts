import { Context } from "../system/Context.mjs";
import { InvitationRestDataService } from "../dataServices/Invitation.rest.dataService.mjs";
import { Invitation } from "../models/Invitation.model.mjs";
import { config } from "../system/Config.mjs";
import moment from "moment"
import { EmailService } from "./EmailService.mjs";
import { makeError } from "../system/error.mjs";
import { AccountService } from "./AccountService.mjs";
import { InvitationLogEntry } from "../models/InvitationLogEntry.model.mjs";
import { InvitationLogEntryRestDataService } from "../dataServices/InvitationLogEntry.rest.dataService.mjs";
import { TokenService } from "./TokenService.mjs";
import { Token } from "../models/Token.mjs";


export class MultiuserService {
    constructor(
        private invitationDataService: InvitationRestDataService,
        private invitationLogEntryDataService: InvitationLogEntryRestDataService,
        private accountService: AccountService,
        private emailService: EmailService,
        private tokenService: TokenService) { }


    async acceptInvitation(context: Context, pin: string) {
        const invitation = await this.invitationDataService.readByPin(context, pin);
        if (!invitation)
            throw makeError(400, "INVALID_INVITATION", "Invitation not found");
        if (invitation.expiresAt() < new Date())
            throw makeError(400, "INVITATION_EXPIRED", "Invitation has expired");
        const account = await this.accountService.read(context, invitation.accountId());
        const logEntry = new InvitationLogEntry({
            accountId: invitation.accountId(),
            email: invitation.email(),
            acceptedDate: moment.utc().toDate(),
            pin: invitation.pin()
        });

        const rtoken = this.tokenService.rtoken(context, account);
        const token = this.tokenService.atoken(context, account, Token.tryDecode(rtoken));
        await this.invitationLogEntryDataService.create(context, logEntry);
        await this.invitationDataService.deleteByAccountIdAndEmailAndPin(context, account.accountId(), invitation.email(), invitation.pin());
        return { invitation, account, rtoken, token };
    }

    async sendInvitation(
        context: Context,
        protocol: string,
        aid: number,
        email: string,
        deviceConfig: string) {

        // await this.invitationDataService.deleteByAccountIdAndEmail(context, aid, email);
        const pin = crypto.randomUUID().toString();
        let invitation = new Invitation({
            accountId: aid,
            email: email,
            pin,
            expiresAt: moment().add(moment.duration(config.invitation_duration)).toDate(),
            deviceConfig
        });
        invitation = await this.invitationDataService.create(context, invitation);

        let state = {
            email,
            target: "app",
            protocol,
            invitation: invitation.pin(),
        };

        let link = `${config.website}/launch?s=${btoa(
            JSON.stringify(state)
        )}`;
        await this.emailService.sendInvitation(context, email, link, pin);
        return invitation;
    }

    async readInvitation(context: Context, pin: string) {
        const invitation = await this.invitationDataService.readByPin(context, pin);
        if (!invitation) {
            throw makeError(400, "INVALID_INVITATION", "Unable to find invitation with a pin number of " + pin);
        }
        const account = await this.accountService.read(context, invitation.accountId());
        return { invitation, account };
    }

    async deleteInvitation(context: Context, pin: string) {
        return this.invitationDataService.deleteByPin(context, pin);
    }

}