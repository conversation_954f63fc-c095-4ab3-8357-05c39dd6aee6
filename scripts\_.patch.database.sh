#!/bin/bash

source ./_.helpers.sh
source ./_.postgres.sh

echo "started $0"

SERVICE="$1"
HOST="$2"
PORT="$3"
USER="$4"
DATABASE_DIR="../database/vnext"

PATCH_NUMBER=$(prompt "Enter the patch number of the file you want to apply for '$SERVICE':")


PATCH_FILE="patch.$SERVICE.human.ddl.$PATCH_NUMBER.sql"

TSC="../node_modules/.bin/tsc"

if [[ ! -f $DATABASE_DIR/$PATCH_FILE ]]
then
    echo "no patch file $PATCH_FILE found"
    echo "exiting..."

    exit 1
fi

if [[ $HOST == "localhost" ]]; then
  export PGPASSWORD="password"
fi

open_pg_connection $HOST $PORT $USER

if [[ $SERVICE == "resource" ]]; then
  rm $DATABASE_DIR/setup/resource*.patch.sql

  $TSC $DATABASE_DIR/inflate.ts --project ../tsconfig.json

  node $DATABASE_DIR/inflate.js \
       -c $DATABASE_DIR/localhost.json \
       -d resource \
       -i $DATABASE_DIR/$PATCH_FILE \
       -o $DATABASE_DIR/setup/~database.patch.sql

  psql -h $HOST -p $PORT -U $USER -d resource0 -f $DATABASE_DIR/setup/resource0.patch.sql

  run_script "$DATABASE_DIR/db.clean.sql" "-v dbnameq='resource0' -v dbname=resource0"
  run_script "$DATABASE_DIR/setup/resource0.dml.sql"
  run_script "$DATABASE_DIR/setup/resource0.secure.sql"

  if [[ $5 ]]; then
    HOST=$5
    echo "changed host to $HOST"
  fi

  if [[ $HOST == "localhost" ]]; then
    export PGPASSWORD="password"
  fi

  psql -h $HOST -p $PORT -U $USER -d resource1 -f $DATABASE_DIR/setup/resource1.patch.sql

  run_script "$DATABASE_DIR/db.clean.sql" "-v dbnameq='resource1' -v dbname=resource1"
  run_script "$DATABASE_DIR/setup/resource1.dml.sql"
  run_script "$DATABASE_DIR/setup/resource1.secure.sql"
else
  run_script "$DATABASE_DIR/$PATCH_FILE"
  run_script "$DATABASE_DIR/db.clean.sql" "-v dbnameq='$SERVICE' -v dbname=$SERVICE"
  run_script "$DATABASE_DIR/setup/$SERVICE.dml.sql"
  run_script "$DATABASE_DIR/setup/$SERVICE.secure.sql"
fi

close_pg_connection

echo "completed $0"
