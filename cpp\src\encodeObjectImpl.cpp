#include "helpers.h"
#include "getFieldInfo.h"
#include <napi.h>

bool helpers::encodeObject(MYBJsonBigRW *writer, int sectionId, const Napi::Object &jsObj, int objectSid)
{

    if (objectSid == -1)
        writer->StartObject();
    else
        writer->StartObject(objectSid);

    auto arrKeys = jsObj.GetPropertyNames();
    for (uint32_t i = 0; i < arrKeys.Length(); ++i)
    {
        std::string key = arrKeys.Get(i).ToString();
        auto value = jsObj.Get(key);
        bool hasValue = !(value.IsNull() || value.IsUndefined());
        if (!hasValue)
        {
            continue;
        }

        if (key == "d")
        {
            auto text = value.ToString().Utf8Value();
            auto d = helpers::text2bin(text);
            writer->Raw((uint8_t *)d.data(), d.size());
        }
        else
        {
            Field f;
            if (getFieldInfo(sectionId, key, f))
            {
                switch (f.type)
                {
                case BJsonType::string:
                    writer->String(f.sid, value.ToString());
                    break;
                case BJsonType::hash:
                    writer->MYHash(f.sid, helpers::hash(value.ToString()));
                    break;
                case BJsonType::trev:
                    writer->MYTRev(f.sid, helpers::trev(value.ToString()));
                    break;
                case BJsonType::varint:
                    if (f.isDate)
                    {
                        writer->Int64(f.sid, value.ToNumber());
                    }
                    else
                    {
                        writer->Int64(f.sid, value.ToNumber());
                    }
                    break;
                case BJsonType::object:
                {
                    /*
                    --DEBUGGING CODE
                    std::string base64String = value.ToString().Utf8Value();
                    std::string blob = base64_decode(base64String);
                    std::string hex = MYString::blobToHexString(blob);
                    */
                    auto d = helpers::text2bin(value.ToString());
                    if (d.size() < 1)
                        break;
                    MYBJsonView existing(d.data(), d.data() + d.size());
                    writer->ExistingObject(f.sid, existing);
                    break;
                }
                case BJsonType::binary:
                {
                    auto buffer = helpers::text2bin(value.ToString().Utf8Value());
                    writer->Binary(f.sid, &buffer[0], buffer.size(), BJsonType::binary);
                    break;
                }
                case BJsonType::true1:
                    writer->Bool(f.sid, value.ToBoolean());
                default:
                    break;
                }
            }
        }
    }
    writer->EndObject();

    return true;
}