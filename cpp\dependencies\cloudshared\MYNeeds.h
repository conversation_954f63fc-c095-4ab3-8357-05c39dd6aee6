#pragma once
#include "bjson.h"
#include <vector>
#include "MYMediaFileType.h"
#include "vector_map.h"
#include "MYLiterals.h"

class MYNeeds;

struct NeedsBits
{
public:
    uint8_t _value;

    NeedsBits() : _value(0) {}
    NeedsBits(no_init_t) {}
    NeedsBits(uint8_t bits) : _value(bits) {}
    NeedsBits(MYMediaFileType::Enum type)
    {
        _value = 1 << (7 - (uint8_t)type);
    }

    static NeedsBits None;
    static NeedsBits RAW;
    static NeedsBits NonRAW;
    static NeedsBits Video;
    static NeedsBits RAWorVideo;
    static NeedsBits XMP;
    static NeedsBits DisplayImage;
    static NeedsBits Thumbnail;
    static NeedsBits Preview;
    static NeedsBits OriginalsAndXMP;
    static NeedsBits OriginalsIgnoringXMP;
    static NeedsBits AnyFiles;

    void reset()
    {
        _value = 0;
    }

    void set(NeedsBits type, bool val = true)
    {
        assert(type._value > 0);
        if (val)
            _value |= type._value;
        else
            _value &= ~type._value;
    }

    bool none() const
    {
        return _value == 0;
    }

    bool any() const
    {
        return _value > 0;
    }

    bool test(NeedsBits type) const
    {
        assert(type._value > 0);
        return (_value & type._value) > 0;
    }

    bool empty() const
    {
        return _value == 0;
    }

    uint8_t toInt() const
    {
        return _value;
    }

    NeedsBits operator|(NeedsBits other) const
    {
        return NeedsBits(_value | other._value);
    }

    NeedsBits operator^(NeedsBits other) const
    {
        return NeedsBits(_value ^ other._value);
    }

    NeedsBits &operator|=(NeedsBits other)
    {
        _value |= other._value;
        return *this;
    }

    NeedsBits &operator&=(NeedsBits other)
    {
        _value &= other._value;
        return *this;
    }

    NeedsBits operator~()
    {
        return NeedsBits(~_value);
    }

    NeedsBits operator&(NeedsBits other) const
    {
        return NeedsBits(_value & other._value);
    }

    NeedsBits operator~() const
    {
        return NeedsBits(~_value);
    }

    bool operator==(NeedsBits other) const
    {
        return _value == other._value;
    }

    bool operator!=(NeedsBits other) const
    {
        return _value != other._value;
    }

    std::string toString() const;
};

static_assert(sizeof(NeedsBits) == sizeof(uint8_t), "Mismatched");

// enum class MYDeviceNeedsForFormatsMask
//{
//     Nothing = 0,
//     Wants = 0x1,
//     Has = 0x2,
//     CanGenerate = 0x4
// };
//
// struct MYDeviceNeedsForFormats
//{
//     MYDeviceNeedsForFormats() {};
//     MYDeviceNeedsForFormats(std::string format, MYDeviceNeedsForFormatsMask mask) : _format(std::move(format)), _wantsHasCanGenerate(mask) {}
//
//     MYDeviceNeedsForFormats(MYDeviceNeedsForFormats&& other) : _format(std::move(other._format))
//     {
//         _wantsHasCanGenerate = other._wantsHasCanGenerate;
//     }
//
//     MYDeviceNeedsForFormats(const MYDeviceNeedsForFormats& other) : _format(other._format)
//     {
//         _wantsHasCanGenerate = other._wantsHasCanGenerate;
//     }
//
//     MYDeviceNeedsForFormats& operator=(const MYDeviceNeedsForFormats& other)
//     {
//         _format = other._format;
//         _wantsHasCanGenerate = other._wantsHasCanGenerate;
//         return *this;
//     }
//
//     MYDeviceNeedsForFormats& operator=(MYDeviceNeedsForFormats&& other)
//     {
//         _format = std::move(other._format);
//         _wantsHasCanGenerate = other._wantsHasCanGenerate;
//         return *this;
//     }
//
//     std::string _format;
//     MYDeviceNeedsForFormatsMask _wantsHasCanGenerate = MYDeviceNeedsForFormatsMask::Nothing;
// };

class MYDeviceNeeds
{
public:
    MYDeviceNeeds(uint64_t needs) : _values(needs){};
    MYDeviceNeeds(MYBJsonIterator &begin, const MYBJsonIterator &end);

    void validate() const
    {
        assert((_policyWants & _softWants).any() == 0);
        assert((_policyWants & _hardWants).any() == 0);
        assert((_hardWants & _softWants).any() == 0);

        assert((_hasLatest & _hasStale).any() == 0);
        assert((_hasLatest & _canGenerateLatestOrStale).any() == 0);
        assert((_hasStale & _canGenerateLatestOrStale).any() == 0);
    }

    ~MYDeviceNeeds()
    {
        // validate();
    }

    bool operator==(const MYDeviceNeeds &other) const
    {
        return _values == other._values;
    }

    bool operator!=(const MYDeviceNeeds &other) const
    {
        return _values != other._values;
    }

    void setPolicyWants(NeedsBits wants)
    {
        _policyWants |= wants;
        validate();
    }

    void resetPolicyWants(NeedsBits wants)
    {
        _policyWants &= ~wants;
    }

    void setSoftWants(NeedsBits wants)
    {
        _softWants |= wants;
        validate();
    }

    void resetSoftWants(NeedsBits wants)
    {
        _softWants &= ~wants;
    }

    void setHardWants(NeedsBits wants)
    {
        _hardWants |= wants;
        validate();
    }

    void resetHardWants(NeedsBits wants)
    {
        _hardWants &= ~wants;
    }

    void resetWants(NeedsBits wants)
    {
        _policyWants &= ~wants;
        _hardWants &= ~wants;
        _softWants &= ~wants;
    }

    void setHasLatest(NeedsBits has)
    {
        _hasLatest |= has;
        validate();
    }

    void resetHasLatest(NeedsBits has)
    {
        _hasLatest &= ~has;
    }

    void setHasStale(NeedsBits has)
    {
        _hasStale |= has;
        validate();
    }

    void resetHasStale(NeedsBits has)
    {
        _hasStale &= ~has;
    }

    void resetHas(NeedsBits has)
    {
        _hasLatest &= ~has;
        _canGenerateLatestOrStale &= ~has;
        _hasStale &= ~has;
    }

    void setCanGenerateLatestOrStale(NeedsBits has)
    {
        _canGenerateLatestOrStale |= has;
        validate();
    }

    void resetCanGenerateLatestOrStale(NeedsBits has)
    {
        _canGenerateLatestOrStale &= ~has;
    }

    void setHardDontWants(NeedsBits data)
    {
        _hardDontWants |= data;
    }

    void resetHardDontWants(NeedsBits data)
    {
        _hardDontWants &= ~data;
    }

    void setCantParse(NeedsBits data)
    {
        _cantParse |= data;
    }

    void resetCantParse(NeedsBits data)
    {
        _cantParse &= ~data;
    }

    const NeedsBits hardOrPolicyWants() const { return _policyWants | _hardWants; }
    const NeedsBits policyWants() const { return _policyWants; }
    const NeedsBits softWants() const { return _softWants; }
    const NeedsBits hardWants() const { return _hardWants; }

    const NeedsBits hardDontWants() const { return _hardDontWants; }
    const NeedsBits cantParse() const { return _cantParse; }

    const NeedsBits hasLatest() const { return _hasLatest; }
    const NeedsBits hasStale() const { return _hasStale; }
    const NeedsBits canGenerateLatestOrStale() const { return _canGenerateLatestOrStale; }
    const NeedsBits hasStaleOrCanGenerate() const { return _hasStale | _canGenerateLatestOrStale; }

    const NeedsBits hasLatestOrStale() const { return _hasLatest | _hasStale; }
    const NeedsBits hasForDisplay() const { return _hasLatest | _hasStale; }

    const NeedsBits canGenerateLatest(const class MYNeeds &knownLatestOriginals) const;

    const NeedsBits anyWants() const
    {
        return policyWants() | softWants() | hardWants();
    }

    bool hardDontWants(NeedsBits types) const { return _hardDontWants.test(types); }
    bool cantParse(NeedsBits types) const { return _cantParse.test(types); }

    bool anyWants(NeedsBits type) const
    {
        return anyWants().test(type);
    }

    bool policyWants(NeedsBits type) const
    {
        return policyWants().test(type);
    }

    bool hardOrPolicyWants(NeedsBits type) const
    {
        return hardOrPolicyWants().test(type);
    }

    bool softWants(NeedsBits type) const
    {
        return softWants().test(type);
    }

    bool hardWants(NeedsBits type) const
    {
        return hardWants().test(type);
    }

    bool hasLatest(NeedsBits type) const
    {
        return hasLatest().test(type);
    }

    bool hasStale(NeedsBits type) const
    {
        return hasStale().test(type);
    }

    bool hasLatestOrStale(NeedsBits type) const
    {
        return hasLatestOrStale().test(type);
    }

    bool hasForDisplay(NeedsBits type) const
    {
        return hasForDisplay().test(type);
    }

    bool canGenerateLatestOrStale(NeedsBits type) const
    {
        return canGenerateLatestOrStale().test(type);
    }

    bool canGenerateLatest(NeedsBits type, const class MYNeeds &needs) const
    {
        return canGenerateLatest(needs).test(type);
    }

    // void setForFormat(const std::string& format, MYDeviceNeedsForFormatsMask wants);
    // void setForFormat(std::string&& format, MYDeviceNeedsForFormatsMask wants);
    // MYDeviceNeedsForFormatsMask getForFormat(const std::string& format);

    bool empty() const
    {
        return _values == 0;
    }

    MYDeviceNeeds operator|(MYDeviceNeeds other) const
    {
        return MYDeviceNeeds(_values | other._values);
    }

    MYDeviceNeeds operator&(MYDeviceNeeds other) const
    {
        return MYDeviceNeeds(_values & other._values);
    }

    MYDeviceNeeds operator~() const
    {
        return MYDeviceNeeds(~_values);
    }

    std::string toString() const;

private:
    friend MYNeeds;

    // std::unique_ptr<std::vector<MYDeviceNeedsForFormats>> _perFormat;

    union
    {
        // This is a compressed storage format. It's 64-bit representation and stored as 64-bit,
        // but moving the most commonly used bits upfront. This makes the storage smaller inside
        // a bjson format which has a variable length integer.
        union
        {
            struct
            {
                // Preserve this order, as well as the StorageFormat order below!
                // hasLatest, hasStale & _canGenerateLatestOrStale are mutually exclusive
                // policyWants, policyWants, softWants are mutually exclusive
                NeedsBits _hasLatest;   // Has   A  &~  Has B
                NeedsBits _policyWants; // Wants A  &~  Wants B
                NeedsBits _softWants;   // Wants B  &~  Wants A
                NeedsBits _hasStale;    // Has   B  &~  Has A

                NeedsBits _canGenerateLatestOrStale; // Has A   &  Has B
                NeedsBits _hardWants;                // Wants A &  Want B
                NeedsBits _cantParse;
                NeedsBits _hardDontWants;
            };
            uint64_t _values;
        };

        struct
        {
            uint32_t _mapped1;
            uint32_t _mapped2;
        };
    };

    union StorageFormat
    {
        union
        {
            struct
            {
                uint8_t _hasA;
                uint8_t _wantsA;
                uint8_t _wantsB;
                uint8_t _hasB;

                uint8_t _cantParse;
                uint8_t _hardDontWants;
                uint8_t _padding1;
                uint8_t _padding2;
            };
            uint64_t _value;
        };

        struct
        {
            uint32_t _hasWant;
            uint32_t _flagsAndPadding;
        };
    };

    void fromStorage(uint64_t value)
    {
        const StorageFormat &storage = (const StorageFormat &)value;

        StorageFormat swapped;
        swapped._hasA = storage._hasB;
        swapped._wantsA = storage._wantsB;
        swapped._wantsB = storage._wantsA;
        swapped._hasB = storage._hasA;

        _mapped1 = storage._hasWant & ~swapped._hasWant;
        _mapped2 = storage._hasWant & swapped._hasWant;
        _cantParse = storage._cantParse;
        _hardDontWants = storage._hardDontWants;

        // The above is the moral equivalent of:

        //_hasLatest._value = storage._hasA & ~storage._hasB;
        //_policyWants._value = storage._wantsA & ~storage._wantsB;
        //_hasStale._value = storage._hasB & ~storage._hasA;
        //_softWants._value = storage._wantsB & ~storage._wantsA;
        //
        //_canGenerateLatestOrStale._value = storage._hasA & storage._hasB;
        //_hardWants._value = storage._wantsA & storage._wantsB;

        validate();
    }

    uint64_t toStorage() const
    {
        validate();

        StorageFormat storage;
        storage._value = _values;

        storage._hasA |= _canGenerateLatestOrStale._value;
        storage._wantsA |= _hardWants._value;
        storage._wantsB |= _hardWants._value;
        storage._hasB |= _canGenerateLatestOrStale._value;

        storage._flagsAndPadding = 0;
        storage._cantParse = _cantParse._value;
        storage._hardDontWants = _hardDontWants._value;

        return storage._value;
    }

    template <typename TBJson>
    void serializeToBJson(TBJson &writer) const
    {
        writer.Uint64(MYLiterals::Needs::values, toStorage());
    }
};

class MYNeeds
{
public:
    typedef vector_map<MYDeviceId, MYDeviceNeeds> devices_type;

    MYNeeds() {}
    MYNeeds(size_t numdevices)
    {
        _devices.reserve(numdevices);
    }
    MYNeeds(size_t numdevices, MYBJsonIterator &begin, const MYBJsonIterator &end);
    MYNeeds(const MYNeeds &);
    MYNeeds(MYNeeds &&other);

    void compact(const vector_set<MYDeviceId> &orderedDeletedDevices);

    MYNeeds &operator=(const MYNeeds &);
    MYNeeds &operator=(MYNeeds &&other);

    bool operator==(const MYNeeds &other) const;
    bool operator!=(const MYNeeds &other) const
    {
        return !(*this == other);
    }

    void deserializeFromBJson(MYBJsonIterator &begin, const MYBJsonIterator &end);

    template <typename TBJson>
    void serializeToBJson(TBJson &writer) const
    {
        if (!_devices.empty())
        {
            for (const auto &device : _devices)
            {
                writer.Uint32(MYLiterals::Needs::deviceId, device.first.toInt());
                device.second.serializeToBJson(writer);
            }
        }
    }

    const MYDeviceNeeds anyDevice() const
    {
        MYDeviceNeeds needs(0);
        for (const auto &deviceNeeds : _devices)
        {
            needs = needs | deviceNeeds.second;
        }
        return needs;
    }

    const MYDeviceNeeds &getDeviceNeedsOrEmpty(MYDeviceId deviceId) const;
    MYDeviceNeeds *getOrCreateDeviceNeeds(MYDeviceId deviceId);
    MYDeviceNeeds *getDeviceNeedsOrNull(MYDeviceId deviceId);
    const MYDeviceNeeds *getDeviceNeedsOrNull(MYDeviceId deviceId) const;
    void removeDeviceNeeds(MYDeviceId deviceId);

    template <typename TFunc>
    void eraseDeviceNeeds(const TFunc &pred)
    {
        _devices.erase_at(std::remove_if(_devices.begin(), _devices.end(), pred), _devices.end());
    }

    void emplace_from_sorted_unchecked(MYDeviceId deviceId);
    void reserve(size_t size);

    const devices_type &getDeviceNeeds() const
    {
        return _devices;
    }

    void clear()
    {
        _devices.clear();
    }

    bool empty() const;
    devices_type::iterator begin()
    {
        return _devices.begin();
    }

    devices_type::iterator end()
    {
        return _devices.end();
    }

    devices_type::const_iterator begin() const
    {
        return _devices.begin();
    }

    devices_type::const_iterator end() const
    {
        return _devices.end();
    }

    std::string toString() const;

private:
    friend MYDeviceNeeds;

    devices_type _devices;
};
