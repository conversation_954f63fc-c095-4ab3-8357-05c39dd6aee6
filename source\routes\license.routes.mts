import express = require("express");
import { microservice as g } from "../microservices/account.microservice.mjs";
import { LicenseStatus, sanitizeOutput } from "../models/License.model.mjs";
import { admin, safeAny, safeNone, secure } from "../system/safe.mjs";
import { License, sanitizeInput } from "../models/License.model.mjs";
import { sanitizeOutput as sanitizeOutputT } from "../models/LicenseTemplate.model.mjs";
import { Mode } from "../models/IModel.mjs";
import { makeError } from "../system/error.mjs";
import { ids } from "../system/Strings.mjs";


export function addLicenseRoutes(router: express.Router) {
  router.put(
    "/accounts/:aid/licenses/:id",
    safeAny,
    secure,
    admin,
    async (req, res, next) => {
      let context = req.context;
      let license = new License(
        sanitizeInput(req.context.any, context.hasAdminRights(), Mode.Update)
      );
      license.accountId(context.aid);
      license.licenseId(req.params.id);
      license = await g.licenseService.update(context, license);
      context.dumpLog();
      return res
        .status(200)
        .json(sanitizeOutput(license.state(), context.hasAdminRights()));
    }
  );

  router.post(
    "/accounts/:aid/licenses",
    safeAny,
    secure,
    async (req, res, next) => {
      let context = req.context;
      let license = new License(
        sanitizeInput(req.context.any, context.hasAdminRights(), Mode.Create)
      );
      if (!license.status())
        license.status(LicenseStatus.Active);
      if (process.env.NODE_ENV === "production" && !context.hasAdminRights()) {
        const template = await g.licenseService.readTemplate(context, context.aid);
        if (!template.public())
          throw makeError(403, ids.NOT_AUTHORIZED, `Not authorized to create a license using license template ${template.templateId()}`);
      }
      let account = await g.accountService.read(context, context.aid);
      license = await g.licenseService.createMylioLicense(context, account, license);
      context.dumpLog();
      return res
        .status(200)
        .json(sanitizeOutput(license.state(), context.hasAdminRights()));
    }
  );

  router.get(
    "/accounts/:aid/licenses/:id",
    safeNone,
    secure,
    async (req, res, next) => {
      let context = req.context;
      let sub = await g.licenseService.read(
        context,
        req.params.id
      );
      context.dumpLog();
      if (!sub) res.status(404).send();
      else
        res
          .status(200)
          .json(sanitizeOutput(sub.state(), context.hasAdminRights()));
    }
  );

  router.get(
    "/accounts/:aid/licenses",
    safeNone,
    secure,
    async (req, res, next) => {
      let context = req.context;
      let subs = await g.licenseService.list(context, context.aid);
      return res
        .status(200)
        .json(
          subs.map((s) => sanitizeOutput(s.state(), context.hasAdminRights()))
        );
    }
  );

  router.delete(
    "/accounts/:aid/licenses/:id",
    safeNone,
    secure,
    async (req, res, next) => {
      let context = req.context;
      let license = await g.licenseService.read(context, req.params.id);
      license.status(LicenseStatus.Deleted);
      license.endDate(new Date());
      license = await g.licenseService.update(context, license);
      return res
        .status(200)
        .send(sanitizeOutput(license, context.hasAdminRights()));
    }
  );

  router.get(
    "/accounts/:aid/license-templates",
    safeNone,
    secure,
    async (req, res, next) => {
      let context = req.context;
      let templates = await g.licenseService.listTemplates(
        context,
        context.aid
      );
      return res
        .status(200)
        .send(
          templates.map((t) => sanitizeOutputT(t, context.hasAdminRights()))
        );
    }
  );


  router.get(
    "/licenses/delete-expired-licenses",
    safeNone,
    async (req, res, next) => {
      let context = req.context;
      setTimeout(async () => {
        await g.licenseService.findAndDeleteExpiredLicenses(context);
      }, 500);
      return res.status(200).send();
    }
  );

  router.get(
    "/license-keys/:k/redeem",
    safeAny,
    secure,
    async (req, res, next) => {
      let context = req.context;
      await g.fastSpringService.redeemLicenseKey(context, context.token.aid(), req.params.k);
      return res.status(200).send({});
    }
  );


  router.post("/accounts/:aid/licenses/:id/revoke",
    safeAny,
    secure,
    admin,
    async (req, res, next) => {
      let context = req.context;
      let license = await g.licenseService.revokeLicense(context, req.params.id);
      return res.status(200).json(sanitizeOutput(license.state(), context.hasAdminRights()));
    }
  );

  router.get("/accounts/:aid/available-upgrades", safeAny, secure, async (req, res, next) => {
    let context = req.context;
    let feature = parseInt(req.query.feature as string || '0');
    let upgrades = await g.licenseService.computeAvailableUpgrades(context, context.aid, feature);
    let skus = upgrades.map(u => u.sku);
    return res.status(200).json(skus);
  });

}


