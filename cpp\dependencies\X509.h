#include <cstdio>
#include <iostream>
#include <openssl/pem.h>
#include <openssl/x509.h>
#include <functional>
#include <memory>

using RSA_ptr = std::unique_ptr<::RSA, decltype(&::RSA_free)>;
using BIO_MEM_ptr = std::unique_ptr<::BIO, decltype(&::BIO_free)>;
using X509_ptr = std::unique_ptr<::X509, decltype(&::X509_free)>;
using BN_ptr = std::unique_ptr<::BIGNUM, decltype(&::BN_free)>;
using X509_REQ_ptr = std::unique_ptr<::X509_REQ, decltype(&::X509_REQ_free)>;
using EVP_PKEY_ptr = std::unique_ptr<::EVP_PKEY, decltype(&::EVP_PKEY_free)>;
using BIO_ptr = std::unique_ptr<::BIO, decltype(&::BIO_free)>;
using ASN1_INTEGER_ptr = std::unique_ptr<::ASN1_INTEGER, decltype(&::ASN1_INTEGER_free)>;

class X509Name
{
public:
	X509Name(X509_NAME *wrapped);
	std::string commonName();
	std::string countryName();
	std::string org();
	void commonName(const std::string &value);
	void setText(int nid, const std::string &value);
	void countryName(const std::string &value);
	void org(const std::string &value);
	std::string getText(int nid);
	friend class X509Certificate;

private:
	X509_NAME *wrapped_;
};

class X509Certificate
{
public:
	X509Certificate(std::string keyPEM, std::string certPEM);
	X509Certificate();
	bool sign();
	bool sign(X509Certificate &ca);
	X509Name issuer();
	X509Name subject();
	void subject(const X509Name &value);
	void issuer(const X509Name &value);
	std::string certificatePEM();
	std::string keyPEM();

private:
	EVP_PKEY_ptr pk_;
	X509_ptr x509_;
	std::string error_;
	EVP_PKEY_ptr makeKey();
	X509_ptr makeX509(EVP_PKEY *pk);
	BIO_ptr string_to_bio(const std::string &value);
	std::string bio_to_string(BIO *bio);
	std::string pk_to_pem(EVP_PKEY *pk);
	std::string x509_to_pem(X509 *x509);
	X509_ptr pem_to_x509(const std::string &pem);
	EVP_PKEY_ptr pem_to_pk(const std::string &pem);
};
