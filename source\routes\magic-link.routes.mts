import express = require("express");
import { microservice as g } from "../microservices/account.microservice.mjs";
import {
  safeNone,
  safeAny,
  secure,
  secureURLToken,
  secureURLTokenImpl,
} from "../system/safe.mjs";
import { sanitizeOutput, Account } from "../models/Account.model.mjs";
import { Context } from "../system/Context.mjs";
import { config, getServices } from "../system/Config.mjs";
import { ids } from "../system/Strings.mjs";
import { Token } from "../models/Token.mjs";

/* tslint:disable */
import { binaryEncoder, sendResponse } from "../system/bjson.cjs";
import { networkInterfaces } from "os";
import { makeError } from "../system/error.mjs";
import { recaptcha } from "../system/recaptcha.mjs";
/* tslint:enable */

export function addMagicLinkRoutes(router: express.Router) {
  router.post("/send-magic-link", safeAny, recaptcha, async (req, res, next) => {
    try {
      const context: Context = req.context;
      const email = context.any.email;
      const target = context.any.target || "app";
      const query = context.any.query || "";
      let protocol = context.any.protocol;
      if (!protocol) protocol = target === "website" ? "https" : "mylio";
      await g.authenticationService.sendMagicLink(
        context,
        email,
        target,
        query,
        protocol
      );
      return res.status(200).send();
    } catch (err) {
      next(err);
    }
  });

  router.get("/authorize-magic-link", safeNone, async (req, res, next) => {
    try {
      let context = req.context;
      let incomming = JSON.parse(atob(req.query.s as string));
      let outgoing = incomming;

      let result = Token.tryVerify(incomming.stoken, config.email_token_secret);
      if (!result.success) {
        if (result.name === "TokenExpiredError")
          outgoing = { error: ids.LINK_EXPIRED };
        else outgoing = { error: ids.UNKNOWN_ERROR };
      }
      let link = `${config.website}/launch?${incomming.query ? `${incomming.query}&` : ``
        }s=${btoa(JSON.stringify(outgoing))}`;
      return res.redirect(link);
    } catch (err) {
      next(err);
    }
  });

  router.get(
    "/exchange-stoken-for-rtoken/:token",
    safeNone,
    secureURLToken,
    async (req, res, next) => {
      try {
        const context: Context = req.context;
        let result = await g.authenticationService.rtoken4stoken(
          context,
          context.token
        );
        context.dumpLog();
        const { account, rtoken, token } = result;
        const services = getServices(context);
        const clean: any = {
          account: sanitizeOutput(account, context.hasAdminRights()),
          token,
          rtoken,
          services,
          key: account.rsaPrivateKey(),
          cert: account.x509Cert(),
        };
        sendResponse(req, res, clean, (r) =>
          binaryEncoder.encode_token_responseV2(r)
        );
      } catch (err) {
        next(err);
      }
    }
  );

  router.post("/send-create-link", safeAny, async (req, res, next) => {
    try {
      let context = req.context;
      const email = context.any.email;
      const target = context.any.target || "app";
      let protocol = context.any.protocol;
      if (!protocol) protocol = target === "website" ? "https" : "mylio";
      await g.authenticationService.sendCreateLink(
        context,
        email,
        target,
        protocol
      );
      context.dumpLog();
      return res.status(200).send();
    } catch (err) {
      next(err);
    }
  });

  router.get("/authorize-create-link", safeNone, async (req, res, next) => {
    try {
      let context = req.context;
      let incomming = JSON.parse(atob(req.query.s as string));
      let outgoing = incomming;
      let result = Token.tryVerify(incomming.ctoken, config.email_token_secret);
      if (!result.success) {
        if (result.name === "TokenExpiredError")
          outgoing = { error: ids.LINK_EXPIRED };
        else outgoing = { error: ids.UNKNOWN_ERROR };
      } else {
        let token = Token.decode(incomming.ctoken);
        let account = await g.accountService.tryBySubAndIdp(
          context,
          token.sub(),
          "mylio"
        );
        if (!account) {
          let account = new Account({
            idp: "mylio",
            sub: token.sub(),
          });
          account = await g.licenseService.createAccountAndLicense(
            context,
            account
          );
        }
        outgoing.stoken = await g.authenticationService.stoken4ctoken(
          context,
          token
        );
        outgoing.ctoken = undefined;
      }

      let link = `${config.website}/launch?s=${btoa(JSON.stringify(outgoing))}`;
      return res.redirect(link);
    } catch (err) {
      next(err);
    }
  });

  router.post("/sign-in-options", safeAny, async (req, res, next) => {
    try {
      let context = req.context;
      let options = await g.accountService.getSignInOptions(
        context,
        context.any.email
      );
      res.status(200).json(options);
    } catch (err) {
      next(err);
    }
  });
}
