//
//  MYStringUtil.cpp
//  MyLoApp
//
//  Created by <PERSON> on 6/5/13.
//  Copyright (c) 2013 MyLO Development LLC. All rights reserved.
//

#include "MYStringUtil.h"
#include <cstring>
#include <sstream>
#include <assert.h>
#include <stdarg.h>
#include <stdint.h>
#include <iomanip>
#include <inttypes.h>
#include <locale>
#include <string>

#ifdef MYLIO_CLIENT
#include <codecvt>
#include "MYPlatform.h"
#include <openssl/rand.h>
#include <openssl/evp.h>
#include <openssl/sha.h>
#include <openssl/hmac.h>
#endif // MYLIO_CLIENT

std::vector<std::string> MYString::split(const std::string &input, char delimiter)
{
    std::vector<std::string> ret;

    if (input.size() > 0)
    {
        std::stringstream ss(input);
        std::string item;

        while (std::getline(ss, item, delimiter))
            ret.push_back(std::move(item));

        // Make sure to catch trailing empty split components
        if (input.back() == delimiter)
            ret.push_back("");
    }
    else
    {
        ret.push_back("");
    }

    return ret;
}

std::vector<std::string> MYString::splitQuoted(const std::string &input, char delimiter)
{
    std::string item;

    std::vector<std::string> ret;

    const char *src = input.c_str();

    bool isInQuote = false;
    while (*src != 0)
    {
        if (*src == '\"')
        {
            isInQuote = !isInQuote;
        }
        else if (!isInQuote && *src == delimiter)
        {
            ret.push_back(item);
            item.clear();
        }
        else
        {
            item.push_back(*src);
        }

        ++src;
    }

    if (!item.empty())
    {
        ret.push_back(item);
    }

    return ret;
}

void MYString::split(const std::string &input, char delimiter, std::vector<std::string> &ret)
{
    if (input.size() > 0)
    {
        std::stringstream ss(input);
        std::string item;

        while (std::getline(ss, item, delimiter))
            ret.push_back(std::move(item));

        // Make sure to catch trailing empty split components
        if (input.back() == delimiter)
            ret.push_back("");
    }
    else
    {
        ret.push_back("");
    }
}

std::set<std::string> MYString::splitToSet(const std::string &input, char delimiter, bool trim)
{
    std::set<std::string> ret;

    if (input.size() > 0)
    {
        std::stringstream ss(input);
        std::string item;

        while (std::getline(ss, item, delimiter))
        {
            if (trim)
            {
                auto trimmed = MYString::trim(std::move(item));
                if (!trimmed.empty())
                {
                    ret.insert(std::move(trimmed));
                }
            }
            else
            {
                ret.insert(std::move(item));
            }
        }
    }

    return ret;
}

std::list<std::string> MYString::splitToList(const std::string &input, char delimiter)
{
    std::list<std::string> ret;

    if (input.size() > 0)
    {
        std::stringstream ss(input);
        std::string item;

        while (std::getline(ss, item, delimiter))
            ret.push_back(std::move(item));
    }

    return ret;
}

std::set<unsigned int> MYString::splitToUIntSet(const std::string &input, char delimiter)
{
    std::set<unsigned int> ret;

    if (input.size() > 0)
    {
        std::stringstream ss(input);
        std::string item;

        while (std::getline(ss, item, delimiter))
            ret.insert((unsigned int)atoi(item.c_str()));
    }

    return ret;
}

std::set<int> MYString::splitToIntSet(const std::string &input, char delimiter)
{
    std::set<int> ret;

    if (input.size() > 0)
    {
        std::stringstream ss(input);
        std::string item;

        while (std::getline(ss, item, delimiter))
            ret.insert(atoi(item.c_str()));
    }
    return ret;
}

std::vector<float> MYString::splitToFloatVector(const std::string &input, char delimiter)
{
    std::vector<float> ret;

    if (input.size() > 0)
    {
        std::stringstream ss(input);
        std::string item;

        while (std::getline(ss, item, delimiter))
            ret.push_back((float)atof(item.c_str()));
    }

    return ret;
}

template <typename T, typename char_type>
std::string toHexInternal(const T &in)
{
    std::ostringstream stream;
    auto hexSize = (int)(sizeof(char_type) * 2);
    for (const auto c : in)
    {
        stream << std::hex << std::setfill('0') << std::setw(hexSize) << std::uppercase << (int)(char_type)(c);
    }

    return stream.str();
}

std::string MYString::toHex(const std::string &in)
{
    return toHexInternal<std::string, unsigned char>(in);
}

std::string MYString::toHex(const std::wstring &in)
{
    return toHexInternal<std::wstring, wchar_t>(in);
}

std::string MYString::fromWString(const std::wstring &source)
{
    // Input is a UTF-16 encoded string of wchar_t.  This is only true on
    // Windows, but nobody uses wstring on other platforms anyway.
    // Transcode this string into UTF-8 and return it as a std::string.
    // Input values fall into one of three ranges:
    //   0xD800..0xDBFF: high surrogate, first half of a pair
    //   0xDC00..0xDFFF: low surrogate, second half of a pair
    //   <0xD800 or >0xDFFF: standalone BMP character
    // We handle errors by producing U+FFFD, "REPLACEMENT CHARACTER".
    // See the Unicode FAQ:
    //   <http://unicode.org/unicode/faq/utf_bom.html#gen8>
    // Loop state: surrogate pairs become single output characters.
    bool pairBegun = false;
    uint16_t pairHighBits = 0xFFFD;
    // Look at all of the 16-bit code
    std::string destination;
    for (auto iter = source.begin(); iter != source.end(); iter++)
    {
        // Read one 16-bit input value from the wstring and decode it.
        wchar_t in = *iter;
        uint32_t out = 0xFFFD;
        if (in >= 0xD800 && in <= 0xDBFF)
        {
            // This high surrogate ought to be the beginning of a pair.
            if (!pairBegun)
            {
                pairBegun = true;
                pairHighBits = in & 0x03FF;
                // Don't emit this as a char: wait til the second half.
                continue;
            }
        }
        else if (in >= 0xDC00 && in <= 0xDFFF)
        {
            // This low surrogate ought to be the end of a pair.
            if (pairBegun)
            {
                // Combine the halves as per the Unicode docs:
                // <http://unicode.org/faq/utf_bom.html#utf16-3>
                out = ((pairHighBits << 10) | (in & 0x03FF)) + 0x10000;
                pairBegun = false;
            }
        }
        else
        {
            if (pairBegun)
            {
                // Unmatched high pair is an error.
                pairBegun = false;
                destination += "\xEF\xBF\xBD";
            }
            out = in;
        }
        // Encode the character via UTF-8 as a sequence of up to four bytes.
        assert(out <= 0x10FFFF);
        if (out <= 0x00007F)
        {
            destination.push_back(out);
        }
        else if (out >= 0x000080 && out <= 0x0007FF)
        {
            destination.push_back(0xC0 | ((out >> 6) & 0x1F));
            destination.push_back(0x80 | (out & 0x3F));
        }
        else if (out >= 0x000800 && out <= 0x00FFFF)
        {
            destination.push_back(0xE0 | ((out >> 12) & 0x0F));
            destination.push_back(0x80 | ((out >> 6) & 0x3F));
            destination.push_back(0x80 | ((out & 0x3F)));
        }
        else if (out >= 0x010000 && out <= 0x10FFFF)
        {
            destination.push_back(0xF0 | ((out >> 18) & 0x07));
            destination.push_back(0x80 | ((out >> 12) & 0x3F));
            destination.push_back(0x80 | ((out >> 6) & 0x3F));
            destination.push_back(0x80 | (out & 0x3F));
        }
    }
    // If the last input value was a high surrogate, that's an error, and
    // we must return an error character. Since this is a special case we'll
    // just embed the UTF-8 encoding of U+FFFD:
    if (pairBegun)
    {
        destination += "\xEF\xBF\xBD";
    }
    return destination;
}

std::wstring MYString::toWString(const std::string &source)
{
    // Input is a UTF-8 encoded string of char. Transcode it for output as
    // a UTF-16 string of wchar_t. This is only correct on Windows, but
    // we don't expect anyone to use wstring anywhere else.
    std::wstring destination;
    destination.reserve(source.size());

    uint32_t out_ch = 0xFFFD;
    unsigned expected = 0;
    for (auto iter = source.begin(); iter != source.end(); iter++)
    {
        char in_ch = *iter;
        // Look for the error case where a coding sequence ends early.
        // We will handle this first so we can reprocess the byte we have
        // discovered as the beginning of its own character sequence.
        if (expected > 0 && 0x80 != (in_ch & 0xC0))
        {
            // The character sequence ended early, which means that
            // the input sequence is malformed. Push an error char, then
            // carry on with the new byte.
            destination.push_back(0xFFFD);
            expected = 0;
        }
        if (expected > 0)
        {
            // We are in the middle of a multi-byte sequence, and we expect
            // to find a continuing byte here.
            assert(0x80 == (in_ch & 0xC0));
            out_ch <<= 6;
            out_ch |= (in_ch & 0x3F);
            expected--;
        }
        else
        {
            // We are not in the middle of a multibyte sequence, so this
            // should either be a single-byte character or the beginning of
            // a sequence, with a length code.
            if (0xF0 == (in_ch & 0xF8))
            {
                out_ch = (in_ch & 0x07);
                expected = 3;
            }
            else if (0xE0 == (in_ch & 0xF0))
            {
                out_ch = (in_ch & 0x0F);
                expected = 2;
            }
            else if (0xC0 == (in_ch & 0xE0))
            {
                out_ch = (in_ch & 0x1F);
                expected = 1;
            }
            else if (0x80 == (in_ch & 0x80))
            {
                // Error! This is a continuing byte, not a leading byte.
                out_ch = 0xFFFD;
                expected = 0;
            }
            else
            {
                // A one-byte character from the ASCII range.
                out_ch = in_ch;
                expected = 0;
            }
        }
        // If the byte we just read completed a character, emit the value
        // to our output string.
        if (0 == expected)
        {
            assert(out_ch <= 0x10FFFF);
            // Produce a UTF-16 code sequence. If the char is in the BMP,
            // we can emit a single code unit; otherwise, we must break it
            // in half and emit a surrogate pair.
            if (out_ch <= 0xFFFF)
            {
                destination.push_back((wchar_t)out_ch);
            }
            else
            {
                out_ch -= 0x10000;
                wchar_t hi = 0xD800 | ((out_ch >> 10) & 0x03FF);
                wchar_t lo = 0xDC00 | (out_ch & 0x03FF);
                destination.push_back((wchar_t)hi);
                destination.push_back((wchar_t)lo);
            }
            out_ch = 0xFFFD;
        }
    }
    // If the input terminated unexpectedly, produce an error character.
    if (expected > 0)
    {
        destination.push_back((wchar_t)0xFFFD);
    }
    return destination;
}

//
// return the number of '\n' in a string
//
int MYString::countNewLines(const std::string &input)
{
    int newLines = 0;

    for (size_t i = 0; i < input.size(); i++)
    {
        if (input[i] == '\n')
        {
            newLines++;
        }
    }

    return (int)newLines;
}

// Generates a string of random, mixed-case alphanumeric chars.
std::string MYString::random(int length)
{
    static std::string charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890";
    std::string result;
    result.resize(length);

    for (int i = 0; i < length; i++)
    {
        result[i] = charset[rand() % charset.length()];
    }

    return result;
}

/*--------------------------------------------------------------------
Slices the provided string into many strings, separating
whenever an instance of the delimiter string is found.
--------------------------------------------------------------------*/
std::vector<std::string> MYString::explode(const std::string &subject, char delimiter)
{
    std::vector<std::string> pieces;

    std::string stdSubject = subject;
    std::string stdEmpty;

    size_t pos = 0;
    while ((pos = stdSubject.find(delimiter, pos)) != std::string::npos)
    {
        std::string currentStr;
        currentStr.insert(0, stdSubject, 0, pos);
        pieces.push_back(currentStr);

        stdSubject.replace(0, pos + 1, stdEmpty);
        pos = 0;
    }

    if (stdSubject.size() != 0)
    {
        std::string currentStr;
        currentStr.insert(0, stdSubject, 0, pos);
        pieces.push_back(currentStr);
    }

    return pieces;
}

/*--------------------------------------------------------------------
Joins an array of strings into one single string with the
provided delimiter character.
--------------------------------------------------------------------*/
std::string MYString::implode(const std::vector<std::string> &strings, char delimiter)
{
    std::stringstream strStream;
    bool emitDelimiter = false;

    for (auto string : strings)
    {
        if (emitDelimiter)
        {
            strStream << delimiter;
        }

        strStream << string;
        emitDelimiter = true;
    }

    return strStream.str();
}

/*--------------------------------------------------------------------
 Joins an array of strings into one single string with the
 provided delimiter character.
 --------------------------------------------------------------------*/
std::string MYString::implode(const std::set<std::string> &strings, char delimiter)
{
    std::stringstream strStream;
    bool emitDelimiter = false;

    for (auto string : strings)
    {
        if (emitDelimiter)
        {
            strStream << delimiter;
        }

        strStream << string;
        emitDelimiter = true;
    }

    return strStream.str();
}

std::string MYString::implode(const std::set<std::string> &strings, const std::string &delimiter)
{
    std::stringstream strStream;
    bool emitDelimiter = false;

    for (auto string : strings)
    {
        if (emitDelimiter)
        {
            strStream << delimiter;
        }

        strStream << string;
        emitDelimiter = true;
    }

    return strStream.str();
}

#ifdef MYLIO_CLIENT
std::wstring MYString::convertToWideString(const std::string &input)
{
    try
    {
        std::wstring_convert<std::codecvt_utf8_utf16<wchar_t>> converter;
        return converter.from_bytes(input);
    }
    catch (std::range_error &)
    {
        size_t length = input.length();
        std::wstring result;
        result.reserve(length);
        for (size_t i = 0; i < length; i++)
        {
            result.push_back(input[i] & 0xFF); // Make sure no negatives
        }
        return result;
    }
}

// This method goes through the entire string to check for unicode encodings
// If any are found it replaces them with UTF8 encodings
std::string MYString::convertUnicodeEncodingsIfAnyToUTF8Encodings(const std::string &input)
{
    std::vector<std::string> contentVector = MYString::split(input, ' ');
    // Iterate through the content one by one
    for (unsigned int i = 0; i < contentVector.size(); i++)
    {
        std::wstring wstr = MYString::convertToWideString(contentVector[i]);
        std::string data = std::wstring_convert<std::codecvt_utf8<wchar_t>>().to_bytes(wstr);

        contentVector[i] = data;
    }
    return MYString::implode(contentVector, ' ');
}
#endif // MYLIO_CLIENT

/*--------------------------------------------------------------------
Formats an integer as a string with no commas.
--------------------------------------------------------------------*/
std::string MYString::uintToString(unsigned int value)
{
    std::stringstream stream;
    stream << value;

    return stream.str();
}

/*--------------------------------------------------------------------
Formats a in64_t as a string with no commas.
--------------------------------------------------------------------*/
std::string MYString::uint64ToString(uint64_t value)
{
    std::stringstream stream;
    stream << value;

    return stream.str();
}

/*--------------------------------------------------------------------
Formats a pointer as a hex string with no commas.
--------------------------------------------------------------------*/
#ifdef MYLIO_CLIENT
std::string MYString::pointerToHexString(void *pv)
{
#pragma warning(push)
#pragma warning(disable : 4996)
#if !defined(PLATFORM_ANDROID)
    if (sizeof(void *) == sizeof(uint64_t))
    {
        char buf[64];
        sprintf(buf, "0x%016" PRIx64, (uint64_t)pv);
        return std::string(buf);
    }
    else
#endif
        if (sizeof(void *) == sizeof(uint32_t))
    {
        char buf[64];
        sprintf(buf, "0x%08x", (uint32_t)(uint64_t)pv);
        return std::string(buf);
    }
    else
    {
        assert(false);
    }
#pragma warning(pop)
}
#endif

/*--------------------------------------------------------------------
Formats a floating-point value with a specified number of
fractional digits.
--------------------------------------------------------------------*/
std::string MYString::floatToString(float value, int maxFractionalDigits, int minFractionalDigits)
{
    std::stringstream stream;

    //  Don't ever print a negative zero.
    if (value == -0)
    {
        value = 0;
    }

    //  It's not clear what we should do if we're passed
    //  a NAN value. We'll assume the caller wants "0".
    if (value != value)
    {
        value = 0.0f;
    }

    if (maxFractionalDigits < 0 || maxFractionalDigits > std::numeric_limits<float>::digits10)
    {
        //  Limit the total digits to the numeric limit for float.
        //  Using too many digits only reduces accuracy.
        maxFractionalDigits = std::numeric_limits<float>::digits10;
    }

    //  minFractionalDigits can't be bigger than maxFractionalDigits
    minFractionalDigits = minFractionalDigits > maxFractionalDigits ? maxFractionalDigits : minFractionalDigits;
    //  minFractionalDigits can't be less than 0
    minFractionalDigits = minFractionalDigits < 0 ? 0 : minFractionalDigits;

    //  Set the precision to maxFractionalDigits and the mode to fixed so
    //  that the string always has maxFractionalDigits. We'll trim
    //  any trailing zeros later if needed.
    stream << std::setprecision(maxFractionalDigits) << std::fixed;
    stream << value;

    //  if max & min fractionalDigits are the same then we're done
    if (maxFractionalDigits == minFractionalDigits)
    {
        return stream.str();
    }

    //  remove any trailing zeros but keep at least minFractionalDigits
    std::string valueString = stream.str();

    size_t dotLocation = valueString.find_first_of('.');
    if (dotLocation != std::string::npos)
    {
        size_t fractionalDigits = valueString.size() - (dotLocation + 1);

        while (valueString.back() == '0' && fractionalDigits > size_t(minFractionalDigits))
        {
            valueString.pop_back();
            fractionalDigits--;
        }
    }

    //  if the last digit is '.' then remove it
    if (valueString.back() == '.')
    {
        valueString.pop_back();
    }

    return valueString;
}

std::string MYString::hexStringToBlob(const std::string &hexString)
{
    if (hexString.empty())
        return "";

    size_t length = hexString.length() / 2;
    std::string s;
    s.resize(length, '\0');

    const char *chars = hexString.c_str();
    for (size_t i = 0; i < length; i++)
    {
        s[i] = hex2byte(chars);
        chars += 2;
    }

    s.resize(length);
    return s;
}

std::string MYString::blobToHexString(const std::string &blob)
{
    if (blob.empty())
        return "";
    else
        return MYString::blobToHexString((unsigned char *)&blob[0], (int)blob.size());
}

std::string MYString::blobToHexString(const std::vector<uint8_t> &buffer)
{
    if (buffer.empty())
        return "";
    else
        return MYString::blobToHexString(&buffer[0], (int)buffer.size());
}

std::string MYString::blobToHexString(const std::pair<unsigned char *, unsigned int> buffer)
{
    return MYString::blobToHexString(buffer.first, buffer.second);
}

std::string _stringFormat(const char *formatText, va_list list)
{
    char buffer[1024];

#ifdef _WIN32
    vsnprintf_s(buffer, 1023, formatText, list);
#else
    vsnprintf(buffer, 1023, formatText, list);
#endif

    return buffer;
}

std::string stringFormat(const char *formatText, ...)
{
    std::string output;

    va_list list;
    va_start(list, formatText);
    {
        output = _stringFormat(formatText, list);
    }
    va_end(list);

    return output;
}

#ifdef MYLIO_CLIENT
std::string stringFormat(const int formatId, ...)
{
    std::string output;

    va_list list;
    va_start(list, formatId);
    {
        output = _stringFormat(getCString((MYStringIds)formatId), list);
    }
    va_end(list);

    return output;
}
#endif

void cleanupUpNegativeZeroFormatting(std::string &out)
{
    // this is a total kludge, but it makes our users unhappy to see negative zeros.
    // there's no way to prevent them from existing, numerically, so we'll sanitize the display.
    if (out.size() > 1 && out[0] == '-')
    {
        bool foundLegit = false;
        for (auto ch : out)
        {
            foundLegit |= (ch != '0' && ch != '.' && ch != '-');
        }
        if (!foundLegit)
        {
            out.erase(0, 1);
        }
    }
}

std::wstring MYString::toLower(std::wstring input)
{
    // This will not work for non-ASCII chars. We need a unicode library.
    // Further, this is the wrong algorithm if you want to prepare a string for
    // a case-insensitive comparison; there is a specific case-folding algorithm
    // defined in the Unicode standard.
    std::transform(input.begin(), input.end(), input.begin(), ::tolower);

    return input;
}

std::string MYString::toLower(std::string input)
{
    // This will not work for non-ASCII chars. We need a unicode library.
    // Further, this is the wrong algorithm if you want to prepare a string for
    // a case-insensitive comparison; there is a specific case-folding algorithm
    // defined in the Unicode standard.
    std::transform(input.begin(), input.end(), input.begin(), ::tolower);

    return input;
}

std::string MYString::toUpper(std::string input)
{
    // This will not work for non-ASCII chars. We need a unicode library.
    // Further, this is the wrong algorithm if you want to prepare a string for
    // a case-insensitive comparison; there is a specific case-folding algorithm
    // defined in the Unicode standard.
    std::transform(input.begin(), input.end(), input.begin(), ::toupper);

    return input;
}

bool MYString::startsWith(const std::string &haystack, const std::string &startingNeedle)
{
    return (haystack.compare(0, startingNeedle.length(), startingNeedle) == 0);
}

bool MYString::startsWith(const std::wstring &haystack, const std::wstring &startingNeedle)
{
    return (haystack.compare(0, startingNeedle.length(), startingNeedle) == 0);
}

bool MYString::endsWith(const std::string &haystack, const std::string &endingNeedle)
{
    if (haystack.size() < endingNeedle.size())
        return false;
    return haystack.substr(haystack.size() - endingNeedle.size()) == endingNeedle;
}

bool MYString::endsWith(const std::wstring &haystack, const std::wstring &endingNeedle)
{
    if (haystack.size() < endingNeedle.size())
        return false;
    return haystack.substr(haystack.size() - endingNeedle.size()) == endingNeedle;
}

bool MYString::contains(const std::string &input, const char data)
{
    size_t dotLocation = input.find(data);
    if (dotLocation == std::string::npos)
        return false; // does not contain

    return true; // contains the character
}

bool MYString::containsIgnoreCase(const std::string &input, const std::string &findThis)
{
    auto found = MYString::toLower(input).find(MYString::toLower(findThis));
    if (found == std::string::npos)
        return false; // does not contain

    return true; // contains string
}

std::string MYString::shrink(const std::string &input, int maxLength)
{
    std::string output = input;

    if (((int)output.size()) > maxLength)
    {
        int lenOfPrefixSuffix = maxLength / 2;
        lenOfPrefixSuffix -= 2;
        output = output.substr(0, lenOfPrefixSuffix) + "..." + output.substr(output.size() - lenOfPrefixSuffix);
    }
    return output;
}

std::string MYString::removeSpaces(const std::string &input)
{
    std::string str = input;

    /* remove multiple spaces */
    auto newEnd = std::unique(str.begin(), str.end(), [](char lhs, char rhs)
                              { return (lhs == rhs) && (lhs == ' '); });
    str.erase(newEnd, str.end());

    /* remove spaces at the end */
    if (!str.empty() && str.back() == ' ')
        str.erase(std::prev(str.end()));

    /* remove spaces at the begin */
    if (!str.empty() && str.front() == ' ')
        str.erase(str.begin());

    return str;
}

/*--------------------------------------------------------------------
 Removes all whitespace characters from a string.
 --------------------------------------------------------------------*/
std::string MYString::removeWhiteSpace(const std::string &input)
{
    auto s = input;

    s.erase(std::remove_if(s.begin(), s.end(),
                           [](char c)
                           {
                               return isspace(c) != 0;
                           }),
            s.end());

    return s;
}

/*--------------------------------------------------------------------
Removes all CR/LF characters from a string.
--------------------------------------------------------------------*/
std::string MYString::removeCrLf(const std::string &input)
{
    auto s = input;

    s.erase(std::remove_if(s.begin(), s.end(),
                           [](char c)
                           {
                               return c == '\n' || c == '\r';
                           }),
            s.end());

    return s;
}

std::string MYString::removeQuotes(const std::string &input)
{
    auto s = input;

    s.erase(std::remove_if(s.begin(), s.end(),
                           [](char c)
                           {
                               return c == '\"';
                           }),
            s.end());

    return s;
}

std::string MYString::padFront(const std::string &input, const unsigned int totalLenght, const char paddingChar)
{
    std::string final;
    int additionalSpaceNeeded = totalLenght - (unsigned int)input.size();

    for (int n = 0; n < additionalSpaceNeeded; n++)
        final += paddingChar;

    final += input;

    return final;
}

std::string MYString::padBack(const std::string &input, const unsigned int totalLenght, const char paddingChar)
{
    std::string final = input;

    int additionalSpaceNeeded = totalLenght - (unsigned int)input.size();

    for (int n = 0; n < additionalSpaceNeeded; n++)
        final += paddingChar;

    return final;
}

//
// Ensure a Prefix text is seperating the existing text and the text to append
//
void MYString::appendWithPrefix(std::string &destination, const std::string &prefixText, const std::string &appendSource)
{
    if (appendSource.empty())
        return; // nothing to append

    if (!destination.empty() && !endsWith(destination, prefixText))
        destination += prefixText;

    destination += appendSource;
}

std::string MYString::escapeChars(const std::string &input, const char *charsToEscape)
{
    std::string result;

    static const char hexdigits[] = "0123456789ABCDEF";
    for (auto iter = input.begin(); iter != input.end(); iter++)
    {
        char ch = *iter;
        if (strchr(charsToEscape, ch) != NULL)
        {
            result += "%";
            int hi = (ch & 0x00F0) >> 4;
            result += hexdigits[hi];
            int lo = (ch & 0x0F);
            result += hexdigits[lo];
        }
        else
        {
            result += ch;
        }
    }
    return result;
}

std::string MYString::replace(const std::string &input, const std::string &findThis, const std::string &replaceWith, bool singlePass)
{
    std::string result = input;

    size_t pos = 0;
    while ((pos = result.find(findThis, pos)) != std::string::npos)
    {
        result.replace(pos, findThis.length(), replaceWith);
        if (singlePass)
        {
            pos += replaceWith.length();
        }
        else
        {
            pos = 0; // start over
        }
    }

    return result;
}

//
// this is an optimized string container finder, it is fast due to the fact that it expect a lower case search string
// Try to find in the Haystack the Needle - ignore case
// the string to find must be supplied in lowercase
//
bool MYString::containsFastIgnoreCaseSearchIsLowerCased(const std::string &strHaystack, const std::string &strNeedleInLowerCase)
{
    auto it = std::search(
        strHaystack.begin(), strHaystack.end(),
        strNeedleInLowerCase.begin(), strNeedleInLowerCase.end(),
        [](char ch1, char ch2)
        {
            // does it match as is
            if (ch1 == ch2)
                return true;

            // no lets try in uppercasing the first char in case char2 was uppercase
            if ('A' <= ch1 && ch1 <= 'Z')
            {
                ch1 -= ('A' - 'a');
            }

            return ch1 == ch2;
        });
    return (it != strHaystack.end());
}

//
// return a trim first line of input text will handle multibyte text
// we will consider a line break being any of these '\n\r\f' and L'\n\r\f' '2028', c=='2029'
//
// not the most efficient fucntion but I tested with a bunch of Multibyte inputs
//
#ifdef MYLIO_CLIENT
inline bool isNewLine(int c)
{
    return (c == '\r' || c == '\n' || c == '\f' ||
            c == L'\r' || c == L'\n' || c == L'\f' ||
            c == '2028' || c == '2029'); // these are supposed to be LineSeprators and ParagraphSeperators
}

std::string MYString::getCleanFirstLine(const std::string &input)
{
    std::wstring tmp1 = MYString::toWString(input);
    std::wstring tmp2;

    for (auto c : tmp1)
    {
        if (isNewLine(c))
            break;

        tmp2 += c;
    }

    std::string tmp3 = MYString::fromWString(tmp2);
    tmp3 = MYString::trim(tmp3);

    return tmp3;
}

std::string MYString::getCleanText(const std::string &input)
{
    std::string output;

    const char *src = input.c_str();
    while (*src != 0)
    {
        if (isNewLine(*src))
        {
            // Replace runs of carriage returns with a single space
            output += ' ';

            while (*src != 0 && isNewLine(*src))
                ++src;
        }
        else
        {
            output += *src++;
        }
    }

    return MYString::trim(output);
}

std::vector<unsigned char> MYString::toHMACsha256(std::vector<unsigned char> key, const std::string &data)
{
    std::vector<unsigned char> output;
    output.resize(SHA256_DIGEST_LENGTH);

    unsigned int len = (unsigned int)output.size();

    HMAC_CTX sha256;
    HMAC_Init(&sha256, &key[0], (int)key.size(), EVP_sha256());
    HMAC_Update(&sha256, (const unsigned char *)&data[0], (int)data.size());
    HMAC_Final(&sha256, &output[0], &len);

    return output;
}

std::vector<uint8_t> MYString::toSha256(const std::string &pass)
{
    std::vector<uint8_t> output;
    output.resize(SHA256_DIGEST_LENGTH);

    SHA256_CTX sha256;
    SHA256_Init(&sha256);
    SHA256_Update(&sha256, pass.c_str(), pass.size());
    SHA256_Final(&output[0], &sha256);

    return output;
}

std::string MYString::fileSizeToString(uint64_t fileSize)
{
    return bytesToReadableFormat(fileSize);
}

std::string MYString::toUpperFirstLetter(const std::string &input)
{
    std::wstring wInput = toWString(input);

    std::wstring wFirstLetter;
    std::wstring wRightSide;

    int isFirst = true;
    for (auto &c : wInput)
    {
        if (isFirst)
        {
            wFirstLetter = c;
            isFirst = false;
        }
        else
        {
            wRightSide += c;
        }
    }

    std::string firstLetter = fromWString(wFirstLetter);
    std::string firstLetterUppercased = transformToUpperCase(firstLetter);

    std::string output;
    output = firstLetterUppercased;
    output += fromWString(wRightSide);

    return output;
}

std::string MYString::transformToLowerCase(const std::string &input)
{
    return Platform::get()->transformString(input, Platform::StringTransformType::LowerCase);
}

std::string MYString::transformToUpperCase(const std::string &input)
{
    return Platform::get()->transformString(input, Platform::StringTransformType::UpperCase);
}
#endif // MYLIO_CLIENT

bool MYString::isNumber(const std::string &input)
{
    auto pos = input.find_first_not_of("0123456789");
    return pos == std::string::npos; // no other char found, so its all numeric
}

bool stringIsNullOrEmpty(const char *str)
{
    if (str == nullptr)
        return true;

    if (strlen(str) == 0)
        return true;

    return false;
}

bool stringIsHex(const char *str)
{
    if (stringIsNullOrEmpty(str))
        return false;

    for (const char *p = str; *p != '\0'; p++)
    {
        if (*p >= '0' && *p <= '9')
            continue;

        if (*p >= 'a' && *p <= 'f')
            continue;

        if (*p >= 'A' && *p <= 'F')
            continue;

        return false;
    }

    return true;
}

bool stringIsBase64(const char *str)
{
    if (stringIsNullOrEmpty(str))
        return false;

    static const char *base64_chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
                                      "abcdefghijklmnopqrstuvwxyz"
                                      "0123456789+/=";
    for (const char *p = str; *p != '\0'; p++)
    {
        if (strchr(base64_chars, *p) != nullptr)
            continue;

        return false;
    }

    return true;
}

#ifdef MYLIO_CLIENT
bool stringAreEqual(const char *str1, const char *str2, bool ignoreCase)
{
    if (str1 == nullptr || str2 == nullptr)
        return false;

    if (ignoreCase)
    {
#ifdef WIN32
        return (_stricmp(str1, str2) == 0) ? true : false;
#else
        return (strcasecmp(str1, str2) == 0) ? true : false;
#endif
    }

    return (strcmp(str1, str2) == 0) ? true : false;
}

bool stringEndsWith(const char *str, const char *ending, bool ignoreCase)
{
    if (stringIsNullOrEmpty(str) || stringIsNullOrEmpty(ending))
        return false;

    size_t strLen = strlen(str);
    size_t endLen = strlen(ending);
    if (endLen > strLen)
        return false;

    str = str + (strLen - endLen);

    return stringAreEqual(str, ending, ignoreCase);
}
#endif // MYLIO_CLIENT

bool stringSafeCopy(char *dst, size_t dstSize, const char *src)
{
    if (dst == nullptr || dstSize <= 0)
        return false;

    *dst = '\0';

    if (src == nullptr)
        return false;

    size_t len = strlen(src);
    if (len >= dstSize)
    {
        assert(false);
        return false;
    }

    memcpy(dst, src, len);
    *(dst + len) = '\0';

    return true;
}

std::string MYString::escapeToHtmlString(const std::string &input)
{
    std::string output;

    for (const auto &c : input)
    {

        switch (c)
        {

        case '<':
            output += "&lt;";
            break;
        case '>':
            output += "&gt;";
            break;
        case '&':
            output += "&amp;";
            break;

        default:
            output += c;
            break;
        }
    }
    return output;
}

LexicographicalVersionCompare::LexicographicalVersionCompare(const std::string &first, const std::string &second, int sect) : sections(sect), strA(first), strB(second), parsedA(sect, 0), parsedB(sect, 0)
{
}

void LexicographicalVersionCompare::parse()
{
    std::stringstream parser(strA);
    parser >> parsedA[0];
    for (int idx = 1; idx < sections; idx++)
    {
        parser.get(); // Skip period
        parser >> parsedA[idx];
    }

    parser.clear();
    parser.str("");
    parser << strB;
    parser >> parsedB[0];
    for (int idx = 1; idx < sections; idx++)
    {
        parser.get(); // Skip period
        parser >> parsedB[idx];
    }
}

bool LexicographicalVersionCompare::firstLessThanorEqualSecond()
{
    // check if the two strings are equal
    if (strA == strB)
    {
        return true;
    }

    parse();
    return std::lexicographical_compare(parsedA.begin(), parsedA.end(), parsedB.begin(), parsedB.end());
}
