import { String } from "aws-sdk/clients/cloudsearchdomain.js";
import fs = require("fs");
import path = require("path");

export abstract class Identifyable {
  constructor(public name: string, public id: string) {}
}

import * as url from "url";
const __filename = url.fileURLToPath(import.meta.url);
const __dirname = url.fileURLToPath(new URL(".", import.meta.url));

class FlowControl {
  constructor(public name: string, public flow: string) {}
  public canGo(from: string, to: string) {
    if (this.fromFlows.size === 0 || this.toFlows.size === 0) this.buildFlows();
    return !!(this.fromFlows.get(from) || []).find((ss) => ss === to);
  }
  public fromFlows = new Map<string, string[]>();
  public toFlows = new Map<string, string[]>();
  private buildFlows() {
    let prevNode = "";
    let prevLt = false;
    let prevGt = false;
    let regEx = /(\w+)(<>|<|>|\b)/g;
    let match = regEx.exec(this.flow);
    while (match) {
      let node = match[1];
      this.fromFlows.set(node, this.fromFlows.get(node) || []);
      this.toFlows.set(node, this.toFlows.get(node) || []);
      if (prevNode) {
        if (prevLt) {
          this.fromFlows.get(node).push(prevNode);
          this.toFlows.get(prevNode).push(node);
        }
        if (prevGt) {
          this.fromFlows.get(prevNode).push(node);
          this.toFlows.get(node).push(prevNode);
        }
      }
      prevNode = node;
      prevLt = !!match.find((v) => v.indexOf("<") >= 0);
      prevGt = !!match.find((v) => v.indexOf(">") >= 0);
      match = regEx.exec(this.flow);
    }
  }
}

export class Field extends Identifyable {
  public default = "";
  public datatype = Datatype.string;
  public flow: string;
  public required = false;
  public autoNumber = false;
  private flows_ = new Map<string, FlowControl | FlowControl[]>();
  addFlow(name: string, flow: string | string[]) {
    name = name.replace("-flow", "");
    if (name === "flow") name = "default";
    if (this.flows_.has(name)) {
      throw `flow ${name} has already been added to this field`;
    }
    if (typeof flow === "string")
      this.flows_.set(name, new FlowControl(name, flow));
    else {
      let flows = [];
      for (let child of flow) {
        flows.push(new FlowControl(name, child));
      }
      this.flows_.set(name, flows);
    }
  }
  flows(name: string) {
    if (!this.flows_.has(name)) {
      throw `flow ${name} is not defined for this field`;
    }
    return this.flows_.get(name);
  }
  public static define(name: string, datatype: Datatype) {
    let f = new Field(name, name);
    f.datatype = datatype;
    return f;
  }
  public canGo(from: string, to: string, mode = "default") {
    if (this.flows_.has(mode)) {
      let flow = this.flows_.get(mode);
      if (flow instanceof FlowControl) {
        return flow.canGo(from, to);
      } else {
        for (let child of flow) {
          if (child.canGo(from, to)) return true;
        }
      }
      return false;
    } else return this.canGo(from, to, "default");
  }
}

type FlowMap = Map<string, string | String[]>;
class Stacks {
  datatype_ = new Array<string>();
  datatype() {
    if (this.datatype_.length) return this.datatype_[this.datatype_.length - 1];
    else return "string";
  }
  flows_ = new Array<FlowMap>();
  flows() {
    if (this.flows_.length) return this.flows_[this.flows_.length - 1];
    else return new Map<string, string | string[]>();
  }
}

export class Entity extends Identifyable {
  public fields: Field[] = [];
  public blocks = new Map<string, string>();
  public generatedBlocks = new Map<string, string>();
  public finds: Array<Array<Field>> = [];
  public keys: Array<Array<Field>> = [];
  public flow: string;
  public plural: string;
  public k: number;
  public static fromFile(name: string) {
    let e = new Entity(name);
    Entity.parseFile(e, name);
    return e;
  }

  private static parseGroup(
    e: Entity,
    group: any,
    groupName: string,
    stacks: Stacks
  ) {
    e.plural = e.plural || group.plural || groupName + "s";
    e.k = e.k || group?.k;
    if (group.datatype) stacks.datatype_.push(group.datatype);
    else stacks.datatype_.push(stacks.datatype());
    let flowNames = Object.keys(group).filter((f) => f.endsWith("flow"));
    if (flowNames.length) {
      let newFlows = new Map<string, string | string[]>(stacks.flows());
      for (let flowName of flowNames) {
        newFlows.set(flowName, group[flowName]);
      }
      stacks.flows_.push(newFlows);
    } else {
      stacks.flows_.push(new Map<string, string | string[]>(stacks.flows()));
    }
    for (let mixin of group.mixin || []) {
      let filename = path.join(__dirname, `/entities/${mixin}.json`);
      Entity.parseGroup(
        e,
        JSON.parse(fs.readFileSync(filename, "utf8")),
        mixin,
        stacks
      );
    }
    for (let f of Object.keys(group.fields) || []) {
      if (f.startsWith("group-")) {
        Entity.parseGroup(e, group.fields[f], f, stacks);
        continue;
      }
      let field = new Field(f, f);
      let source = group.fields[f];
      if (e.fields.find((f) => f.id === field.id))
        throw `entity ${e.name} has duplicate field ${f}`;
      e.fields.push(field);
      field.autoNumber = !!source.autoNumber;
      field.datatype = (Datatype[source.datatype] ||
        Datatype[stacks.datatype()]) as Datatype;
      field.default = source.default || field.default;
      field.required = source.required || field.required;

      let fieldFlowNames = Object.keys(source).filter((k) =>
        k.endsWith("flow")
      );
      let fieldFlows = new Map<string, string | string[]>(stacks.flows());
      for (let fieldFlowName of fieldFlowNames) {
        fieldFlows.set(fieldFlowName, source[fieldFlowName]);
      }
      for (let [name, flow] of fieldFlows) {
        field.addFlow(name, flow);
      }
    }

    stacks.datatype_.pop();
    stacks.flows_.pop();
  }

  private static parseFile(e: Entity, name: string) {
    let root = JSON.parse(
      fs.readFileSync(path.join(__dirname, `/entities/${name}.json`), "utf8")
    );
    let stacks = new Stacks();

    Entity.parseGroup(e, root, name, stacks);

    if (root.directives) {
      if (root.directives.identify) {
        for (let key of root.directives.identify) {
          //@ts-ignore
          e.keys.push(
            key.map((keyFieldName) => {
              let f = e.fields.find((f) => f.name === keyFieldName);
              f.required = true;
              return f;
            })
          );
        }
      }
      if (root.directives.find) {
        for (let key of root.directives.find) {
          //@ts-ignore
          e.finds.push(
            key.map((keyFieldName) => {
              let f = e.fields.find((f) => f.name === keyFieldName);
              return f;
            })
          );
        }
      }
    }
  }

  constructor(name: string) {
    super(name, name);
  }
}

export enum Datatype {
  string = 1,
  decimal,
  int32,
  int64,
  date,
  boolean,
  binary,
  json,
}

export class Find extends Identifyable {
  public fields: Array<Field> = [];
}
