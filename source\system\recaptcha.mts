import express = require("express");
import { error, makeError } from "./error.mjs";
import { config } from "./Config.mjs";

export async function recaptcha(req: express.Request, res, next) {

    let context = req.context;
    if (context && context.token && context.token.hasAdminRights()) {
        return next();
    }

    if (req.get("User-Agent").indexOf("Mylio") >= 0)
        return next();

    if (!config.recaptcha_secret)
        return next();

    let recaptcha = req.header("X-Recaptcha-Token");
    if (!recaptcha)
        return next(makeError(401, "RECAPTCHA_TOKEN_MISSING", "Access denied"));


    const response = await fetch('https://www.google.com/recaptcha/api/siteverify?'
        + new URLSearchParams({
            secret: config.recaptcha_secret,
            response: recaptcha
        }),
        {
            method: "POST",
        });

    let payload = await response.json();
    if (!payload.success || (payload.score || 0) < 0.5) {
        return next(makeError(401, "RECAPTCHA_FAILED", "Access denied"));
    }

    return next();
}