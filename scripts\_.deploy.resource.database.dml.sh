#!/bin/bash

echo "started $0"

source ./_.postgres.sh

HOST=$1
PORT=$2
USER=$3

if [[ $HOST == "localhost" ]]
then
    export PGPASSWORD="password"
fi

open_pg_connection $HOST $PORT $USER

run_script '../database/vnext/setup/resource0.dml.sql'
run_script '../database/vnext/setup/resource0.secure.sql'

if [[ $4 ]]
then
    HOST=$4
    echo "changed hosts to $4"
fi

if [[ $HOST == "localhost" ]]
then
    export PGPASSWORD="password"
fi

run_script '../database/vnext/setup/resource1.dml.sql'
run_script '../database/vnext/setup/resource1.secure.sql'

close_pg_connection

echo "completed $0"
