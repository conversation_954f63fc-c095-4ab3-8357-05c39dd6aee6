#!/bin/bash

set -e
echo "started $0"

# Prompt the user for an instance number
read -p "Enter the instance number: " instance

# Validate that the input is a numeric value
if ! [[ "$instance" =~ ^[0-9]+$ ]]; then
    echo "Error: Instance must be a numeric value."
    exit 1
fi

# Construct the first parameter using the input instance
first_parameter="account${instance}-rds.mylio.com"

# Call the external script with the modified parameter
./_.deploy.account.database.sh "$first_parameter" 5432 superuser prod

echo "completed $0"