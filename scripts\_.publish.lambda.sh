#!/bin/bash

echo "started $0"
set -e

NAME="$1"
FILE=".//debug/source/lambdas/$NAME.lambda.js"
OUTDIR="../publications/cloudv3/lambda/$NAME"

echo "publishing to $OUTDIR"

pushd ../

rm -rf $OUTDIR
mkdir -p $OUTDIR

./node_modules/.bin/webpack --env.entry=$FILE --env.outDir=$OUTDIR --env.outFile=$NAME

cp -R ./config $OUTDIR/config
cp -R ./source/templates $OUTDIR/templates
cp ./package.json $OUTDIR/package.json

if [ -f "$OUTDIR/config/local.json" ]
then
    rm $OUTDIR/config/local.json
fi

pushd $OUTDIR

npm install --production
zip -r -X $NAME.zip *

popd
cp ./scripts/push.lambda.s3.sh $OUTDIR

if [[ $(uname -s) == "Darwin" ]]; then # osx
    sed -i "" "s/<name>/$NAME/g" "$OUTDIR/push.lambda.s3.sh"
    sed -i "" "s/<file>/$NAME.zip/g" "$OUTDIR/push.lambda.s3.sh"
else
    sed -i "s/<name>/$NAME/g" "$OUTDIR/push.lambda.s3.sh"
    sed -i "s/<file>/$NAME.zip/g" "$OUTDIR/push.lambda.s3.sh"
fi

popd

echo "completed $0"
