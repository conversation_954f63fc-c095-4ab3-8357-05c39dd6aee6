// import { test, describe, mock, before, after, it } from "node:test";
// import assert from "node:assert";
// import { Account } from "../models/Account.model.mjs";
// import { AccountService } from "../services/AccountService.mjs";
// import { AccountRestDataService } from "../dataServices/Account.rest.dataService.mjs";
// import { DeviceService } from "../services/DeviceService.mjs";
// import { DeviceRestDataService } from "../dataServices/Device.rest.dataService.mjs";
// import { DeviceDataRestDataService } from "../dataServices/DeviceData.rest.dataService.mjs";
// import { tx } from "../system/Postgres.mjs";
// import { EmailService } from "../services/EmailService.mjs";
// import { TokenService } from "../services/TokenService.mjs";
// import { Context } from "../system/Context.mjs";
// import { ISubscription, Subscription } from "../models/Subscription.model.mjs";
// import { SubscriptionService } from "../services/SubscriptionService.mjs";
// import { SubscriptionRestDataService } from "../dataServices/Subscription.rest.dataService.mjs";
// import { FeaturesetRestDataService } from "../dataServices/Featureset.rest.dataService.mjs";
// import moment = require("moment");
// import { config } from "../system/Config.mjs";
// import { SubscriptionTemplateRestDataService } from "../dataServices/SubscriptionTemplate.rest.dataService.mjs";
// import { sanitizeInput, sanitizeOutput } from "../models/Subscription.model.mjs";
// import { sanitizeInput as sai, sanitizeOutput as sao} from "../models/Account.model.mjs";
// import { Mode } from "../models/IModel.mjs";
// import { SubscriptionTemplate } from "../models/SubscriptionTemplate.model.mjs";
//   apiVersion: "2022-11-15",
// });

// async function addTemplateKey(context: Context, templateId: string) {
//     let dataService = new SubscriptionTemplateKeyRestDataService();
//     let key = new SubscriptionTemplateKey({
//         templateId,
//         key: crypto.randomUUID()
//     });
//     await dataService.create(context, key);
//     return key.key();
// }

// async function addTemplate(context: Context, template:  SubscriptionTemplate) {
//     let dataService = new SubscriptionTemplateRestDataService();
//     await dataService.deleteByTemplateId(context, template.templateId());
//     return await dataService.create(context, template);
// }

// describe("The subscription Lifecycle", async (t) => {
//   const idp = "mylio";
//   const sub = "<EMAIL>";
//   let accountService: AccountService;
//   let deviceService: DeviceService;
//   let subscriptionService: SubscriptionService;
//   let context = new Context();
//   let account: Account;
//   let _1000F: Subscription;
//   let _1015F: Subscription;

//   before(async (t) => {
//     deviceService = new DeviceService(
//       new DeviceRestDataService(),
//       new DeviceDataRestDataService(),
//       tx
//     );
//     accountService = new AccountService(
//       new AccountRestDataService(),
//       deviceService,
//       new EmailService(new TokenService()),
//       tx
//     );
//     subscriptionService = new SubscriptionService(
//       new SubscriptionRestDataService(),
//       accountService,
//       new FeaturesetRestDataService(),
//       new SubscriptionTemplateRestDataService(),
//       new SubscriptionTemplateKeyRestDataService()
//     );
//     account = await accountService.tryBySubAndIdp(context, sub, idp);
//     if (account) await accountService.delete(context, account.accountId());
//   });

//   it("Create the account", async (t) => {
//       // run this code as a user
//      context.hasAdminRights = () => false;
//     let payload = {
//       sub: "<EMAIL>",
//       email: "<EMAIL>",
//       idp: "mylio",
//       role: "user",
//     };
//     account = await subscriptionService.createAccountAndSubscribe(
//       context,
//       new Account(sai(payload, context.hasAdminRights(), Mode.Create))
//     );
//     assert(!!account, "Account not created");
//     context.aid = account.accountId();
//     _1000F = (await subscriptionService.list(context, account.accountId()))[0];
//     assert(!!_1000F, "Subscription not created on account creation");
//     assert(account.planId() === "1000", "Account plan id not set");
//   });

//   it("Crate a new 1015 Trial", async (t) => {
//       // run this code as a user
//      context.hasAdminRights = () => false;
//     let subscription = new Subscription(sanitizeInput({
//       accountId: account.accountId(),
//       templateId: "MYLIO-1015-P30D",
//     }, context.hasAdminRights(), Mode.Create));
//     _1015F = await subscriptionService.subscribe(context, subscription);
//     assert(!!_1015F, "1015 Trial not created");
//     account = await accountService.read(context, account.accountId());
//     assert.equal(account.planId(), "1015", "Account not upgraded to 1015");
//     assert.equal(
//       account.deviceLimit(),
//       64,
//       "Account device limit not assigned"
//     );
//   });

//     it("Boost the 1015 trial", async t => {
//         // only admins can boost subs
//         context.hasAdminRights = () => true;
//         let sub = new Subscription(sanitizeInput({
//             accountId: account.accountId(),
//             subscriptionId: _1015F.subscriptionId(),
//             customerId: _1015F.customerId(),
//             photoLimit: ********,
//             deviceLimit: 100
//         }, context.hasAdminRights(), Mode.Update));
//         sub = await subscriptionService.update(context, sub);
//         account = await accountService.read(context, account.accountId());
//         assert.equal(account.deviceLimit(), 100, "Account device limit not boosted" );

//         sub = await subscriptionService.delete(
//                 context,
//                 sub.accountId(),
//                 sub.customerId(),
//                 sub.subscriptionId()
//             );
//         account = await accountService.read(context, sub.accountId());
//         assert(account.photoLimit() === 2000000, "The boost has not been undone on deleted");
//     });

//     it("Add a VIP subscription", async t => {
//         let templateId = "MYLIO-1015-P100Y"
//         // only admins can boost subs
//         context.hasAdminRights = () => true;
//         let templateKey = await addTemplateKey(context, templateId);
//         let sub = new Subscription(sanitizeInput({
//             accountId: account.accountId(),
//             templateId: templateId,
//             templateKey
//         }, context.hasAdminRights(), Mode.Create));
//         sub = await subscriptionService.subscribe(context, sub);
//         account = await accountService.read(context, account.accountId());
//         assert(account.nextPlanDate() > moment.utc().add(moment.duration("P50Y")).toDate(), "VIP subscription not applied" );
//         sub = await subscriptionService.delete(context, sub.accountId(), sub.customerId(), sub.subscriptionId());
//         account = await accountService.read(context, account.accountId());
//         assert(account.planId() === "1000", "Account did not revert to essentials after deleting the vip sub");
//     });

//     it("Subscription should fail for an invalid coupon", async t => {
//         let templateId = "I_DONT_EXIST";
//         // only admins can boost subs
//         context.hasAdminRights = () => true;
//         let templateKey = await addTemplateKey(context, templateId);
//         let sub = new Subscription(sanitizeInput({
//             accountId: account.accountId(),
//             templateId: templateId,
//             templateKey
//         }, context.hasAdminRights(), Mode.Create));
//         try {
//             sub = await subscriptionService.subscribe(context, sub);
//             assert.fail("Subscripton succeeded with an invalid coupon");
//         }
//         catch(err) {
//             assert(err.code === "INVALID_TEMPLATE", "Subscription failed with the wrong error");
//         }
//     });

//     it("Subscription should fail if user uses an admin template", async t => {
//         let templateId = "MYLIO-1015-P100Y"
//         // only admins can boost subs
//         context.hasAdminRights = () => false;
//         let templateKey = await addTemplateKey(context, templateId);
//         let sub = new Subscription(sanitizeInput({
//             accountId: account.accountId(),
//             templateId: templateId,
//             templateKey
//         }, context.hasAdminRights(), Mode.Create));
//         try {
//             sub = await subscriptionService.subscribe(context, sub);
//             assert.fail("succeeded in creating the subscription");
//         }
//         catch (err) {
//             assert(err.code === "NOT_AUTHORIZED_TEMPLATE", "Subscripion failed with the wrong error message");
//         }
//     });

//     it("Subscription should fail if the UPA(uses per account) exceeded", async t => {
//         let templateId = "TEST_USES";
//         let template = new SubscriptionTemplate({
//             manager: "M",
//             templateId,
//             upa: 1,
//             featuresetId: "1015",
//             duration: "P1Y",
//         });
//         await addTemplate(context, template);
//         context.hasAdminRights = () => false;
//         let sub = new Subscription(sanitizeInput({
//             accountId: account.accountId(),
//             templateId
//         }, context.hasAdminRights(), Mode.Create));

//         sub = await subscriptionService.subscribe(context, sub);
//         assert(sub, "Creating the first sub failed");

//         try {
//             sub = await subscriptionService.subscribe(context, sub);
//             assert.fail("succeeded in creating the subscription with no uses left on template");
//         }
//         catch (err) {
//             assert(err.code === "TEMPLATE_UPA_EXCEEDED", "Subscripion failed with the wrong error message");
//         }
//     });

//     it("Create a 1015 Stripe Subscription", async t => {
//         let data  = new StripeData({
//             accountId: account.accountId(),
//         planId: "1015",
//             billingCycle: "monthly",
//         });

//         let card = new StripeCard({
//             cvc: "999",
//             zip: "99999",
//             expMonth: 10,
//             expYear: 2029,
//             number: "****************"
//         });

//         _1015S = await stripeService.create(context, data, card);
//         assert(!!_1015S, "Stripe record not created");
//         let sub = await subscriptionService.read(context, account.accountId(), _1015S.customerId(), _1015S.smrId());
//         assert(sub, "Subscription not created");
//     });

//     it("Update the billing Cyle and Credit card for the Stripe Sub", async t => {
//         let update = new StripeData({
//             planId: "1015",
//             billingCycle: "yearly",
//             accountId: account.accountId(),
//             customerId: _1015S.customerId(),
//             smrId: _1015S.smrId()
//         });

//         let card = new StripeCard({
//             cvc: "999",
//             zip: "99999",
//             expMonth: 10,
//             expYear: 2029,
//             number: "****************"
//         });

//         _1015S = await stripeService.update(context, update, card);
//         assert(!!_1015S, "Updated sub not read")
//     });

//     it("Cancel the Stripe Sub", async t => {
//         let smr = new StripeData({
//             accountId: account.accountId(),
//             customerId: _1015S.customerId(),
//             smrId: _1015S.smrId()
//         });
//         smr.canceled(true);
//         _1015S = await stripeService.update(context, smr, undefined);
//         let ss = await stripe.subscriptions.retrieve(_1015S.smrId());
//         assert(ss.cancel_at_period_end, "Stripe's records do not reflect the cancelation");
//         let sub = await subscriptionService.read(
//             context
//             , account.accountId()
//             , _1015S.customerId()
//             , _1015S.smrId()
//         );
//         assert(sub.canceled(), "Mylio's records show subscription was not canceled");
//     });

//     after(async (t) => {
//     console.log("done");
//     setTimeout(() => {
//       process.exit(0);
//     }, 200);
//   });
// });
