import express from "express";
import { config } from "../system/Config.mjs";
import crypto from "crypto";
import { microservice as g } from "../microservices/account.microservice.mjs";
import { Context } from "../system/Context.mjs";
import { makeError } from "../system/error.mjs";
import FastSpringService from "../services/FastSpringService.mjs";
import { post } from "../system/fetch.mjs";

export default function addAppStoreWebhook(router: express.Router) {
  router.post("/appstore/webhook", express.json(), async (req, res, next) => {
    let context = new Context();
    await g.appStoreService.handleNotification(context, req.body.signedPayload);
    res.status(200).send();
  });

  router.get("/appstore/send-test-notification", async (req, res, next) => {
    g.appStoreService.requestTestNotification();
  });

  router.post(
    "/accounts/:aid/handlePurchase",
    express.json(),
    async (req, res, next) => {
      let context = new Context();
      await g.appStoreService.handleReceipt(
        context,
        parseInt(req.params.aid),
        req.body.receipt
      );
      res.status(200).send();
    }
  );

  router.post(
    "/accounts/:aid/availablePurchases",
    express.json(),
    (req, res, next) => {
      return res.status(200).json({
        products: [
          { productId: "1022_yearly", planId: "1022" },
          { productId: "1022_monthly", planId: "1022" },
        ],
      });
    }
  );
}
