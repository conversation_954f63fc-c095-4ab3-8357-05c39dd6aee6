"use strict";

import * as express from "express";
import { microservice as g } from "../microservices/account.microservice.mjs";
import {
    safeAny,
} from "../system/safe.mjs";
import { Context } from "../system/Context.mjs";
import { InvitationRestDataService } from "../dataServices/Invitation.rest.dataService.mjs";
import { recaptcha } from "../system/recaptcha.mjs";
import { sanitizeOutput as sanitizeAccount } from "../models/Account.model.mjs";
import { sanitizeOutput as sanitizeInvitation } from "../models/Invitation.model.mjs";
import { composeResult } from "./authentication.routes.mjs";

import rateLimit from 'express-rate-limit';
/* tslint:enable */

// Configure the rate limiter: limit each IP to 100 requests per 15 minutes
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 20, // limit each IP to 100 requests per windowMs
    message: { code: "TOO_MANY_INVITATIONS", message: "Too many invitations sent from this IP, please try again after 15 minutes" }
});

export function addMultiUserRoutes(router: express.Router) {

    // Apply the rate limiter middleware to the specific route
    router.post("/accounts/:aid/invitations", safeAny, recaptcha, limiter, async (req, res, next) => {
        const context: Context = req.context;
        const email = context.any.email.toLowerCase();
        const aid = context.aid;
        const deviceConfig = context.any.deviceConfig || "{}";
        const protocol = context.any.protocol || "mylio";
        const invitation = await g.multiuserService.sendInvitation(context, protocol, aid, email, deviceConfig);
        return res.status(200).json(sanitizeInvitation(invitation, context.hasAdminRights()));
    });

    router.get("/invitations/:pin/accept", safeAny, recaptcha, async (req, res, next) => {
        const context: Context = req.context;
        const pin = req.params.pin
        const { invitation, account, rtoken } = await g.multiuserService.acceptInvitation(context, pin);
        const result = composeResult(context, rtoken, account, rtoken, invitation);
        return res.status(200).send(result);
    });

    router.delete("/accounts/:aid/invitations/:pin", safeAny, recaptcha, async (req, res, next) => {
        const context: Context = req.context;
        const pin = req.params.pin;
        const aid = context.aid;
        await g.multiuserService.deleteInvitation(context, pin);
        return res.status(200).send({ status: "OK" });
    });


}
