import { test, describe, mock, before, after, it } from "node:test";
import assert from "node:assert";
import { CloudClient } from "./CloudClient.mjs";
import { ILicense, LicenseStatus, Manager } from "../models/License.model.mjs";
import { createUsers, subscribe, sleep } from "./FastSpringTestHelpers.mjs";
import { IAccount } from "../models/Account.model.mjs";
import { IInvitation } from "../models/Invitation.model.mjs";

const HOST = "https://account-test-0.mylio.com";
const PASSWORD = "password";
const ADMIN_USER = "<EMAIL>";
const BUYER = "<EMAIL>";

describe("The license Lifecycle", async (t) => {
    let adminCloud = new CloudClient(HOST);
    let licenseKey = "";
    let cloud: CloudClient;
    let users: CloudClient[] = [];
    let invitation: IInvitation;
    let groupLicense: ILicense;


    before(async (t) => {
        users = await createUsers([BUYER]);
        cloud = users[0];
    });

    it("Start with a personal", async (t) => {
        let license = {
            accountId: cloud.account.accountId,
            manager: Manager.TestPaid,
            templateId: "personal",
            endDate: new Date()
        };
        await cloud.post("/accounts/:aid/licenses", license);
        const account: IAccount = await cloud.get("/accounts/:aid");
        assert(account.licenseTemplateId === "personal");
    });

    it("Upgrade to a personal-group", async (t) => {
        let license = {
            accountId: cloud.account.accountId,
            manager: Manager.TestPaid,
            templateId: "personal-group",
            endDate: new Date()

        };
        groupLicense = await cloud.post("/accounts/:aid/licenses", license);
        const account: IAccount = await cloud.get("/accounts/:aid");
        assert(account.licenseTemplateId === "personal-group");
    });

    it("Send an invitation", async (t) => {
        invitation = {
            email: "<EMAIL>",
            accountId: cloud.account.accountId,
            deviceConfig: JSON.stringify({ test: "TEST" }),
            //@ts-ignore
            protocol: "mylio"
        };
        invitation = await cloud.post("/accounts/:aid/invitations", invitation);
        assert(!!invitation.pin);
    });

    it("Accept the invitation", async (t) => {
        const result: { account: IAccount, invitation: IInvitation } = await cloud.get(`/invitations/${invitation.pin}/accept`);
        assert(result.account.accountId === cloud.account.accountId);
        assert(JSON.parse(result.invitation.deviceConfig).test === "TEST");
    });

    it("Delete the personal-group license", async (t) => {
        groupLicense.status = LicenseStatus.Deleted;
        await cloud.delete(`/accounts/:aid/licenses/${groupLicense.licenseId}`, groupLicense);
        const account: IAccount = await cloud.get("/accounts/:aid");
        assert(account.licenseTemplateId === "personal");
    });

    after(async (t) => {
        console.log("done");
        setTimeout(() => {
            process.exit(0);
        }, 200);
    });
});
