



create table a0.device_data(
    flags int NULL,
	modified_time timestamptz NULL,
	created_time timestamptz NULL,
	account_id int NOT NULL,
	deleted boolean NULL,
	t bytea NULL,
	d bytea NULL,
	device_id int NOT NULL,
	build text NULL,
	last_access_time timestamptz NULL,
	os text NULL,
	media_count int NULL,
	protocol_version int NULL,
	version text NULL,
	last_startup_time timestamptz NULL,
	last_hid_time timestamptz NULL,
	last_import_time timestamptz NULL,
	original_size int8 NULL,
	local_original_size int8 NULL
);

alter table a0.device_data
add primary key (account_id,device_id);

 

 create index ix_device_data_by_account_id on a0.device_data(account_id);
create index ix_device_data_by_account_id_and_device_id on a0.device_data(account_id,device_id);

