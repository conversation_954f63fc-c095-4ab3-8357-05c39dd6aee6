import * as url from "url";
const __filename = url.fileURLToPath(import.meta.url);
const __dirname = url.fileURLToPath(new URL(".", import.meta.url));

import path = require("path");
import { generate, mgenerate, ICompileUnit } from "./generate.mjs";

export async function generateAll() {
  console.log("Generate all started");

  let dbFolder = path.join(__dirname, "../../database/vnext");
  let serviceFolder = path.join(__dirname, "../../source/services");
  let dsFolder = path.join(__dirname, "../../source/dataServices");
  let modelFolder = path.join(__dirname, "../../source/models");
  let cppFolder = path.join(__dirname, "../../cpp/src/");
  let mobxFolder = path.join(__dirname, "../../mobx/models");

  let rest: ICompileUnit[] = [
    { template: "ddl.sql", location: dbFolder },
    { template: "rest.sql", location: dbFolder },
    { template: "model.mts", location: modelFolder },
    { template: "rest.dataService.mts", location: dsFolder },
  ];

  let sync: ICompileUnit[] = [
    { template: "sync.sql", location: dbFolder },
    { template: "sync.service.mts", location: serviceFolder },
  ];

  let model: ICompileUnit[] = [
    { template: "model.mts", location: modelFolder },
    { template: "mobx.ts", location: mobxFolder },
  ];

  let mobx: ICompileUnit[] = [{ template: "mobx.ts", location: mobxFolder }];

  let restAndSync = rest.concat(sync);
  await generate("account", rest);
  await generate("account", mobx);
  await generate("refresh_token", rest);
  await generate("device", restAndSync);
  await generate("device_data", restAndSync);
  await generate("message", restAndSync);
  await generate("account_metadata", rest);
  await generate("refresh_token", rest);
  await generate("system_property", restAndSync);
  await generate("user_property", restAndSync);
  await generate("license", rest);
  await generate("license", mobx);
  await generate("license_template", rest);
  await generate("license_template", mobx);
  await generate("device", mobx);
  await generate("device_data", mobx);
  await generate("support_ticket", model);
  await generate("pin", rest);
  await generate("invitation", rest);
  await generate("invitation_log_entry", rest);

  mgenerate(
    { template: "getFieldInfo.cpp", location: cppFolder },
    "account",
    "device",
    "device_data",
    "message",
    "system_property",
    "user_property"
  );

  console.log("Generate all completed");
}
await generateAll();
