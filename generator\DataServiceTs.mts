import { <PERSON>ti<PERSON>, Field } from "./Entity.mjs";
import { <PERSON>riptHelper, blockN, jsname, jsdt } from "./ScriptHelper.mjs";
import * as changeCase from "change-case";




export class DataServiceTs {
  public b: ScriptHelper;
  private _script = "";

  constructor(
    private kind: string,
    private e: Entity,
    private h: ScriptHelper
  ) {
    this._script = `
import { query } from "../system/Postgres.mjs";
import { ${jsname(e)}, I${jsname(e)}} from "../models/${jsname(e)}.model.mjs";
import { Context } from "../system/Context.mjs";
import { makeError } from "../system/error.mjs";


export class ${jsname(this.e)}${changeCase.pascalCase(kind)}DataService {

    ${blockN(e, `${kind}_public_members`)}

  public query(context: Context, sql: string, params: any[]) {
    return query < I${jsname(e)}> (context, sql, params)
      .then(results => {
        if (results.length === 1) {
          if (${h.pk
        .map((f) => `!results[0].${jsname(f)}`)
        .join(" && ")
      }) {
      return [];
    }
  }
  return results.filter(o => !!o).map(o => new ${jsname(e)}(o));
});
    }
`;
  }

  public func(name: string) {
    let sql = `\n\t\tpublic ${changeCase.camelCase(
      name
    )
      } (context: Context, entity: ${jsname(this.e)}) {
          ${blockN(this.e, `${name}_on_validate`)}
  let params = [
    ${this.h.pgParams
        .map((f) => `entity.${jsname(f)}()`)
        .join(",\n\t\t\t\t")
      }
  ];
  return this
    .query(context, "select * from a0.${this.e.name
      }_${name} (${this.h.pgParams.map(
        (f, i) => `$${i + 1}`
      )
      }) ", params)
  .then(r => r[0]);
        }\n`;
    this._script += sql;
    return sql;
  }

  public mfunc(name: string, multi: Array<Array<Field>>, returnsSet = false) {
    let sql = multi
      .map((fields) => {
        let fullName = `${name}_by_${fields.map((f) => f.name).join("_and_")} `;
        this.ffunc(fullName, fields, returnsSet);
      })
      .join("\n\n");
    return sql;
  }

  /* tslint:disable */
  public ffunc(name: string, fields: Array<Field>, returnsSet = false) {
    let sql = `\n\t\tpublic ${changeCase.camelCase(name)} (context: Context, ${fields
      .map((f) => `${jsname(f)}: ${jsdt(f)}`)
      .join(", ")}) {
  let params = [
    ${fields.map((f) => jsname(f)).join(",\n\t\t\t\t")}
  ];
  return this
    .query(context, "select * from a0.${this.e.name
      }_${name} (${fields.map((f, i) => `$${i + 1}`)}) ", params)${returnsSet ? "; " : ".then(r => r[0]); "
      }
                
        }\n`;
    this._script += sql;
    return sql;
  }
  /* tslint:enable */

  public text(text: string) {
    this._script += `\n\t${text} \n`;
  }

  public toString() {
    this._script += "\n}\n";
    return this._script;
  }
}
