

import Validation from "../system/Validation.mjs";
import { IModel } from "./IModel.mjs";







export interface IInvitation {
    flags?: number;
	modifiedTime?: Date;
	createdTime?: Date;
	accountId?: number;
	email?: string;
	pin?: string;
	expiresAt?: Date;
	deviceConfig?: string;
}


export class Invitation 
implements IModel {
    private _state: IInvitation;

    


    
    changed = false;

    constructor(state: IInvitation) {
        this.state(state);
    }

    isUndefined() {
        return this._state !== void 0;
    }

    async validate(skipUndefined?: boolean) {
        let v = new Validation(this, skipUndefined);
        


        return v;
    }

    rtt() {
        return "Invitation"; 
    }

    state (value?: IInvitation) {
        if (value !== undefined) { 
            this._state = value;
            
            


        }
        return this._state;
    }

    flags(value?: number) {
                if (value !== void 0) {
                    if (this.state().flags !== value) {
                        this.state().flags = value;
                        this.changed = true;
                    }
                }
                return this.state().flags;
            };

		modifiedTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().modifiedTime !== value) {
                        this.state().modifiedTime = value;
                        this.changed = true;
                    }
                }
                return this.state().modifiedTime;
            };

		createdTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().createdTime !== value) {
                        this.state().createdTime = value;
                        this.changed = true;
                    }
                }
                return this.state().createdTime;
            };

		accountId(value?: number) {
                if (value !== void 0) {
                    if (this.state().accountId !== value) {
                        this.state().accountId = value;
                        this.changed = true;
                    }
                }
                return this.state().accountId;
            };

		email(value?: string) {
                if (value !== void 0) {
                    if (this.state().email !== value) {
                        this.state().email = value;
                        this.changed = true;
                    }
                }
                return this.state().email;
            };

		pin(value?: string) {
                if (value !== void 0) {
                    if (this.state().pin !== value) {
                        this.state().pin = value;
                        this.changed = true;
                    }
                }
                return this.state().pin;
            };

		expiresAt(value?: Date) {
                if (value !== void 0) {
                    if (this.state().expiresAt !== value) {
                        this.state().expiresAt = value;
                        this.changed = true;
                    }
                }
                return this.state().expiresAt;
            };

		deviceConfig(value?: string) {
                if (value !== void 0) {
                    if (this.state().deviceConfig !== value) {
                        this.state().deviceConfig = value;
                        this.changed = true;
                    }
                }
                return this.state().deviceConfig;
            };

    differs(original: Invitation) {
        return (
            (this.flags() !== void 0 && this.flags() !== original.flags())
		 || (this.modifiedTime() !== void 0 && this.modifiedTime() !== original.modifiedTime())
		 || (this.createdTime() !== void 0 && this.createdTime() !== original.createdTime())
		 || (this.accountId() !== void 0 && this.accountId() !== original.accountId())
		 || (this.email() !== void 0 && this.email() !== original.email())
		 || (this.pin() !== void 0 && this.pin() !== original.pin())
		 || (this.expiresAt() !== void 0 && this.expiresAt() !== original.expiresAt())
		 || (this.deviceConfig() !== void 0 && this.deviceConfig() !== original.deviceConfig())
        );
    }







}



export function sanitizeInput(source: Invitation, amdin: boolean, mode: string) : IInvitation;
export function sanitizeInput(source: IInvitation, admin: boolean, mode: string) : IInvitation;
export function sanitizeInput(source: Invitation | IInvitation, admin = false, mode="default"): IInvitation {
    let s: IInvitation;
    if (source instanceof Invitation)
        s = source.state();
    else
        s = source;        
    let t = {} as IInvitation;
    
    
        
            switch(mode) {
                
                
                
            }
            t.flags = s.flags;
		t.accountId = s.accountId;
		t.email = s.email;
		t.pin = s.pin;
		t.expiresAt = s.expiresAt;
		t.deviceConfig = s.deviceConfig;
        
    return t;
}

export function sanitizeOutput(source: Invitation, amdin: boolean) : IInvitation;
export function sanitizeOutput(source: IInvitation, admin: boolean) : IInvitation;
export function sanitizeOutput(source: Invitation | IInvitation, admin = false): IInvitation {
    let s: IInvitation;
    if (source instanceof Invitation)
        s = source.state();
    else
        s = source;        
    let t = {} as IInvitation;
    
    
    
    t.flags = s.flags;	
t.modifiedTime = s.modifiedTime;	
t.createdTime = s.createdTime;	
t.accountId = s.accountId;	
t.email = s.email;	
t.pin = s.pin;	
t.expiresAt = s.expiresAt;	
t.deviceConfig = s.deviceConfig;
    return t;
}

export function mergeState(dbVersion: IInvitation, newVersion: IInvitation) {
    let targetState: IInvitation = {};
    targetState.flags = newVersion.flags === undefined ? dbVersion.flags : newVersion.flags;
	targetState.modifiedTime = newVersion.modifiedTime === undefined ? dbVersion.modifiedTime : newVersion.modifiedTime;
	targetState.createdTime = newVersion.createdTime === undefined ? dbVersion.createdTime : newVersion.createdTime;
	targetState.accountId = newVersion.accountId === undefined ? dbVersion.accountId : newVersion.accountId;
	targetState.email = newVersion.email === undefined ? dbVersion.email : newVersion.email;
	targetState.pin = newVersion.pin === undefined ? dbVersion.pin : newVersion.pin;
	targetState.expiresAt = newVersion.expiresAt === undefined ? dbVersion.expiresAt : newVersion.expiresAt;
	targetState.deviceConfig = newVersion.deviceConfig === undefined ? dbVersion.deviceConfig : newVersion.deviceConfig;
    return targetState;
}

export function merge(dbVersion: Invitation, newVersion: Invitation) {
    return new Invitation(mergeState(dbVersion.state(), newVersion.state()));
}
