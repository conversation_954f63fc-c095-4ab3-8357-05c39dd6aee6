#include <node.h>
#include <node_buffer.h>
#include "MYTrev.h"
#include "bjson.h"
#include "MYLiterals.h"
#include "base64.h"
#include "MYHash.h"
#include <napi.h>

Napi::Buffer<uint8_t> encode_object(Napi::Env isolate, const Napi::Object &json, int sectionId);
Napi::Buffer<uint8_t> encode_account_ping_request(Napi::Env isolate, const Napi::Object &ping);
Napi::Buffer<uint8_t> encode_account_ping_response(Napi::Env isolate, const Napi::Object &ping);
Napi::Buffer<uint8_t> encode_resource_ping_response(Napi::Env isolate, const Napi::Object &ping);
Napi::Buffer<uint8_t> encode_ping256(Napi::Env isolate, const Napi::Object &ping);
Napi::<PERSON>uffer<uint8_t> encode_sync(Napi::Env isolate, const Napi::Object &sync);
Napi::Buffer<uint8_t> encode_support_ticket(Napi::Env isolate, const Napi::Object &ticket);

void encode_services_and_account(MYBJsonBigRW *writer, const Napi::Object &jsObj);
Napi::Buffer<uint8_t> encode_rtoken_response(Napi::Env, const Napi::Object &jsObj);
Napi::Buffer<uint8_t> encode_token_response(
		Napi::Env,
		const Napi::Object &jsObj,
		std::string tokenKey = "token",
		int literalKey = MYLiterals::token);
Napi::Buffer<uint8_t> encode_token_responseV2(
		Napi::Env,
		const Napi::Object &jsObj);
