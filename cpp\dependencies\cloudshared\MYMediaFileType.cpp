//
//  MYMediaFileType.cpp
//  MyLoApp
//
//  Created by <PERSON><PERSON> on 8/21/13.
//  Copyright (c) 2013 MyLO Development LLC. All rights reserved.
//

#include "MYMediaFileType.h"
#include "MYMediaDevice.h"
#include <unordered_map>
#include "MYStringUtil.h"
#include "MYLiterals.h"

std::unordered_map<std::string, MYMediaFileType::Enum> _extToTypeMap;
std::set<std::string> _extSpecialDecoderSet;

class ExtMapInitializer
{
public:
    ExtMapInitializer()
    {
        static const char *s_nonRawFileExts[] =
            {
                "jpg", "jpeg", "tif", "tiff", "gif", "png", "bmp", "thm", "heic",
                NULL};

        static const char *s_rawFileTypes[] =
            {
                "3fr", "arw", "srf", "sr2", "crw", "cr2", "dcs", "dcr", "drf", "k25", "kdc",
                "dng", "mos", "mrw", "nef", "nrw", "orf", "pef", "ptx", "raf", "raw", "rw2",
                "raw", "rwl", "srw",
                NULL};

        static const char *s_specialRawFileTypes[] =
            {
                "psd", "psb", "pdf",
                "txt", "text",
                NULL};

        // static const char *s_audioFileTypes[] =
        //{
        //     "wav", "mp3", "flac",
        //     NULL
        //
        // };

        static const char *s_videoFileTypes[] =
            {
                "mov", "wmv", "mp4", "avi", "mts", "mkv", "m4v", "mpg", "mpeg", "asf", "rm", "mod", "3gp", "3g2",
                NULL};

        static const char *s_xmpFileTypes[] =
            {
                "xmp",
                NULL};

        struct TypeMapEntry
        {
            const char **extList;
            MYMediaFileType::Enum fileType;
        };

        static const TypeMapEntry s_entries[] =
            {
                {s_nonRawFileExts, MYMediaFileType::NonRAW},
                {s_rawFileTypes, MYMediaFileType::RAW},
                {s_specialRawFileTypes, MYMediaFileType::RAW},
                {s_videoFileTypes, MYMediaFileType::Video},
                {s_xmpFileTypes, MYMediaFileType::XMP},
            };

        for (unsigned int entryIndex = 0; entryIndex < sizeof(s_entries) / sizeof(s_entries[0]); entryIndex++)
        {
            const char **extList = s_entries[entryIndex].extList;
            while (*extList != NULL)
            {
                std::string ext = *extList;
                _extToTypeMap[ext] = s_entries[entryIndex].fileType;

                extList++;
            }
        }

        for (unsigned int entryIndex = 0; entryIndex < sizeof(s_specialRawFileTypes) / sizeof(s_specialRawFileTypes[0]); entryIndex++)
        {
            if (s_specialRawFileTypes[entryIndex] != nullptr)
            {
                std::string ext = s_specialRawFileTypes[entryIndex];
                _extSpecialDecoderSet.insert(ext);
            }
        }
    }
};

static ExtMapInitializer s_mapInitializer;

/*static*/ MYMediaFileType::Enum MYMediaFileType::getTypeFromExtension(const std::string &rawext)
{
    std::string ext = rawext;
    std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);

    auto it = _extToTypeMap.find(ext);
    if (it == _extToTypeMap.end())
        return Enum::Unknown;
    return it->second;
}
// isSpecialDecoderRAW
//
// Special Raw file extensions return MYMediaFileType::RAW when getTypeFromExtension() is called but they
// have special decoders so this call can be used to differenciate from other (real) RAW file extensions
/*static*/ bool MYMediaFileType::isSpecialDecoderRAW(const std::string &extension)
{
    if (extension.empty())
        return false;

    std::string ext = extension;
    std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);

    auto it = _extSpecialDecoderSet.find(ext);
    if (it == _extSpecialDecoderSet.end())
        return false;
    return true;
}

// isPDF
//
#ifdef MYLIO_CLIENT
/*static*/ bool MYMediaFileType::isPDF(const std::string &extension)
{
    return (strcasecmp(extension.c_str(), "pdf") == 0);
}

// isPSD
//
/*static*/ bool MYMediaFileType::isPSD(const std::string &extension)
{
    return (strcasecmp(extension.c_str(), "psd") == 0);
}

// isPSB
//
/*static*/ bool MYMediaFileType::isPSB(const std::string &extension)
{
    return (strcasecmp(extension.c_str(), "psb") == 0);
}

// isNote
//
/*static*/ bool MYMediaFileType::isNote(const std::string &extension)
{
    return ((strcasecmp(extension.c_str(), "txt") == 0) || (strcasecmp(extension.c_str(), "text") == 0));
}

// isJpg
//
/*static*/ bool MYMediaFileType::isJpg(const std::string &extension)
{
    return (strcasecmp(extension.c_str(), "jpg") == 0) || (strcasecmp(extension.c_str(), "jpeg") == 0);
}
// isMov
//
/*static*/ bool MYMediaFileType::isMov(const std::string &extension)
{
    return (strcasecmp(extension.c_str(), "mov") == 0);
}
#endif // MYLIO_CLIENT

//
// display image logic
//
const char *const MYMediaFileType::DisplayImagePostfix = "_display";

bool MYMediaFileType::isDisplayImage(const std::string &filenameNoExt)
{
    std::string displayImagePostfix = MYMediaFileType::DisplayImagePostfix;

    size_t tagIndex = filenameNoExt.rfind(displayImagePostfix.c_str());

    // starts with the suffix?
    if (tagIndex == 0)
        return false;

    return (tagIndex != std::string::npos && tagIndex == filenameNoExt.length() - displayImagePostfix.length());
}

std::string MYMediaFileType::stripDisplayImagePostfix(const std::string &filenameNoExt)
{
    std::string result = filenameNoExt;

    if (MYMediaFileType::isDisplayImage(filenameNoExt))
    {
        std::string displayImagePostfix = MYMediaFileType::DisplayImagePostfix;
        result = filenameNoExt.substr(0, filenameNoExt.length() - displayImagePostfix.length());
    }

    return result;
}

std::string MYMediaFileType::appendDisplayImagePostfix(const std::string &filenameNoExt)
{
    std::string result = filenameNoExt;

    if (!MYMediaFileType::isDisplayImage(filenameNoExt))
    {
        result += MYMediaFileType::DisplayImagePostfix;
    }

    return result;
}

std::string MYMediaFileType::getDisplayImagePostfix()
{
    return MYMediaFileType::DisplayImagePostfix;
}

// int MYMediaFileType::getHasMask(Enum type)
//{
//     if (type == RAW) return (3 << (int)MDLHasBits::HasRAW);
//     if (type == NonRAW) return  (3 << (int)MDLHasBits::HasNonRAW);
//     if (type == Video) return  (3 << (int)MDLHasBits::HasVideo);
//     if (type == XMP) return  (3 << (int)MDLHasBits::HasXMP);
//     if (type == DisplayImage) return (3 << (int)MDLHasBits::HasDisplayImage);
//     if (type == Preview) return  (3 << (int)MDLHasBits::HasPreview);
//     if (type == Thumbnail) return  (3 << (int)MDLHasBits::HasThumbnail);
//
//     assert(false);
//     return  (3 << (int)MDLHasBits::HasRAW) | (3 << (int)MDLHasBits::HasVideo);
// }
//
// int MYMediaFileType::getNeedsMask(Enum type)
//{
//     if (type == RAW) return (7 << (int)MDLNeedsBits::NeedsOriginals);
//     if (type == NonRAW) return  (7 << (int)MDLNeedsBits::NeedsOriginals);
//     if (type == Video) return  (7 << (int)MDLNeedsBits::NeedsOriginals);
//     if (type == XMP) return  (7 << (int)MDLNeedsBits::NeedsOriginals);
//     if (type == DisplayImage) return (7 << (int)MDLNeedsBits::NeedsOriginals);
//     if (type == Preview) return  (7 << (int)MDLNeedsBits::NeedsPreview);
//     if (type == Thumbnail) return  (7 << (int)MDLNeedsBits::NeedsThumbnail);
//
//     assert(false);
//     return  (7 << (int)MDLNeedsBits::NeedsOriginals);
// }
//
// MDLHas MYMediaFileType::getHasMaskFromBits(Enum type, int value)
//{
//     if (type == RAW) return (MDLHas)((value >> (int)MDLHasBits::HasRAW) & 3);
//     if (type == NonRAW) return (MDLHas)((value >> (int)MDLHasBits::HasNonRAW) & 3);
//     if (type == Video) return (MDLHas)((value >> (int)MDLHasBits::HasVideo) & 3);
//     if (type == XMP) return (MDLHas)((value >> (int)MDLHasBits::HasXMP) & 3);
//     if (type == DisplayImage) return (MDLHas)((value >> (int)MDLHasBits::HasDisplayImage) & 3);
//     if (type == Preview) return (MDLHas)((value >> (int)MDLHasBits::HasPreview) & 3);
//     if (type == Thumbnail) return (MDLHas)((value >> (int)MDLHasBits::HasThumbnail) & 3);
//
//     assert(false);
//     return  MDLHas::DoesntHave;
// }
//
//
// MDLNeeds MYMediaFileType::getNeedsMaskFromBits(Enum type, int value)
//{
//     if (type == RAW) return (MDLNeeds)((value >> (int)MDLNeedsBits::NeedsOriginals) & 7);
//     if (type == NonRAW) return (MDLNeeds)((value >> (int)MDLNeedsBits::NeedsOriginals) & 7);
//     if (type == Video) return (MDLNeeds)((value >> (int)MDLNeedsBits::NeedsOriginals) & 7);
//     if (type == XMP) return (MDLNeeds)((value >> (int)MDLNeedsBits::NeedsOriginals) & 7);
//     if (type == DisplayImage) return (MDLNeeds)((value >> (int)MDLNeedsBits::NeedsOriginals) & 7);
//     if (type == Preview) return (MDLNeeds)((value >> (int)MDLNeedsBits::NeedsPreview) & 7);
//     if (type == Thumbnail) return (MDLNeeds)((value >> (int)MDLNeedsBits::NeedsThumbnail) & 7);
//
//     assert(false);
//     return MDLNeeds::ForcedNo;
// }

const std::array<MYMediaFileType::Enum, 7> MYMediaFileType::typeList =
    {
        MYMediaFileType::RAW,
        MYMediaFileType::NonRAW,
        MYMediaFileType::Video,
        MYMediaFileType::DisplayImage,
        MYMediaFileType::XMP,
        MYMediaFileType::Preview,
        MYMediaFileType::Thumbnail};

const MYHashRef MYHashRefEmpty = 0xFFFF;
const MYStringRef MYStringRefEmpty = 0xFFFF;
const MYStringRef MYStringRefNonDefaultEmpty = 0x3FEF; // This gets stored for fields where the default (missing) value means something else. This always means "".

std::array<std::array<const char, 4>, 16> g_commonFileExtensions = *(std::array<std::array<const char, 4>, 16> *)"CR2\0"
                                                                                                                 "DNG\0"
                                                                                                                 "JPG\0"
                                                                                                                 "arw\0"
                                                                                                                 "cr2\0"
                                                                                                                 "dng\0"
                                                                                                                 "jpg\0"
                                                                                                                 "mp4\0"
                                                                                                                 "nef\0"
                                                                                                                 "orf\0"
                                                                                                                 "pdf\0"
                                                                                                                 "psd\0"
                                                                                                                 "raf\0"
                                                                                                                 "rw2\0"
                                                                                                                 "rwl\0"
                                                                                                                 "srw\0";

std::array<std::string, 16> g_commonFileExtensionsString =
    {
        "CR2",
        "DNG",
        "JPG",
        "arw",
        "cr2",
        "dng",
        "jpg",
        "mp4",
        "nef",
        "orf",
        "pdf",
        "psd",
        "raf",
        "rw2",
        "rwl",
        "srw"};

bool MYFileFormatInterner::isInternedId(uint16_t id)
{
    return (id == MYStringRefEmpty) || ((id >= 0x3FEF) && (id <= 0x3FFF));
}

const std::string &MYFileFormatInterner::getFormatFromId(MYStringRef id, MYMediaFileType::Enum type)
{
    if (id == MYStringRefEmpty)
    {
        switch (type)
        {
        case MYMediaFileType::NoType:
            return g_emptyString;

        case MYMediaFileType::RAW:
            return g_dngFormat;

        case MYMediaFileType::Video:
            return g_vidFormat;

        case MYMediaFileType::XMP:
            return g_xmpFormat;

        case MYMediaFileType::DisplayImage:
        case MYMediaFileType::NonRAW:
        case MYMediaFileType::Preview:
        case MYMediaFileType::Thumbnail:
            return g_jpgFormat;

        default:
            break;
        }
        assert(false);
    }

    if (id == MYStringRefNonDefaultEmpty)
    {
        return g_emptyString;
    }

    assert(isInternedId(id));
    return g_commonFileExtensionsString[id - 0x3FF0];
}

MYStringRef MYFileFormatInterner::getIdFromFormat(const std::string &format, MYMediaFileType::Enum type)
{
    switch (type)
    {
    case MYMediaFileType::NoType:
        if (format.empty())
            return MYStringRefEmpty;
        break;

    case MYMediaFileType::RAW:
        if (g_dngFormat == format)
            return MYStringRefEmpty;
        break;

    case MYMediaFileType::Video:
        if (g_vidFormat == format)
            return MYStringRefEmpty;
        break;

    case MYMediaFileType::XMP:
        if (g_xmpFormat == format)
            return MYStringRefEmpty;
        break;

    case MYMediaFileType::DisplayImage:
    case MYMediaFileType::NonRAW:
    case MYMediaFileType::Preview:
    case MYMediaFileType::Thumbnail:
        if (g_jpgFormat == format)
            return MYStringRefEmpty;
        break;

    default:
        break;
    }

    if (format.empty())
    {
        return MYStringRefNonDefaultEmpty;
    }

    if (format.length() != 3)
    {
        return 0;
    }

    std::array<const char, 4> formatBuffer = *(const std::array<const char, 4> *)format.c_str();

    const auto &pos = std::lower_bound(
        g_commonFileExtensions.begin(), g_commonFileExtensions.end(), formatBuffer);

    if (pos == g_commonFileExtensions.end())
    {
        return 0;
    }

    if (*pos != formatBuffer)
    {
        return 0;
    }

    std::array<const char, 4> *first = &(*g_commonFileExtensions.begin());
    std::array<const char, 4> *second = &(*pos);
    auto index = (second - first);
    assert(index < 16);
    return 0x3FF0 + (MYStringRef)index;
}
