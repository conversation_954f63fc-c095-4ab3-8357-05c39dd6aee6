import { test, describe, mock, before, after, it } from "node:test";
import assert from "node:assert";
import { CloudClient } from "./CloudClient.mjs";
import { <PERSON>uilder, By, Browser, until } from "selenium-webdriver";
import { ILicense, Manager } from "../models/License.model.mjs";
import { createUsers, subscribeNK, sleep, buyStorage } from "./FastSpringTestHelpers.mjs";

const HOST = "https://willem.mylio.com";
const PASSWORD = "password";
const ADMIN_USER = "<EMAIL>";
const BUYER = "<EMAIL>";



describe("The license Lifecycle", async (t) => {
    let adminCloud = new CloudClient(HOST);
    let licenseKey = "";
    let l1Cloud: CloudClient;
    let users: CloudClient[] = [];

    before(async (t) => {
        users = await createUsers(["<EMAIL>"]);
        l1Cloud = users[0];
    });

    it("buy a plus subscription (no storage)", async (t) => {
        await subscribeNK(l1Cloud.account.sub);
        await sleep(5000);
        let licenses = await l1Cloud.get<ILicense[]>("/accounts/:aid/licenses");
        let plusLicense = licenses.find(l => l.manager === Manager.FastSpring && l.accountId === l1Cloud.account.accountId);
        assert(!!plusLicense, "license not found");
    });

    it("preview adding 2tb", async (t) => {
        await buyStorage(l1Cloud.account.sub, "twoTBmonth");
        await sleep(5000);
        let licenses = await l1Cloud.get<ILicense[]>("/accounts/:aid/licenses");
        let storageLicense = licenses.find(l => l.manager === Manager.FastSpring && l.accountId === l1Cloud.account.accountId);
        assert(!!storageLicense, "license not found");
    });



    after(async (t) => {
        console.log("done");
        setTimeout(() => {
            process.exit(0);
        }, 200);
    });
});
