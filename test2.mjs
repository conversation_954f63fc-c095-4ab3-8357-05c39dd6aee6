import fs from 'fs';
import path from 'path';

// For Windows path, use a literal string or the `path` module:
const outputFilePath = path.join('C:', 'returns', 'subscriptions.csv');

// FastSpring API credentials
const FASTSPRING_API_USERNAME = process.env.FASTSPRING_API_USERNAME || 'C96DV4ZVRT-QTWPPAV_TMQ';
const FASTSPRING_API_PASSWORD = process.env.FASTSPRING_API_PASSWORD || '-9Nbw8euTX6rk_MCb9AznQ';


// Basic Auth token
const token = Buffer.from(
    `${FASTSPRING_API_USERNAME}:${FASTSPRING_API_PASSWORD}`
).toString('base64');

// FastSpring API base
const BASE_URL = 'https://api.fastspring.com';

/**
 * Retrieve all FastSpring subscriptions (paginated).
 */
async function listAllSubscriptions() {
    let endpoint = '/subscriptions?limit=100';
    let allSubscriptions = [];
    let page = 0;
    while (endpoint) {
        const url = `${BASE_URL}${endpoint}`;
        const response = await fetch(url, {
            headers: {
                Authorization: `Basic ${token}`,
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`HTTP Error: ${response.status}\n${errorText}`);
        }

        const data = await response.json();
        const { subscriptions, nextPage } = data;
        if (nextPage) {
            endpoint = `/subscriptions?limit=100&page=${nextPage}`;
        }
        else
            endpoint = null;

        allSubscriptions.push(...subscriptions);
    }

    return allSubscriptions;
}

/**
 * Convert subscription objects to CSV format.
 * 
 * Customize the fields you want to include in the CSV.
 */
function subscriptionsToCSV(subscriptions) {
    // Choose the fields you want in your CSV
    const fields = [
        'id',
        'state',
        'active',
        'nextChargeDate',
        'customerId',
        // add or remove fields as needed
    ];

    // Create CSV header
    const header = fields.join(',');

    // Create CSV rows
    const rows = subscriptions.map(sub => {
        return fields.map(field => {
            // Convert undefined to empty string, or handle special cases
            let value = sub[field] ?? '';
            // Escape quotes, and wrap in quotes if you want to handle commas within data
            // e.g., "some value, with comma" => "\"some value, with comma\""
            if (typeof value === 'string') {
                value = value.replace(/"/g, '""'); // Escape double quotes
                value = `"${value}"`; // Wrap each field in double quotes
            }
            return value;
        }).join(',');
    });

    // Combine header and rows
    return [header, ...rows].join('\n');
}

// Main function: fetch, convert to CSV, write to file
(async () => {
    try {
        const subscriptions = await listAllSubscriptions();
        console.log(`Fetched ${subscriptions.length} subscriptions.`);

        // Convert to CSV
        const csvData = subscriptionsToCSV(subscriptions);

        // Write file to C:\returns\subscriptions.csv
        fs.writeFileSync(outputFilePath, csvData, 'utf8');
        console.log(`Subscriptions have been written to: ${outputFilePath}`);
    } catch (error) {
        console.error('Error:', error.message);
        process.exit(1);
    }
})();
