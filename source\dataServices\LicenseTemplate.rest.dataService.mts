
import { query } from "../system/Postgres.mjs";
import { LicenseTemplate, ILicenseTemplate} from "../models/LicenseTemplate.model.mjs";
import { Context } from "../system/Context.mjs";
import { makeError } from "../system/error.mjs";


export class LicenseTemplateRestDataService {

    



  public query(context: Context, sql: string, params: any[]) {
    return query < ILicenseTemplate> (context, sql, params)
      .then(results => {
        if (results.length === 1) {
          if (!results[0].templateId) {
      return [];
    }
  }
  return results.filter(o => !!o).map(o => new LicenseTemplate(o));
});
    }

		public create (context: Context, entity: LicenseTemplate) {
          


  let params = [
    entity.flags(),
				entity.modifiedTime(),
				entity.createdTime(),
				entity.templateId(),
				entity.duration(),
				entity.uses(),
				entity.availableUpgrades(),
				entity.displayName(),
				entity.public(),
				entity.weight(),
				entity.deviceLimit(),
				entity.photoLimit(),
				entity.features(),
				entity.cloudStorageLimit()
  ];
  return this
    .query(context, "select * from a0.license_template_create ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14) ", params)
  .then(r => r[0]);
        }

		public readByTemplateId (context: Context, templateId: string) {
  let params = [
    templateId
  ];
  return this
    .query(context, "select * from a0.license_template_read_by_template_id  ($1) ", params).then(r => r[0]); 
                
        }

		public update (context: Context, entity: LicenseTemplate) {
          


  let params = [
    entity.flags(),
				entity.modifiedTime(),
				entity.createdTime(),
				entity.templateId(),
				entity.duration(),
				entity.uses(),
				entity.availableUpgrades(),
				entity.displayName(),
				entity.public(),
				entity.weight(),
				entity.deviceLimit(),
				entity.photoLimit(),
				entity.features(),
				entity.cloudStorageLimit()
  ];
  return this
    .query(context, "select * from a0.license_template_update ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14) ", params)
  .then(r => r[0]);
        }

		public deleteByTemplateId (context: Context, templateId: string) {
  let params = [
    templateId
  ];
  return this
    .query(context, "select * from a0.license_template_delete_by_template_id  ($1) ", params).then(r => r[0]); 
                
        }

}
