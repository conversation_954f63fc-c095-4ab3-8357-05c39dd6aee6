import express = require("express");
import { config, getServices } from "../system/Config.mjs";
import {
  safeAny,
  safeAccount,
  secure,
  safeNone,
  s,
  b,
} from "../system/safe.mjs";
import { microservice } from "../microservices/account.microservice.mjs";
import { Context } from "../system/Context.mjs";

export interface IAmazonRequest extends express.Request {
  accessToken: string;
  refreshToken: string;
}

interface IResult {
  error?: string;
  token?: string;
}

export function addAmazonRoutes(router: express.Router) {
  router.get(
    "/accounts/:aid/amazon/drive/access-token",
    safeNone,
    secure,
    (req, res, next) => {
      const context = req.context;
      return microservice.amazonService
        .getDriveAccessToken(context, context.aid)
        .then((token) => {
          return res.status(200).json({ token });
        })
        .catch(next);
    }
  );

  router.get("/amazon/authorize", (req, res, next) => {
    return microservice.amazonService
      .addRefreshToken(s(req.query.code), s(req.query.state))
      .then(() => {
        res.setHeader(
          "location",
          `${config.cloud}/amazon/loggedin?success=true`
        );
        res.status(302).end();
        return res;
      })
      .catch((err) => {
        let reason = new Buffer(JSON.stringify(err.error)).toString("hex");
        res.setHeader(
          "location",
          `${config.cloud}/amazon/loggedin?success=false&reason=${reason}`
        );
        res.status(302).end();
        return res;
      })
      .catch(next);
  });

  router.post(
    "/accounts/:aid/amazon/drive/reservation",
    safeAny,
    secure,
    (req, res, next) => {
      try {
        return microservice.amazonService
          .reserveDrive(req.context, req.context.aid)
          .then((reservation) => {
            return res.status(200).json(reservation);
          })
          .catch(next);
      } catch (err) {
        next(err);
      }
    }
  );

  router.put(
    "/accounts/:aid/amazon/:task/revoke",
    safeNone,
    secure,
    (req, res, next) => {
      const context: Context = req.context;
      const aid = context.aid;
      const task = req.params.task;

      return microservice.amazonService
        .revokeToken(context, aid, task)
        .then(() => {
          context.dumpLog();
          return res.sendStatus(200);
        })
        .catch(next);
    }
  );

  router.get(
    "/accounts/:aid/amazon/drive/url",
    safeNone,
    secure,
    (req, res, next) => {
      const context = req.context;
      return res.status(200).json({
        url: microservice.amazonService.getDriveOAuthUrl(
          context,
          context.aid,
          b(req.query.encrypt),
          b(req.query.allowUnlimited)
        ),
      });
    }
  );

  router.get("/amazon/loggedin", (req, res, next) => {
    res
      .status(200)
      .contentType("text/html")
      /* tslint:disable */
      .write(
        `<html>
          <body>
            <h1 id="warning" style="display:none;color:red">SECURITY WARNING: Please treat the URL above as you would your password and do not share it with anyone<h1>
            <script>
              (function() {
                setTimeout(function() {
                  document.getElementById("warning").style.display = "inline-block";
                }, 2000);
              })();
            </script>
          </body>
        </html>`
      );
    /* tslint:enable */

    res.end();
  });
}
