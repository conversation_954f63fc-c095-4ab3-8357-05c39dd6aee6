import { TsFile } from "../TsFile.mjs";
import { Entity, Field } from "../Entity.mjs";
import {
  ScriptHelper,
  jsdt,
  jsname,
  blockI,
  blockN,
} from "../ScriptHelper.mjs";
import _ from "lodash";

function writeRoleSection(e: Entity, from: string, to: string) {
  function writeActionSection(
    from: string,
    to: string,
    mode: string,
    fields: Field[]
  ) {
    if (fields.length) {
      return `case "${mode}":\n\t\t${fields
        .map((f) => `t.${jsname(f)} = s.${jsname(f)};`)
        .join("\n\t\t")}\n\t\tbreak;`;
    }
    return "";
  }

  let fieldLists = [
    e.fields.filter((f) => f.canGo(from, to, "create")),
    e.fields.filter((f) => f.canGo(from, to, "read")),
    e.fields.filter((f) => f.canGo(from, to, "update")),
    e.fields.filter((f) => f.canGo(from, to, "delete")),
  ];

  let all = new Array<Field>();
  for (let v of fieldLists[0]) {
    let f = false;
    for (let i = 1; i < fieldLists.length; ++i) {
      let match = fieldLists[i].find((f) => f.id === v.id);
      if (!match) {
        f = true;
        break;
      }
    }
    if (f) continue;
    all.push(v);
  }

  for (let fields of fieldLists) _.pullAllBy(fields, all, "id");

  let create = writeActionSection(from, to, "create", fieldLists[0]);
  let read = writeActionSection(from, to, "read", fieldLists[1]);
  let update = writeActionSection(from, to, "update", fieldLists[2]);
  let del = writeActionSection(from, to, "delete", fieldLists[3]);
  let alls = all.map((f) => `t.${jsname(f)} = s.${jsname(f)};`).join("\n\t\t");

  if (
    create.length > 0 ||
    read.length > 0 ||
    update.length > 0 ||
    del.length > 0 ||
    alls.length > 0
  ) {
    return `
        ${from === "admin" || to === "admin" ? `if (admin) {` : ""}
            switch(mode) {
                ${create.length ? create : ""}
                ${update.length ? update : ""}
                ${del.length ? del : ""}
            }
            ${alls.length ? alls : ""}
        ${from === "admin" || to === "admin" ? `}` : ""}`;
  }
  return "";
}

function writeOutputSanitizer(e: Entity, h: ScriptHelper) {
  let userFields = e.fields.filter((f) => f.canGo("cloud", "web"));
  let adminFields = e.fields.filter((f) => f.canGo("cloud", "admin"));
  return `
export function sanitizeOutput(source: ${jsname(
    e
  )}, amdin: boolean) : I${jsname(e)};
export function sanitizeOutput(source: I${jsname(
    e
  )}, admin: boolean) : I${jsname(e)};
export function sanitizeOutput(source: ${jsname(e)} | I${jsname(
    e
  )}, admin = false): I${jsname(e)} {
    let s: I${jsname(e)};
    if (source instanceof ${jsname(e)})
        s = source.state();
    else
        s = source;        
    let t = {} as I${jsname(e)};
    ${adminFields.length > 0 ? "if (admin) {\n" : ""}
    ${adminFields
      .map((f) => {
        return `t.${jsname(f)} = s.${jsname(f)};`;
      })
      .join("\t\t\n")}
    ${adminFields.length > 0 ? "}\n" : ""}
    ${userFields
      .map((f) => {
        return `t.${jsname(f)} = s.${jsname(f)};`;
      })
      .join("\t\n")}
    return t;
}`;
}

function writeInputSanitizer(e: Entity, h: ScriptHelper) {
  return `
export function sanitizeInput(source: ${jsname(
    e
  )}, amdin: boolean, mode: string) : I${jsname(e)};
export function sanitizeInput(source: I${jsname(
    e
  )}, admin: boolean, mode: string) : I${jsname(e)};
export function sanitizeInput(source: ${jsname(e)} | I${jsname(
    e
  )}, admin = false, mode="default"): I${jsname(e)} {
    let s: I${jsname(e)};
    if (source instanceof ${jsname(e)})
        s = source.state();
    else
        s = source;        
    let t = {} as I${jsname(e)};
    ${writeRoleSection(e, "admin", "cloud")}
    ${writeRoleSection(e, "web", "cloud")}
    return t;
}`;
}

function writeMerge(e: Entity, h: ScriptHelper) {
  return `
export function merge(dbVersion: ${jsname(e)}, newVersion: ${jsname(e)}) {
    return new ${jsname(e)}(mergeState(dbVersion.state(), newVersion.state()));
}`;
}

function writeMergeState(e: Entity, h: ScriptHelper) {
  let userFields = e.fields;
  return `
export function mergeState(dbVersion: I${jsname(e)}, newVersion: I${jsname(
    e
  )}) {
    let targetState: I${jsname(e)} = {};
    ${userFields
      .map(
        (u) =>
          `targetState.${jsname(u)} = newVersion.${jsname(
            u
          )} === undefined ? dbVersion.${jsname(u)} : newVersion.${jsname(u)};`
      )
      .join("\n\t")}
    return targetState;
}`;
}

export function template(entity: Entity, h: ScriptHelper) {
  let ts = new TsFile(entity);

  ts.write(
    (e, b) => `

import Validation from "../system/Validation.mjs";
import { IModel } from "./IModel.mjs";
${blockN(e, "imports")}
${blockN(e, "enums")}

export interface I${jsname(e)} {
    ${h.modelFields.map((f) => `${jsname(f)}?: ${jsdt(f)}`).join(";\n\t")};
}


export class ${jsname(e)} 
implements IModel {
    private _state: I${jsname(e)};

    ${blockN(e, "model_public_members")}
    
    changed = false;

    constructor(state: I${jsname(e)}) {
        this.state(state);
    }

    isUndefined() {
        return this._state !== void 0;
    }

    async validate(skipUndefined?: boolean) {
        let v = new Validation(this, skipUndefined);
        ${blockN(e, "validate")}
        return v;
    }

    rtt() {
        return "${jsname(e)}"; 
    }

    state (value?: I${jsname(e)}) {
        if (value !== undefined) { 
            this._state = value;
            ${h.modelFields
              .filter((f) => !!f.default)
              .map(
                (f) =>
                  `if (this._state.${jsname(
                    f
                  )} === undefined) this._state.${jsname(f)} = ${
                    jsdt(f) === "string" ? `"${f.default}"` : f.default
                  };`
              )
              .join("\n\t\t\t")}
            ${blockN(e, "load_state")}
        }
        return this._state;
    }

    ${h.modelFields
      .map((f) =>
        (() => {
          let key = `__p_${jsname(f)}`;
          let expr = `${jsname(f)}(value?: ${jsdt(f)}) {
                if (value !== void 0) {
                    if (this.state().${jsname(f)} !== value) {
                        this.state().${jsname(f)} = value;
                        this.changed = true;
                    }
                }
                return this.state().${jsname(f)};
            };`;
          return blockI(e, key, expr);
        })()
      )
      .join("\n\n\t\t")}

    differs(original: ${jsname(e)}) {
        return (
            ${h.modelFields
              .map(
                (f) =>
                  `(this.${jsname(f)}() !== void 0 && this.${jsname(
                    f
                  )}() !== original.${jsname(f)}())`
              )
              .join("\n\t\t || ")}
        );
    }




${blockN(e, "private_members")}
}


${writeInputSanitizer(e, h)}
${writeOutputSanitizer(e, h)}
${writeMergeState(e, h)}
${writeMerge(e, h)}
`
  );

  return ts.toString();
}
