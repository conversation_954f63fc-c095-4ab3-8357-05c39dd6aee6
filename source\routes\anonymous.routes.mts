import express = require("express");
import { microservice as g } from "../microservices/account.microservice.mjs";
import { safeAccount, secureURLToken, safeNone } from "../system/safe.mjs";

export function addAnonymousRoutes(router: express.Router) {
  router.get("/groove", safeNone, (req, res, next) => {
    let context = req.context;
    let email = context.query.email;
    let apiToken = context.query.api_token;

    return g.accountService
      .handleGrooveWebhook(context, email, apiToken)
      .then((result) => {
        context.dumpLog();
        return res.status(200).json(result);
      })
      .catch(next);
  });
}
