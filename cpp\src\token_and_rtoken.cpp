#include "encode_functions.h"
#include "helpers.h"
#include <napi.h>

void encode_services_and_account(MYBJsonBigRW *writer, const Napi::Object &jsObj)
{

	if (jsObj.HasOwnProperty("services"))
	{
		auto services = jsObj.Get("services").As<Napi::Array>();
		// // { token: "XXX", services: [
		writer->StartArray(MYLiterals::services);
		for (size_t i = 0; i < services.Length(); i++)
		{
			auto service = services.Get(i).ToObject();
			writer->StartObject();
			writer->String(MYLiterals::name, service.Get("name").ToString());
			writer->StartArray(MYLiterals::uri);
			auto uris = service.Get("uri").As<Napi::Array>();
			for (size_t j = 0; j < uris.Length(); j++)
			{
				writer->String(uris.Get(j).ToString());
			}
			writer->EndArray();
			writer->EndObject();
		}
		writer->EndArray();
	}

	if (jsObj.HasOwnProperty("account"))
	{
		auto account = jsObj.Get("account").ToObject();
		helpers::encodeObject(writer, MYLiterals::accounts, account, MYLiterals::accounts);
	}
}

Napi::Buffer<uint8_t> encode_token_responseV2(
	Napi::Env env,
	const Napi::Object &jsObj)
{
	// {
	MYBJsonBigRW *writer = new MYBJsonBigRW();
	writer->StartObject();
	// { token: "XXX"
	writer->String(MYLiterals::token, jsObj.Get("token").ToString());

	writer->String(MYLiterals::rtoken, jsObj.Get("rtoken").ToString());

	auto cert = (std::string)jsObj.Get("cert").ToString();
	if (!cert.empty())
		writer->String(MYLiterals::height, cert);

	auto key = (std::string)jsObj.Get("key").ToString();
	if (!key.empty())
		writer->String(MYLiterals::width, key);

	auto created = (bool)jsObj.Get("created").ToBoolean();
	if (created)
		writer->Bool(MYLiterals::accountCreated, created);

	encode_services_and_account(writer, jsObj);

	writer->EndObject();

	return Napi::Buffer<uint8_t>::New(
		env,
		(uint8_t *)writer->pbegin(),
		writer->psize(),
		helpers::freeWriter,
		writer);
}

Napi::Buffer<uint8_t> encode_token_response(
	Napi::Env env,
	const Napi::Object &jsObj,
	std::string tokenKey,
	int literalKey)
{
	// {
	MYBJsonBigRW *writer = new MYBJsonBigRW();
	writer->StartObject();

	// { token: "XXX"
	writer->String(literalKey, jsObj.Get(tokenKey).ToString());
	auto cert = (std::string)jsObj.Get("cert").ToString();
	if (!cert.empty())
		writer->String(MYLiterals::accountCert, cert);

	auto key = (std::string)jsObj.Get("key").ToString();
	if (!key.empty())
		writer->String(MYLiterals::accountPrivateKey, key);

	encode_services_and_account(writer, jsObj);

	writer->EndObject();

	return Napi::Buffer<uint8_t>::New(
		env,
		(uint8_t *)writer->pbegin(),
		writer->psize(),
		helpers::freeWriter,
		writer);
}

Napi::Buffer<uint8_t> encode_rtoken_response(
	Napi::Env env,
	const Napi::Object &response)
{
	return encode_token_response(env, response, "rtoken", MYLiterals::rtoken);
}

Napi::Object decode_rtoken_request(Napi::Env env, const Napi::Buffer<uint8_t> &bjson)
{

	auto data = bjson.Data();
	auto size = bjson.Length();
	auto endPtr = data + size;

	MYBJsonIterator current = MYBJsonIterator(data, endPtr);
	MYBJsonIterator end(endPtr, endPtr);

	auto root = Napi::Object::New(env);

	while (++current != end)
	{
		if (current->key() == MYLiterals::idp)
		{
			root.Set("idp", current->asString());
		}
		else if (current->key() == MYLiterals::sub)
		{
			root.Set("sub", current->asString());
		}
		else if (current->key() == MYLiterals::password)
		{
			root.Set("password", current->asString());
		}
		else if (current->key() == MYLiterals::legacyAccountId)
		{
			root.Set("aid", current->asString());
		}
		else if (current->key() == MYLiterals::pin)
		{
			root.Set("pin", current->asString());
		}
		else if (current->key() == MYLiterals::codeVerifier)
		{
			root.Set("codeVerifier", current->asString());
		}
		else if (current->key() == MYLiterals::codeChallenge)
		{
			root.Set("codeChallenge", current->asString());
		}
	}

	return root;
}

Napi::Object decode_client_subscription_request(Napi::Env env, const Napi::Buffer<uint8_t> &bjson)
{
	auto data = (uint8_t *)bjson.Data();
	auto size = bjson.Length();
	auto endPtr = data + size;

	MYBJsonIterator current = MYBJsonIterator(data, endPtr);
	MYBJsonIterator end(endPtr, endPtr);

	auto root = Napi::Object::New(env);

	while (++current != end)
	{
		if (current->key() == MYLiterals::sub)
		{
			root.Set("sub", current->asString());
		}
		if (current->key() == MYLiterals::email)
		{
			root.Set("email", current->asString());
		}
		else if (current->key() == MYLiterals::flags)
		{
			root.Set("flags", current->asInt32());
		}
		else if (current->key() == MYLiterals::password)
		{
			root.Set("password", current->asString());
		}
		else if (current->key() == MYLiterals::idp)
		{
			root.Set("idp", current->asString());
		}
		if (current->key() == MYLiterals::role)
		{
			root.Set("role", current->asString());
		}
		else if (current->key() == MYLiterals::planId)
		{
			root.Set("planId", current->asString());
		}
	}

	return root;
}

Napi::Buffer<uint8_t> encode_client_subscription_request(Napi::Env env, const Napi::Object &jsObj)
{
	MYBJsonRW *writer = new MYBJsonRW();
	writer->StartObject();

	writer->String(MYLiterals::sub, jsObj.Get("sub").ToString());
	writer->Int32(MYLiterals::flags, jsObj.Get("flags").ToNumber());
	writer->String(MYLiterals::password, jsObj.Get("password").ToString());
	writer->String(MYLiterals::idp, jsObj.Get("idp").ToString());
	writer->String(MYLiterals::role, jsObj.Get("role").ToString());
	writer->String(MYLiterals::planId, jsObj.Get("planId").ToString());

	return Napi::Buffer<uint8_t>::New(
		env,
		(uint8_t *)writer->pbegin(),
		writer->psize(),
		helpers::freeWriter,
		writer);
}
