{"plural": "devices", "k": 4, "mixin": ["all", "syncable"], "flow": "web<>cloud<>postgres<>disk", "fields": {"device_id": {"datatype": "int32"}, "name": {"datatype": "string"}, "device_type": {"datatype": "int32", "required": true}, "nickname": {"datatype": "string"}, "encrypt": {"datatype": "boolean"}, "creation_time": {"datatype": "date"}, "long_id": {"datatype": "binary"}, "support_ticket": {"datatype": "binary"}}, "directives": {"identify": [["account_id", "device_id"]], "find": [["account_id"], ["account_id", "device_type"]]}}