// hello.cc
#include <napi.h>
#include <node.h>
#include "MYTrev.h"
#include "bjson.h"
#include "MYLiterals.h"
#include "base64.h"
#include "Field.h"
#include "encode_functions.h"
#include "decode_functions.h"
#include "helpers.h"
#include "generate_certificates.h"

#define NAPI_VERSION 7

Napi::Buffer<uint8_t> node_encode_account_ping_request(const Napi::CallbackInfo &info)
{
	return encode_account_ping_request(info.Env(), info[0].ToObject());
}

Napi::Buffer<uint8_t> node_encode_account_ping_response(const Napi::CallbackInfo &info)
{
	return encode_account_ping_response(info.Env(), info[0].ToObject());
}

Napi::Object node_decode_resource_ping_request(const Napi::CallbackInfo &info)
{

	return decode_resource_ping_request(info.Env(), info[0].As<Napi::<PERSON>uffer<uint8_t>>());
}

Napi::Buffer<uint8_t> node_encode_resource_ping_response(const Napi::CallbackInfo &info)
{

	return encode_resource_ping_response(info.Env(), info[0].ToObject());
}

Napi::Buffer<uint8_t> node_encode_ping256(const Napi::CallbackInfo &info)
{

	return encode_ping256(info.Env(), info[0].ToObject());
}

Napi::Buffer<uint8_t> node_encode_sync(const Napi::CallbackInfo &info)
{

	return encode_sync(info.Env(), info[0].ToObject());
}
Napi::Object node_decode_account_ping_request(const Napi::CallbackInfo &info)
{

	return decode_account_ping_request(info.Env(), info[0].As<Napi::Buffer<uint8_t>>());
}

Napi::Object node_decode_account_ping_response(const Napi::CallbackInfo &info)
{

	return decode_account_ping_response(info.Env(), info[0].As<Napi::Buffer<uint8_t>>());
}

Napi::Object node_decode_ping256(const Napi::CallbackInfo &info)
{

	return decode_ping256(info.Env(), info[0].As<Napi::Buffer<uint8_t>>());
}

Napi::Object node_decode_sync(const Napi::CallbackInfo &info)
{

	return decode_sync(info.Env(), info[0].As<Napi::Buffer<uint8_t>>());
}

Napi::Object node_decode_rtoken_request(const Napi::CallbackInfo &info)
{

	return decode_rtoken_request(info.Env(), info[0].As<Napi::Buffer<uint8_t>>());
}

Napi::Object node_decode_client_subscription_request(const Napi::CallbackInfo &info)
{

	return decode_client_subscription_request(info.Env(), info[0].As<Napi::Buffer<uint8_t>>());
}

Napi::Buffer<uint8_t> node_encode_client_subscription_request(const Napi::CallbackInfo &info)
{

	return encode_client_subscription_request(info.Env(), info[0].ToObject());
}

Napi::Buffer<uint8_t> node_encode_object(const Napi::CallbackInfo &info)
{

	auto sectionId = info[1].ToNumber();
	return encode_object(info.Env(), info[0].ToObject(), (int)sectionId);
}

Napi::Object node_decode_object(const Napi::CallbackInfo &info)
{

	auto sectionId = info[1].ToNumber();
	return decode_object(info.Env(), info[0].As<Napi::Buffer<uint8_t>>(), (int)sectionId);
}

Napi::Object node_decode_account(const Napi::CallbackInfo &info)
{

	return decode_object(info.Env(), info[0].As<Napi::Buffer<uint8_t>>(), MYLiterals::accounts);
}

Napi::Buffer<uint8_t> node_encode_account(const Napi::CallbackInfo &info)
{

	return encode_object(info.Env(), info[0].ToObject(), MYLiterals::accounts);
}

Napi::Buffer<uint8_t> node_encode_support_ticket(const Napi::CallbackInfo &info)
{
	return encode_support_ticket(info.Env(), info[0].ToObject());
}

Napi::Object node_decode_support_ticket(const Napi::CallbackInfo &info)
{
	return decode_support_ticket(info.Env(), info[0].As<Napi::Buffer<uint8_t>>());
}

Napi::Object node_generate_account_certificate(const Napi::CallbackInfo &info)
{
	return generate_account_certificate(info.Env(), info[0].ToObject());
}

Napi::Buffer<uint8_t> node_encode_rtoken_response(const Napi::CallbackInfo &info)
{
	return encode_rtoken_response(info.Env(), info[0].ToObject());
}

Napi::Buffer<uint8_t> node_encode_token_response(const Napi::CallbackInfo &info)
{
	return encode_token_response(info.Env(), info[0].ToObject());
}
Napi::Buffer<uint8_t> node_encode_token_responseV2(const Napi::CallbackInfo &info)
{
	return encode_token_responseV2(info.Env(), info[0].ToObject());
}

Napi::Object crash(const Napi::CallbackInfo &info)
{

	return decode_object(info.Env(), info[0].As<Napi::Buffer<uint8_t>>(), MYLiterals::accounts);
}

Napi::Object Init(Napi::Env env, Napi::Object exports)
{

	exports.Set(Napi::String::New(env, "encode_ping256"), Napi::Function::New(env, node_encode_ping256));
	exports.Set(Napi::String::New(env, "encode_sync"), Napi::Function::New(env, node_encode_sync));
	exports.Set(Napi::String::New(env, "decode_ping256"), Napi::Function::New(env, node_decode_ping256));
	exports.Set(Napi::String::New(env, "decode_sync"), Napi::Function::New(env, node_decode_sync));
	exports.Set(Napi::String::New(env, "decode_rtoken_request"), Napi::Function::New(env, node_decode_rtoken_request));
	exports.Set(Napi::String::New(env, "decode_client_subscription_request"), Napi::Function::New(env, node_decode_client_subscription_request));
	exports.Set(Napi::String::New(env, "encode_client_subscription_request"), Napi::Function::New(env, node_encode_client_subscription_request));
	exports.Set(Napi::String::New(env, "encode_object"), Napi::Function::New(env, node_encode_object));
	exports.Set(Napi::String::New(env, "decode_object"), Napi::Function::New(env, node_decode_object));
	exports.Set(Napi::String::New(env, "encode_account"), Napi::Function::New(env, node_encode_account));
	exports.Set(Napi::String::New(env, "decode_account"), Napi::Function::New(env, node_decode_account));
	exports.Set(Napi::String::New(env, "crash"), Napi::Function::New(env, crash));
	exports.Set(Napi::String::New(env, "encode_support_ticket"), Napi::Function::New(env, node_encode_support_ticket));
	exports.Set(Napi::String::New(env, "decode_support_ticket"), Napi::Function::New(env, node_decode_support_ticket));

	exports.Set(Napi::String::New(env, "generate_account_certificate"), Napi::Function::New(env, node_generate_account_certificate));

	exports.Set(Napi::String::New(env, "encode_account_ping_request"), Napi::Function::New(env, node_encode_account_ping_request));
	exports.Set(Napi::String::New(env, "encode_account_ping_response"), Napi::Function::New(env, node_encode_account_ping_response));
	exports.Set(Napi::String::New(env, "decode_account_ping_request"), Napi::Function::New(env, node_decode_account_ping_request));
	exports.Set(Napi::String::New(env, "decode_account_ping_response"), Napi::Function::New(env, node_decode_account_ping_response));
	exports.Set(Napi::String::New(env, "decode_resource_ping_request"), Napi::Function::New(env, node_decode_resource_ping_request));
	exports.Set(Napi::String::New(env, "encode_resource_ping_response"), Napi::Function::New(env, node_encode_resource_ping_response));
	exports.Set(Napi::String::New(env, "encode_token_response"), Napi::Function::New(env, node_encode_token_response));
	exports.Set(Napi::String::New(env, "encode_token_responseV2"), Napi::Function::New(env, node_encode_token_responseV2));
	exports.Set(Napi::String::New(env, "encode_rtoken_response"), Napi::Function::New(env, node_encode_rtoken_response));
	exports.Set(Napi::String::New(env, "crash"), Napi::Function::New(env, crash));
	return exports;
}

NODE_API_MODULE(binary_encoder, Init)
