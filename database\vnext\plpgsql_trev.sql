


create or replace function public.mylio_now()
returns int4
as
$$
declare
	result int;
begin
	select extract(epoch from now())::int - 1325376000 into result;
	return result;
end;
$$
language plpgsql immutable;



create or replace function public.buffer_write_int4_le(buffer bytea, pos int, bits int4)
returns bytea
as
$$
begin
	buffer = set_byte(buffer, pos, bits);
	buffer = set_byte(buffer, pos + 1, bits >> 8);
	buffer = set_byte(buffer, pos + 2, bits >> 16);
	buffer = set_byte(buffer, pos + 3, bits >> 24);
	return buffer;
end;
$$
language plpgsql immutable;


create or replace function public.buffer_write_int8_le(buffer bytea, pos int, bits int8)
returns bytea
as
$$
begin
	buffer = set_byte(buffer, pos, bits);
	buffer = set_byte(buffer, pos + 1, bits >> 8);
	buffer = set_byte(buffer, pos + 2, bits >> 16);
	buffer = set_byte(buffer, pos + 3, bits >> 24);
	buffer = set_byte(buffer, pos + 4, bits >> 32);
	buffer = set_byte(buffer, pos + 5, bits >> 40);
	buffer = set_byte(buffer, pos + 6, bits >> 48);
	buffer = set_byte(buffer, pos + 7, bits >> 56);
	return buffer;
end;
$$
language plpgsql immutable;

create or replace function public.buffer_write_int4_be(buffer bytea, pos int, bits int)
returns bytea
as
$$
begin
	buffer = set_byte(buffer, pos, bits >> 24);
	buffer = set_byte(buffer, pos + 1, bits >> 16);
	buffer = set_byte(buffer, pos + 2, bits >> 8);
	buffer = set_byte(buffer, pos + 3, bits);
	return buffer;
end;
$$
language plpgsql immutable;


create or replace function public.buffer_write_int8_be(buffer bytea, pos int, bits int)
returns bytea
as
$$
begin
	buffer = set_byte(buffer, pos, bits >> 56);
	buffer = set_byte(buffer, pos + 1, bits >> 48);
	buffer = set_byte(buffer, pos + 2, bits >> 40);
	buffer = set_byte(buffer, pos + 3, bits >> 32);
	buffer = set_byte(buffer, pos + 4, bits >> 24);
	buffer = set_byte(buffer, pos + 5, bits >> 16);
	buffer = set_byte(buffer, pos + 6, bits >> 8);
	buffer = set_byte(buffer, pos + 7, bits);
	return buffer;
end;
$$
language plpgsql immutable;

create or replace function public.buffer_read_int4_le(buffer bytea, pos int)
returns int
as
$$
begin
	return get_byte(buffer, pos)::int
		+ (get_byte(buffer, pos + 1) << 8)::int
		+ (get_byte(buffer, pos + 2) << 16)::int
		+ (get_byte(buffer, pos + 3) << 24)::int;
end;
$$
language plpgsql immutable;


create or replace function public.buffer_read_int8_le(buffer bytea, pos int)
returns int
as
$$
begin
	if (octet_length(buffer) - pos) < 8 then
		raise exception 'buffer_read_int8_le - length %, pos % out of range', octet_length(buffer), pos;
	else
		return get_byte(buffer, pos)::int
			+ (get_byte(buffer, pos + 1) << 8)::int
			+ (get_byte(buffer, pos + 2) << 16)::int
			+ (get_byte(buffer, pos + 3) << 24)::int
			+ (get_byte(buffer, pos + 4) << 32)::int
			+ (get_byte(buffer, pos + 5) << 40)::int
			+ (get_byte(buffer, pos + 6) << 48)::int
			+ (get_byte(buffer, pos + 7) << 56)::int;
	end if;
end;
$$
language plpgsql immutable;



create or replace function public.buffer_read_int4_be(buffer bytea, pos int)
returns int
as
$$
begin
	return (get_byte(buffer, pos) << 24)::int
		+ (get_byte(buffer, pos + 1) << 16 )::int
		+ (get_byte(buffer, pos + 2) << 8)::int
		+ get_byte(buffer, pos + 3)::int;
end;
$$
language plpgsql immutable;


create or replace function public.buffer_read_int8_be(buffer bytea, pos int)
returns int
as
$$
begin
	return (get_byte(buffer, pos) << 56)::int
		+ (get_byte(buffer, pos + 1) << 48 )::int
		+ (get_byte(buffer, pos + 2) << 40)::int
		+ (get_byte(buffer, pos + 3) << 32 )::int
		+ (get_byte(buffer, pos + 4) << 24)::int
		+ (get_byte(buffer, pos + 5) << 16 )::int
		+ (get_byte(buffer, pos + 6) << 8)::int
		+ get_byte(buffer, pos + 7)::int;
end;
$$
language plpgsql immutable;


create or replace function public.new_trev4(id int4, k int4)
returns bytea
as
$$
declare
	buffer bytea;
	orev int;
begin
	buffer = E'\\x000000000000000000000000000000000000000000000001000000000000000000';
				  
	buffer = buffer_write_int4_le(buffer, 0, id);
	buffer = buffer_write_int4_be(buffer, 24, mylio_now());
	buffer = set_byte(buffer, 32, k);
	return buffer;
end;
$$
language plpgsql immutable;


CREATE OR REPLACE FUNCTION public.new_trev_hash(
	id bytea,
	k integer)
    RETURNS bytea
    LANGUAGE 'plpgsql'

    COST 100
    IMMUTABLE 
    
AS $BODY$
declare
	buffer bytea;
	orev int;
begin
	buffer := id || E'\\x00000001000000000000000000';
	buffer := buffer_write_int4_be(buffer, 24, mylio_now());
	buffer := set_byte(buffer, 32, k);
	return buffer;
end;
$BODY$;


create or replace function public.new_trev8(id int8, k int2)
returns bytea
as
$$
declare
	buffer bytea;
	orev int;
begin
	buffer = E'\\x000000000000000000000000000000000000000000000001000000000000000000';
	buffer = buffer_write_int8_le(buffer, 0, id);
	buffer = buffer_write_int4_be(buffer, 24, mylio_now());
	buffer = set_byte(buffer, 32, k);
	return buffer;
end;
$$
language plpgsql immutable;


create or replace function public.next_trev(trev bytea)
returns bytea
as
$$
declare 
	orev int;
begin
	orev = buffer_read_int4_be(trev, 20);
	trev = buffer_write_int4_be(trev, 20, orev + 1);
	trev = buffer_write_int4_be(trev, 24, mylio_now());
	trev = buffer_write_int4_be(trev, 28, 0);	
	return trev;
end;
$$
language plpgsql immutable;


create or replace function public.trev_has_same_id(client bytea, cloud bytea) 
returns boolean
as
$$
begin
	return substring(client from 1 for 20) = substring(cloud from 1 for 20);
end;
$$
language plpgsql;



create or replace function public.trev_id4(trev bytea) 
returns int4
as
$$
begin
	return buffer_read_int4_le(trev, 0);
end;
$$
language plpgsql immutable;




 