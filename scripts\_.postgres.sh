#!/bin/bash

function open_pg_connection() {
    HOST=$1
    PORT=$2
    USER=$3

    echo "host: $HOST"
    echo "port: $PORT"
    echo "user: $USER"

    if [ -z "$PGPASSWORD" ]
    then
        ask_password
    fi

    psql -q -h $HOST -p $PORT -U $USER -d postgres -v "ON_ERROR_STOP=1" -c "select 'connection succeeded' as status"

    if [[ "$?" = "0" ]]
    then
        echo "connection succeeded"
    else
        echo "connection failed"
        exit 1
    fi
}

function close_pg_connection() {
    HOST=""
    PORT=""
    USER=""
    export PGPASSWORD=''
}

function run_script() {
    if [ -z "$PGPASSWORD" ]
    then
        ask_password
    fi

    file=$1
    args=$2

    echo "starting script $1"
    psql -q -h $HOST -p $PORT -d postgres -U $USER -d postgres -f $file $args
    echo "completed script $1"
}

function ask_password() {
    echo "enter password for this connection"
    stty_orig=`stty -g` # save original terminal setting.
    stty -echo          # turn-off echoing.
    read -s PASSWD      # read the password
    stty $stty_orig     # restore terminal setting.
    export PGPASSWORD=$PASSWD
}

function run_command() {
    if [ -z "$PGPASSWORD" ]; then ask_password; fi;

    echo "$1"
    psql -h $HOST -p $PORT -U $USER -d account0 -c "$1"
}

function upsert_system_property() {
    if [ -z "$PGPASSWORD" ]; then ask_password; fi;

    NAME="$1"
    VALUE="${*:2}"
    VALUE=${VALUE// /""} # remove extra spaces

    echo "Updating system propery: $NAME..."
    psql -q -h $HOST -p $PORT -d account0 -U $USER -v name=\'$NAME\' -v value=\'$VALUE\' -f upsert_system_property.sql
    echo "Update complete!"
}
