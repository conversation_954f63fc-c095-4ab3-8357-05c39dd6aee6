

    drop view if exists a0."LicenseTemplate" cascade;

    create or replace view a0."LicenseTemplate" as
    select
    flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		template_id as "templateId",
		duration,
		uses,
		available_upgrades as "availableUpgrades",
		display_name as "displayName",
		public,
		weight,
		device_limit as "deviceLimit",
		photo_limit as "photoLimit",
		features,
		cloud_storage_limit as "cloudStorageLimit"
    from a0.license_template;
    

drop function if exists a0.license_template_create; 
        create function a0.license_template_create(
            _flags int,
	_modified_time timestamptz,
	_created_time timestamptz,
	_template_id text,
	_duration text,
	_uses int,
	_available_upgrades text,
	_display_name text,
	_public boolean,
	_weight int,
	_device_limit int,
	_photo_limit int,
	_features int,
	_cloud_storage_limit int
        )
        returns a0."LicenseTemplate"
        as $$
        
    declare
        result a0."LicenseTemplate";
        
    begin
        
        


       
        


        
        
        
        
        _modified_time := coalesce(_modified_time, now());
        _created_time := coalesce(_created_time, now());
        insert into a0.license_template (
            flags,
	modified_time,
	created_time,
	template_id,
	duration,
	uses,
	available_upgrades,
	display_name,
	public,
	weight,
	device_limit,
	photo_limit,
	features,
	cloud_storage_limit
        )
        values(
            _flags,
			_modified_time,
			_created_time,
			_template_id,
			_duration,
			_uses,
			_available_upgrades,
			_display_name,
			_public,
			_weight,
			_device_limit,
			_photo_limit,
			_features,
			_cloud_storage_limit
        )
        returning
            flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		template_id as "templateId",
		duration,
		uses,
		available_upgrades as "availableUpgrades",
		display_name as "displayName",
		public,
		weight,
		device_limit as "deviceLimit",
		photo_limit as "photoLimit",
		features,
		cloud_storage_limit as "cloudStorageLimit"
        into result;

        



        

    
        



        return result;
    end;
        $$
        language plpgsql;
        

drop function if exists a0.license_template_update; 
        create function a0.license_template_update(
            _flags int,
	_modified_time timestamptz,
	_created_time timestamptz,
	_template_id text,
	_duration text,
	_uses int,
	_available_upgrades text,
	_display_name text,
	_public boolean,
	_weight int,
	_device_limit int,
	_photo_limit int,
	_features int,
	_cloud_storage_limit int
        )
        returns a0."LicenseTemplate"
        as $$
        
    declare
        result a0."LicenseTemplate";
        
    begin
        
        


       
        


        
        
        _modified_time := now();
        update a0.license_template
        set
            flags = _flags,
			modified_time = _modified_time,
			created_time = _created_time,
			duration = _duration,
			uses = _uses,
			available_upgrades = _available_upgrades,
			display_name = _display_name,
			public = _public,
			weight = _weight,
			device_limit = _device_limit,
			photo_limit = _photo_limit,
			features = _features,
			cloud_storage_limit = _cloud_storage_limit
        where template_id = _template_id
        returning
            flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		template_id as "templateId",
		duration,
		uses,
		available_upgrades as "availableUpgrades",
		display_name as "displayName",
		public,
		weight,
		device_limit as "deviceLimit",
		photo_limit as "photoLimit",
		features,
		cloud_storage_limit as "cloudStorageLimit"
        into result;

        

    
        



        return result;
    end;
        $$
        language plpgsql;
        

drop function if exists a0.license_template_read_by_template_id; 
        create function a0.license_template_read_by_template_id(
            _template_id text
        )
        returns a0."LicenseTemplate"
        as $$
        select
        flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		template_id as "templateId",
		duration,
		uses,
		available_upgrades as "availableUpgrades",
		display_name as "displayName",
		public,
		weight,
		device_limit as "deviceLimit",
		photo_limit as "photoLimit",
		features,
		cloud_storage_limit as "cloudStorageLimit"
        from a0.license_template
        where template_id = _template_id;
        $$
        language sql;
        

drop function if exists a0.license_template_delete_by_template_id; 
        create function a0.license_template_delete_by_template_id(
            _template_id text
        )
        returns void
        as $$
        
    declare
        
        
    begin
        
        


       
        


        

    delete from a0.license_template
    where template_id = _template_id;

    
    
        



        
    end;
        $$
        language plpgsql;
        
