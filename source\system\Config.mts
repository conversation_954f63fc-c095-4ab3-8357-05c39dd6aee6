import {
  SecretsManagerClient,
  GetSecretValueCommand,
} from "@aws-sdk/client-secrets-manager";

import untyped_config = require("config");
import { LogLevel } from "./LogLevel.mjs";
import { Context } from "./Context.mjs";
import _ = require("lodash");
import { Console } from "console";
import { DatabaseError } from "pg";
import { InventoryDestinationFilterSensitiveLog } from "@aws-sdk/client-s3";
import { builtinModules } from "module";

interface IService {
  name?: string;
  uri?: string;
  protocolVersion: number;
}

export interface IMylioConfig {
  fastspring_api_secret: string;
  port?: number;
  email_from?: string;
  email_from_name?: string;
  access_token_secret?: string;
  sendgrid_api_key?: string;
  sqsQueueUrl?: string;
  awsRegion?: string;
  awsAccessKey?: string;
  awsSecretAccessKey?: string;
  sqsEndpoints?: string;
  ambassador_referral_endpoint?: string;
  nodeId?: string;
  postgres_creds?: string;
  postgres_telemetry?: string;
  postgres_datawarehouse?: string;
  website?: string;
  cloud?: string;
  wordpress?: string;
  reset_token_secret?: string;
  noTelemetry?: string;
  refresh_token_lifespan?: string;
  access_token_lifespan?: string;
  subscription_refund_duration?: string;
  facebook?: { clientID?: string; clientSecret?: string };
  instagram?: { clientID?: string; clientSecret?: string };
  shard_count?: number;
  reset_password_link_lifespan?: string;
  amazon?: {
    clientID?: string;
    clientSecret?: string;
  };
  apple?: {
    authority: string;
    clientIds: [string];
    teamId: string;
    privateKeyId: string;
    privateKey: string;
    requestSignAlg: string;
    redirectUrl: string;
    appStorePassword: string;
    appStoreUrl?: string;
    appStoreSecret: string;
    appstoreEnvironment: string;
  };
  backtrace?: {
    username?: string;
    password?: string;
  };
  google?: {
    clientID?: string;
    clientSecret?: string;
  };
  microsoft?: {
    clientID?: string;
    clientSecret?: string;
  };
  email_token_secret?: string;
  connectionStrings?: any;
  pgcreds?: string;
  services?: IService[];
  minimum_supported_build?: number;
  logLevel?: LogLevel;
  flickr_app_key?: string;
  flickr_api_secret?: string;
  s3_key?: string;
  s3_secret?: string;
  s3_bucket?: string;
  s3_support_bucket?: string;
  admins?: string[];
  cloud_v2?: string;
  groove_private_key?: string;
  rendezvous_token_secret?: string;
  r_pgcreds?: string;
  pg_timeout?: number;
  bad_client_timeout?: number;
  stun_token_lifetime?: number;
  stun_secret?: string;
  protocols?: any;
  breaking_builds?: number[];
  bad_accounts: string;
  create_plan_change_date: string;
  mylio_api_key: string;
  magic_link_token_lifespan: string;
  create_link_token_lifespan: string;
  login_link_token_lifespan: string;
  recaptcha_secret: string;
  fastspring: {
    template_key: string;
    webhook_secret: string;
    api_username: string;
    api_password: string;
    api_secret: string;
    email_blacklist: string;
    email_whitelist: string;
  };
  invitation_duration: string;
  resource_throttle: string;
}

export let config = untyped_config as unknown as IMylioConfig;
export async function loadConfig() {
  console.log(`USE_SECRETS_MANAGER: ${process.env.USE_SECRETS_MANAGER}`);
  if (process.env.USE_SECRETS_MANAGER === "1") {
    const client = new SecretsManagerClient({ region: "us-west-2" });
    const command = new GetSecretValueCommand({
      SecretId: "production/mylio/FaceBookSecret",
    });
    const response = await client.send(command);
    config.facebook.clientSecret = JSON.parse(
      response.SecretString
    ).FacebookAppSecret;
  }
}

export async function testSecrets() {
  const client = new SecretsManagerClient({ region: "us-west-2" });
  const command = new GetSecretValueCommand({
    SecretId: "production/mylio/FaceBookSecret",
  });
  const response = await client.send(command);
  return JSON.parse(
    response.SecretString
  ).FacebookAppSecret;
}

export function getServices(context: Context): IService[] {
  return config.services.filter(
    (s) => !s.protocolVersion || s.protocolVersion === context.userAgent?.protocolVersion
  );
}

untyped_config.util.getConfigSources();
