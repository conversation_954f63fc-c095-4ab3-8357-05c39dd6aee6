
DO
$body$
BEGIN
   IF NOT EXISTS (
      SELECT
      FROM   pg_catalog.pg_user
      WHERE  usename = 'datawarehouse') THEN
        CREATE USER datawarehouse NOSUPERUSER PASSWORD 'password';
   END IF;
   IF NOT EXISTS (
      SELECT
      FROM   pg_catalog.pg_user
      WHERE  usename = 'cloud') THEN
        CREATE USER cloud NOSUPERUSER PASSWORD 'password';
   END IF;
END
$body$;

REVOKE ALL ON DATABASE /* r::database */ resource0 FROM PUBLIC;
GRANT CONNECT ON DATABASE /* r::database */ resource0 TO cloud;
GRANT CONNECT ON DATABASE /* r::database */ resource0 TO datawarehouse;

\c /* r::database */ resource0;
REVOKE USAGE ON SCHEMA public FROM PUBLIC;
REVOKE ALL ON ALL TABLES IN SCHEMA public FROM PUBLIC;
REVOKE ALL ON ALL SEQUENCES IN SCHEMA public FROM PUBLIC;
REVOKE ALL ON ALL FUNCTIONS IN SCHEMA public FROM PUBLIC;
GRANT USAGE ON SCHEMA public TO cloud;
GRANT SELECT, INSERT, UPDATE, <PERSON>LETE ON ALL TABLES IN SCHEMA public TO cloud;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO cloud;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO cloud;

/* repeat for each schema */
REVOKE USAGE ON SCHEMA x0 FROM PUBLIC;
REVOKE ALL ON ALL TABLES IN SCHEMA x0 FROM PUBLIC;
REVOKE ALL ON ALL SEQUENCES IN SCHEMA x0 FROM PUBLIC;
REVOKE ALL ON ALL FUNCTIONS IN SCHEMA x0 FROM PUBLIC;

GRANT USAGE ON SCHEMA x0 TO cloud;
grant temp on database /* r::database */ resource0 to cloud;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA x0 TO cloud;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA x0 TO cloud;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA x0 TO cloud;
/* end schemas */

grant execute on function digest(bytea, text) to public;
grant execute on function digest(text, text) to public;

