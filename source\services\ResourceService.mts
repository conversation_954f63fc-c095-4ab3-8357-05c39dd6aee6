import { query, cs } from "../system/Postgres.mjs";
import { Context } from "../system/Context.mjs";
import { config, getServices } from "../system/Config.mjs";
import { Bucket } from "../system/merkle.mjs";

type DeleteStatement = { sql: string; cs: string };

export class ResourceService {
  constructor() {
    // noop
  }

  public async deleteResources(context: Context, aid: number) {
    let connectionStrings: any = {};
    for (let shard of Object.keys(config.connectionStrings)) {
      const connectionString = cs(context, shard);
      connectionStrings[shard] = connectionString;
    }
    let runningQueries = new Array<any>();
    let completed = new Array<any>();
    for (let shard of Object.keys(connectionStrings)) {
      if (!shard.startsWith("x"))
        continue;
      const connectionString = cs(context, shard);
      if (completed.includes(connectionStrings[shard]))
        continue;
      completed.push(connectionString);
      const sql =
        `do $$
      begin
          SET statement_timeout to 600000;
          perform delete_resources(${aid});
          SET statement_timeout to 60000;
      end$$`;
      runningQueries.push(query(
        context,
        sql,
        [],
        connectionString
      ));
      await Promise.all(runningQueries);
    }
  }
}
