import express = require("express");
import { microservice } from "../microservices/account.microservice.mjs";

export function addBacktraceRoutes(router: express.Router) {
  router.get("/backtrace/:filename/exists", (req, res, next) => {
    return microservice.backtraceService
      .crashdumpExists(req.context, req.params.filename)
      .then((exists) => {
        return res.status(200).json({ exists });
      })
      .catch(next);
  });
}
