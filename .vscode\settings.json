// Place your settings in this file to overwrite default and user settings.
{
  "files.exclude": {
    "generator/**/**/*.js": true,
    "generator/**/**/*.map": true,
    "source/**/*.js": true,
    "source/**/*.map": true,
    "**/.git": true,
    "**/.svn": true,
    "**/.hg": true,
    "**/.DS_Store": true
  },
  "typescript.tsdk": "./node_modules/typescript/lib",
  "json.format.enable": true,
  "files.eol": "\n",
  "vsicons.presets.angular": false,
  "files.associations": {
    "chrono": "cpp",
    "regex": "cpp",
    "system_error": "cpp",
    "xfunctional": "cpp",
    "xlocmon": "cpp",
    "algorithm": "cpp",
    "array": "cpp",
    "atomic": "cpp",
    "bitset": "cpp",
    "cctype": "cpp",
    "cinttypes": "cpp",
    "cmath": "cpp",
    "condition_variable": "cpp",
    "cstddef": "cpp",
    "cstdint": "cpp",
    "cstdio": "cpp",
    "cstdlib": "cpp",
    "cstring": "cpp",
    "ctime": "cpp",
    "cwchar": "cpp",
    "deque": "cpp",
    "exception": "cpp",
    "fstream": "cpp",
    "functional": "cpp",
    "initializer_list": "cpp",
    "iomanip": "cpp",
    "ios": "cpp",
    "iosfwd": "cpp",
    "iostream": "cpp",
    "istream": "cpp",
    "iterator": "cpp",
    "limits": "cpp",
    "list": "cpp",
    "locale": "cpp",
    "map": "cpp",
    "memory": "cpp",
    "mutex": "cpp",
    "new": "cpp",
    "ostream": "cpp",
    "ratio": "cpp",
    "set": "cpp",
    "sstream": "cpp",
    "stack": "cpp",
    "stdexcept": "cpp",
    "streambuf": "cpp",
    "string": "cpp",
    "thread": "cpp",
    "tuple": "cpp",
    "type_traits": "cpp",
    "typeinfo": "cpp",
    "unordered_map": "cpp",
    "utility": "cpp",
    "vector": "cpp",
    "xfacet": "cpp",
    "xhash": "cpp",
    "xiosbase": "cpp",
    "xlocale": "cpp",
    "xlocbuf": "cpp",
    "xlocinfo": "cpp",
    "xlocmes": "cpp",
    "xlocnum": "cpp",
    "xloctime": "cpp",
    "xmemory": "cpp",
    "xmemory0": "cpp",
    "xstddef": "cpp",
    "xstring": "cpp",
    "xtr1common": "cpp",
    "xtree": "cpp",
    "xutility": "cpp",
    "string_view": "cpp"
  }
}
