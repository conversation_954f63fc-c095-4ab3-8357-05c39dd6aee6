let emailRegEx =
  /^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

export class Validation {
  public errors: any;
  private _object: any;
  private _skipUndefined: boolean;

  public skipUndefined(value?: boolean) {
    if (value !== void 0) {
      this._skipUndefined = value;
    }
    return this._skipUndefined;
  }

  constructor(object: any, skipUndefined?: boolean, errors?: any) {
    this.errors = errors || {};
    this._skipUndefined = skipUndefined;
    this._object = object;
  }

  public range(field: string, min: number, max: number) {
    let value = this._object[field]();
    if (!(!isNaN(value) && value >= min && value <= max)) {
      this.errors[field] = `must be a valid number between ${min} and ${max}`;
    }
  }

  public addError(field: string, message: string) {
    let current = this.errors[field];
    if (current) {
      this.errors[field] = current + "\n" + message;
    } else {
      this.errors[field] = message;
    }
  }

  public require(field: string, message?: string) {
    let value = this._object[field]();
    if (value !== void 0 || !this._skipUndefined) {
      if (!this._hasValue(value)) {
        this.addError(field, message || "required");
      }
    }
  }

  public email(field: string, message?: string) {
    let value = this._object[field]();
    if (this._canValidate(value) && !emailRegEx.test(value)) {
      this.addError(field, message || "not a valid email");
    }
  }

  public strongPassword(field: string, message?: string) {
    const PASSWORD_ERROR =
      "The password must be at least 8 characters long.\n" +
      "The password must contain at least one lowercase letter.\n" +
      "The password must contain at least one uppercase letter.\n";

    let value: string = this._object[field]();

    if (this._canValidate(value)) {
      let valid = true;

      if (value.length < 8) {
        valid = false;
      }

      if (!/[a-z]/.test(value)) {
        valid = false;
      }

      if (!/[A-Z]/.test(value)) {
        valid = false;
      }

      if (!valid) {
        this.addError(field, PASSWORD_ERROR);
      }
    }
  }

  public match(lhsField: string, rhsField: string, message?: string) {
    let lhsValue = this._object[lhsField]();
    let rhsValue = this._object[rhsField]();
    if (this._canValidate(lhsValue) && this._canValidate(rhsValue)) {
      if (lhsValue !== rhsValue) {
        this.addError(
          lhsField,
          message || `${lhsField} must match ${rhsField}`
        );
      }
    }
  }

  public beTrue(field: string, message?: string) {
    let value = this._object[field]();
    if (this._canValidate(value, true)) {
      if (value !== true) {
        this.addError(field, message || "Must be selected");
      }
    }
  }

  public hasOne(firstField: string, secondField: string, message?: string) {
    let firstVal = this._object[firstField]();
    let secondVal = this._object[secondField]();
    if (!this._hasValue(firstVal) || !this._hasValue(secondVal)) {
      this.addError(
        firstField,
        message ||
          "either " + firstField + " or " + secondField + " is required"
      );
      this.addError(
        secondField,
        message ||
          "either " + firstField + " or " + secondField + " is required"
      );
    }
  }

  public addErrors(...errors: any[]) {
    this.errors = { ...this.errors, ...errors };
  }

  private _hasValue(value: any) {
    return !(value === void 0 || (typeof value === "string" && value === ""));
  }

  private _canValidate(value: any, canValidateUndefined?: boolean) {
    return (
      this._hasValue(value) || (!this._skipUndefined && canValidateUndefined)
    );
  }
}

export default Validation;
