#include "helpers.h"
#include "MYHash.h"
#include <iostream>
#include <napi.h>

namespace helpers
{
	std::string t(int id)
	{
		switch (id)
		{
		case MYLiterals::resources:
			return "resources";
		case MYLiterals::accounts:
			return "accounts";
		case MYLiterals::systemProperties:
			return "systemProperties";
		case MYLiterals::userProperties:
			return "userProperties";
		case MYLiterals::messages:
			return "messages";
		case MYLiterals::devices:
			return "devices";
		case MYLiterals::devicedata:
			return "devicedata";
		case MYLiterals::have:
			return "have";
		case MYLiterals::data:
			return "data";
		case MYLiterals::trev:
			return "t";
		case MYLiterals::aid:
			return "account_id";
		case MYLiterals::idp:
			return "idp";
		case MYLiterals::want:
			return "want";
		}
		assert(true);
		return "";
	}

	int rt(const std::string &id)
	{
		if (id == "resources")
			return MYLiterals::resources;
		if (id == "accounts")
			return MYLiterals::accounts;
		if (id == "devices")
			return MYLiterals::devices;
		if (id == "devicedata")
			return MYLiterals::devicedata;
		if (id == "messages")
			return MYLiterals::messages;
		if (id == "systemProperties")
			return MYLiterals::systemProperties;
		if (id == "have")
			return MYLiterals::have;
		if (id == "data")
			return MYLiterals::data;
		if (id == "want")
			return MYLiterals::want;
		if (id == "userProperties")
			return MYLiterals::userProperties;
		std::cout << id.c_str();
		assert(false);
		return -1;
	}

	std::vector<uint8_t> text2bin(const std::string &text)
	{
		std::vector<uint8_t> vec;
		base64_decode(text.c_str(), text.length(), vec);
		return vec;
	}

	std::string bin2text(const std::string &binary)
	{
		return base64_encode((unsigned char *)binary.c_str(), binary.length());
	}

	std::string bin2text(const char *start, int length)
	{
		std::string buffer(start, length);
		return bin2text(buffer);
	}

	std::string bin2text(const uint8_t *start, int length)
	{
		std::string buffer((char *)start, length);
		return bin2text(buffer);
	}

	void freeWriter(Napi::Env env, uint8_t *data, void *hint)
	{
		delete (MYBJsonBigRW *)hint;
	}

	void freeX(Napi::Env env, uint8_t *data, void *hint)
	{
		delete (x *)hint;
	}

	std::string hash64(const MYHash &hash)
	{
		return hash.toString(true);
	}

	MYTRev trev(const std::string &b64)
	{
		return MYTRev::fromString(b64, true);
	}

	std::string trev64(const MYTRev &t)
	{
		return t.toString(true);
	}

	MYHash hash(const std::string &h64)
	{
		return MYHash::fromHashString(h64, true);
	}

	void writeBinary(MYBJsonBigRW *writer, int key, const std::string &base64)
	{
		writer->BinaryBase64(key, base64.c_str(), base64.length());
	}

	void writeBinary(MYBJsonBigRW *writer, const std::string &base64)
	{
		writer->BinaryBase64(base64.c_str(), base64.length());
	}
}
