
import { makeAutoObservable } from "mobx"
    


    




export interface ISupportTicket {
    flags?: number;
	modifiedTime?: Date;
	createdTime?: Date;
	subject?: string;
	comments?: string;
	sendScreenshot?: boolean;
	includeCatalog?: boolean;
	logLevel?: string;
	logLevelFlags?: number;
	requestId?: string;
	consoleCommandString?: string;
	ttl?: number;
}

export interface IWireSupportTicket {
    flags?: number;
	modifiedTime?: string;
	createdTime?: string;
	subject?: string;
	comments?: string;
	sendScreenshot?: boolean;
	includeCatalog?: boolean;
	logLevel?: string;
	logLevelFlags?: number;
	requestId?: string;
	consoleCommandString?: string;
	ttl?: number;
}

export class SupportTicket implements ISupportTicket {
    flags?: number;
	modifiedTime?: Date;
	createdTime?: Date;
	subject?: string;
	comments?: string;
	sendScreenshot?: boolean;
	includeCatalog?: boolean;
	logLevel?: string;
	logLevelFlags?: number;
	requestId?: string;
	consoleCommandString?: string;
	ttl?: number;
    setFlags(value: number) { this.changed = true; this.flags = value; }
	setModifiedTime(value: Date) { this.changed = true; this.modifiedTime = value; }
	setCreatedTime(value: Date) { this.changed = true; this.createdTime = value; }
	setSubject(value: string) { this.changed = true; this.subject = value; }
	setComments(value: string) { this.changed = true; this.comments = value; }
	setSendScreenshot(value: boolean) { this.changed = true; this.sendScreenshot = value; }
	setIncludeCatalog(value: boolean) { this.changed = true; this.includeCatalog = value; }
	setLogLevel(value: string) { this.changed = true; this.logLevel = value; }
	setLogLevelFlags(value: number) { this.changed = true; this.logLevelFlags = value; }
	setRequestId(value: string) { this.changed = true; this.requestId = value; }
	setConsoleCommandString(value: string) { this.changed = true; this.consoleCommandString = value; }
	setTtl(value: number) { this.changed = true; this.ttl = value; }
    changed = false;
    setChanged() {
        this.changed = true;
    }

    clearChanged() {
        this.changed = false;
    }

    constructor(state? : IWireSupportTicket | ISupportTicket) {
        if (!state)
            throw "An SupportTicket must have a valid start state";
        this.flags = state.flags;;
	if (typeof(state.modifiedTime) === "string")
            this.modifiedTime = new Date(state.modifiedTime);
         else
            this.modifiedTime = state.modifiedTime;
	if (typeof(state.createdTime) === "string")
            this.createdTime = new Date(state.createdTime);
         else
            this.createdTime = state.createdTime;
	this.subject = state.subject;;
	this.comments = state.comments;;
	this.sendScreenshot = state.sendScreenshot;;
	this.includeCatalog = state.includeCatalog;;
	this.logLevel = state.logLevel;;
	this.logLevelFlags = state.logLevelFlags;;
	this.requestId = state.requestId;;
	this.consoleCommandString = state.consoleCommandString;;
	this.ttl = state.ttl;
        makeAutoObservable(this, {
            flags: true,
			modifiedTime: true,
			createdTime: true,
			subject: true,
			comments: true,
			sendScreenshot: true,
			includeCatalog: true,
			logLevel: true,
			logLevelFlags: true,
			requestId: true,
			consoleCommandString: true,
			ttl: true
        });

    }

    state() : ISupportTicket {
        return {
            flags : this.flags,
		modifiedTime : this.modifiedTime,
		createdTime : this.createdTime,
		subject : this.subject,
		comments : this.comments,
		sendScreenshot : this.sendScreenshot,
		includeCatalog : this.includeCatalog,
		logLevel : this.logLevel,
		logLevelFlags : this.logLevelFlags,
		requestId : this.requestId,
		consoleCommandString : this.consoleCommandString,
		ttl : this.ttl
        };
    }

    asWire() : IWireSupportTicket {
        return {
            flags : this.flags,
		modifiedTime : this.modifiedTime ? this.modifiedTime.toISOString() : undefined,
		createdTime : this.createdTime ? this.createdTime.toISOString() : undefined,
		subject : this.subject,
		comments : this.comments,
		sendScreenshot : this.sendScreenshot,
		includeCatalog : this.includeCatalog,
		logLevel : this.logLevel,
		logLevelFlags : this.logLevelFlags,
		requestId : this.requestId,
		consoleCommandString : this.consoleCommandString,
		ttl : this.ttl
        };
    }

    



    


}


