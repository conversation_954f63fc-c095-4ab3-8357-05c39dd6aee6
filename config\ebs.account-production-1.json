{"cloud": "https://account-production-1.mylio.com", "website": "http://accounttest.mylio.com", "connectionStrings": {"a0": "pg://<user>:<password>@migration.c04zdcgu39tj.us-west-2.rds.amazonaws.com:5432/account0?sslmode=disable"}, "services": [{"name": "resource", "protocolVersion": 24, "uri": ["https://resource-production-1.mylio.com"]}, {"name": "resource", "protocolVersion": 23, "uri": ["https://resource-production-0.mylio.com"]}, {"name": "account", "uri": ["https://account-production-1.mylio.com"]}, {"name": "reverse_geocoding", "uri": ["https://api.opencagedata.com/geocode/v1/json?q=%f,%f&pretty=0&no_annotations=1&key=74306074d3a5ef1f7c86ea43062367a6"]}, {"name": "geocoding", "uri": ["https://api.opencagedata.com/geocode/v1/json?q=%s&pretty=0&no_annotations=1&key=74306074d3a5ef1f7c86ea43062367a6"]}, {"name": "telemetry", "uri": ["https://telemetry-test-1.mylio.com"]}, {"name": "cloudSignalChannel", "protocolVersion": 22, "uri": ["signal-prod.mylio.com:443"]}, {"name": "cloudSignalChannel", "protocolVersion": 23, "uri": ["signal-prod-23.mylio.com:443"]}]}