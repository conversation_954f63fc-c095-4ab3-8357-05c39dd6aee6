import express = require("express");
import { microservice as g } from "../microservices/account.microservice.mjs";
import { sanitizeOutput } from "../models/Message.model.mjs";
import { safeMessage, safeNone, secure } from "../system/safe.mjs";

export function addMessageRoutes(router: express.Router) {
  router.put(
    "/accounts/:aid/messages/:mid",
    safeMessage,
    secure,
    (req, res, next) => {
      let context = req.context;
      return g.messageService
        .update(context, context.aid, context.message)
        .then((message) => {
          context.dumpLog();
          return res
            .status(200)
            .json(sanitizeOutput(message, context.hasAdminRights()));
        })
        .catch(next);
    }
  );

  router.delete(
    "/accounts/:aid/messages/:mid",
    safeNone,
    secure,
    (req, res, next) => {
      let context = req.context;
      return g.messageService
        .delete(context, context.aid, context.did)
        .then((message) => {
          context.dumpLog();
          return res.sendStatus(200);
        })
        .catch(next);
    }
  );

  router.get(
    "/accounts/:aid/messages/:mid",
    safeNone,
    secure,
    (req, res, next) => {
      let context = req.context;
      return g.messageService
        .read(context, context.aid, context.did)
        .then((message) => {
          context.dumpLog();
          return res
            .status(200)
            .json(sanitizeOutput(message, context.hasAdminRights()));
        })
        .catch(next);
    }
  );

  router.post(
    "/accounts/:aid/messages",
    safeMessage,
    secure,
    (req, res, next) => {
      let context = req.context;
      return g.messageService
        .create(context, context.aid, context.message)
        .then((message) => {
          context.dumpLog();
          return res
            .status(200)
            .json(sanitizeOutput(message, context.hasAdminRights()));
        })
        .catch(next);
    }
  );

  router.get("/accounts/:aid/messages", safeNone, secure, (req, res, next) => {
    let context = req.context;
    return g.messageService
      .list(context, context.aid)
      .then((messages) => {
        context.dumpLog();
        return res
          .status(200)
          .json(
            messages.map((d) => sanitizeOutput(d, context.hasAdminRights()))
          );
      })
      .catch(next);
  });
}
