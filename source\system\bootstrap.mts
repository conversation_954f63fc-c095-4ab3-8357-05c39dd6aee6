import pg = require("pg");
import rawConfig = require("config");
import { config, getServices } from "../system/Config.mjs";

export function bootstrap() {
  if (process.env.NODE_ENV !== "production") {
    console.log(`$HOSTNAME: ${process.env.HOSTNAME}`);
    console.log("Configuration files applied");
    console.log(
      JSON.stringify(
        (rawConfig.util as any).getConfigSources().map((s) => s.name),
        null,
        2
      )
    );
    console.log(JSON.stringify(config, null, 2));
  }

  if (!hasValidConnectionStrings()) {
    return Promise.reject("Config was not loaded correctly");
  }

  (pg as any).defaults.poolSize = 25;

  // catch uncaught exceptions
  process.on("uncaughtException", function (err) {
    // handle the error safely
    console.error(err);
    console.error(err.stack || {});
  });

  return Promise.resolve();
}

function hasValidConnectionStrings() {
  return (
    config.connectionStrings &&
    (config.connectionStrings.a0 ||
      config.connectionStrings.x0 ||
      config.connectionStrings.t0 ||
      config.connectionStrings.d0)
  );
}
