{"cloud": "https://account-production-2.mylio.com", "website": "http://account-1.mylio.com", "connectionStrings": {"a0": "pg://<user>:<password>@account-production-migration.c04zdcgu39tj.us-west-2.rds.amazonaws.com:5432/account0"}, "services": [{"name": "resource", "protocolVersion": 22, "uri": ["https://resource-test-1.mylio.com"]}, {"name": "resource", "protocolVersion": 23, "uri": ["https://resource-test-1.mylio.com"]}, {"name": "account", "uri": ["https://merkle3account.mylio.com"]}, {"name": "reverse_geocoding", "uri": ["https://api.opencagedata.com/geocode/v1/json?q=%f,%f&pretty=0&no_annotations=1&key=74306074d3a5ef1f7c86ea43062367a6"]}, {"name": "geocoding", "uri": ["https://api.opencagedata.com/geocode/v1/json?q=%s&pretty=0&no_annotations=1&key=74306074d3a5ef1f7c86ea43062367a6"]}, {"name": "telemetry", "uri": ["https://telemetry-test-1.mylio.com"]}, {"name": "cloudSignalChannel", "protocolVersion": 22, "uri": ["signal-prod.mylio.com:443"]}, {"name": "cloudSignalChannel", "protocolVersion": 23, "uri": ["signal-prod-23.mylio.com:443"]}]}