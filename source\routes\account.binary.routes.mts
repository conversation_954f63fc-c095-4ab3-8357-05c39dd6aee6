import express = require("express");
/* tslint:disable */
import { binaryEncoder, sendResponse } from "../system/bjson.cjs";
import { makeError } from "../system/error.mjs";
/* tslint:enable */

export function addBinaryRoutes(router: express.Router) {
  router.post("/accounts/:aid/ping", (req, res, next) => {
    if (req.header("Content-Type") === "application/octet-stream") {
      try {
        // let reqid = new Date().valueOf();
        // console.log(`{"r":${reqid}, "p":"${req.path}", "a":"${req.header("User-Agent")}"}`);
        req.body = binaryEncoder.decode_account_ping_request(req.body);
        // console.log(`{"s":${reqid}, "p":"${req.path}", "a":"${req.header("User-Agent")}"}`);
      } catch (err) {
        next({ message: err });
      }
    }
    next();
  });

  router.post("/accounts/:aid/sync", (req, res, next) => {
    if (req.header("Content-Type") === "application/octet-stream") {
      try {
        if (req.params.aid === "782326")
          return next(makeError(500, "ACCOUNT_DISABLED"));

        let reqid = new Date().valueOf();
        //console.log(`{"r":${reqid}, "p":"${req.path}", "a":"${req.header("User-Agent")}"}`);
        req.body = binaryEncoder.decode_sync(req.body);
        //console.log(`{"s":${reqid}, "p":"${req.path}", "a":"${req.header("User-Agent")}"}`);
      } catch (err) {
        next({ message: err });
      }
    }
    next();
  });

  router.post("/accounts/:aid/ping256", (req, res, next) => {
    if (req.header("Content-Type") === "application/octet-stream") {
      try {
        // let reqid = new Date().valueOf();
        // console.log(`{"r":${reqid}, "p":"${req.path}", "a":"${req.header("User-Agent")}"}`);
        req.body = binaryEncoder.decode_ping256(req.body);
        // console.log(`{"s":${reqid}, "p":"${req.path}", "a":"${req.header("User-Agent")}"}`);
      } catch (err) {
        next(err);
      }
    }
    next();
  });

  router.post("/accounts/x/rtoken", (req, res, next) => {
    if (req.header("Content-Type") === "application/octet-stream") {
      try {
        req.body = binaryEncoder.decode_rtoken_request(req.body);
        // console.log(req.originalUrl + " rtoken worked");
      } catch (err) {
        next(err);
      }
    }
    next();
  });

  router.post("/accounts/:aid/token", (req, res, next) => {
    if (req.header("Content-Type") === "application/octet-stream") {
      try {
        req.body = binaryEncoder.decode_token_request(req.body);
        // console.log(req.originalUrl + " ping256 worked");
      } catch (err) {
        next(err);
      }
    }
    next();
  });

  router.post("/v2/accounts/x/rtoken", (req, res, next) => {
    if (req.header("Content-Type") === "application/octet-stream") {
      try {
        req.body = binaryEncoder.decode_rtoken_request(req.body);
        // console.log(req.originalUrl + " rtoken worked");
      } catch (err) {
        next(err);
      }
    }
    next();
  });

  router.post("/v3/accounts/x/rtoken", (req, res, next) => {
    if (req.header("Content-Type") === "application/octet-stream") {
      try {
        req.body = binaryEncoder.decode_rtoken_request(req.body);
        // console.log(req.originalUrl + " rtoken worked");
      } catch (err) {
        next(err);
      }
    }
    next();
  });

  router.post("/v4/accounts/x/rtoken", (req, res, next) => {
    if (req.header("Content-Type") === "application/octet-stream") {
      try {
        req.body = binaryEncoder.decode_rtoken_request(req.body);
        // console.log(req.originalUrl + " rtoken worked");
      } catch (err) {
        next(err);
      }
    }
    next();
  });

  router.post("/v2/accounts/:aid/token", (req, res, next) => {
    if (req.header("Content-Type") === "application/octet-stream") {
      try {
        req.body = binaryEncoder.decode_token_request(req.body);
        // console.log(req.originalUrl + " ping256 worked");
      } catch (err) {
        next(err);
      }
    }
    next();
  });

  router.post("/accounts", (req, res, next) => {
    if (req.header("Content-Type") === "application/octet-stream") {
      try {
        // console.log("trying to decode subscription");
        req.body = binaryEncoder.decode_client_subscription_request(req.body);
        // console.log("succeeded in decoding subscription");
        // console.log(JSON.stringify(req.body));
      } catch (err) {
        next(err);
      }
    }
    next();
  });

  router.get("/crash", (req, res, next) => {
    binaryEncoder.crash();
  });
}
