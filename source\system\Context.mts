import { Account } from "../models/Account.model.mjs";
import { Device } from "../models/Device.model.mjs";
import { Message } from "../models/Message.model.mjs";
import { config, getServices } from "../system/Config.mjs";
import { Token } from "../models/Token.mjs";
import { LogLevel } from "./LogLevel.mjs";
import { UserAgent } from "./UserAgent.mjs";

const ERROR_BLACKLIST = {
  LOCKED: true,
};

export class LogEntry {
  constructor(
    public level: LogLevel,
    public requestId: number,
    public time: number,
    public event: string,
    public data?: any,
    public error?: any
  ) { }

  public toString() {
    return JSON.stringify(this);
  }
}

export class Context {
  public static nextRequestId = 0;

  public did: number;
  public account: Account;
  public aid: number;
  public device: Device;
  public tokenString: string;
  public audience: string;
  public tx: any;
  public requestId: number;
  public txLevel = 0;
  public idp: string;
  public sub: string;
  public logEntries: LogEntry[] = [];
  public mid: number;
  public message: Message;
  public any: any;
  public query: any;
  public couponId: string;
  public token: Token;
  public task: string;
  public errorEntry: LogEntry;
  public hasTelemetryEntry: boolean;
  public origin: string;
  public userAgent: UserAgent

  constructor() {
    this.requestId = new Date().valueOf();
  }

  public debug(event: string, data?: any) {
    this.log(LogLevel.debug, event, data);
  }

  public info(event: string, data?: any) {
    this.log(LogLevel.info, event, data);
  }

  public warn(event: string, data?: any) {
    this.log(LogLevel.warning, event, data);
  }

  public awsDebug(event: string, data?: any) {
    this.log(LogLevel.AWSDebug, event, data);
  }

  public hasAdminRights() {
    if (!this.token) return false;

    return this.token.hasAdminRights();
  }

  public hasServiceRights() {
    return false;
  }

  public telemetry(event: string, data?: any) {
    data = data || {};

    if (this.aid) data.aid = this.aid;
    if (this.did) data.did = this.did;

    if (this.token) {
      if (this.token.adminAid()) {
        data.adminAid = this.token.adminAid();
      }
      if (this.token.did() && this.did && this.token.did() !== this.did) {
        data.fromDevice = this.token.did();
        data.toDevice = this.did;
      }
      if (this.token.aid() && !data.aid) {
        data.aid = this.token.aid();
      }
    }

    if (!data.did) {
      // default did to '0'
      data.did = 0;
    }

    this.log(LogLevel.telemetry, event, data);
  }

  public error(error: any, event: string, data?: any, isSystemError = false) {
    let stringifyableError = {
      code: error && error.code,
      message: error && error.message,
      type: error && error.message,
      stack: error && error.stack,
      status: error && error.httpStatus,
    };

    let level = isSystemError ? LogLevel.systemError : LogLevel.userError;
    this.log(level, event, data, stringifyableError);
  }

  public log(level: LogLevel, event: string, data?: any, error?: any) {
    if (level === LogLevel.telemetry) {
      this.hasTelemetryEntry = true;
    }

    if (!this.errorEntry && error) {
      if (ERROR_BLACKLIST[error && error.code]) {
        return;
      }
      this.errorEntry = new LogEntry(
        level,
        this.requestId,
        new Date().valueOf(),
        event,
        data,
        error
      );
    } else {
      this.logEntries.push(
        new LogEntry(
          level,
          this.requestId,
          new Date().valueOf(),
          event,
          data,
          error
        )
      );
    }
  }

  public dumpLog() {
    let output = {
      parseLog: this.hasTelemetryEntry,
      requestId: this.requestId,
      errorEntry: this.errorEntry,
      logEntries: this.collectLogEntries(),
    };

    if (
      (output.errorEntry && output.errorEntry.level >= config.logLevel) ||
      output.logEntries.length > 0
    ) {
      console.log(JSON.stringify(output));
    }
  }

  private collectLogEntries() {
    // if there's an error, return full context
    if (this.errorEntry && this.errorEntry.level >= config.logLevel) {
      return this.logEntries;
    }
    // if there is a telemetry log, only return telemetry logs
    if (this.hasTelemetryEntry && config.logLevel <= LogLevel.telemetry) {
      return this.logEntries.filter((e) => e.level === LogLevel.telemetry);
    }
    // otherwise, return logs allowed by current log level
    return this.logEntries.filter((e) => e.level >= config.logLevel);
  }
}
