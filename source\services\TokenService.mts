import { Context } from "../system/Context.mjs";
import { Token, Role, TokenType } from "../models/Token.mjs";
import { Account } from "../models/Account.model.mjs";
import { config, getServices } from "../system/Config.mjs";

export const Audience = {
  Client: "client.mylio.com",
  Website: "website.mylio.com",
};

export class TokenService {
  public shortLivedToken(context: Context, account: Account, lifetime: string) {
    return new Token({
      aud: context.audience,
      idp: account.idp(),
      aid: account.accountId(),
      iss: "*.mylio.com",
      role: 0,
      shard: 0,
      sub: account.sub(),
      tokenType: TokenType.url,
    }).sign(config.email_token_secret, lifetime);
  }

  public rtoken(
    context: Context,
    account: Account,
    adminAccount?: Account
  ): string {
    return new Token({
      aid: account.accountId(),
      aud: context.audience,
      idp: account.idp(),
      iss: "*.mylio.com",
      role: account.role() === "admin" ? Role.admin : Role.user,
      shard: 0,
      sub: account.sub(),
      adminAid: adminAccount && adminAccount.accountId(),
      tokenType: TokenType.refresh,
    }).sign(
      config.access_token_secret +
        account.salt() +
        config.refresh_token_lifespan,
      config.refresh_token_lifespan
    );
  }

  public tryVerifyRToken(
    context: Context,
    rtokenString: string,
    account: Account
  ) {
    return Token.tryVerify(
      rtokenString,
      config.access_token_secret +
        account.salt() +
        config.refresh_token_lifespan
    );
  }

  public atoken(context: Context, account: Account, rtoken: Token) {
    rtoken.tokenType(TokenType.access);
    return rtoken.sign(
      config.access_token_secret,
      config.access_token_lifespan
    );
  }
}
