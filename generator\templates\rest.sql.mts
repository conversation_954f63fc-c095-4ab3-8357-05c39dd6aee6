import { Datatype, Entity } from "../Entity.mjs";
import {
  jsname,
  ScriptHelper,
  pname,
  blockI,
  blockN,
  paramName,
} from "../ScriptHelper.mjs";
import { PgScript, Language } from "../PgScript.mjs";
import _ = require("lodash");

export function template(entity: Entity, h: ScriptHelper) {
  let s = new PgScript(entity, h);
  let canMerkle = entity.fields.find((f) => f.name === "t");

  s.text(`
    drop view if exists a0."${jsname(entity)}" cascade;

    create or replace view a0."${jsname(entity)}" as
    select
    ${h.selectList}
    from a0.${entity.name};
    `);

  s.dfunc(
    "create",
    Language.plpgSql,
    (e, b) => `
        ${b.diskFields
          .filter((f) => f.autoNumber)
          .map((f) => `${pname(f)} := nextval('a0.${e.name}_${f.name}');`)
          .join("\n\t\t")}
        ${h.updatable
          .filter((f) => f.name === "t" && b.dk.datatype === Datatype.int32)
          .map((f) => `_t := public.new_trev4(${pname(b.dk)}, ${e.k});`)}
        ${h.updatable
          .filter((f) => f.name === "t" && b.dk.datatype === Datatype.binary)
          .map((f) => `_t := public.new_trev_hash(${pname(b.dk)}, ${e.k});`)}
        ${h.updatable
          .filter((f) => f.name === "modified_time")
          .map((f) => `_modified_time := coalesce(_modified_time, now());`)}
        ${h.updatable
          .filter((f) => f.name === "created_time")
          .map((f) => `_created_time := coalesce(_created_time, now());`)}
        insert into a0.${e.name} (
            ${b.updatable.map((f) => f.name).join(",\n\t")}
        )
        values(
            ${b.updatable
              .map((f) => blockI(e, `__i_${f.name}`))
              .join(",\n\t\t\t")}
        )
        returning
            ${b.selectList}
        into result;

        ${blockN(e, "before_merkle")}

        ${h.updatable
          .filter((f) => f.name === "t")
          .map((f) => `perform a0.${e.name}_merkle(_account_id);`)}

    `
  );

  s.dfunc(
    "update",
    Language.plpgSql,
    (e, b) => `
        ${h.updatable
          .filter((f) => f.name === "t")
          .map((f) => `_t := public.next_trev(${pname(f)});`)}
        ${h.updatable
          .filter((f) => f.name === "modified_time")
          .map((f) => `_modified_time := now();`)}
        update a0.${e.name}
        set
            ${_.differenceBy(b.updatable, b.pk)
              .map((f) => `${f.name} = ${blockI(e, `__u_${f.name}`)}`)
              .join(",\n\t\t\t")}
        where ${b.pk.map((f) => `${f.name} = ${pname(f)}`).join(" and ")}
        returning
            ${b.selectList}
        into result;

        ${h.updatable
          .filter((f) => f.name === "t")
          .map((f) => `perform a0.${e.name}_merkle(_account_id);`)}

    `
  );

  s.mfunc(
    "read",
    Language.sql,
    entity.keys,
    (e, b, params) =>
      `select
        ${b.selectList}
        from a0.${e.name}
        where ${params
          .map(
            (f) =>
              `${f.name} = ${
                f.datatype === Datatype.binary
                  ? `decode(${paramName(f)}, 'base64')`
                  : pname(f)
              }`
          )
          .join(" and ")};`
  );

  let keys = entity.keys;
  if (h.pk.length > 1) keys.push([h.pk[0]]);
  s.mfunc(
    "delete",
    Language.plpgSql,
    keys,
    (e, b, params) => `

    delete from a0.${e.name}
    where ${params.map((f) => `${f.name} = ${pname(f)}`).join(" and ")};

    ${h.updatable
      .filter((f) => f.name === "t")
      .map((f) => `perform a0.${e.name}_merkle(_account_id);`)}
    `,
    false,
    true
  );

  s.mfunc(
    "find",
    Language.sql,
    entity.finds,
    (e, b, params) =>
      `select
        ${b.selectList}
        from a0.${e.name}
        where ${params
          .map(
            (f) =>
              `${f.name} = ${
                f.datatype === Datatype.binary
                  ? `decode(${paramName(f)}, 'base64')`
                  : pname(f)
              }`
          )
          .join(" and ")};`,
    true
  );

  return s.toString();
}
