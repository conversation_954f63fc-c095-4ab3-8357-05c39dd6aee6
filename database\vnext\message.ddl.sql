

create sequence a0.message_message_id;

create table a0.message(
    flags int NULL,
	modified_time timestamptz NULL,
	created_time timestamptz NULL,
	account_id int NOT NULL,
	deleted boolean NULL,
	t bytea NULL,
	d bytea NULL,
	device_id int NULL,
	message_id int NOT NULL,
	seconds_to_display int NULL,
	displayed int NULL,
	message text NULL,
	link text NULL
);

alter table a0.message
add primary key (account_id,message_id);

 

 create index ix_message_by_account_id on a0.message(account_id);

