create or replace function a0.account_merkle(_account_id int)
        returns void
        as
        $$
            update a0.account_metadata
                set account_merkle = agg.merkle
            from (
                select account_id, digest(string_agg(t, null), 'sha1') as merkle
                from (
                    select account_id, t from a0.account where account_id = _account_id
                    order by t
                ) as x
                group by account_id
            ) as agg
            where a0.account_metadata.account_id = agg.account_id
            and (a0.account_metadata.account_merkle != agg.merkle or a0.account_metadata.account_merkle is null);
        $$
        language sql;
