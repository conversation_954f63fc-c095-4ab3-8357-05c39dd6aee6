
import { query } from "../system/Postgres.mjs";
import { InvitationLogEntry, IInvitationLogEntry} from "../models/InvitationLogEntry.model.mjs";
import { Context } from "../system/Context.mjs";
import { makeError } from "../system/error.mjs";


export class InvitationLogEntryRestDataService {

    



  public query(context: Context, sql: string, params: any[]) {
    return query < IInvitationLogEntry> (context, sql, params)
      .then(results => {
        if (results.length === 1) {
          if (!results[0].accountId && !results[0].email && !results[0].pin) {
      return [];
    }
  }
  return results.filter(o => !!o).map(o => new InvitationLogEntry(o));
});
    }

		public create (context: Context, entity: InvitationLogEntry) {
          


  let params = [
    entity.flags(),
				entity.modifiedTime(),
				entity.createdTime(),
				entity.accountId(),
				entity.email(),
				entity.pin(),
				entity.acceptedDate()
  ];
  return this
    .query(context, "select * from a0.invitation_log_entry_create ($1,$2,$3,$4,$5,$6,$7) ", params)
  .then(r => r[0]);
        }

		public readByAccountIdAndEmailAndPin (context: Context, accountId: number, email: string, pin: string) {
  let params = [
    accountId,
				email,
				pin
  ];
  return this
    .query(context, "select * from a0.invitation_log_entry_read_by_account_id_and_email_and_pin  ($1,$2,$3) ", params).then(r => r[0]); 
                
        }

		public readByAccountId (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.invitation_log_entry_read_by_account_id  ($1) ", params).then(r => r[0]); 
                
        }

		public update (context: Context, entity: InvitationLogEntry) {
          


  let params = [
    entity.flags(),
				entity.modifiedTime(),
				entity.createdTime(),
				entity.accountId(),
				entity.email(),
				entity.pin(),
				entity.acceptedDate()
  ];
  return this
    .query(context, "select * from a0.invitation_log_entry_update ($1,$2,$3,$4,$5,$6,$7) ", params)
  .then(r => r[0]);
        }

		public deleteByAccountIdAndEmailAndPin (context: Context, accountId: number, email: string, pin: string) {
  let params = [
    accountId,
				email,
				pin
  ];
  return this
    .query(context, "select * from a0.invitation_log_entry_delete_by_account_id_and_email_and_pin  ($1,$2,$3) ", params).then(r => r[0]); 
                
        }

		public deleteByAccountId (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.invitation_log_entry_delete_by_account_id  ($1) ", params).then(r => r[0]); 
                
        }

}
