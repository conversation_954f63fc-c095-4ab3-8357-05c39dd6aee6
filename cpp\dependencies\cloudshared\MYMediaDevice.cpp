// #include "MYMedia.h"
#include "MYMediaDevice.h"
#include <cmath>

#ifdef MYLIO_CLIENT
#include "MYNetworkNode.h"
// MYMediaDeviceDynamicField::MYMediaDeviceDynamicField(const MYNetworkNode* device_)
//{
//     memset(this, 0, sizeof(MYMediaDeviceDynamicField));
//     if (device_)
//     {
//         device = device_;
//         mediaColumn = device->getMediaColumn();
//     }
// }
//
// void MYMediaDeviceDynamicField::copyNonMovableMembers(const MYMediaDeviceDynamicField& from)
//{
//     device = from.device;
//     needsHas = from.needsHas;
//     needsHasModified = from.needsHasModified;
//     needsHasModifiedReplicableBits = from.needsHasModifiedReplicableBits;
//     hrev = from.hrev;
//     hrevModified = from.hrevModified;
//     nrev = from.nrev;
//     nrevModified = from.nrevModified;
//     anyNeedsModified = from.anyNeedsModified;
//     mediaColumn = from.mediaColumn;
//
//     inNeedsHas = from.inNeedsHas;
//     inNrev = from.inNrev;
//     inHrev = from.inHrev;
//
//     exNeedsHas = from.exNeedsHas;
//     exNrev = from.exNrev;
//     exHrev = from.exHrev;
// }
//
// #ifndef CHECKFOR_MDF_COPIES
// MYMediaDeviceDynamicField::MYMediaDeviceDynamicField(const MYMediaDeviceDynamicField& that)
//{
//     copyNonMovableMembers(that);
// }
#endif

// MYMediaDeviceDynamicField::MYMediaDeviceDynamicField(MYMediaDeviceDynamicField&& that)
//{
//     copyNonMovableMembers(that);
// }

// MYDeviceId MYMediaDeviceDynamicField::getDeviceId() const
//{
//     return device->getDeviceId();
// }
//
// bool MYMediaDeviceDynamicField::isCloud() const
//{
//     return device->isCloud();
// }
//
// bool MYMediaDeviceDynamicField::isThisDevice() const
//{
//     return device->isThisDevice();
// }
//
//
// bool MYMediaDeviceDynamicField::setNRev(unsigned int newNRev)
//{
//     if (this->nrev == newNRev)
//     {
//         return false;
//     }
//
//     this->nrev = newNRev;
//     this->nrevModified = true;
//     return true;
// }
//
// bool MYMediaDeviceDynamicField::setHRev(unsigned int newHRev)
//{
//     if (this->hrev == newHRev)
//     {
//         return false;
//     }
//
//     this->hrev = newHRev;
//     this->hrevModified = true;
//
//     return true;
// }
//
// bool MYMediaDeviceDynamicField::setNeedsHasBits(int newNeedsHas, bool overwriteWithoutBumpingRevs)
//{
//     if (newNeedsHas == this->needsHas)
//     {
//         return false;
//     }
//
//     // Basic validation
//     assert(MDLBitMask::isValidNeedsHas(newNeedsHas));
//
//     if (!overwriteWithoutBumpingRevs)
//     {
//         if ((this->needsHas & (unsigned int)MDLHasBits::AnyHasMask) != (newNeedsHas & (unsigned int)MDLHasBits::AnyHasMask))
//         {
//             // We have a change in 'has' bits, rev the hrev
//             this->hrev++;
//             this->hrevModified = true;
//             this->needsHasModifiedReplicableBits = true;
//         }
//
//         bool checkReplicableBits = this->isCloud() && this->needsHasModifiedReplicableBits;
//
//         if ((this->needsHas & (unsigned int)MDLNeedsBits::AnyNeedsMask) != (newNeedsHas & (unsigned int)MDLNeedsBits::AnyNeedsMask))
//         {
//             this->anyNeedsModified = true;
//             checkReplicableBits = true;
//         }
//
//         if (checkReplicableBits)
//         {
//             if ((getReplicableBitsFromNeedsHas(this->isCloud(), this->needsHas) & (unsigned int)MDLNeedsBits::AnyNeedsMask) !=
//                 (getReplicableBitsFromNeedsHas(this->isCloud(), newNeedsHas) & (unsigned int)MDLNeedsBits::AnyNeedsMask))
//             {
//                 // We have a change in 'needs' bits, rev the nrev
//                 this->nrev++;
//                 this->nrevModified = true;
//                 this->needsHasModifiedReplicableBits = true;
//             }
//         }
//     }
//
//     this->needsHas = newNeedsHas;
//     this->needsHasModified = true;
//
//     return true;
// }
//
// #ifndef CHECKFOR_MDF_COPIES
// MYMediaDeviceDynamicField& MYMediaDeviceDynamicField::operator =(const MYMediaDeviceDynamicField& that)
//{
//     device = that.device;
//     copyNonMovableMembers(that);
//     return *this;
// }
// #endif
// #endif

//
// bool MYMediaDeviceField::operator<(const MYMediaDeviceField& that) const
//{
//    if (_column < that._column)
//    {
//        return true;
//    }
//
//    return false;
//}
//
// bool MYMediaDeviceField::operator== (const MYMediaDeviceField& that) const
//{
//    if (_column == that._column)
//    {
//        assert(_deviceId == that._deviceId);
//        return true;
//    }
//    return false;
//}
//
// MYMediaDeviceField::MYMediaDeviceField(bool deleted) : _column(0), _isdeleted(deleted)
//{
//}
//
// MYMediaDeviceField::MYMediaDeviceField(const MYMediaDeviceField& that) :
//_deviceId(that._deviceId), _column(that._column), _isdeleted(that._isdeleted),
//_needsHasFieldName(that._needsHasFieldName), _hrevFieldName(that._hrevFieldName), _nrevFieldName(that._nrevFieldName), _columnsAndIndexes(that._columnsAndIndexes), _fromColumns(that._fromColumns), _deviceType(that._deviceType)
//{
//}
//
// MYMediaDeviceField::MYMediaDeviceField(MYMediaDeviceField&& that) :
//_deviceId(std::move(that._deviceId)), _column(that._column), _isdeleted(that._isdeleted),
//_needsHasFieldName(std::move(that._needsHasFieldName)), _hrevFieldName(std::move(that._hrevFieldName)), _nrevFieldName(std::move(that._nrevFieldName)), _columnsAndIndexes(that._columnsAndIndexes), _fromColumns(std::move(that._fromColumns)), _deviceType(that._deviceType)
//{
//}
//
// #ifndef CHECKFOR_MDF_COPIES
// MYMediaDeviceField& MYMediaDeviceField::operator=(const MYMediaDeviceField& that)
//{
//    _deviceId = that._deviceId;
//    _column = that._column;
//    _isdeleted = that._isdeleted;
//    _needsHasFieldName = that._needsHasFieldName;
//    _hrevFieldName = that._hrevFieldName;
//    _nrevFieldName = that._nrevFieldName;
//    _columnsAndIndexes = that._columnsAndIndexes;
//
//    return *this;
//}
//
// const std::string MYMediaColumnNames::getLegacyRandomOrderFieldName() const
//{
//    return "RandomOrder" + MYString::uintToString(this->_column);
//}
//
// const std::string MYMediaColumnNames::getLegacyOtherDeviceNeedsFieldName() const
//{
//    return "OtherDeviceNeeds" + MYString::uintToString(this->_column);
//}
//
// const std::string MYMediaColumnNames::getLegacyOtherDeviceNeedsIndex() const
//{
//    return "MDLOtherDeviceNeeds" + MYString::uintToString(this->_column) + "Index";
//}
//
// const std::string MYMediaColumnNames::getLegacyRandomOrderIndexName() const
//{
//    return "RandomOrder" + MYString::uintToString(this->_column) + "Index";
//}

// Don't call this for the cloud - the cloud bits are all supposed to be replicated since
// the cloud can't perform the FDL etc. calculations itself (if it one day does, then this should change)
// MDLNeeds getReplicableNeeds(const MDLNeeds& needs)
//{
//    switch (needs)
//    {
//    case MDLNeeds::InheritedNo:
//    case MDLNeeds::InheritedYes:
//        return MDLNeeds::InheritedNo;
//
//    case MDLNeeds::ForcedNo:
//    case MDLNeeds::InheritedYesOverridingForcedNo:
//        return MDLNeeds::ForcedNo;
//
//    case MDLNeeds::ForcedYes:
//        return MDLNeeds::ForcedYes;
//
//    default:
//        assert(false);
//        return MDLNeeds::InheritedNo;
//    }
//}
//
// int getReplicableBitsFromNeedsHas(bool isCloud, int needsHas)
//{
//    if (needsHas == 0)
//    {
//        return 0;
//    }
//
//    if (isCloud)
//    {
//        // Has bits replicate except for HasAllOriginals.
//        needsHas &= ~(3 << (int)MDLHasBits::HasAllOriginals);
//
//        // For the cloud, we need to set/unset the 'Needs' bit based on when we set/unset the 'Has' bit.
//        MDLNeeds needs = MDLBitMask::GetNeedsBitsForValue(MDLNeedsBits::NeedsOriginals, needsHas);
//        if ((MDLBitMask::GetHasBitsForValue(MDLHasBits::HasRAW, needsHas) == MDLHas::DoesntHave) &&
//            (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasNonRAW, needsHas) == MDLHas::DoesntHave) &&
//            (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasVideo, needsHas) == MDLHas::DoesntHave) &&
//            (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasDisplayImage, needsHas) == MDLHas::DoesntHave) &&
//            (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasXMP, needsHas) == MDLHas::DoesntHave))
//        {
//            // If the cloud doesn't yet 'have' it, don't mark it as 'needs' on the cloud. This
//            // means we don't need to replicate the needs bit to the cloud until it has it.
//            needs = getReplicableNeeds(needs);
//        }
//        MDLBitMask::SetNeedsBitsForValue(needsHas, MDLNeedsBits::NeedsOriginals, needs);
//
//
//        needs = MDLBitMask::GetNeedsBitsForValue(MDLNeedsBits::NeedsPreview, needsHas);
//        if ( (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasPreview, needsHas) == MDLHas::DoesntHave) &&
//             (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasXMP, needsHas) == MDLHas::DoesntHave) )
//        {
//            // If the cloud doesn't yet 'have' it, don't mark it as 'needs' on the cloud. This
//            // means we don't need to replicate the needs bit to the cloud until it has it.
//            needs = getReplicableNeeds(needs);
//        }
//        MDLBitMask::SetNeedsBitsForValue(needsHas, MDLNeedsBits::NeedsPreview, needs);
//
//
//        needs = MDLBitMask::GetNeedsBitsForValue(MDLNeedsBits::NeedsThumbnail, needsHas);
//        if ((MDLBitMask::GetHasBitsForValue(MDLHasBits::HasThumbnail, needsHas) == MDLHas::DoesntHave) &&
//            (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasXMP, needsHas) == MDLHas::DoesntHave))
//        {
//            // If the cloud doesn't yet 'have' it, don't mark it as 'needs' on the cloud. This
//            // means we don't need to replicate the needs bit to the cloud until it has it.
//            needs = getReplicableNeeds(needs);
//        }
//        MDLBitMask::SetNeedsBitsForValue(needsHas, MDLNeedsBits::NeedsThumbnail, needs);
//
//        return needsHas;
//    }
//
//    // Has bits replicate except for HasAllOriginals.
//    needsHas &= ~(3 << (int)MDLHasBits::HasAllOriginals);
//
//    // Not all needs bit replicate.
//    MDLNeeds needs = MDLBitMask::GetNeedsBitsForValue(MDLNeedsBits::NeedsOriginals, needsHas);
//    needs = getReplicableNeeds(needs);
//    MDLBitMask::SetNeedsBitsForValue(needsHas, MDLNeedsBits::NeedsOriginals, needs);
//
//    needs = MDLBitMask::GetNeedsBitsForValue(MDLNeedsBits::NeedsPreview, needsHas);
//    needs = getReplicableNeeds(needs);
//    MDLBitMask::SetNeedsBitsForValue(needsHas, MDLNeedsBits::NeedsPreview, needs);
//
//    needs = MDLBitMask::GetNeedsBitsForValue(MDLNeedsBits::NeedsThumbnail, needsHas);
//    needs = getReplicableNeeds(needs);
//    MDLBitMask::SetNeedsBitsForValue(needsHas, MDLNeedsBits::NeedsThumbnail, needs);
//
//    return needsHas;
//}
//
// MDLHas MDLBitMask::GetHasBitsForValue(MDLHasBits bit, int val)
//{
//    return (MDLHas)(3 & (val >> (int)bit));
//}
//
// MDLNeeds MDLBitMask::GetNeedsBitsForValue(MDLNeedsBits bit, int val)
//{
//    return (MDLNeeds)(7 & (val >> (int)bit));
//}
//
// void MDLBitMask::SetHasBitsForValue(int& current, MDLHasBits bit, MDLHas val)
//{
//    int zero = 3 << (int)bit;
//    int zeromask = ~zero;
//    int valuemask = (((int)val & 3) << (int)bit);
//    int newVal = (current & zeromask) | valuemask;
//    current = newVal;
//}
//
// void MDLBitMask::SetNeedsBitsForValue(int& current, MDLNeedsBits bit, MDLNeeds val)
//{
//    int zero = 7 << (int)bit;
//    int zeromask = ~zero;
//    int valuemask = (((int)val & 7) << (int)bit);
//    int newVal = (current & zeromask) | valuemask;
//    current = newVal;
//}
//
// bool MDLBitMask::isValidMDLHas(MDLHas has)
//{
//    if (has == MDLHas::Has)
//        return true;
//    if (has == MDLHas::CanGenerate)
//        return true;
//    if (has == MDLHas::DoesntHave)
//        return true;
//
//    return false;
//}
//
// bool MDLBitMask::isValidMDLNeeds(MDLNeeds needs)
//{
//    if (needs == MDLNeeds::ForcedNo)
//        return true;
//    if (needs == MDLNeeds::ForcedYes)
//        return true;
//    if (needs == MDLNeeds::InheritedNo)
//        return true;
//    if (needs == MDLNeeds::InheritedYes)
//        return true;
//    //if (needs == MDLNeeds::NotPossible)
//    //    return true;
//    if (needs == MDLNeeds::InheritedYesOverridingForcedNo)
//        return true;
//    if (needs == (MDLNeeds)6) // Old MDLNeeds::ProtectedYes
//        return true;
//
//    return false;
//}
//
// bool MDLBitMask::isValidNeedsHas(int needsHas)
//{
//    if (!isValidMDLHas(GetHasBitsForValue(MDLHasBits::HasAllOriginals, needsHas)))
//        return false;
//    if (!isValidMDLHas(GetHasBitsForValue(MDLHasBits::HasDisplayImage, needsHas)))
//        return false;
//    if (!isValidMDLHas(GetHasBitsForValue(MDLHasBits::HasNonRAW, needsHas)))
//        return false;
//    if (!isValidMDLHas(GetHasBitsForValue(MDLHasBits::HasPreview, needsHas)))
//        return false;
//    if (!isValidMDLHas(GetHasBitsForValue(MDLHasBits::HasRAW, needsHas)))
//        return false;
//    if (!isValidMDLHas(GetHasBitsForValue(MDLHasBits::HasThumbnail, needsHas)))
//        return false;
//    if (!isValidMDLHas(GetHasBitsForValue(MDLHasBits::HasVideo, needsHas)))
//        return false;
//    if (!isValidMDLHas(GetHasBitsForValue(MDLHasBits::HasXMP, needsHas)))
//        return false;
//
//    if (!isValidMDLNeeds(GetNeedsBitsForValue(MDLNeedsBits::NeedsOriginals, needsHas)))
//        return false;
//    if (!isValidMDLNeeds(GetNeedsBitsForValue(MDLNeedsBits::NeedsPreview, needsHas)))
//        return false;
//    if (!isValidMDLNeeds(GetNeedsBitsForValue(MDLNeedsBits::NeedsThumbnail, needsHas)))
//        return false;
//
//    needsHas &= ~((int)MDLHasBits::AnyHasMask | (int)MDLHasBits::LocalOnlyHasBits);
//    needsHas &= ~(int)MDLNeedsBits::AnyNeedsMask;
//
//    if (needsHas != 0)
//        return false;
//
//    return true;
//}

void appendString(std::string &str, const char *s, bool &appended, std::string prefix /*= ""*/)
{
    if (appended)
    {
        str += ", ";
    }
    else
    {
        str += prefix;
        appended = true;
    }

    str += s;
}

std::string NeedsCalculationDebugToString(int flags)
{
    std::string flagString = "";
    bool appended = false;
    // if (flags & (int)NeedsCalculationReason::NotPossible) appendString(flagString, "Not possible", appended);
    if (flags & (int)NeedsCalculationReason::Heuristic)
        appendString(flagString, "Heuristic", appended);
    if (flags & (int)NeedsCalculationReason::FDL)
        appendString(flagString, "FDL", appended);
    if (flags & (int)NeedsCalculationReason::Travel)
        appendString(flagString, "Travel", appended);
    if (flags & (int)NeedsCalculationReason::Shuttle)
        appendString(flagString, "Shuttle", appended);
    // if (flags & (int)NeedsCalculationReason::NotPossibleButElsewhere) appendString(flagString, "Not Possible But Elsewhere", appended);
    if (flags & (int)0x20)
        appendString(flagString, "Legacy: Considered cached", appended);
    if (flags & (int)NeedsCalculationReason::Forced)
        appendString(flagString, "Forced", appended);
    if (flags & (int)NeedsCalculationReason::TravelCounted)
        appendString(flagString, "Counted as travel protected", appended);
    if (flags & (int)NeedsCalculationReason::CanGenerate)
        appendString(flagString, "Can generate", appended);
    if (flags & (int)NeedsCalculationReason::UpgradeProtection)
        appendString(flagString, "Upgraded protection", appended);
    if (flags & (int)NeedsCalculationReason::GeneratePreviewsOrThumbs)
        appendString(flagString, "Generate Previews/Thumbs", appended);
    if (flags & (int)NeedsCalculationReason::HasLastOriginal)
        appendString(flagString, "Last original in the world", appended);
    if (flags & (int)NeedsCalculationReason::NotLocallyProtected)
        appendString(flagString, "Not locally protected", appended);
    if (flags & (int)NeedsCalculationReason::NotRemotelyProtected)
        appendString(flagString, "Not remotely protected", appended);
    if (flags & (int)NeedsCalculationReason::AllKnownOriginals)
        appendString(flagString, "Has all known originals", appended);
    if (flags & (int)NeedsCalculationReason::ReplicableNonRaw)
        appendString(flagString, "Has replicable NonRaw", appended);
    if (flags & (int)NeedsCalculationReason::ReplicableRaw)
        appendString(flagString, "Has replicable RAW", appended);
    if (flags & (int)NeedsCalculationReason::ReplicableXMP)
        appendString(flagString, "Has replicable XMP", appended);
    if (flags & (int)NeedsCalculationReason::ReplicableVideo)
        appendString(flagString, "Has replicable Video", appended);
    if (flags & (int)NeedsCalculationReason::ReplicableDisplayImage)
        appendString(flagString, "Has replicable DisplayImage", appended);
    if (flags & (int)NeedsCalculationReason::DataHashMismatch)
        appendString(flagString, "Local Basis != Original DataHash", appended);
    if (flags & (int)NeedsCalculationReason::NoLocalDataHash)
        appendString(flagString, "No local data hash", appended);
    if (flags & (int)NeedsCalculationReason::PreservedUserSetting)
        appendString(flagString, "Preserved MDL ForcedYes/No", appended);
    if (flags & (int)NeedsCalculationReason::ODNotHasAndWant)
        appendString(flagString, "OtherDevice-Does have, but want", appended);
    if (flags & (int)NeedsCalculationReason::ODSkipDueToLocalCanGenerate)
        appendString(flagString, "OtherDevice-Skipping because it can locally generate", appended);
    if (flags & (int)NeedsCalculationReason::ODAddedBytes)
        appendString(flagString, "OtherDevice-Has bytes needed", appended);
    if (flags & (int)NeedsCalculationReason::ODDeletedResource)
        appendString(flagString, "OtherDevice-Folder was deleted", appended);
    if (flags & (int)NeedsCalculationReason::CropZoomFactorMisMatch)
        appendString(flagString, "CropZoomFactor mismatch", appended);
    if (flags & (int)NeedsCalculationReason::IsDraft)
        appendString(flagString, "Draft thumbnail", appended);
    if (flags & (int)NeedsCalculationReason::VisualEditHashMisMatch)
        appendString(flagString, "VisualEditHash mismatch", appended);
    if (flags & (int)NeedsCalculationReason::NoMediaFileOfThisType)
        appendString(flagString, "No media file of this type", appended);
    return flagString;
}
//
// std::string NeedsToString(MDLNeeds needs)
//{
//    switch (needs)
//    {
//    case MDLNeeds::ForcedNo:
//        return "ForcedNo";
//    case MDLNeeds::ForcedYes:
//        return "ForcedYes";
//    case MDLNeeds::InheritedNo:
//        return "CalcNo";
//    case MDLNeeds::InheritedYes:
//        return "CalcYes";
//    case MDLNeeds::InheritedYesOverridingForcedNo:
//        return "CalcYesOverridingForcedNo";
//    //case MDLNeeds::NotPossible:
//    //    return "NotPossible";
//    default:
//        return MYString::uintToString((int)needs); // I really hate XCODE warnings on this.
//    }
//    return MYString::uintToString((int)needs);
//}
//
// std::string HasToString(std::string type, MDLHas has)
//{
//    switch (has)
//    {
//    case MDLHas::Has:
//        return type;
//    case MDLHas::CanGenerate:
//        return "CanGen:" + type;
//    case MDLHas::DoesntHave:
//        return "-";
//    case MDLHas::HasOrCanGenerate:
//        return "HasOrCanGenerate:" + type;
//    }
//    return "";
//}
//
// std::string NeedsHasToString(int flags)
//{
//    std::string flagString = "Needs: ";
//
//    flagString += "T=" + NeedsToString(MDLBitMask::GetNeedsBitsForValue(MDLNeedsBits::NeedsThumbnail, flags));
//    flagString += ", P=" + NeedsToString(MDLBitMask::GetNeedsBitsForValue(MDLNeedsBits::NeedsPreview, flags));
//    flagString += ", O=" + NeedsToString(MDLBitMask::GetNeedsBitsForValue(MDLNeedsBits::NeedsOriginals, flags));
//
//    flagString += ", Has: ";
//    bool appended = false;
//    if (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasThumbnail, flags) == MDLHas::Has)
//        appendString(flagString, "T", appended, "");
//    if (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasPreview, flags) == MDLHas::Has)
//        appendString(flagString, "P", appended, "");
//    if (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasNonRAW, flags) == MDLHas::Has)
//        appendString(flagString, "N", appended, "");
//    if (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasRAW, flags) == MDLHas::Has)
//        appendString(flagString, "R", appended, "");
//    if (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasXMP, flags) == MDLHas::Has)
//        appendString(flagString, "X", appended, "");
//    if (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasVideo, flags) == MDLHas::Has)
//        appendString(flagString, "V", appended, "");
//    if (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasDisplayImage, flags) == MDLHas::Has)
//        appendString(flagString, "D", appended, "");
//
//    if (appended)
//        flagString += " ";
//    else
//        flagString += "Nothing";
//
//    if (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasAllOriginals, flags) == MDLHas::Has)
//        flagString += "(AllKnownOriginals) ";
//
//    appended = false;
//    if (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasThumbnail, flags) == MDLHas::CanGenerate)
//        appendString(flagString, "T", appended, "CanGenerate:");
//    if (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasPreview, flags) == MDLHas::CanGenerate)
//        appendString(flagString, "P", appended, "CanGenerate:");
//    if (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasNonRAW, flags) == MDLHas::CanGenerate)
//        appendString(flagString, "N", appended, "CanGenerate:");
//    if (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasRAW, flags) == MDLHas::CanGenerate)
//        appendString(flagString, "R", appended, "CanGenerate:");
//    if (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasXMP, flags) == MDLHas::CanGenerate)
//        appendString(flagString, "X", appended, "CanGenerate:");
//    if (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasVideo, flags) == MDLHas::CanGenerate)
//        appendString(flagString, "V", appended, "CanGenerate:");
//    if (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasDisplayImage, flags) == MDLHas::CanGenerate)
//        appendString(flagString, "D", appended, "CanGenerate:");
//
//    return flagString;
//}
//
// std::string NeedsHasSpecificToString(int flags, MDLNeedsBits needs)
//{
//    std::string flagString = "";
//
//    flagString += "Needs:" + NeedsToString(MDLBitMask::GetNeedsBitsForValue(needs, flags));
//
//    flagString += ", Has:";
//
//    std::string tmpString = "";
//    bool appended = false;
//    switch (needs)
//    {
//    case MDLNeedsBits::NeedsThumbnail:
//        if (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasThumbnail, flags) == MDLHas::DoesntHave)
//            flagString += "No";
//        else
//            flagString += HasToString("T", MDLBitMask::GetHasBitsForValue(MDLHasBits::HasThumbnail, flags));
//        break;
//
//    case MDLNeedsBits::NeedsPreview:
//        if (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasPreview, flags) == MDLHas::DoesntHave)
//            flagString += "No";
//        else
//            flagString += HasToString("P", MDLBitMask::GetHasBitsForValue(MDLHasBits::HasThumbnail, flags));
//        break;
//
//    case MDLNeedsBits::NeedsOriginals:
//        if ((MDLBitMask::GetHasBitsForValue(MDLHasBits::HasNonRAW, flags) == MDLHas::DoesntHave) &&
//            (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasRAW, flags) == MDLHas::DoesntHave) &&
//            (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasXMP, flags) == MDLHas::DoesntHave) &&
//            (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasVideo, flags) == MDLHas::DoesntHave) &&
//            (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasDisplayImage, flags) == MDLHas::DoesntHave))
//        {
//            flagString += "Nothing";
//        }
//        else
//        {
//            if (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasNonRAW, flags) != MDLHas::DoesntHave)
//                appendString(tmpString, (HasToString("N", MDLBitMask::GetHasBitsForValue(MDLHasBits::HasNonRAW, flags))).c_str(), appended);
//
//            if (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasRAW, flags) != MDLHas::DoesntHave)
//                appendString(tmpString, (HasToString("R", MDLBitMask::GetHasBitsForValue(MDLHasBits::HasRAW, flags))).c_str(), appended);
//
//            if (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasXMP, flags) != MDLHas::DoesntHave)
//                appendString(tmpString, (HasToString("X", MDLBitMask::GetHasBitsForValue(MDLHasBits::HasXMP, flags))).c_str(), appended);
//
//            if (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasVideo, flags) != MDLHas::DoesntHave)
//                appendString(tmpString, (HasToString("V", MDLBitMask::GetHasBitsForValue(MDLHasBits::HasVideo, flags))).c_str(), appended);
//
//            if (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasDisplayImage, flags) != MDLHas::DoesntHave)
//                appendString(tmpString, (HasToString("D", MDLBitMask::GetHasBitsForValue(MDLHasBits::HasDisplayImage, flags))).c_str(), appended);
//
//            if (MDLBitMask::GetHasBitsForValue(MDLHasBits::HasAllOriginals, flags) != MDLHas::DoesntHave)
//                appendString(tmpString, "(AllKnownOriginals)", appended);
//        }
//
//    case MDLNeedsBits::AnyNeedsMask:
//        static_assert(true, "xcode doesn't understand bitmasks");
//    }
//
//    if (!tmpString.empty())
//    {
//        flagString += tmpString;
//    }
//
//    return flagString;
//}

std::string SearchAttributesToString(MDLSearchAttributes flags)
{
    std::string flagString = "";
    bool appended = false;
    if (flags & MDLSearchAttributes::GeneratePreviewsOrThumbs)
        appendString(flagString, "Generate Previews/Thumbs", appended);
    if (flags & MDLSearchAttributes::DetectFaces)
        appendString(flagString, "DetectFaces", appended);

    return flagString;
}

// Used by VS debugger visualization - do not use directly
// MDLNeedsHasBitsDbg _dummy;
//
// std::string MYMediaColumnNames::NeedsHasPrefix = "NeedsHas";
// std::string MYMediaColumnNames::HRevPrefix = "HRev";
// std::string MYMediaColumnNames::NRevPrefix = "NRev";
//
