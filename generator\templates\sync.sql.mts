import { Entity, Datatype } from "../Entity.mjs";
import {
  jsname,
  pname,
  sqldt,
  ppdt,
  <PERSON><PERSON>t<PERSON>elper,
  blockI,
  blockN,
} from "../ScriptHelper.mjs";

export function template(e: Entity, h: ScriptHelper) {
  let dataFields = h.diskFields;
  let where = `account_id = _account_id and substring(t from 1 for 20) = substring(_t from 1 for 20)`;
  let __data = `__${e.name}_sync_data`;
  return `

create or replace function a0.${e.name}_presync()
returns void
as
$$
    create temp table if not exists ${__data} (
        ${dataFields
      .map((f) => {
        return `${pname(f)} ${sqldt(f)}`;
      })
      .join(",\n\t")}
    );
    truncate table ${__data};

    create temp table if not exists __have (
        _account_id int,
        _t bytea
    );
    truncate table __have;
    
    ${blockN(e, `${e.name}_presync`)}
$$
language sql;


select * from a0.${e.name}_presync();



create or replace function a0.${e.name}_merkle(__account_id int)
returns void
as
$$
    update a0.account_metadata
        set ${e.name}_merkle = agg.merkle
    from (
        select account_id, digest(string_agg(t, null), 'sha1') as merkle
        from (
            select account_id, t from a0.${e.name
    } where account_id = __account_id
            order by t
        ) as x
        group by account_id
    ) as agg
    where a0.account_metadata.account_id = agg.account_id
    and (a0.account_metadata.${e.name
    }_merkle != agg.merkle or a0.account_metadata.${e.name}_merkle is null);
$$
language sql;

drop function if exists a0.${e.name}_sync_have(int, text[]) cascade;
create or replace function a0.${e.name
    }_sync_have(__account_id int, _have_text text[])
returns table (
    op int,
    ${h.pgParams.map((f) => `"${jsname(f)}" ${ppdt(f)}`).join(",\n")}
)
as
$$
    insert into __have
    select __account_id, decode(t, 'base64') as _t from unnest(_have_text) as t;
    create index if not exists __have_index on __have(_account_id, substring(_t from 1 for 20), _t);

    select 
        3 as op,
        ${h.pgParams
      .map((f) => {
        if (f.name === "t") return "encode(_t, 'base64') as t";
        return "null";
      })
      .join(",\n\t")}
    from __have
    left join a0.${e.name} cloud 
    on cloud.account_id = __have._account_id
    and substring(t from 1 for 20) = substring(_t from 1 for 20)
    where (t is null or t < _t)

    union all

    select
        2 as op,
        ${h.pgParams.map((f) => blockI(e, `__s_${f.name}`)).join(",\n\t\t")}
    from a0.${e.name} cloud
    left join __have on _account_id = account_id and substring(t from 1 for 20) = substring(_t from 1 for 20)
    where account_id = __account_id
    and (_t < t or _t is null);    

$$
language sql;

drop function if exists a0.${e.name}_sync_data(int, json) cascade;
create or replace function a0.${e.name
    }_sync_data(__account_id int, _data_json json)
returns table (
    op int,
    ${h.pgParams.map((f) => `"${jsname(f)}" ${ppdt(f)}`).join(",\n")}
)
as
$$

${blockN(e, `${e.name}_before_data_sync`)}

insert into ${__data} 
select
    ${dataFields
      .map((f) => {
        switch (f.datatype) {
          case Datatype.int64:
            return `"${jsname(f)}"::int8 as ${pname(f)}`; ``
          case Datatype.binary:
            return `decode("${jsname(f)}", 'base64') as ${pname(f)}`;
          default:
            if (f.name === "account_id") return "__account_id";
            if (f.name === "modified_time") return "now()";
            if (jsname(f) !== f.name) return `"${jsname(f)}" as ${pname(f)}`;
            else return `${f.name} as ${pname(f)}`;
        }
      })
      .join(",\n\t")}
    from json_to_recordset(_data_json) 
    as t(
        ${h.pgParams
      .filter((f) => f.name != "modified_time")
      .map((f) => `"${jsname(f)}" ${ppdt(f)}`)
      .join(",\n\t")}
    );

create index if not exists ${__data}_index on ${__data}(_account_id, substring(_t from 1 for 20), _t);


update a0.${e.name} cloud
    set ${dataFields
      .filter((f) => !e.keys[0].find((f2) => f2.name === f.name))
      .map((f) => `${f.name} = ${pname(f)}`)
      .join(",\n\t")}
from ${__data} client
where ${where}
and _t > t;

update ${__data}
    set _t = least(E'\\\\x000000000000000000000000000000000000000000000000000000000000000000', decode((a0.${e.name
    }_create(
        ${h.pgParams
      .map((f) => {
        if (f.name === "t") return "null";
        if (f.datatype === Datatype.binary)
          return `encode(${pname(f)}, 'base64')`;
        if (f.datatype === Datatype.int64)
          return `${pname(f)}::text`;
        if (f.name === "account_id") return "__account_id";
        return pname(f);
      })
      .join(",\n\t\t")})).t, 'base64'))
where ${pname(h.dk)} is null;

insert into a0.${e.name} (
    ${h.diskFields.map((f) => f.name).join(",\n\t")}
)
select
    ${h.diskFields.map((f) => pname(f)).join(",\n\t")}
from ${__data}
where not exists (select * from a0.${e.name} where ${where});

select from a0.${e.name}_merkle(__account_id);

${blockN(e, `${e.name}_after_data_sync`)}

select
    2 as op,
    ${h.pgParams.map((f) => blockI(e, `__s_${f.name}`)).join(",\n\t\t")}
from a0.${e.name} cloud
join ${__data} on ${where}
where (_t < t or ${pname(h.dk)} is null);

$$
language sql;

drop function if exists a0.${e.name}_sync(int, json, boolean, text[], boolean) cascade;
create or replace function a0.${e.name
    }_sync(__account_id int, _data_json json, _do_data boolean, _have text[], _do_have boolean)
returns table (
    op int,
    ${h.pgParams.map((f) => `"${jsname(f)}" ${ppdt(f)}`).join(",\n")}
)
as
$$
begin
    perform * 
    from a0.account_metadata 
    where account_id = __account_id for update;

    perform a0.${e.name}_presync();
    if (_do_data) then
        return query select * from a0.${e.name
    }_sync_data(__account_id, _data_json);
    end if;
    if (_do_have) then
        return query select * from a0.${e.name}_sync_have(__account_id, _have);
    end if;
end;
$$
language plpgsql;
`;
}
