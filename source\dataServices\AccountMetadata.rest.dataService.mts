
import { query } from "../system/Postgres.mjs";
import { AccountMetadata, IAccountMetadata} from "../models/AccountMetadata.model.mjs";
import { Context } from "../system/Context.mjs";
import { makeError } from "../system/error.mjs";


export class AccountMetadataRestDataService {

    
/* b::rest_public_members */

/* end */


  public query(context: Context, sql: string, params: any[]) {
    return query < IAccountMetadata> (context, sql, params)
      .then(results => {
        if (results.length === 1) {
          if (!results[0].accountId) {
      return [];
    }
  }
  return results.filter(o => !!o).map(o => new AccountMetadata(o));
});
    }

		public create (context: Context, entity: AccountMetadata) {
          


  let params = [
    entity.flags(),
				entity.modifiedTime(),
				entity.createdTime(),
				entity.accountId(),
				entity.accountMerkle(),
				entity.deviceMerkle(),
				entity.messageMerkle(),
				entity.systemPropertyMerkle(),
				entity.userPropertyMerkle(),
				entity.deviceDataMerkle(),
				entity.nextDeviceId(),
				entity.nextMessageId(),
				entity.bootstrapDeviceId(),
				entity.nextSystemPropertyId()
  ];
  return this
    .query(context, "select * from a0.account_metadata_create ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14) ", params)
  .then(r => r[0]);
        }

		public readByAccountId (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.account_metadata_read_by_account_id  ($1) ", params).then(r => r[0]); 
                
        }

		public update (context: Context, entity: AccountMetadata) {
          


  let params = [
    entity.flags(),
				entity.modifiedTime(),
				entity.createdTime(),
				entity.accountId(),
				entity.accountMerkle(),
				entity.deviceMerkle(),
				entity.messageMerkle(),
				entity.systemPropertyMerkle(),
				entity.userPropertyMerkle(),
				entity.deviceDataMerkle(),
				entity.nextDeviceId(),
				entity.nextMessageId(),
				entity.bootstrapDeviceId(),
				entity.nextSystemPropertyId()
  ];
  return this
    .query(context, "select * from a0.account_metadata_update ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14) ", params)
  .then(r => r[0]);
        }

		public deleteByAccountId (context: Context, accountId: number) {
  let params = [
    accountId
  ];
  return this
    .query(context, "select * from a0.account_metadata_delete_by_account_id  ($1) ", params).then(r => r[0]); 
                
        }

}
