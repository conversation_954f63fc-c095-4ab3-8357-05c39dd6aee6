

import Validation from "../system/Validation.mjs";
import { IModel } from "./IModel.mjs";

/* b::imports */

/* end */


/* b::enums */
export enum FeatureBits0 { }

export enum AccountFlags {
    canTrialPlus = 0b1,
    canTrialCreate = 0b1000,
    paid = 0b10000,
    canBuyPlus = 0b100000,
    canBuyCreate = 0b1000000,
    canBuyCloudStorage = 0b10000000,
}
/* end */


export interface IAccount {
    flags?: number;
	modifiedTime?: Date;
	createdTime?: Date;
	accountId?: number;
	t?: string;
	cipher?: string;
	clientCipher?: string;
	clientCipherVersion?: number;
	minBuild?: number;
	peerToPeerKey?: string;
	clientPeerToPeerKey?: string;
	clientPeerToPeerKeyVersion?: number;
	rsaPrivateKey?: string;
	x509Cert?: string;
	tfa?: boolean;
	idp?: string;
	sub?: string;
	email?: string;
	passwordHash?: string;
	passwordHashVersion?: number;
	salt?: string;
	passwordSetTime?: Date;
	planId?: string;
	role?: string;
	deviceLimit?: number;
	photoLimit?: number;
	cloudStorageLimit?: number;
	features?: number;
	nextPlanDate?: Date;
	availableUpgrades?: string;
	licenseTemplateId?: string;
	licenseDisplayName?: string;
	licenseManager?: string;
	licenseFlags?: number;
	availableUpgradesFeatures?: number;
	licenseId?: string;
	password?: string;
	affiliateId?: string;
}


export class Account 
implements IModel {
    private _state: IAccount;

    
/* b::model_public_members */
// public members

    public isMylio() {
        return this.idp() === "mylio";
    }

    /* b::model_public_members */
    // public members

    canTrialPlus(value?: boolean) {
        if (value !== void 0) {
            if (value) this.flags(this.flags() | AccountFlags.canTrialPlus);
            else this.flags(this.flags() & ~AccountFlags.canTrialPlus);
        }
        return !!(this.flags() & AccountFlags.canTrialPlus);
    }

    canTrialCreate(value?: boolean) {
        if (value !== void 0) {
            if (value) this.flags(this.flags() | AccountFlags.canTrialCreate);
            else this.flags(this.flags() & ~AccountFlags.canTrialCreate);
        }
        return !!(this.flags() & AccountFlags.canTrialCreate);
    }

    canBuyPlus(value?: boolean) {
        if (value !== void 0) {
            if (value) this.flags(this.flags() | AccountFlags.canBuyPlus);
            else this.flags(this.flags() & ~AccountFlags.canBuyPlus);
        }
        return !!(this.flags() & AccountFlags.canBuyPlus);
    }

    canBuyCreate(value?: boolean) {
        if (value !== void 0) {
            if (value) this.flags(this.flags() | AccountFlags.canBuyCreate);
            else this.flags(this.flags() & ~AccountFlags.canBuyCreate);
        }
        return !!(this.flags() & AccountFlags.canBuyCreate);
    }

    canBuyCloudStorage(value?: boolean) {
        if (value !== void 0) {
            if (value) this.flags(this.flags() | AccountFlags.canBuyCloudStorage);
            else this.flags(this.flags() & ~AccountFlags.canBuyCloudStorage);
        }
        return !!(this.flags() & AccountFlags.canBuyCloudStorage);
    }

    paid(value?: boolean) {
        if (value !== void 0) {
            if (value) this.flags(this.flags() | AccountFlags.paid);
            else this.flags(this.flags() & ~AccountFlags.paid);
        }
        return !!(this.flags() & AccountFlags.paid);
    }
/* end */

    
    changed = false;

    constructor(state: IAccount) {
        this.state(state);
    }

    isUndefined() {
        return this._state !== void 0;
    }

    async validate(skipUndefined?: boolean) {
        let v = new Validation(this, skipUndefined);
        
/* b::validate */
// validation
        if (this.accountId()) {
            v.require("t");
        }
        v.require("idp");
        v.require("sub");
        // v.email("email");
/* end */

        return v;
    }

    rtt() {
        return "Account"; 
    }

    state (value?: IAccount) {
        if (value !== undefined) { 
            this._state = value;
            
            


        }
        return this._state;
    }

    flags(value?: number) {
                if (value !== void 0) {
                    if (this.state().flags !== value) {
                        this.state().flags = value;
                        this.changed = true;
                    }
                }
                return this.state().flags;
            };

		modifiedTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().modifiedTime !== value) {
                        this.state().modifiedTime = value;
                        this.changed = true;
                    }
                }
                return this.state().modifiedTime;
            };

		createdTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().createdTime !== value) {
                        this.state().createdTime = value;
                        this.changed = true;
                    }
                }
                return this.state().createdTime;
            };

		accountId(value?: number) {
                if (value !== void 0) {
                    if (this.state().accountId !== value) {
                        this.state().accountId = value;
                        this.changed = true;
                    }
                }
                return this.state().accountId;
            };

		t(value?: string) {
                if (value !== void 0) {
                    if (this.state().t !== value) {
                        this.state().t = value;
                        this.changed = true;
                    }
                }
                return this.state().t;
            };

		cipher(value?: string) {
                if (value !== void 0) {
                    if (this.state().cipher !== value) {
                        this.state().cipher = value;
                        this.changed = true;
                    }
                }
                return this.state().cipher;
            };

		clientCipher(value?: string) {
                if (value !== void 0) {
                    if (this.state().clientCipher !== value) {
                        this.state().clientCipher = value;
                        this.changed = true;
                    }
                }
                return this.state().clientCipher;
            };

		clientCipherVersion(value?: number) {
                if (value !== void 0) {
                    if (this.state().clientCipherVersion !== value) {
                        this.state().clientCipherVersion = value;
                        this.changed = true;
                    }
                }
                return this.state().clientCipherVersion;
            };

		minBuild(value?: number) {
                if (value !== void 0) {
                    if (this.state().minBuild !== value) {
                        this.state().minBuild = value;
                        this.changed = true;
                    }
                }
                return this.state().minBuild;
            };

		peerToPeerKey(value?: string) {
                if (value !== void 0) {
                    if (this.state().peerToPeerKey !== value) {
                        this.state().peerToPeerKey = value;
                        this.changed = true;
                    }
                }
                return this.state().peerToPeerKey;
            };

		clientPeerToPeerKey(value?: string) {
                if (value !== void 0) {
                    if (this.state().clientPeerToPeerKey !== value) {
                        this.state().clientPeerToPeerKey = value;
                        this.changed = true;
                    }
                }
                return this.state().clientPeerToPeerKey;
            };

		clientPeerToPeerKeyVersion(value?: number) {
                if (value !== void 0) {
                    if (this.state().clientPeerToPeerKeyVersion !== value) {
                        this.state().clientPeerToPeerKeyVersion = value;
                        this.changed = true;
                    }
                }
                return this.state().clientPeerToPeerKeyVersion;
            };

		rsaPrivateKey(value?: string) {
                if (value !== void 0) {
                    if (this.state().rsaPrivateKey !== value) {
                        this.state().rsaPrivateKey = value;
                        this.changed = true;
                    }
                }
                return this.state().rsaPrivateKey;
            };

		x509Cert(value?: string) {
                if (value !== void 0) {
                    if (this.state().x509Cert !== value) {
                        this.state().x509Cert = value;
                        this.changed = true;
                    }
                }
                return this.state().x509Cert;
            };

		tfa(value?: boolean) {
                if (value !== void 0) {
                    if (this.state().tfa !== value) {
                        this.state().tfa = value;
                        this.changed = true;
                    }
                }
                return this.state().tfa;
            };

		idp(value?: string) {
                if (value !== void 0) {
                    if (this.state().idp !== value) {
                        this.state().idp = value;
                        this.changed = true;
                    }
                }
                return this.state().idp;
            };

		sub(value?: string) {
                if (value !== void 0) {
                    if (this.state().sub !== value) {
                        this.state().sub = value;
                        this.changed = true;
                    }
                }
                return this.state().sub;
            };

		email(value?: string) {
                if (value !== void 0) {
                    if (this.state().email !== value) {
                        this.state().email = value;
                        this.changed = true;
                    }
                }
                return this.state().email;
            };

		passwordHash(value?: string) {
                if (value !== void 0) {
                    if (this.state().passwordHash !== value) {
                        this.state().passwordHash = value;
                        this.changed = true;
                    }
                }
                return this.state().passwordHash;
            };

		passwordHashVersion(value?: number) {
                if (value !== void 0) {
                    if (this.state().passwordHashVersion !== value) {
                        this.state().passwordHashVersion = value;
                        this.changed = true;
                    }
                }
                return this.state().passwordHashVersion;
            };

		salt(value?: string) {
                if (value !== void 0) {
                    if (this.state().salt !== value) {
                        this.state().salt = value;
                        this.changed = true;
                    }
                }
                return this.state().salt;
            };

		passwordSetTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().passwordSetTime !== value) {
                        this.state().passwordSetTime = value;
                        this.changed = true;
                    }
                }
                return this.state().passwordSetTime;
            };

		planId(value?: string) {
                if (value !== void 0) {
                    if (this.state().planId !== value) {
                        this.state().planId = value;
                        this.changed = true;
                    }
                }
                return this.state().planId;
            };

		role(value?: string) {
                if (value !== void 0) {
                    if (this.state().role !== value) {
                        this.state().role = value;
                        this.changed = true;
                    }
                }
                return this.state().role;
            };

		deviceLimit(value?: number) {
                if (value !== void 0) {
                    if (this.state().deviceLimit !== value) {
                        this.state().deviceLimit = value;
                        this.changed = true;
                    }
                }
                return this.state().deviceLimit;
            };

		photoLimit(value?: number) {
                if (value !== void 0) {
                    if (this.state().photoLimit !== value) {
                        this.state().photoLimit = value;
                        this.changed = true;
                    }
                }
                return this.state().photoLimit;
            };

		cloudStorageLimit(value?: number) {
                if (value !== void 0) {
                    if (this.state().cloudStorageLimit !== value) {
                        this.state().cloudStorageLimit = value;
                        this.changed = true;
                    }
                }
                return this.state().cloudStorageLimit;
            };

		features(value?: number) {
                if (value !== void 0) {
                    if (this.state().features !== value) {
                        this.state().features = value;
                        this.changed = true;
                    }
                }
                return this.state().features;
            };

		nextPlanDate(value?: Date) {
                if (value !== void 0) {
                    if (this.state().nextPlanDate !== value) {
                        this.state().nextPlanDate = value;
                        this.changed = true;
                    }
                }
                return this.state().nextPlanDate;
            };

		availableUpgrades(value?: string) {
                if (value !== void 0) {
                    if (this.state().availableUpgrades !== value) {
                        this.state().availableUpgrades = value;
                        this.changed = true;
                    }
                }
                return this.state().availableUpgrades;
            };

		licenseTemplateId(value?: string) {
                if (value !== void 0) {
                    if (this.state().licenseTemplateId !== value) {
                        this.state().licenseTemplateId = value;
                        this.changed = true;
                    }
                }
                return this.state().licenseTemplateId;
            };

		licenseDisplayName(value?: string) {
                if (value !== void 0) {
                    if (this.state().licenseDisplayName !== value) {
                        this.state().licenseDisplayName = value;
                        this.changed = true;
                    }
                }
                return this.state().licenseDisplayName;
            };

		licenseManager(value?: string) {
                if (value !== void 0) {
                    if (this.state().licenseManager !== value) {
                        this.state().licenseManager = value;
                        this.changed = true;
                    }
                }
                return this.state().licenseManager;
            };

		licenseFlags(value?: number) {
                if (value !== void 0) {
                    if (this.state().licenseFlags !== value) {
                        this.state().licenseFlags = value;
                        this.changed = true;
                    }
                }
                return this.state().licenseFlags;
            };

		availableUpgradesFeatures(value?: number) {
                if (value !== void 0) {
                    if (this.state().availableUpgradesFeatures !== value) {
                        this.state().availableUpgradesFeatures = value;
                        this.changed = true;
                    }
                }
                return this.state().availableUpgradesFeatures;
            };

		licenseId(value?: string) {
                if (value !== void 0) {
                    if (this.state().licenseId !== value) {
                        this.state().licenseId = value;
                        this.changed = true;
                    }
                }
                return this.state().licenseId;
            };

		password(value?: string) {
                if (value !== void 0) {
                    if (this.state().password !== value) {
                        this.state().password = value;
                        this.changed = true;
                    }
                }
                return this.state().password;
            };

		affiliateId(value?: string) {
                if (value !== void 0) {
                    if (this.state().affiliateId !== value) {
                        this.state().affiliateId = value;
                        this.changed = true;
                    }
                }
                return this.state().affiliateId;
            };

    differs(original: Account) {
        return (
            (this.flags() !== void 0 && this.flags() !== original.flags())
		 || (this.modifiedTime() !== void 0 && this.modifiedTime() !== original.modifiedTime())
		 || (this.createdTime() !== void 0 && this.createdTime() !== original.createdTime())
		 || (this.accountId() !== void 0 && this.accountId() !== original.accountId())
		 || (this.t() !== void 0 && this.t() !== original.t())
		 || (this.cipher() !== void 0 && this.cipher() !== original.cipher())
		 || (this.clientCipher() !== void 0 && this.clientCipher() !== original.clientCipher())
		 || (this.clientCipherVersion() !== void 0 && this.clientCipherVersion() !== original.clientCipherVersion())
		 || (this.minBuild() !== void 0 && this.minBuild() !== original.minBuild())
		 || (this.peerToPeerKey() !== void 0 && this.peerToPeerKey() !== original.peerToPeerKey())
		 || (this.clientPeerToPeerKey() !== void 0 && this.clientPeerToPeerKey() !== original.clientPeerToPeerKey())
		 || (this.clientPeerToPeerKeyVersion() !== void 0 && this.clientPeerToPeerKeyVersion() !== original.clientPeerToPeerKeyVersion())
		 || (this.rsaPrivateKey() !== void 0 && this.rsaPrivateKey() !== original.rsaPrivateKey())
		 || (this.x509Cert() !== void 0 && this.x509Cert() !== original.x509Cert())
		 || (this.tfa() !== void 0 && this.tfa() !== original.tfa())
		 || (this.idp() !== void 0 && this.idp() !== original.idp())
		 || (this.sub() !== void 0 && this.sub() !== original.sub())
		 || (this.email() !== void 0 && this.email() !== original.email())
		 || (this.passwordHash() !== void 0 && this.passwordHash() !== original.passwordHash())
		 || (this.passwordHashVersion() !== void 0 && this.passwordHashVersion() !== original.passwordHashVersion())
		 || (this.salt() !== void 0 && this.salt() !== original.salt())
		 || (this.passwordSetTime() !== void 0 && this.passwordSetTime() !== original.passwordSetTime())
		 || (this.planId() !== void 0 && this.planId() !== original.planId())
		 || (this.role() !== void 0 && this.role() !== original.role())
		 || (this.deviceLimit() !== void 0 && this.deviceLimit() !== original.deviceLimit())
		 || (this.photoLimit() !== void 0 && this.photoLimit() !== original.photoLimit())
		 || (this.cloudStorageLimit() !== void 0 && this.cloudStorageLimit() !== original.cloudStorageLimit())
		 || (this.features() !== void 0 && this.features() !== original.features())
		 || (this.nextPlanDate() !== void 0 && this.nextPlanDate() !== original.nextPlanDate())
		 || (this.availableUpgrades() !== void 0 && this.availableUpgrades() !== original.availableUpgrades())
		 || (this.licenseTemplateId() !== void 0 && this.licenseTemplateId() !== original.licenseTemplateId())
		 || (this.licenseDisplayName() !== void 0 && this.licenseDisplayName() !== original.licenseDisplayName())
		 || (this.licenseManager() !== void 0 && this.licenseManager() !== original.licenseManager())
		 || (this.licenseFlags() !== void 0 && this.licenseFlags() !== original.licenseFlags())
		 || (this.availableUpgradesFeatures() !== void 0 && this.availableUpgradesFeatures() !== original.availableUpgradesFeatures())
		 || (this.licenseId() !== void 0 && this.licenseId() !== original.licenseId())
		 || (this.password() !== void 0 && this.password() !== original.password())
		 || (this.affiliateId() !== void 0 && this.affiliateId() !== original.affiliateId())
        );
    }





/* b::private_members */

/* end */

}



export function sanitizeInput(source: Account, amdin: boolean, mode: string) : IAccount;
export function sanitizeInput(source: IAccount, admin: boolean, mode: string) : IAccount;
export function sanitizeInput(source: Account | IAccount, admin = false, mode="default"): IAccount {
    let s: IAccount;
    if (source instanceof Account)
        s = source.state();
    else
        s = source;        
    let t = {} as IAccount;
    
    
        
            switch(mode) {
                
                
                
            }
            t.flags = s.flags;
		t.accountId = s.accountId;
		t.t = s.t;
		t.cipher = s.cipher;
		t.clientCipher = s.clientCipher;
		t.clientCipherVersion = s.clientCipherVersion;
		t.minBuild = s.minBuild;
		t.peerToPeerKey = s.peerToPeerKey;
		t.clientPeerToPeerKey = s.clientPeerToPeerKey;
		t.clientPeerToPeerKeyVersion = s.clientPeerToPeerKeyVersion;
		t.rsaPrivateKey = s.rsaPrivateKey;
		t.x509Cert = s.x509Cert;
		t.tfa = s.tfa;
		t.idp = s.idp;
		t.sub = s.sub;
		t.email = s.email;
		t.password = s.password;
		t.affiliateId = s.affiliateId;
        
    return t;
}

export function sanitizeOutput(source: Account, amdin: boolean) : IAccount;
export function sanitizeOutput(source: IAccount, admin: boolean) : IAccount;
export function sanitizeOutput(source: Account | IAccount, admin = false): IAccount {
    let s: IAccount;
    if (source instanceof Account)
        s = source.state();
    else
        s = source;        
    let t = {} as IAccount;
    
    
    
    t.flags = s.flags;	
t.modifiedTime = s.modifiedTime;	
t.createdTime = s.createdTime;	
t.accountId = s.accountId;	
t.t = s.t;	
t.cipher = s.cipher;	
t.clientCipher = s.clientCipher;	
t.clientCipherVersion = s.clientCipherVersion;	
t.minBuild = s.minBuild;	
t.peerToPeerKey = s.peerToPeerKey;	
t.clientPeerToPeerKey = s.clientPeerToPeerKey;	
t.clientPeerToPeerKeyVersion = s.clientPeerToPeerKeyVersion;	
t.rsaPrivateKey = s.rsaPrivateKey;	
t.x509Cert = s.x509Cert;	
t.tfa = s.tfa;	
t.idp = s.idp;	
t.sub = s.sub;	
t.email = s.email;	
t.planId = s.planId;	
t.role = s.role;	
t.deviceLimit = s.deviceLimit;	
t.photoLimit = s.photoLimit;	
t.cloudStorageLimit = s.cloudStorageLimit;	
t.features = s.features;	
t.nextPlanDate = s.nextPlanDate;	
t.availableUpgrades = s.availableUpgrades;	
t.licenseTemplateId = s.licenseTemplateId;	
t.licenseDisplayName = s.licenseDisplayName;	
t.licenseManager = s.licenseManager;	
t.licenseFlags = s.licenseFlags;	
t.availableUpgradesFeatures = s.availableUpgradesFeatures;	
t.licenseId = s.licenseId;	
t.affiliateId = s.affiliateId;
    return t;
}

export function mergeState(dbVersion: IAccount, newVersion: IAccount) {
    let targetState: IAccount = {};
    targetState.flags = newVersion.flags === undefined ? dbVersion.flags : newVersion.flags;
	targetState.modifiedTime = newVersion.modifiedTime === undefined ? dbVersion.modifiedTime : newVersion.modifiedTime;
	targetState.createdTime = newVersion.createdTime === undefined ? dbVersion.createdTime : newVersion.createdTime;
	targetState.accountId = newVersion.accountId === undefined ? dbVersion.accountId : newVersion.accountId;
	targetState.t = newVersion.t === undefined ? dbVersion.t : newVersion.t;
	targetState.cipher = newVersion.cipher === undefined ? dbVersion.cipher : newVersion.cipher;
	targetState.clientCipher = newVersion.clientCipher === undefined ? dbVersion.clientCipher : newVersion.clientCipher;
	targetState.clientCipherVersion = newVersion.clientCipherVersion === undefined ? dbVersion.clientCipherVersion : newVersion.clientCipherVersion;
	targetState.minBuild = newVersion.minBuild === undefined ? dbVersion.minBuild : newVersion.minBuild;
	targetState.peerToPeerKey = newVersion.peerToPeerKey === undefined ? dbVersion.peerToPeerKey : newVersion.peerToPeerKey;
	targetState.clientPeerToPeerKey = newVersion.clientPeerToPeerKey === undefined ? dbVersion.clientPeerToPeerKey : newVersion.clientPeerToPeerKey;
	targetState.clientPeerToPeerKeyVersion = newVersion.clientPeerToPeerKeyVersion === undefined ? dbVersion.clientPeerToPeerKeyVersion : newVersion.clientPeerToPeerKeyVersion;
	targetState.rsaPrivateKey = newVersion.rsaPrivateKey === undefined ? dbVersion.rsaPrivateKey : newVersion.rsaPrivateKey;
	targetState.x509Cert = newVersion.x509Cert === undefined ? dbVersion.x509Cert : newVersion.x509Cert;
	targetState.tfa = newVersion.tfa === undefined ? dbVersion.tfa : newVersion.tfa;
	targetState.idp = newVersion.idp === undefined ? dbVersion.idp : newVersion.idp;
	targetState.sub = newVersion.sub === undefined ? dbVersion.sub : newVersion.sub;
	targetState.email = newVersion.email === undefined ? dbVersion.email : newVersion.email;
	targetState.passwordHash = newVersion.passwordHash === undefined ? dbVersion.passwordHash : newVersion.passwordHash;
	targetState.passwordHashVersion = newVersion.passwordHashVersion === undefined ? dbVersion.passwordHashVersion : newVersion.passwordHashVersion;
	targetState.salt = newVersion.salt === undefined ? dbVersion.salt : newVersion.salt;
	targetState.passwordSetTime = newVersion.passwordSetTime === undefined ? dbVersion.passwordSetTime : newVersion.passwordSetTime;
	targetState.planId = newVersion.planId === undefined ? dbVersion.planId : newVersion.planId;
	targetState.role = newVersion.role === undefined ? dbVersion.role : newVersion.role;
	targetState.deviceLimit = newVersion.deviceLimit === undefined ? dbVersion.deviceLimit : newVersion.deviceLimit;
	targetState.photoLimit = newVersion.photoLimit === undefined ? dbVersion.photoLimit : newVersion.photoLimit;
	targetState.cloudStorageLimit = newVersion.cloudStorageLimit === undefined ? dbVersion.cloudStorageLimit : newVersion.cloudStorageLimit;
	targetState.features = newVersion.features === undefined ? dbVersion.features : newVersion.features;
	targetState.nextPlanDate = newVersion.nextPlanDate === undefined ? dbVersion.nextPlanDate : newVersion.nextPlanDate;
	targetState.availableUpgrades = newVersion.availableUpgrades === undefined ? dbVersion.availableUpgrades : newVersion.availableUpgrades;
	targetState.licenseTemplateId = newVersion.licenseTemplateId === undefined ? dbVersion.licenseTemplateId : newVersion.licenseTemplateId;
	targetState.licenseDisplayName = newVersion.licenseDisplayName === undefined ? dbVersion.licenseDisplayName : newVersion.licenseDisplayName;
	targetState.licenseManager = newVersion.licenseManager === undefined ? dbVersion.licenseManager : newVersion.licenseManager;
	targetState.licenseFlags = newVersion.licenseFlags === undefined ? dbVersion.licenseFlags : newVersion.licenseFlags;
	targetState.availableUpgradesFeatures = newVersion.availableUpgradesFeatures === undefined ? dbVersion.availableUpgradesFeatures : newVersion.availableUpgradesFeatures;
	targetState.licenseId = newVersion.licenseId === undefined ? dbVersion.licenseId : newVersion.licenseId;
	targetState.password = newVersion.password === undefined ? dbVersion.password : newVersion.password;
	targetState.affiliateId = newVersion.affiliateId === undefined ? dbVersion.affiliateId : newVersion.affiliateId;
    return targetState;
}

export function merge(dbVersion: Account, newVersion: Account) {
    return new Account(mergeState(dbVersion.state(), newVersion.state()));
}
