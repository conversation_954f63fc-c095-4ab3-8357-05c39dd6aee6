import express = require("express");
import { config } from "../system/Config.mjs";
import {
  safeAny,
  safeAccount,
  secure,
  safeNone,
  b,
  s,
} from "../system/safe.mjs";
import { microservice } from "../microservices/account.microservice.mjs";
import { Context } from "../system/Context.mjs";

export interface IInstagramRequest extends express.Request {
  accessToken: string;
  refreshToken: string;
}

function getExpectedRedirect(req): string | null {
  const userAgent = req.header("User-Agent");
  const isMobile =
    userAgent &&
    (userAgent.startsWith("Mylio iOS") ||
      userAgent.startsWith("Mylio Android"));

  if (b(req.query.redirect)) {
    return req.query.redirect;
  }

  if (isMobile) {
    return "com.mylio:";
  }

  return null;
}

export function addInstagramRoutes(router: express.Router) {
  router.get(
    "/accounts/:aid/instagram/import-images/access-token",
    safeNone,
    secure,
    (req, res, next) => {
      const context = req.context;
      return microservice.instagramService
        .getImportImagesAccessToken(context, context.aid)
        .then((token) => {
          return res.status(200).json({ token });
        })
        .catch(next);
    }
  );

  router.get(
    "/accounts/:aid/instagram/import-images/url",
    safeNone,
    secure,
    (req, res, next) => {
      const context = req.context;
      let redirect = getExpectedRedirect(req);
      return res.status(200).json({
        url: microservice.instagramService.getImportImagesOAuthUrl(
          context,
          context.aid,
          b(req.query.encrypt),
          redirect
        ),
      });
    }
  );

  router.get("/instagram/authorize", (req, res, next) => {
    let state = JSON.parse(
      Buffer.from(s(req.query.state), "base64").toString("ascii")
    );

    let redirect = config.cloud;
    if (state.redirect == null || state.redirect == "embedded") {
      redirect = config.cloud;
    } else if (state.isMobile === true) {
      redirect = "com.mylio:";
    } else {
      redirect = state.redirect;
    }

    return microservice.instagramService
      .addRefreshToken(s(req.query.code), state)
      .then(() => {
        res.setHeader(
          "location",
          `${redirect}/instagram/loggedin?success=true`
        );
        res.status(302).end();
        return res;
      })
      .catch((err) => {
        let reason = Buffer.from(JSON.stringify(err.error)).toString("hex");
        res.setHeader(
          "location",
          `${redirect}/instagram/loggedin?success=false&reason=${reason}`
        );
        res.status(302).end();
        return res;
      })
      .catch(next);
  });

  router.put(
    "/accounts/:aid/instagram/:task/revoke",
    safeNone,
    secure,
    (req, res, next) => {
      const context: Context = req.context;
      const aid = context.aid;
      const task = req.params.task;

      return microservice.instagramService
        .revokeToken(context, aid, task)
        .then(() => {
          context.dumpLog();
          return res.sendStatus(200);
        })
        .catch(next);
    }
  );

  router.get("/instagram/loggedin", (req, res, next) => {
    res
      .status(200)
      .contentType("text/html")
      /* tslint:disable */
      .write(
        `<html>
          <body>
            <h1 id="warning" style="display:none;color:red">SECURITY WARNING: Please treat the URL above as you would your password and do not share it with anyone<h1>
            <script>
              (function() {
                setTimeout(function() {
                  document.getElementById("warning").style.display = "inline-block";
                }, 2000);
              })();
            </script>
          </body>
        </html>`
      );
    /* tslint:enable */

    res.end();
  });
}
