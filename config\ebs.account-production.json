{"port": 443, "website": "https://account.mylio.com", "cloud": "https://merkle3account.mylio.com", "connectionStrings": {"a0": "pg://<user>:<password>@account-production-0.c04zdcgu39tj.us-west-2.rds.amazonaws.com:5432/account0?sslmode=disable", "x0": "pg://<user>:<password>@resource-0-0-rds.mylio.com:5432/resource0?sslmode=disable", "x4": "pg://<user>:<password>@resource-0-1-rds.mylio.com:5432/resource1?sslmode=disable", "x8": "pg://<user>:<password>@resource-0-2-rds.mylio.com:5432/resource2?sslmode=disable", "xc": "pg://<user>:<password>@resource-0-3-rds.mylio.com:5432/resource3?sslmode=disable"}, "services": [{"name": "resource", "protocolVersion": 23, "uri": ["https://resource-production-0.mylio.com"]}, {"name": "resource", "protocolVersion": 24, "uri": ["https://resource-production-1.mylio.com"]}, {"name": "account", "uri": ["https://merkle3account.mylio.com"]}, {"name": "reverse_geocoding", "uri": ["https://api.opencagedata.com/geocode/v1/json?q=%f,%f&pretty=0&no_annotations=1&key=74306074d3a5ef1f7c86ea43062367a6"]}, {"name": "geocoding", "uri": ["https://api.opencagedata.com/geocode/v1/json?q=%s&pretty=0&no_annotations=1&key=74306074d3a5ef1f7c86ea43062367a6"]}, {"name": "telemetry", "uri": ["https://telemetry-production-0.mylio.com"]}, {"name": "cloudSignalChannel", "protocolVersion": 24, "uri": ["signal-prod.mylio.com:443"]}, {"name": "cloudSignalChannel", "protocolVersion": 23, "uri": ["signal-prod-23.mylio.com:443"]}], "apple": {"redirectUrl": "https://merkle3account.mylio.com/apple/redirect"}, "stripe_version": "2019-12-03"}