import { extend } from "lodash";
import { <PERSON><PERSON><PERSON>, Manager } from "../models/License.model.mjs";
import { CloudClient } from "./CloudClient.mjs";
import { <PERSON><PERSON><PERSON>, By, Browser, until, WebDriver } from "selenium-webdriver";

const PASSWORD = "password";
const HOST = "https://account-test-0.mylio.com";
const BUYER = "<EMAIL>";

type BillingCycle = "monthly" | "one-year" | "two-year";
type StorageQty = "none" | "twoTBmonth" | "fiveTBmonth";


export function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

export async function createUsers(users: string[]) {
    let result: CloudClient[] = [];
    for (let user of users) {
        try {
            let cloud = new CloudClient(HOST);
            try {
                await cloud.signin(user, PASSWORD);
                let licenses = await cloud.get<ILicense[]>("/accounts/:aid/licenses");
                for (let license of licenses) {
                    await cloud.delete(`/accounts/:aid/licenses/${license.licenseId}`);
                }
                await cloud.delete("/accounts/:aid");
            }
            catch (e) {
                // ignore
            }
            await cloud.post("/accounts", { idp: "mylio", sub: user, email: user, role: "user", password: PASSWORD });
            await cloud.signin(user, PASSWORD);
            result.push(cloud);
        }
        catch (e) {
            console.log(e);
            // ignore, the account may not exist
        }
    }
    return result;
}


export async function subscribe(billingCycle: BillingCycle, strorageQty: StorageQty) {
    let driver = await new Builder().forBrowser(Browser.FIREFOX).build();
    await driver.get("https://myliodev.wpengine.com/buy-now/");
    await driver.findElement(By.className(billingCycle)).click();
    await driver.findElement(By.className(strorageQty)).click();
    let buyButton = await driver.findElement(By.linkText("Buy"));
    await driver.executeScript("arguments[0].click();", buyButton);
    await completeFsCheckoutForm(driver, true);
    await driver.wait(until.elementLocated(By.xpath("//h4[contains(text(), 'License Key:')]")), 10000);
    let licenseKeyElement = await driver.wait(until.elementLocated(By.xpath("//h4[contains(text(), 'License Key:')]")), 10000);
    let licenseKeyText = await licenseKeyElement.getText();
    const licenseKey = licenseKeyText.replace('License Key: ', '');
    await driver.quit();

    return licenseKey;
}

async function completeFsCheckoutForm(driver: WebDriver, extendedVersion = true) {
    await driver.wait(until.elementLocated(By.id("fsc-popup-frame")), 10000);
    let iframe = await driver.findElement(By.id("fsc-popup-frame"));
    await driver.switchTo().frame(iframe);
    try {
        await driver.wait(until.elementLocated(By.id("savedPaymentMethod")), 5000);
    }
    catch (e) {
        await driver.wait(until.elementLocated(By.id("card-number")), 10000);
        if (extendedVersion) {
            await driver.findElement(By.id("contact-email")).click();
            await driver.findElement(By.id("contact-email")).sendKeys(BUYER);
            await driver.findElement(By.id("contact-first-name")).sendKeys("w");
            await driver.findElement(By.id("contact-last-name")).sendKeys("m");
        }
        await driver.findElement(By.id("card-number")).sendKeys("4242 4242 4242 4242");
        await driver.findElement(By.id("card-expire-month")).sendKeys("10");
        await driver.findElement(By.id("card-expire-year")).sendKeys("30");
        await driver.findElement(By.id("card-security")).sendKeys("*SNRR");
        await driver.findElement(By.id("postal")).sendKeys("98569");
        await driver.wait(until.elementIsEnabled(driver.findElement(By.className("pay-button"))), 10000);
    }
    let payButton = await driver.findElement(By.className("pay-button"));
    await driver.executeScript("arguments[0].click();", payButton);
}

export async function signInToAccountWebsite(driver: WebDriver, sub: string) {
    await driver.get("https://accounttest.mylio.com/");
    await driver.wait(until.elementLocated(By.id("TextFieldEmail")), 10000);
    await driver.findElement(By.id("TextFieldEmail")).sendKeys(sub);
    await driver.findElement(By.id("ButtonSignIn")).click();
    await driver.wait(until.elementLocated(By.id("TextFieldPassword")), 10000);
    await driver.findElement(By.id("TextFieldPassword")).sendKeys(PASSWORD);
    await driver.findElement(By.id("ButtonContinue")).click();
}

export async function subscribeNK(sub: string) {
    let driver = await new Builder().forBrowser(Browser.CHROME).build();
    signInToAccountWebsite(driver, sub);
    await driver.wait(until.elementLocated(By.id("HeaderBUYLink")), 10000);
    await driver.findElement(By.id("HeaderBUYLink")).click();
    await completeFsCheckoutForm(driver, false);
    await driver.quit();
}

export async function buyStorage(sub: string, storage: StorageQty) {
    let driver = await new Builder().forBrowser(Browser.FIREFOX).build();
    await driver.get("https://accounttest.mylio.com/");
    await driver.wait(until.elementLocated(By.id("TextFieldEmail")), 10000);
    await driver.findElement(By.id("TextFieldEmail")).sendKeys(sub);
    await driver.findElement(By.id("ButtonSignIn")).click();
    await driver.wait(until.elementLocated(By.id("TextFieldPassword")), 10000);
    await driver.findElement(By.id("TextFieldPassword")).sendKeys(PASSWORD);
    await driver.findElement(By.id("ButtonContinue")).click();
    await driver.wait(until.elementLocated(By.id("ButtonMylioDrive")), 10000);
    await driver.findElement(By.id("ButtonMylioDrive")).click();
    await completeFsCheckoutForm(driver, false);
    await driver.quit();
}   
