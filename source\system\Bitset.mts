export class Bitset {
  private _buffer: <PERSON><PERSON>er;
  constructor(size: number);
  constructor(data: Buffer);
  constructor(data: string, encoding: string);
  constructor(data: string | Buffer | number, encoding?: BufferEncoding) {
    if (typeof data === "string") {
      this._buffer = new Buffer(data, encoding);
    } else if (typeof data === "number") {
      this._buffer = Buffer.alloc(data / 8, 0);
    } else {
      this._buffer = data;
    }
  }

  public get(bit: number) {
    let bytePos = Math.floor(this._buffer.length / 8);
    let byte = this._buffer.readUInt8(bytePos);
    return !!(byte & (1 << bit % 8));
  }

  public set(bit: number) {
    let bytePos = Math.floor(bit / 8);
    let bitPos = bit % 8;
    let byte = this._buffer.readUInt8(bytePos);
    byte = byte | (1 << bitPos);
    this._buffer.writeUInt8(byte, bytePos);
  }

  public toString(encoding: BufferEncoding) {
    return this._buffer.toString(encoding);
  }

  public toInt32() {
    return this._buffer.readUInt32LE(0);
  }

  public toInt16() {
    return this._buffer.readUInt16LE(0);
  }
}
