const { STSClient, GetSessionTokenCommand } = require("@aws-sdk/client-sts");
const { readFileSync, writeFileSync } = require("fs");
const { join } = require("path");
const readline = require("node:readline/promises").createInterface({
  input: process.stdin,
  output: process.stdout,
  terminal: true,
});
async function main() {
  const username = await readline.question("Enter your username: ");
  const mfaToken = await readline.question("Enter your MFA token code: ");
  const stsClient = new STSClient({ region: "us-west-2" });
  const mfaParams = {
    DurationSeconds: 12 * 3600,
    SerialNumber: `arn:aws:iam::988863557391:mfa/${username}`,
    TokenCode: mfaToken.trim(),
  };
  try {
    console.log(mfaParams);
    const { Credentials } = await stsClient.send(
      new GetSessionTokenCommand(mfaParams)
    );
    const credentials = {
      accessKeyId: Credentials.AccessKeyId,
      secretAccessKey: Credentials.SecretAccessKey,
      sessionToken: Credentials.SessionToken,
    };
    const credentialsFilePath = join(process.env.HOME, ".aws", "config");
    const credentialsFileContent = readFileSync(credentialsFilePath, {
      encoding: "utf-8",
    });
    const profileHeader = "profile eb-cli";
    let updatedCredentialsFileContent;
    if (credentialsFileContent.includes(profileHeader)) {
      updatedCredentialsFileContent = credentialsFileContent.replace(
        /\[profile eb-cli\]\naws_access_key_id\s*=\s*[^\n]*\naws_secret_access_key\s*=\s*[^\n]*(\naws_session_token\s*=\s*[^\n]*)?/,
        `[profile eb-cli]\naws_access_key_id = ${credentials.accessKeyId}\naws_secret_access_key = ${credentials.secretAccessKey}\naws_session_token = ${credentials.sessionToken}\n`
      );
    } else {
      updatedCredentialsFileContent =
        credentialsFileContent.trim() +
        `\n\n[${profileHeader}]\naws_access_key_id = ${credentials.accessKeyId}\naws_secret_access_key = ${credentials.secretAccessKey}\naws_session_token = ${credentials.sessionToken}\n`;
    }
    writeFileSync(credentialsFilePath, updatedCredentialsFileContent);
    console.log(`Credentials written successfully to ${credentialsFilePath}`);
  } catch (error) {
    console.error(error);
  } finally {
    readline.close();
  }
}
main();
//# sourceMappingURL=data:application/json;base64,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
