branch-defaults:
  default:
    environment: <service>-<type>-<version>
environment-defaults:
  <service>-<type>-<version>:
    branch: null
    repository: null
global:
  application_name: <service>
  default_ec2_keyname: null
  default_platform: Docker running on 64bit Amazon Linux 2
  default_region: us-west-2
  include_git_submodules: true
  instance_profile: null
  platform_name: null
  platform_version: null
  profile: eb-cli
  sc: null
  workspace_type: Application
