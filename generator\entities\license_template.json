{"plural": "licenseTemplates", "flow": "web<cloud<>postgres<>disk", "datatype": "string", "mixin": ["all"], "fields": {"template_id": {}, "duration": {}, "uses": {"datatype": "int32"}, "available_upgrades": {"datatype": "string"}, "display_name": {"datatype": "string"}, "public": {"datatype": "boolean"}, "weight": {"datatype": "int32"}, "device_limit": {"datatype": "int32"}, "photo_limit": {"datatype": "int32"}, "features": {"datatype": "int32"}, "cloud_storage_limit": {"datatype": "int32"}}, "directives": {"identify": [["template_id"]]}}