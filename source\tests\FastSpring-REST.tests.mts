import { test, describe, mock, before, after, it } from "node:test";
import assert from "node:assert";
import { CloudClient } from "./CloudClient.mjs";
import { <PERSON>uilder, By, Browser, until } from "selenium-webdriver";
import { <PERSON>ice<PERSON>, Manager } from "../models/License.model.mjs";
import { createUsers, subscribeNK, sleep, buyStorage } from "./FastSpringTestHelpers.mjs";

const HOST = "https://willem.mylio.com";
const PASSWORD = "password";
const ADMIN_USER = "<EMAIL>";
const BUYER = "<EMAIL>";
const PAID_SUB = "<EMAIL>";
const FREE_SUB = "<EMAIL>";

process.env.https_proxy = "http://127.0.0.1:8888";

describe("The license Lifecycle", async (t) => {
    let paid: CloudClient;
    let free: CloudClient;
    let users: CloudClient[] = [];


    before(async (t) => {
        // users = await createUsers([PAID_SUB, FREE_SUB]);
        // await subscribeNK("<EMAIL>");
        // paid = users[0];
        // free = users[1];

        paid = new CloudClient(HOST);
        await paid.signin(PAID_SUB, PASSWORD);
        free = new CloudClient(HOST);
        await free.signin(FREE_SUB, PASSWORD);

    });

    it("Get customer status and data for paid customer", async (t) => {
        let customerData = await paid.get<any>("/accounts/:aid/fastspring/customer-data");
        assert(customerData.length > 0);
    });

    it("Get customer status and data for free customer", async (t) => {
        let customerData = await free.get<any>("/accounts/:aid/fastspring/customer-data");
        assert(customerData.length === 0);
    });

    after(async (t) => {
        console.log("done");
        setTimeout(() => {
            process.exit(0);
        }, 200);
    });
});
