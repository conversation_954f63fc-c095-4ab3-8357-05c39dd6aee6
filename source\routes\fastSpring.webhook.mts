import express from "express";
import { config } from "../system/Config.mjs";
import crypto from "crypto";
import { microservice as g } from "../microservices/account.microservice.mjs";
import { Context } from "../system/Context.mjs";
import { makeError } from "../system/error.mjs";
import FastSpringService from "../services/FastSpringService.mjs";

export default function addFastSpringWebhook(router: express.Router) {
  router.post(
    "/fastspring/webhook",
    express.json({
      verify: (req, res, buf) => {
        req.rawBody = buf;
      },
    }),
    async (req, res, next) => {

      const signature = req.headers["x-fs-signature"] as string;
      if (!verifySignature(req.rawBody, signature)) {
        throw makeError(
          403,
          "INVALID_FASTSPRING_SIGNATURE",
          "FastSpring signature did not mach payload"
        );
      }
      let payload = req.body;
      let context = new Context();
      await g.fastSpringService.handleEvents(context, payload);
      res.status(200).send();
    }
  );
}

function verifySignature(body: Buffer | string, signature: string) {
  const hmac = crypto.createHmac("sha256", config.fastspring.webhook_secret);
  if (typeof body === "string") hmac.update(body, "utf-8");
  else hmac.update(body);
  const expectedSignature = hmac.digest("base64");
  return expectedSignature === signature;
}
