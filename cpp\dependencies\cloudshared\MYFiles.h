#pragma once
#include "bjson.h"
#include <vector>
#include <map>
#include "MYHash.h"
#include "MYTrev.h"
#include "vector_map.h"
#include "MYMediaFileType.h"
#include "MYNeeds.h"
#include "MYPrimitives.h"

class MYFile;
class MYFiles;

class myfile_construct_for_aggregation
{
};

class MYFile final
{
public:
    MYFile(const MYFile &);
    MYFile &operator=(const MYFile &);
    MYFile &operator=(MYFile &&);
    MYFile(MYFile &&);

    MYFile(MYFiles *myFiles, MYMediaFileType::Enum fileType);
    MYFile(MYFiles *myFiles, MYBJsonIterator &begin, const MYBJsonIterator &end);
    MYFile(MYBJsonIterator &begin, const MYBJsonIterator &end, const myfile_construct_for_aggregation &);

    MYFile &operator+=(const MYFile &other);

    const std::string &getFormat() const;
    bool setFormat(const std::string &newFormat);
    bool setFormat(std::string &&newFormat);

    MYMediaFileType::Enum getMediaType() const { return _mediaType; }

    uint64_t getFileDateTime() const { return _fileDateTime; }
    bool setFileDateTime(uint64_t newFileDateTime);

    uint64_t getFileSize() const { return _fileSize; }
    bool setFileSize(uint64_t newFileSize);

    bool hasOriginalDataHash() const { return _originalDataHash != MYHashRefEmpty; }
    const MYHashRef getRefOriginalDataHash() const { return _originalDataHash; }
    const MYHash &getOriginalDataHash() const;
    bool setOriginalDataHash(const MYHash &newOriginalDataHash);

    unsigned int getOrientation() const { return _orientation; }
    bool setOrientation(unsigned int newOrientation);

    unsigned int getWidth() const { return _width; }
    bool setWidth(unsigned int newWidth);

    unsigned int getHeight() const { return _height; }
    bool setHeight(unsigned int newHeight);

    bool getIsGenerated() const { return _isGenerated; }
    bool setIsGenerated(bool newIsGenerated);

    bool isModified() const;
    std::bitset<16> _modifiedFields;

    std::string toString() const;

    MYBJsonIterator getExtensionProperty(int id) const;
    bool clearExtensionProperty(int id);

private:
    friend MYFiles;

    MYFiles *_myFiles = nullptr;

    int64_t _fileDateTime = 0;
    uint64_t _fileSize = 0;

    int _orientation = 0;
    int _width = 0;

    int _height = 0;

    // Remember to modify files.compact if you modify these
    MYStringRef _format = MYStringRefEmpty;
    MYHashRef _originalDataHash = MYHashRefEmpty;

    MYMediaFileType::Enum _mediaType = MYMediaFileType::Enum::NoType;
    bool _isGenerated = false;

    std::unique_ptr<MYBJsonSmallRW> _extensionData;

    static MYHashRef getOriginalDataHashRefFromBJson(MYBJsonIterator &begin, const MYBJsonIterator &end, NeedsBits fileTypes);
    void serializeToBJson(MYBJsonRW &writer) const;
};

class MYFiles
{
public:
    MYFiles() {}
    MYFiles(MYBJsonIterator &begin, const MYBJsonIterator &end);
    MYFiles(const MYFiles &);
    MYFiles(MYFiles &&other);

    void deserializeFromBJson(MYBJsonIterator &begin, const MYBJsonIterator &end);
    void clear();

    static MYHash getOriginalDataHashFromBJson(MYBJsonIterator &begin, const MYBJsonIterator &end, NeedsBits fileTypes);
    static uint64_t getRvnShortHash(const MYHash &rawOrVideoOriginalDataHash, const MYHash &nonRawOriginalDataHash)
    {
        uint32_t rvHash32;
        if (!rawOrVideoOriginalDataHash.empty())
        {
            rvHash32 = rawOrVideoOriginalDataHash.getShortHash();
            if (rvHash32 == 0)
            {
                rvHash32 = 1;
            }
        }
        else
        {
            rvHash32 = 0;
        }

        uint32_t nHash32;
        if (!nonRawOriginalDataHash.empty())
        {
            nHash32 = nonRawOriginalDataHash.getShortHash();
            if (nHash32 == 0)
            {
                nHash32 = 1;
            }
        }
        else
        {
            nHash32 = 0;
        }

        uint64_t rvnHash = rvHash32;
        rvnHash <<= 32;
        rvnHash |= nHash32;
        return rvnHash;
    }

    MYFiles &operator+=(const MYBJsonView &view);
    MYFiles &operator-=(const MYBJsonView &view);

    MYFiles &operator=(const MYFiles &);
    MYFiles &operator=(MYFiles &&other);

    bool operator==(const MYFiles &other) const;

    bool hasVisualEditHash() const { return _visualEditHash != MYHashRefEmpty; }
    const MYHashRef getRefVisualEditHash() const { return _visualEditHash; }
    const MYHash &getVisualEditHash() const;
    bool setVisualEditHash(const MYHash &newVisualEditHash);

    unsigned int getOrientation() const { return _orientation; }
    bool setOrientation(unsigned int newOrientation);

    void compact();
    void serializeToBJson(MYBJsonRW &writer) const;

    bool removeMediaFileForType(MYMediaFileType::Enum mediaFileType, bool fetchLegacyPreviewThumb = false);
    const MYFile &getMediaFileOrEmpty(MYMediaFileType::Enum mediaFileType) const;
    MYFile *getMediaFileOrNull(MYMediaFileType::Enum mediaFileType, bool fetchLegacyPreviewThumb = false);
    const MYFile *getMediaFileOrNull(MYMediaFileType::Enum mediaFileType) const;
    MYFile *getOrCreateMediaFile(MYMediaFileType::Enum mediaFileType);

    const NeedsBits getSupportMediaTypes() const;

    std::vector<MYFile> &getFiles()
    {
        return _files;
    }

    const std::vector<MYFile> &getFiles() const
    {
        return _files;
    }

    bool isModified() const;
    std::bitset<16> _modifiedFields;

    bool empty() const;

    std::vector<MYFile>::iterator begin()
    {
        return _files.begin();
    }

    std::vector<MYFile>::iterator end()
    {
        return _files.end();
    }

    std::vector<MYFile>::const_iterator begin() const
    {
        return _files.begin();
    }

    std::vector<MYFile>::const_iterator end() const
    {
        return _files.end();
    }

    bool _isConstructed = 1;

    std::string toString() const;

private:
    friend MYFile;
    friend class Test;

    MYHashRef getOrCreateHashRef(const MYHash &hash);
    MYStringRef getOrCreateStringRef(const std::string &string, MYMediaFileType::Enum fileType);
    MYStringRef getOrCreateStringRef(std::string &&string, MYMediaFileType::Enum fileType);

    const MYHash &getHash(MYHashRef hashref) const;
    const std::string &getString(MYStringRef stringRef, MYMediaFileType::Enum fileType) const;

    // Remember to modify files.compact if you modify these
    MYHashRef _visualEditHash = MYHashRefEmpty;
    int _orientation = 0;

    // typedef std::vector<MYFile, short_alloc<MYFile, 2048, alignof(MYFile)>> smallvec;
    // typename smallvec::allocator_type::arena_type _buffer_embed;

    std::vector<MYFile> _files;

    // Perf measurement:
    // On a 1.3ghz CPU we can sequentially insert/look-up from a list of 10 hashes, 5 million times per second
    // On a 1.3ghz CPU we can sequentially insert/look-up from a list of 10 short strings, 1.2 million times per second
    // On a 1.3ghz CPU we can sequentially insert/look-up from a list of 10 long strings (60 to 100 chars), 300000 times per second
    std::vector<MYHash> _hashMap;
    std::vector<std::string> _stringMap;

    std::unique_ptr<MYBJsonSmallRW> _extensionData;
};

enum class SupportTicketLogs
{
    None = 0,
    Basic = 0x0001,
    Extended = 0x0002,
    All = 0xFFFF
};

struct SupportTicketInfo
{
    bool takeScreenShot = false;
    bool includeCatalog = false;
    bool requestLogs = false;
    bool isFromWebSite = false;
    SupportTicketLogs logs = SupportTicketLogs::None;
    std::chrono::system_clock::time_point validUntilTime;
    std::string additionalFile;
    std::string crashFile;
    std::string subject;
    std::string commentsToSend;
    std::vector<std::string> consoleCommands;
    std::string localScreenShotPath;

    MYHash supportTicketRequestId;

    void SerializeToBJson(MYBJsonRW &bjson);
    void DeserializeFromBJson(MYBJsonView bjson);
};
