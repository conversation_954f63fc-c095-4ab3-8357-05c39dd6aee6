import express = require("express");
import { config, getServices } from "../system/Config.mjs";
import { safeNone } from "../system/safe.mjs";
import { healthService, HealthGroups } from "../services/HealthService.mjs";

export function addHealthRoutes(router: express.Router) {
  router.get("/health", safeNone, (req, res, next) => {
    let context = req.context;

    return healthService
      .status(context)
      .then((results) => {
        context.dumpLog();
        return res.status(200).json(results);
      })
      .catch(next);
  });

  router.get("/status", safeNone, (req, res, next) => {
    return res.sendStatus(200);
  });
}
