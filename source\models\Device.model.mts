

import Validation from "../system/Validation.mjs";
import { IModel } from "./IModel.mjs";




/* b::enums */
export enum DeviceType {
  Unknown = 0,

  // bits 0 to 4 -> instance
  // bits 5 to 8
  // 0001 -> cloud-like device
  // 0010 -> desktop device
  // 0100 -> mobile device
  // 1000 -> attached (non-cpu) device

  Cloud = 0x11, // 0b0001 0001
  AmazonDrive = 0x12, // 0b0001 0010
  GoogleDrive = 0x13,
  Leica = 0x14, // 0b0001 0011
  MicrosoftDrive = 0x15,

  Mac = 0x21, // 0b0010 0001
  PC = 0x22, // 0b0010 0010
  PowerShell = 0x23, // 0b0010 0011

  iPad = 0x41, // 0b0100 0001
  iPhonePod = 0x42, // 0b0100 0010
  Android = 0x43, // 0b0100 0011

  RemovableDevice = 0x81, // 0b1000 0001
  NAS = 0x82, // 0b1000 0002
}

export enum DeviceFlags {
  retired = 1,
}
/* end */


export interface IDevice {
    flags?: number;
	modifiedTime?: Date;
	createdTime?: Date;
	accountId?: number;
	deleted?: boolean;
	t?: string;
	d?: string;
	deviceId?: number;
	name?: string;
	deviceType?: number;
	nickname?: string;
	encrypt?: boolean;
	creationTime?: Date;
	longId?: string;
	supportTicket?: string;
}


export class Device 
implements IModel {
    private _state: IDevice;

    
/* b::model_public_members */
public isRetired() {
    return !!(this.flags() & DeviceFlags.retired);
  }

  public retire() {
    this.flags(this.flags() | DeviceFlags.retired);
  }

  public restore() {
    this.flags(this.flags() & 0xfffffffe);
  }
/* end */

    
    changed = false;

    constructor(state: IDevice) {
        this.state(state);
    }

    isUndefined() {
        return this._state !== void 0;
    }

    async validate(skipUndefined?: boolean) {
        let v = new Validation(this, skipUndefined);
        
/* b::validate */

/* end */

        return v;
    }

    rtt() {
        return "Device"; 
    }

    state (value?: IDevice) {
        if (value !== undefined) { 
            this._state = value;
            if (this._state.deleted === undefined) this._state.deleted = false;
            


        }
        return this._state;
    }

    flags(value?: number) {
                if (value !== void 0) {
                    if (this.state().flags !== value) {
                        this.state().flags = value;
                        this.changed = true;
                    }
                }
                return this.state().flags;
            };

		modifiedTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().modifiedTime !== value) {
                        this.state().modifiedTime = value;
                        this.changed = true;
                    }
                }
                return this.state().modifiedTime;
            };

		createdTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().createdTime !== value) {
                        this.state().createdTime = value;
                        this.changed = true;
                    }
                }
                return this.state().createdTime;
            };

		accountId(value?: number) {
                if (value !== void 0) {
                    if (this.state().accountId !== value) {
                        this.state().accountId = value;
                        this.changed = true;
                    }
                }
                return this.state().accountId;
            };

		deleted(value?: boolean) {
                if (value !== void 0) {
                    if (this.state().deleted !== value) {
                        this.state().deleted = value;
                        this.changed = true;
                    }
                }
                return this.state().deleted;
            };

		t(value?: string) {
                if (value !== void 0) {
                    if (this.state().t !== value) {
                        this.state().t = value;
                        this.changed = true;
                    }
                }
                return this.state().t;
            };

		d(value?: string) {
                if (value !== void 0) {
                    if (this.state().d !== value) {
                        this.state().d = value;
                        this.changed = true;
                    }
                }
                return this.state().d;
            };

		deviceId(value?: number) {
                if (value !== void 0) {
                    if (this.state().deviceId !== value) {
                        this.state().deviceId = value;
                        this.changed = true;
                    }
                }
                return this.state().deviceId;
            };

		name(value?: string) {
                if (value !== void 0) {
                    if (this.state().name !== value) {
                        this.state().name = value;
                        this.changed = true;
                    }
                }
                return this.state().name;
            };

		deviceType(value?: number) {
                if (value !== void 0) {
                    if (this.state().deviceType !== value) {
                        this.state().deviceType = value;
                        this.changed = true;
                    }
                }
                return this.state().deviceType;
            };

		nickname(value?: string) {
                if (value !== void 0) {
                    if (this.state().nickname !== value) {
                        this.state().nickname = value;
                        this.changed = true;
                    }
                }
                return this.state().nickname;
            };

		encrypt(value?: boolean) {
                if (value !== void 0) {
                    if (this.state().encrypt !== value) {
                        this.state().encrypt = value;
                        this.changed = true;
                    }
                }
                return this.state().encrypt;
            };

		creationTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().creationTime !== value) {
                        this.state().creationTime = value;
                        this.changed = true;
                    }
                }
                return this.state().creationTime;
            };

		longId(value?: string) {
                if (value !== void 0) {
                    if (this.state().longId !== value) {
                        this.state().longId = value;
                        this.changed = true;
                    }
                }
                return this.state().longId;
            };

		supportTicket(value?: string) {
                if (value !== void 0) {
                    if (this.state().supportTicket !== value) {
                        this.state().supportTicket = value;
                        this.changed = true;
                    }
                }
                return this.state().supportTicket;
            };

    differs(original: Device) {
        return (
            (this.flags() !== void 0 && this.flags() !== original.flags())
		 || (this.modifiedTime() !== void 0 && this.modifiedTime() !== original.modifiedTime())
		 || (this.createdTime() !== void 0 && this.createdTime() !== original.createdTime())
		 || (this.accountId() !== void 0 && this.accountId() !== original.accountId())
		 || (this.deleted() !== void 0 && this.deleted() !== original.deleted())
		 || (this.t() !== void 0 && this.t() !== original.t())
		 || (this.d() !== void 0 && this.d() !== original.d())
		 || (this.deviceId() !== void 0 && this.deviceId() !== original.deviceId())
		 || (this.name() !== void 0 && this.name() !== original.name())
		 || (this.deviceType() !== void 0 && this.deviceType() !== original.deviceType())
		 || (this.nickname() !== void 0 && this.nickname() !== original.nickname())
		 || (this.encrypt() !== void 0 && this.encrypt() !== original.encrypt())
		 || (this.creationTime() !== void 0 && this.creationTime() !== original.creationTime())
		 || (this.longId() !== void 0 && this.longId() !== original.longId())
		 || (this.supportTicket() !== void 0 && this.supportTicket() !== original.supportTicket())
        );
    }





/* b::private_members */

/* end */

}



export function sanitizeInput(source: Device, amdin: boolean, mode: string) : IDevice;
export function sanitizeInput(source: IDevice, admin: boolean, mode: string) : IDevice;
export function sanitizeInput(source: Device | IDevice, admin = false, mode="default"): IDevice {
    let s: IDevice;
    if (source instanceof Device)
        s = source.state();
    else
        s = source;        
    let t = {} as IDevice;
    
    
        
            switch(mode) {
                
                
                
            }
            t.flags = s.flags;
		t.accountId = s.accountId;
		t.deleted = s.deleted;
		t.t = s.t;
		t.d = s.d;
		t.deviceId = s.deviceId;
		t.name = s.name;
		t.deviceType = s.deviceType;
		t.nickname = s.nickname;
		t.encrypt = s.encrypt;
		t.creationTime = s.creationTime;
		t.longId = s.longId;
		t.supportTicket = s.supportTicket;
        
    return t;
}

export function sanitizeOutput(source: Device, amdin: boolean) : IDevice;
export function sanitizeOutput(source: IDevice, admin: boolean) : IDevice;
export function sanitizeOutput(source: Device | IDevice, admin = false): IDevice {
    let s: IDevice;
    if (source instanceof Device)
        s = source.state();
    else
        s = source;        
    let t = {} as IDevice;
    
    
    
    t.flags = s.flags;	
t.modifiedTime = s.modifiedTime;	
t.createdTime = s.createdTime;	
t.accountId = s.accountId;	
t.deleted = s.deleted;	
t.t = s.t;	
t.d = s.d;	
t.deviceId = s.deviceId;	
t.name = s.name;	
t.deviceType = s.deviceType;	
t.nickname = s.nickname;	
t.encrypt = s.encrypt;	
t.creationTime = s.creationTime;	
t.longId = s.longId;	
t.supportTicket = s.supportTicket;
    return t;
}

export function mergeState(dbVersion: IDevice, newVersion: IDevice) {
    let targetState: IDevice = {};
    targetState.flags = newVersion.flags === undefined ? dbVersion.flags : newVersion.flags;
	targetState.modifiedTime = newVersion.modifiedTime === undefined ? dbVersion.modifiedTime : newVersion.modifiedTime;
	targetState.createdTime = newVersion.createdTime === undefined ? dbVersion.createdTime : newVersion.createdTime;
	targetState.accountId = newVersion.accountId === undefined ? dbVersion.accountId : newVersion.accountId;
	targetState.deleted = newVersion.deleted === undefined ? dbVersion.deleted : newVersion.deleted;
	targetState.t = newVersion.t === undefined ? dbVersion.t : newVersion.t;
	targetState.d = newVersion.d === undefined ? dbVersion.d : newVersion.d;
	targetState.deviceId = newVersion.deviceId === undefined ? dbVersion.deviceId : newVersion.deviceId;
	targetState.name = newVersion.name === undefined ? dbVersion.name : newVersion.name;
	targetState.deviceType = newVersion.deviceType === undefined ? dbVersion.deviceType : newVersion.deviceType;
	targetState.nickname = newVersion.nickname === undefined ? dbVersion.nickname : newVersion.nickname;
	targetState.encrypt = newVersion.encrypt === undefined ? dbVersion.encrypt : newVersion.encrypt;
	targetState.creationTime = newVersion.creationTime === undefined ? dbVersion.creationTime : newVersion.creationTime;
	targetState.longId = newVersion.longId === undefined ? dbVersion.longId : newVersion.longId;
	targetState.supportTicket = newVersion.supportTicket === undefined ? dbVersion.supportTicket : newVersion.supportTicket;
    return targetState;
}

export function merge(dbVersion: Device, newVersion: Device) {
    return new Device(mergeState(dbVersion.state(), newVersion.state()));
}
