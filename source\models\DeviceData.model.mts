

import Validation from "../system/Validation.mjs";
import { IModel } from "./IModel.mjs";







export interface IDeviceData {
    flags?: number;
	modifiedTime?: Date;
	createdTime?: Date;
	accountId?: number;
	deleted?: boolean;
	t?: string;
	d?: string;
	deviceId?: number;
	build?: string;
	lastAccessTime?: Date;
	os?: string;
	mediaCount?: number;
	protocolVersion?: number;
	version?: string;
	lastStartupTime?: Date;
	lastHidTime?: Date;
	lastImportTime?: Date;
	originalSize?: string;
	localOriginalSize?: string;
}


export class DeviceData 
implements IModel {
    private _state: IDeviceData;

    


    
    changed = false;

    constructor(state: IDeviceData) {
        this.state(state);
    }

    isUndefined() {
        return this._state !== void 0;
    }

    async validate(skipUndefined?: boolean) {
        let v = new Validation(this, skipUndefined);
        


        return v;
    }

    rtt() {
        return "DeviceData"; 
    }

    state (value?: IDeviceData) {
        if (value !== undefined) { 
            this._state = value;
            if (this._state.deleted === undefined) this._state.deleted = false;
            


        }
        return this._state;
    }

    flags(value?: number) {
                if (value !== void 0) {
                    if (this.state().flags !== value) {
                        this.state().flags = value;
                        this.changed = true;
                    }
                }
                return this.state().flags;
            };

		modifiedTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().modifiedTime !== value) {
                        this.state().modifiedTime = value;
                        this.changed = true;
                    }
                }
                return this.state().modifiedTime;
            };

		createdTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().createdTime !== value) {
                        this.state().createdTime = value;
                        this.changed = true;
                    }
                }
                return this.state().createdTime;
            };

		accountId(value?: number) {
                if (value !== void 0) {
                    if (this.state().accountId !== value) {
                        this.state().accountId = value;
                        this.changed = true;
                    }
                }
                return this.state().accountId;
            };

		deleted(value?: boolean) {
                if (value !== void 0) {
                    if (this.state().deleted !== value) {
                        this.state().deleted = value;
                        this.changed = true;
                    }
                }
                return this.state().deleted;
            };

		t(value?: string) {
                if (value !== void 0) {
                    if (this.state().t !== value) {
                        this.state().t = value;
                        this.changed = true;
                    }
                }
                return this.state().t;
            };

		d(value?: string) {
                if (value !== void 0) {
                    if (this.state().d !== value) {
                        this.state().d = value;
                        this.changed = true;
                    }
                }
                return this.state().d;
            };

		deviceId(value?: number) {
                if (value !== void 0) {
                    if (this.state().deviceId !== value) {
                        this.state().deviceId = value;
                        this.changed = true;
                    }
                }
                return this.state().deviceId;
            };

		build(value?: string) {
                if (value !== void 0) {
                    if (this.state().build !== value) {
                        this.state().build = value;
                        this.changed = true;
                    }
                }
                return this.state().build;
            };

		lastAccessTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().lastAccessTime !== value) {
                        this.state().lastAccessTime = value;
                        this.changed = true;
                    }
                }
                return this.state().lastAccessTime;
            };

		os(value?: string) {
                if (value !== void 0) {
                    if (this.state().os !== value) {
                        this.state().os = value;
                        this.changed = true;
                    }
                }
                return this.state().os;
            };

		mediaCount(value?: number) {
                if (value !== void 0) {
                    if (this.state().mediaCount !== value) {
                        this.state().mediaCount = value;
                        this.changed = true;
                    }
                }
                return this.state().mediaCount;
            };

		protocolVersion(value?: number) {
                if (value !== void 0) {
                    if (this.state().protocolVersion !== value) {
                        this.state().protocolVersion = value;
                        this.changed = true;
                    }
                }
                return this.state().protocolVersion;
            };

		version(value?: string) {
                if (value !== void 0) {
                    if (this.state().version !== value) {
                        this.state().version = value;
                        this.changed = true;
                    }
                }
                return this.state().version;
            };

		lastStartupTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().lastStartupTime !== value) {
                        this.state().lastStartupTime = value;
                        this.changed = true;
                    }
                }
                return this.state().lastStartupTime;
            };

		lastHidTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().lastHidTime !== value) {
                        this.state().lastHidTime = value;
                        this.changed = true;
                    }
                }
                return this.state().lastHidTime;
            };

		lastImportTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().lastImportTime !== value) {
                        this.state().lastImportTime = value;
                        this.changed = true;
                    }
                }
                return this.state().lastImportTime;
            };

		originalSize(value?: string) {
                if (value !== void 0) {
                    if (this.state().originalSize !== value) {
                        this.state().originalSize = value;
                        this.changed = true;
                    }
                }
                return this.state().originalSize;
            };

		localOriginalSize(value?: string) {
                if (value !== void 0) {
                    if (this.state().localOriginalSize !== value) {
                        this.state().localOriginalSize = value;
                        this.changed = true;
                    }
                }
                return this.state().localOriginalSize;
            };

    differs(original: DeviceData) {
        return (
            (this.flags() !== void 0 && this.flags() !== original.flags())
		 || (this.modifiedTime() !== void 0 && this.modifiedTime() !== original.modifiedTime())
		 || (this.createdTime() !== void 0 && this.createdTime() !== original.createdTime())
		 || (this.accountId() !== void 0 && this.accountId() !== original.accountId())
		 || (this.deleted() !== void 0 && this.deleted() !== original.deleted())
		 || (this.t() !== void 0 && this.t() !== original.t())
		 || (this.d() !== void 0 && this.d() !== original.d())
		 || (this.deviceId() !== void 0 && this.deviceId() !== original.deviceId())
		 || (this.build() !== void 0 && this.build() !== original.build())
		 || (this.lastAccessTime() !== void 0 && this.lastAccessTime() !== original.lastAccessTime())
		 || (this.os() !== void 0 && this.os() !== original.os())
		 || (this.mediaCount() !== void 0 && this.mediaCount() !== original.mediaCount())
		 || (this.protocolVersion() !== void 0 && this.protocolVersion() !== original.protocolVersion())
		 || (this.version() !== void 0 && this.version() !== original.version())
		 || (this.lastStartupTime() !== void 0 && this.lastStartupTime() !== original.lastStartupTime())
		 || (this.lastHidTime() !== void 0 && this.lastHidTime() !== original.lastHidTime())
		 || (this.lastImportTime() !== void 0 && this.lastImportTime() !== original.lastImportTime())
		 || (this.originalSize() !== void 0 && this.originalSize() !== original.originalSize())
		 || (this.localOriginalSize() !== void 0 && this.localOriginalSize() !== original.localOriginalSize())
        );
    }







}



export function sanitizeInput(source: DeviceData, amdin: boolean, mode: string) : IDeviceData;
export function sanitizeInput(source: IDeviceData, admin: boolean, mode: string) : IDeviceData;
export function sanitizeInput(source: DeviceData | IDeviceData, admin = false, mode="default"): IDeviceData {
    let s: IDeviceData;
    if (source instanceof DeviceData)
        s = source.state();
    else
        s = source;        
    let t = {} as IDeviceData;
    
    
        
            switch(mode) {
                
                
                
            }
            t.flags = s.flags;
		t.accountId = s.accountId;
		t.deleted = s.deleted;
		t.t = s.t;
		t.d = s.d;
		t.deviceId = s.deviceId;
		t.build = s.build;
		t.lastAccessTime = s.lastAccessTime;
		t.os = s.os;
		t.mediaCount = s.mediaCount;
		t.protocolVersion = s.protocolVersion;
		t.version = s.version;
		t.lastStartupTime = s.lastStartupTime;
		t.lastHidTime = s.lastHidTime;
		t.lastImportTime = s.lastImportTime;
		t.originalSize = s.originalSize;
		t.localOriginalSize = s.localOriginalSize;
        
    return t;
}

export function sanitizeOutput(source: DeviceData, amdin: boolean) : IDeviceData;
export function sanitizeOutput(source: IDeviceData, admin: boolean) : IDeviceData;
export function sanitizeOutput(source: DeviceData | IDeviceData, admin = false): IDeviceData {
    let s: IDeviceData;
    if (source instanceof DeviceData)
        s = source.state();
    else
        s = source;        
    let t = {} as IDeviceData;
    
    
    
    t.flags = s.flags;	
t.modifiedTime = s.modifiedTime;	
t.createdTime = s.createdTime;	
t.accountId = s.accountId;	
t.deleted = s.deleted;	
t.t = s.t;	
t.d = s.d;	
t.deviceId = s.deviceId;	
t.build = s.build;	
t.lastAccessTime = s.lastAccessTime;	
t.os = s.os;	
t.mediaCount = s.mediaCount;	
t.protocolVersion = s.protocolVersion;	
t.version = s.version;	
t.lastStartupTime = s.lastStartupTime;	
t.lastHidTime = s.lastHidTime;	
t.lastImportTime = s.lastImportTime;	
t.originalSize = s.originalSize;	
t.localOriginalSize = s.localOriginalSize;
    return t;
}

export function mergeState(dbVersion: IDeviceData, newVersion: IDeviceData) {
    let targetState: IDeviceData = {};
    targetState.flags = newVersion.flags === undefined ? dbVersion.flags : newVersion.flags;
	targetState.modifiedTime = newVersion.modifiedTime === undefined ? dbVersion.modifiedTime : newVersion.modifiedTime;
	targetState.createdTime = newVersion.createdTime === undefined ? dbVersion.createdTime : newVersion.createdTime;
	targetState.accountId = newVersion.accountId === undefined ? dbVersion.accountId : newVersion.accountId;
	targetState.deleted = newVersion.deleted === undefined ? dbVersion.deleted : newVersion.deleted;
	targetState.t = newVersion.t === undefined ? dbVersion.t : newVersion.t;
	targetState.d = newVersion.d === undefined ? dbVersion.d : newVersion.d;
	targetState.deviceId = newVersion.deviceId === undefined ? dbVersion.deviceId : newVersion.deviceId;
	targetState.build = newVersion.build === undefined ? dbVersion.build : newVersion.build;
	targetState.lastAccessTime = newVersion.lastAccessTime === undefined ? dbVersion.lastAccessTime : newVersion.lastAccessTime;
	targetState.os = newVersion.os === undefined ? dbVersion.os : newVersion.os;
	targetState.mediaCount = newVersion.mediaCount === undefined ? dbVersion.mediaCount : newVersion.mediaCount;
	targetState.protocolVersion = newVersion.protocolVersion === undefined ? dbVersion.protocolVersion : newVersion.protocolVersion;
	targetState.version = newVersion.version === undefined ? dbVersion.version : newVersion.version;
	targetState.lastStartupTime = newVersion.lastStartupTime === undefined ? dbVersion.lastStartupTime : newVersion.lastStartupTime;
	targetState.lastHidTime = newVersion.lastHidTime === undefined ? dbVersion.lastHidTime : newVersion.lastHidTime;
	targetState.lastImportTime = newVersion.lastImportTime === undefined ? dbVersion.lastImportTime : newVersion.lastImportTime;
	targetState.originalSize = newVersion.originalSize === undefined ? dbVersion.originalSize : newVersion.originalSize;
	targetState.localOriginalSize = newVersion.localOriginalSize === undefined ? dbVersion.localOriginalSize : newVersion.localOriginalSize;
    return targetState;
}

export function merge(dbVersion: DeviceData, newVersion: DeviceData) {
    return new DeviceData(mergeState(dbVersion.state(), newVersion.state()));
}
