// Layer 0 header. Never #include anything in this file.
#pragma once

#define ENUM_FLAGS(T)                                                                                           \
  inline bool operator&(T x, T y) { return (static_cast<uint64_t>(x) & static_cast<uint64_t>(y)) > 0; };        \
  inline T operator*(T x, T y) { return static_cast<T>(static_cast<uint64_t>(x) & static_cast<uint64_t>(y)); }; \
  inline T operator|(T x, T y) { return static_cast<T>(static_cast<uint64_t>(x) | static_cast<uint64_t>(y)); }; \
  inline T operator^(T x, T y) { return static_cast<T>(static_cast<uint64_t>(x) ^ static_cast<uint64_t>(y)); }; \
  inline T operator~(T x) { return static_cast<T>(~static_cast<uint64_t>(x)); };                                \
  inline T &operator&=(T &x, T y)                                                                               \
  {                                                                                                             \
    x = static_cast<T>(static_cast<uint64_t>(x) & static_cast<uint64_t>(y));                                    \
    return x;                                                                                                   \
  };                                                                                                            \
  inline T &operator|=(T &x, T y)                                                                               \
  {                                                                                                             \
    x = static_cast<T>(static_cast<uint64_t>(x) | static_cast<uint64_t>(y));                                    \
    return x;                                                                                                   \
  };                                                                                                            \
  inline T &operator^=(T &x, T y)                                                                               \
  {                                                                                                             \
    x = x ^ y;                                                                                                  \
    return x;                                                                                                   \
  };
