

import Validation from "../system/Validation.mjs";
import { IModel } from "./IModel.mjs";







export interface IUserProperty {
    flags?: number;
	modifiedTime?: Date;
	createdTime?: Date;
	accountId?: number;
	deleted?: boolean;
	t?: string;
	d?: string;
	userPropertyId?: string;
	name?: string;
	value?: string;
}


export class UserProperty 
implements IModel {
    private _state: IUserProperty;

    


    
    changed = false;

    constructor(state: IUserProperty) {
        this.state(state);
    }

    isUndefined() {
        return this._state !== void 0;
    }

    async validate(skipUndefined?: boolean) {
        let v = new Validation(this, skipUndefined);
        


        return v;
    }

    rtt() {
        return "UserProperty"; 
    }

    state (value?: IUserProperty) {
        if (value !== undefined) { 
            this._state = value;
            if (this._state.deleted === undefined) this._state.deleted = false;
            


        }
        return this._state;
    }

    flags(value?: number) {
                if (value !== void 0) {
                    if (this.state().flags !== value) {
                        this.state().flags = value;
                        this.changed = true;
                    }
                }
                return this.state().flags;
            };

		modifiedTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().modifiedTime !== value) {
                        this.state().modifiedTime = value;
                        this.changed = true;
                    }
                }
                return this.state().modifiedTime;
            };

		createdTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().createdTime !== value) {
                        this.state().createdTime = value;
                        this.changed = true;
                    }
                }
                return this.state().createdTime;
            };

		accountId(value?: number) {
                if (value !== void 0) {
                    if (this.state().accountId !== value) {
                        this.state().accountId = value;
                        this.changed = true;
                    }
                }
                return this.state().accountId;
            };

		deleted(value?: boolean) {
                if (value !== void 0) {
                    if (this.state().deleted !== value) {
                        this.state().deleted = value;
                        this.changed = true;
                    }
                }
                return this.state().deleted;
            };

		t(value?: string) {
                if (value !== void 0) {
                    if (this.state().t !== value) {
                        this.state().t = value;
                        this.changed = true;
                    }
                }
                return this.state().t;
            };

		d(value?: string) {
                if (value !== void 0) {
                    if (this.state().d !== value) {
                        this.state().d = value;
                        this.changed = true;
                    }
                }
                return this.state().d;
            };

		userPropertyId(value?: string) {
                if (value !== void 0) {
                    if (this.state().userPropertyId !== value) {
                        this.state().userPropertyId = value;
                        this.changed = true;
                    }
                }
                return this.state().userPropertyId;
            };

		name(value?: string) {
                if (value !== void 0) {
                    if (this.state().name !== value) {
                        this.state().name = value;
                        this.changed = true;
                    }
                }
                return this.state().name;
            };

		value(value?: string) {
                if (value !== void 0) {
                    if (this.state().value !== value) {
                        this.state().value = value;
                        this.changed = true;
                    }
                }
                return this.state().value;
            };

    differs(original: UserProperty) {
        return (
            (this.flags() !== void 0 && this.flags() !== original.flags())
		 || (this.modifiedTime() !== void 0 && this.modifiedTime() !== original.modifiedTime())
		 || (this.createdTime() !== void 0 && this.createdTime() !== original.createdTime())
		 || (this.accountId() !== void 0 && this.accountId() !== original.accountId())
		 || (this.deleted() !== void 0 && this.deleted() !== original.deleted())
		 || (this.t() !== void 0 && this.t() !== original.t())
		 || (this.d() !== void 0 && this.d() !== original.d())
		 || (this.userPropertyId() !== void 0 && this.userPropertyId() !== original.userPropertyId())
		 || (this.name() !== void 0 && this.name() !== original.name())
		 || (this.value() !== void 0 && this.value() !== original.value())
        );
    }







}



export function sanitizeInput(source: UserProperty, amdin: boolean, mode: string) : IUserProperty;
export function sanitizeInput(source: IUserProperty, admin: boolean, mode: string) : IUserProperty;
export function sanitizeInput(source: UserProperty | IUserProperty, admin = false, mode="default"): IUserProperty {
    let s: IUserProperty;
    if (source instanceof UserProperty)
        s = source.state();
    else
        s = source;        
    let t = {} as IUserProperty;
    
    
        
            switch(mode) {
                
                
                
            }
            t.flags = s.flags;
		t.accountId = s.accountId;
		t.deleted = s.deleted;
		t.t = s.t;
		t.d = s.d;
		t.userPropertyId = s.userPropertyId;
		t.name = s.name;
		t.value = s.value;
        
    return t;
}

export function sanitizeOutput(source: UserProperty, amdin: boolean) : IUserProperty;
export function sanitizeOutput(source: IUserProperty, admin: boolean) : IUserProperty;
export function sanitizeOutput(source: UserProperty | IUserProperty, admin = false): IUserProperty {
    let s: IUserProperty;
    if (source instanceof UserProperty)
        s = source.state();
    else
        s = source;        
    let t = {} as IUserProperty;
    
    
    
    t.flags = s.flags;	
t.modifiedTime = s.modifiedTime;	
t.createdTime = s.createdTime;	
t.accountId = s.accountId;	
t.deleted = s.deleted;	
t.t = s.t;	
t.d = s.d;	
t.userPropertyId = s.userPropertyId;	
t.name = s.name;	
t.value = s.value;
    return t;
}

export function mergeState(dbVersion: IUserProperty, newVersion: IUserProperty) {
    let targetState: IUserProperty = {};
    targetState.flags = newVersion.flags === undefined ? dbVersion.flags : newVersion.flags;
	targetState.modifiedTime = newVersion.modifiedTime === undefined ? dbVersion.modifiedTime : newVersion.modifiedTime;
	targetState.createdTime = newVersion.createdTime === undefined ? dbVersion.createdTime : newVersion.createdTime;
	targetState.accountId = newVersion.accountId === undefined ? dbVersion.accountId : newVersion.accountId;
	targetState.deleted = newVersion.deleted === undefined ? dbVersion.deleted : newVersion.deleted;
	targetState.t = newVersion.t === undefined ? dbVersion.t : newVersion.t;
	targetState.d = newVersion.d === undefined ? dbVersion.d : newVersion.d;
	targetState.userPropertyId = newVersion.userPropertyId === undefined ? dbVersion.userPropertyId : newVersion.userPropertyId;
	targetState.name = newVersion.name === undefined ? dbVersion.name : newVersion.name;
	targetState.value = newVersion.value === undefined ? dbVersion.value : newVersion.value;
    return targetState;
}

export function merge(dbVersion: UserProperty, newVersion: UserProperty) {
    return new UserProperty(mergeState(dbVersion.state(), newVersion.state()));
}
