

import Validation from "../system/Validation.mjs";
import { IModel } from "./IModel.mjs";

/* b::imports */
import { LicenseFlags } from "./LicenseFlags.mjs";
/* end */


/* b::enums */
export enum Manager {
    FastSpring = "F",
    Apple = "A",
    My<PERSON> = "M",
}

export class Templates {
    static free = "free";
    static personal = "personal";
    static personalGroup = "personal-group";
    static business = "business";
    static businessGroup = "business-group";
}
/* end */


export interface ILicenseTemplate {
    flags?: number;
	modifiedTime?: Date;
	createdTime?: Date;
	templateId?: string;
	duration?: string;
	uses?: number;
	availableUpgrades?: string;
	displayName?: string;
	public?: boolean;
	weight?: number;
	deviceLimit?: number;
	photoLimit?: number;
	features?: number;
	cloudStorageLimit?: number;
}


export class LicenseTemplate 
implements IModel {
    private _state: ILicenseTemplate;

    
/* b::model_public_members */
// public members

    licenseFlags_: LicenseFlags;
    licenseFlags() {
        if (!this.licenseFlags_) {
            this.licenseFlags_ = new LicenseFlags(this._state.flags);
            this.licenseFlags_.onChange = ((flags: number) => {
                this._state.flags = flags
            });
        }
        return this.licenseFlags_;
    }
/* end */

    
    changed = false;

    constructor(state: ILicenseTemplate) {
        this.state(state);
    }

    isUndefined() {
        return this._state !== void 0;
    }

    async validate(skipUndefined?: boolean) {
        let v = new Validation(this, skipUndefined);
        


        return v;
    }

    rtt() {
        return "LicenseTemplate"; 
    }

    state (value?: ILicenseTemplate) {
        if (value !== undefined) { 
            this._state = value;
            
            


        }
        return this._state;
    }

    flags(value?: number) {
                if (value !== void 0) {
                    if (this.state().flags !== value) {
                        this.state().flags = value;
                        this.changed = true;
                    }
                }
                return this.state().flags;
            };

		modifiedTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().modifiedTime !== value) {
                        this.state().modifiedTime = value;
                        this.changed = true;
                    }
                }
                return this.state().modifiedTime;
            };

		createdTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().createdTime !== value) {
                        this.state().createdTime = value;
                        this.changed = true;
                    }
                }
                return this.state().createdTime;
            };

		templateId(value?: string) {
                if (value !== void 0) {
                    if (this.state().templateId !== value) {
                        this.state().templateId = value;
                        this.changed = true;
                    }
                }
                return this.state().templateId;
            };

		duration(value?: string) {
                if (value !== void 0) {
                    if (this.state().duration !== value) {
                        this.state().duration = value;
                        this.changed = true;
                    }
                }
                return this.state().duration;
            };

		uses(value?: number) {
                if (value !== void 0) {
                    if (this.state().uses !== value) {
                        this.state().uses = value;
                        this.changed = true;
                    }
                }
                return this.state().uses;
            };

		availableUpgrades(value?: string) {
                if (value !== void 0) {
                    if (this.state().availableUpgrades !== value) {
                        this.state().availableUpgrades = value;
                        this.changed = true;
                    }
                }
                return this.state().availableUpgrades;
            };

		displayName(value?: string) {
                if (value !== void 0) {
                    if (this.state().displayName !== value) {
                        this.state().displayName = value;
                        this.changed = true;
                    }
                }
                return this.state().displayName;
            };

		public(value?: boolean) {
                if (value !== void 0) {
                    if (this.state().public !== value) {
                        this.state().public = value;
                        this.changed = true;
                    }
                }
                return this.state().public;
            };

		weight(value?: number) {
                if (value !== void 0) {
                    if (this.state().weight !== value) {
                        this.state().weight = value;
                        this.changed = true;
                    }
                }
                return this.state().weight;
            };

		deviceLimit(value?: number) {
                if (value !== void 0) {
                    if (this.state().deviceLimit !== value) {
                        this.state().deviceLimit = value;
                        this.changed = true;
                    }
                }
                return this.state().deviceLimit;
            };

		photoLimit(value?: number) {
                if (value !== void 0) {
                    if (this.state().photoLimit !== value) {
                        this.state().photoLimit = value;
                        this.changed = true;
                    }
                }
                return this.state().photoLimit;
            };

		features(value?: number) {
                if (value !== void 0) {
                    if (this.state().features !== value) {
                        this.state().features = value;
                        this.changed = true;
                    }
                }
                return this.state().features;
            };

		cloudStorageLimit(value?: number) {
                if (value !== void 0) {
                    if (this.state().cloudStorageLimit !== value) {
                        this.state().cloudStorageLimit = value;
                        this.changed = true;
                    }
                }
                return this.state().cloudStorageLimit;
            };

    differs(original: LicenseTemplate) {
        return (
            (this.flags() !== void 0 && this.flags() !== original.flags())
		 || (this.modifiedTime() !== void 0 && this.modifiedTime() !== original.modifiedTime())
		 || (this.createdTime() !== void 0 && this.createdTime() !== original.createdTime())
		 || (this.templateId() !== void 0 && this.templateId() !== original.templateId())
		 || (this.duration() !== void 0 && this.duration() !== original.duration())
		 || (this.uses() !== void 0 && this.uses() !== original.uses())
		 || (this.availableUpgrades() !== void 0 && this.availableUpgrades() !== original.availableUpgrades())
		 || (this.displayName() !== void 0 && this.displayName() !== original.displayName())
		 || (this.public() !== void 0 && this.public() !== original.public())
		 || (this.weight() !== void 0 && this.weight() !== original.weight())
		 || (this.deviceLimit() !== void 0 && this.deviceLimit() !== original.deviceLimit())
		 || (this.photoLimit() !== void 0 && this.photoLimit() !== original.photoLimit())
		 || (this.features() !== void 0 && this.features() !== original.features())
		 || (this.cloudStorageLimit() !== void 0 && this.cloudStorageLimit() !== original.cloudStorageLimit())
        );
    }







}



export function sanitizeInput(source: LicenseTemplate, amdin: boolean, mode: string) : ILicenseTemplate;
export function sanitizeInput(source: ILicenseTemplate, admin: boolean, mode: string) : ILicenseTemplate;
export function sanitizeInput(source: LicenseTemplate | ILicenseTemplate, admin = false, mode="default"): ILicenseTemplate {
    let s: ILicenseTemplate;
    if (source instanceof LicenseTemplate)
        s = source.state();
    else
        s = source;        
    let t = {} as ILicenseTemplate;
    
    
        
            switch(mode) {
                
                
                
            }
            t.flags = s.flags;
        
    return t;
}

export function sanitizeOutput(source: LicenseTemplate, amdin: boolean) : ILicenseTemplate;
export function sanitizeOutput(source: ILicenseTemplate, admin: boolean) : ILicenseTemplate;
export function sanitizeOutput(source: LicenseTemplate | ILicenseTemplate, admin = false): ILicenseTemplate {
    let s: ILicenseTemplate;
    if (source instanceof LicenseTemplate)
        s = source.state();
    else
        s = source;        
    let t = {} as ILicenseTemplate;
    
    
    
    t.flags = s.flags;	
t.modifiedTime = s.modifiedTime;	
t.createdTime = s.createdTime;	
t.templateId = s.templateId;	
t.duration = s.duration;	
t.uses = s.uses;	
t.availableUpgrades = s.availableUpgrades;	
t.displayName = s.displayName;	
t.public = s.public;	
t.weight = s.weight;	
t.deviceLimit = s.deviceLimit;	
t.photoLimit = s.photoLimit;	
t.features = s.features;	
t.cloudStorageLimit = s.cloudStorageLimit;
    return t;
}

export function mergeState(dbVersion: ILicenseTemplate, newVersion: ILicenseTemplate) {
    let targetState: ILicenseTemplate = {};
    targetState.flags = newVersion.flags === undefined ? dbVersion.flags : newVersion.flags;
	targetState.modifiedTime = newVersion.modifiedTime === undefined ? dbVersion.modifiedTime : newVersion.modifiedTime;
	targetState.createdTime = newVersion.createdTime === undefined ? dbVersion.createdTime : newVersion.createdTime;
	targetState.templateId = newVersion.templateId === undefined ? dbVersion.templateId : newVersion.templateId;
	targetState.duration = newVersion.duration === undefined ? dbVersion.duration : newVersion.duration;
	targetState.uses = newVersion.uses === undefined ? dbVersion.uses : newVersion.uses;
	targetState.availableUpgrades = newVersion.availableUpgrades === undefined ? dbVersion.availableUpgrades : newVersion.availableUpgrades;
	targetState.displayName = newVersion.displayName === undefined ? dbVersion.displayName : newVersion.displayName;
	targetState.public = newVersion.public === undefined ? dbVersion.public : newVersion.public;
	targetState.weight = newVersion.weight === undefined ? dbVersion.weight : newVersion.weight;
	targetState.deviceLimit = newVersion.deviceLimit === undefined ? dbVersion.deviceLimit : newVersion.deviceLimit;
	targetState.photoLimit = newVersion.photoLimit === undefined ? dbVersion.photoLimit : newVersion.photoLimit;
	targetState.features = newVersion.features === undefined ? dbVersion.features : newVersion.features;
	targetState.cloudStorageLimit = newVersion.cloudStorageLimit === undefined ? dbVersion.cloudStorageLimit : newVersion.cloudStorageLimit;
    return targetState;
}

export function merge(dbVersion: LicenseTemplate, newVersion: LicenseTemplate) {
    return new LicenseTemplate(mergeState(dbVersion.state(), newVersion.state()));
}
