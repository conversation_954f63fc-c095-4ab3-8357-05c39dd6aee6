

    drop view if exists a0."InvitationLogEntry" cascade;

    create or replace view a0."InvitationLogEntry" as
    select
    flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		email,
		pin,
		accepted_date as "acceptedDate"
    from a0.invitation_log_entry;
    

drop function if exists a0.invitation_log_entry_create; 
        create function a0.invitation_log_entry_create(
            _flags int,
	_modified_time timestamptz,
	_created_time timestamptz,
	_account_id int,
	_email text,
	_pin text,
	_accepted_date timestamptz
        )
        returns a0."InvitationLogEntry"
        as $$
        
    declare
        result a0."InvitationLogEntry";
        
    begin
        
        


       
        


        
        
        
        
        _modified_time := coalesce(_modified_time, now());
        _created_time := coalesce(_created_time, now());
        insert into a0.invitation_log_entry (
            flags,
	modified_time,
	created_time,
	account_id,
	email,
	pin,
	accepted_date
        )
        values(
            _flags,
			_modified_time,
			_created_time,
			_account_id,
			_email,
			_pin,
			_accepted_date
        )
        returning
            flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		email,
		pin,
		accepted_date as "acceptedDate"
        into result;

        



        

    
        



        return result;
    end;
        $$
        language plpgsql;
        

drop function if exists a0.invitation_log_entry_update; 
        create function a0.invitation_log_entry_update(
            _flags int,
	_modified_time timestamptz,
	_created_time timestamptz,
	_account_id int,
	_email text,
	_pin text,
	_accepted_date timestamptz
        )
        returns a0."InvitationLogEntry"
        as $$
        
    declare
        result a0."InvitationLogEntry";
        
    begin
        
        


       
        


        
        
        _modified_time := now();
        update a0.invitation_log_entry
        set
            flags = _flags,
			modified_time = _modified_time,
			created_time = _created_time,
			accepted_date = _accepted_date
        where account_id = _account_id and email = _email and pin = _pin
        returning
            flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		email,
		pin,
		accepted_date as "acceptedDate"
        into result;

        

    
        



        return result;
    end;
        $$
        language plpgsql;
        

drop function if exists a0.invitation_log_entry_read_by_account_id_and_email_and_pin; 
        create function a0.invitation_log_entry_read_by_account_id_and_email_and_pin(
            _account_id int,
	_email text,
	_pin text
        )
        returns a0."InvitationLogEntry"
        as $$
        select
        flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		email,
		pin,
		accepted_date as "acceptedDate"
        from a0.invitation_log_entry
        where account_id = _account_id and email = _email and pin = _pin;
        $$
        language sql;
        

drop function if exists a0.invitation_log_entry_delete_by_account_id_and_email_and_pin; 
        create function a0.invitation_log_entry_delete_by_account_id_and_email_and_pin(
            _account_id int,
	_email text,
	_pin text
        )
        returns void
        as $$
        
    declare
        
        
    begin
        
        


       
        


        

    delete from a0.invitation_log_entry
    where account_id = _account_id and email = _email and pin = _pin;

    
    
        



        
    end;
        $$
        language plpgsql;
        

drop function if exists a0.invitation_log_entry_delete_by_account_id; 
        create function a0.invitation_log_entry_delete_by_account_id(
            _account_id int
        )
        returns void
        as $$
        
    declare
        
        
    begin
        
        


       
        


        

    delete from a0.invitation_log_entry
    where account_id = _account_id;

    
    
        



        
    end;
        $$
        language plpgsql;
        
