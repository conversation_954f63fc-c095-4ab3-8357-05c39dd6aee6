import _ = require("lodash");
import moment = require("moment");
import { Context } from "../system/Context.mjs";
import { config, getServices } from "../system/Config.mjs";
import { Account } from "../models/Account.model.mjs";
import { error } from "../system/error.mjs";
import { binaryEncoder } from "../system/bjson.cjs";

export interface ICertResponse {
  keyPEM: string;
  certificatePEM: string;
}

export class CertificateService {
  public static CreateSelfSignedRootForAccount(
    context: Context,
    account: Account
  ): { keyPEM: string; certificatePEM: string } {
    return binaryEncoder.generate_account_certificate(account.state());
  }
}
