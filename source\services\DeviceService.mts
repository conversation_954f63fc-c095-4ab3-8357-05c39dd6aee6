import moment = require("moment");
import crypto = require("crypto");

import { Device, merge, DeviceType } from "../models/Device.model.mjs";
import { DeviceData } from "../models/DeviceData.model.mjs";
import { error } from "../system/error.mjs";
import { DeviceRestDataService } from "../dataServices/Device.rest.dataService.mjs";
import { Context } from "../system/Context.mjs";
import { Itx } from "../system/data.mjs";
import { check } from "../system/check.mjs";
import { ids } from "../system/Strings.mjs";
import { binaryEncoder } from "../system/bjson.cjs";
import { DeviceDataRestDataService } from "../dataServices/DeviceData.rest.dataService.mjs";
import { SupportTicket } from "../models/SupportTicket.model.mjs";

enum SupportTicketLogLevel {
  None = 0,
  Basic = 0x0001,
  Extended = 0x0002,
  All = 0xffff,
}

interface DeviceAndData {
  device: Device;
  data: DeviceData;
}

/*
export interface ISupportTicket {
    subject: string;
    comments: string;
    sendScreenshot: boolean;
    includeCatalog: boolean;
    logLevel: "none" | "basic" | "all";
    logLevelFlags: SupportTicketLogLevel;
    requestId: string;
    consoleCommands: string;
    ttl: number;
}
*/

export class DeviceService {
  constructor(
    private dataService: DeviceRestDataService,
    private deviceDataService: DeviceDataRestDataService,
    private tx: Itx<Device>
  ) {}

  public listDeviceDataForAccount(context: Context, accountId: number) {
    return this.deviceDataService
      .findByAccountId(context, accountId)
      .then((deviceData) => this.deviceDataBase64ToHex(context, deviceData));
  }

  public readDeviceData(context: Context, accountId: number, deviceId: number) {
    return this.deviceDataService
      .findByAccountIdAndDeviceId(context, accountId, deviceId)
      .then((deviceData) => this.deviceDataBase64ToHex(context, deviceData))
      .then((deviceData) => deviceData[0]);
  }

  public newId(context: Context, aid: number) {
    return this.dataService.newId(context, aid);
  }

  public reserve(context: Context, aid: number, dType: DeviceType) {
    return this.dataService.reserve(context, aid, dType);
  }

  public async create(context: Context, aid: number, device: Device) {
    device.accountId(aid);
    await check(device);
    if (!device.creationTime()) {
      device.creationTime(new Date());
    }
    return this.dataService.create(context, device);
  }

  async list(context: Context, aid: number) {
    let data = await this.deviceDataService.findByAccountId(context, aid);
    let devices = await this.dataService.findByAccountId(context, aid);
    let result = new Array<{ device: Device; data: DeviceData }>();
    for (let device of devices) {
      let datum = data.find((dd) => dd.deviceId() === device.deviceId());
      result.push({ device, data: datum });
    }
    return result;
  }

  public read(context: Context, aid: number, did: number) {
    return this.dataService
      .readByAccountIdAndDeviceId(context, aid, did)
      .then((device) => {
        if (!device) {
          return error<Device>(404, ids.DEVICE_NOT_VALID_FOR_ACCOUNT);
        }
        return device;
      }) as Promise<Device>;
  }

  public async update(context: Context, aid: number, device: Device) {
    return this.tx(context, async () => {
      let dbVersion = await this.dataService.readByAccountIdAndDeviceId(
        context,
        device.accountId(),
        device.deviceId()
      );
      device = merge(dbVersion, device);
      await check(device);
      return this.dataService.update(context, device);
    });
  }

  public delete(context: Context, aid: number, did: number) {
    let deviceToDelete = new Device({
      accountId: aid,
      deviceId: did,
      deleted: true,
    });

    return this.update(context, aid, deviceToDelete);
  }

  public hardDelete(context: Context, aid: number, did: number) {
    return this.dataService.deleteByAccountIdAndDeviceId(context, aid, did);
  }

  public findByType(context: Context, aid: number, type: number) {
    return this.dataService.findByAccountIdAndDeviceType(context, aid, type);
  }

  public createCloudDevice(context, aid: number) {
    let device = new Device({
      accountId: aid,
      deleted: false,
      deviceType: DeviceType.Cloud,
      name: "Cloud",
      nickname: "Cloud",
      encrypt: false,
      creationTime: new Date(),
    });

    return this.create(context, aid, device);
  }

  async createSupportTicket(
    context: Context,
    aid: number,
    did: number,
    ticket: SupportTicket
  ) {
    let device = await this.read(context, aid, did);
    switch (ticket.logLevel()) {
      case "all":
        ticket.logLevelFlags(SupportTicketLogLevel.All);
        break;
      case "basic":
        ticket.logLevelFlags(SupportTicketLogLevel.Basic);
        break;
      default:
        ticket.logLevelFlags(SupportTicketLogLevel.None);
    }
    ticket.ttl(moment().add(7, "days").diff(moment("2012-01-01"), "seconds"));
    ticket.requestId(crypto.randomBytes(20).toString("base64"));
    let unencoded = {
      ...ticket.state(),
      consoleCommands: ticket.consoleCommandString()?.split("\n"),
    };
    const buffer: Buffer = binaryEncoder.encode_support_ticket(unencoded);
    device.supportTicket(buffer.toString("base64"));
    return this.update(context, aid, device);
  }

  async createSupportTicketsForDevices(
    context: Context,
    aid: number,
    ticket: SupportTicket
  ) {
    let dnds = await this.list(context, aid);
    let validDnds = dnds.filter((dd) => {
      if (dd.device.deleted()) return false;
      switch (dd.device.deviceType()) {
        case DeviceType.NAS:
        case DeviceType.PowerShell:
        case DeviceType.RemovableDevice:
        case DeviceType.AmazonDrive:
        case DeviceType.GoogleDrive:
        case DeviceType.Cloud:
          return false;
        default:
          return true;
      }
    });
    for (let dnd of validDnds) {
      await this.createSupportTicket(
        context,
        aid,
        dnd.device.deviceId(),
        ticket
      );
    }
  }

  private deviceDataBase64ToHex(context: Context, deviceData: DeviceData[]) {
    return deviceData.map((dd) => {
      const t = dd.t();
      const d = dd.d();

      if (t) {
        dd.t(Buffer.from(t, "base64").toString("hex"));
      }
      if (d) {
        dd.d(Buffer.from(d, "base64").toString("hex"));
      }

      return dd;
    });
  }
}
