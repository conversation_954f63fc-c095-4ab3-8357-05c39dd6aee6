#ifndef JSON_CONFIG_H_INCLUDED
#define JSON_CONFIG_H_INCLUDED

/// If defined, indicates that json library is embedded in CppTL library.
// # define JSON_IN_CPPTL 1

/// If defined, indicates that json may leverage CppTL library
// #  define JSON_USE_CPPTL 1
/// If defined, indicates that cpptl vector based map should be used instead of std::map
/// as Value container.
// #  define JSON_USE_CPPTL_SMALLMAP 1
/// If defined, indicates that Json specific container should be used
/// (hash table & simple deque container with customizable allocator).
/// THIS FEATURE IS STILL EXPERIMENTAL!
// #  define JSON_VALUE_USE_INTERNAL_MAP 1
/// Force usage of standard new/malloc based allocator instead of memory pool based allocator.
/// The memory pools allocator used optimization (initializing Value and ValueInternalLink
/// as if it was a POD) that may cause some validation tool to report errors.
/// Only has effects if JSON_VALUE_USE_INTERNAL_MAP is defined.
// #  define JSON_USE_SIMPLE_INTERNAL_ALLOCATOR 1

/// If defined, indicates that <PERSON><PERSON> use exception to report invalid type manipulation
/// instead of C assert macro.
#define JSON_USE_EXCEPTION 1

#ifdef JSON_IN_CPPTL
#include <cpptl/config.h>
#ifndef JSON_USE_CPPTL
#define JSON_USE_CPPTL 1
#endif
#endif

#ifdef JSON_IN_CPPTL
#define JSON_API CPPTL_API
#elif defined(JSON_DLL_BUILD)
#define JSON_API __declspec(dllexport)
#elif defined(JSON_DLL)
#define JSON_API __declspec(dllimport)
#else
#define JSON_API
#endif

#endif // JSON_CONFIG_H_INCLUDED
