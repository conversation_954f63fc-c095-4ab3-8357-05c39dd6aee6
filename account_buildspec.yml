version: 0.2

phases:
  install:
    commands: 
      - n 20.18.0
      
  pre_build:
    commands:
      - echo Logging in to Amazon ECR...
      - aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin $ECR_REGISTRY
      - COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)
      - echo "ECR Registry = $ECR_REGISTRY"
      - echo "ECR Repository = $ECR_REPOSITORY"
      - echo "Full Image Path = $ECR_REGISTRY/$ECR_REPOSITORY"
      - |
        if [ ! -z "$CODEBUILD_WEBHOOK_HEAD_REF" ]; then
          # Extract MYLO-XXXXX from branch name
          TICKET_NUMBER=$(echo $CODEBUILD_WEBHOOK_HEAD_REF | grep -o 'MYLO-[0-9]\+')
          if [ ! -z "$TICKET_NUMBER" ]; then
            ENVIRONMENT="${TICKET_NUMBER}"
          else
            echo "Error: Branch name must contain MY<PERSON><PERSON> ticket number (e.g., MYLO-60248)"
            exit 1
          fi
        else
          ENVIRONMENT="test"
        fi
      - echo "Selected environment = $ENVIRONMENT"
      - echo "Prebuild complete"
  
  build:
    commands:
      - echo Build started on `date`
      - node --version
      - npm install
      - npm run build
      - chmod +x scripts/*
      - ./scripts/_.publish.ecs.sh ebs account test 0
      - ls -al
      - cd dist
      - echo Building Docker image...
      - docker build --platform=linux/arm64 -t $ECR_REGISTRY/$ECR_REPOSITORY:${ENVIRONMENT}-${COMMIT_HASH} .
      - docker tag $ECR_REGISTRY/$ECR_REPOSITORY:${ENVIRONMENT}-${COMMIT_HASH} $ECR_REGISTRY/$ECR_REPOSITORY:${ENVIRONMENT}-latest
      - echo "Build completed!"
  
  
  post_build:
    commands:
      - echo Pushing Docker images to ECR...
      - docker push $ECR_REGISTRY/$ECR_REPOSITORY:${ENVIRONMENT}-${COMMIT_HASH}
      - docker push $ECR_REGISTRY/$ECR_REPOSITORY:${ENVIRONMENT}-latest
      - echo Build completed on `date`
      - echo "Image URI - ${ECR_REGISTRY}/${ECR_REPOSITORY}:${ENVIRONMENT}-${COMMIT_HASH}"
      - cd ..
      - |
        if [ "$ENVIRONMENT" = "test" ]; then
          CONTAINER_NAME="account-test-0"
        else
          CONTAINER_NAME="account-production-0"
        fi
      - printf '[{"name":"%s","imageUri":"%s"}]' "$CONTAINER_NAME" "${ECR_REGISTRY}/${ECR_REPOSITORY}:${ENVIRONMENT}-latest" > imagedefinitions.json
      - cat imagedefinitions.json


artifacts:
  files:
    - imagedefinitions.json
  base-directory: .