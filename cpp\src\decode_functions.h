#include <node.h>

Napi::Object decode_account_ping_request(Napi::Env env, const Napi::<PERSON>uff<PERSON><uint8_t> &bjson);
Napi::Object decode_account_ping_response(Napi::Env env, const Napi::<PERSON>uff<PERSON><uint8_t> &bjson);
Napi::Object decode_resource_ping_request(Napi::Env env, const Napi::Buffer<uint8_t> &jsObj);
Napi::Object decode_resource_ping_response(Napi::Env env, const Napi::Buffer<uint8_t> &jsObj);
Napi::Object decode_ping256(Napi::Env env, const Napi::Buffer<uint8_t> &bjson);
Napi::Object decode_sync(Napi::Env env, const Napi::Buffer<uint8_t> &bjson);
Napi::Object decode_rtoken_request(Napi::Env env, const Napi::Buffer<uint8_t> &bjson);
Napi::Object decode_object(Napi::Env env, const Napi::Buffer<uint8_t> &bjson, int sectionId);
Napi::Object decode_client_subscription_request(Napi::Env env, const Napi::Buffer<uint8_t> &bjson);
Napi::Buffer<uint8_t> encode_client_subscription_request(Napi::Env env, const Napi::Object &jsObj);
Napi::Object decode_support_ticket(Napi::Env env, const Napi::Buffer<uint8_t> &bjson);
