

import Validation from "../system/Validation.mjs";
import { IModel } from "./IModel.mjs";

/* b::imports */
import { LicenseFlags } from "./LicenseFlags.mjs";
import { FeatureBits } from "./FeatureBits.mjs";
import { Context } from "../system/Context.mjs";
import { makeError } from "../system/error.mjs";
/* end */


/* b::enums */
export enum Manager {
    FastSpring = "F",
    Apple = "A",
    Mylio = "M",
    TestPaid = "P",
    Stripe = "S"
}

export enum LicenseStatus {
    Active = 1,
    Deleted = 2,
    Canceled = 3,
    Trial = 4
}
/* end */


export interface ILicense {
    flags?: number;
	modifiedTime?: Date;
	createdTime?: Date;
	licenseId?: string;
	accountId?: number;
	manager?: string;
	endDate?: Date;
	templateId?: string;
	activationKey?: string;
	deviceLimit?: number;
	photoLimit?: number;
	features?: number;
	cloudStorageLimit?: number;
	availableUpgrades?: string;
	status?: number;
}


export class License 
implements IModel {
    private _state: ILicense;

    
/* b::model_public_members */
static makeId(context: Context, license: License): string {
        return `${license.manager()}.${crypto.randomUUID()}.NOKEY.0`;
    }

    // public members

    private featureBits_: FeatureBits;
    featureBits() {
        if (!this.featureBits_) {
            this.featureBits_ = new FeatureBits(this._state.features);
            this.featureBits_.onChange = ((value: number) => {
                this._state.features = value
            });
        }
        return this.featureBits_;
    }

    licenseFlags_: LicenseFlags;
    licenseFlags() {
        if (!this.licenseFlags_) {
            this.licenseFlags_ = new LicenseFlags(this._state.flags);
            this.licenseFlags_.onChange = ((flags: number) => {
                this._state.flags = flags
            });
        }
        return this.licenseFlags_;
    }

    deleted() {
        if (!this.state().status)
            throw makeError(400, "License status is not set");
        return this.status() === LicenseStatus.Deleted;
    };
/* end */

    
    changed = false;

    constructor(state: ILicense) {
        this.state(state);
    }

    isUndefined() {
        return this._state !== void 0;
    }

    async validate(skipUndefined?: boolean) {
        let v = new Validation(this, skipUndefined);
        


        return v;
    }

    rtt() {
        return "License"; 
    }

    state (value?: ILicense) {
        if (value !== undefined) { 
            this._state = value;
            
            


        }
        return this._state;
    }

    flags(value?: number) {
                if (value !== void 0) {
                    if (this.state().flags !== value) {
                        this.state().flags = value;
                        this.changed = true;
                    }
                }
                return this.state().flags;
            };

		modifiedTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().modifiedTime !== value) {
                        this.state().modifiedTime = value;
                        this.changed = true;
                    }
                }
                return this.state().modifiedTime;
            };

		createdTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().createdTime !== value) {
                        this.state().createdTime = value;
                        this.changed = true;
                    }
                }
                return this.state().createdTime;
            };

		licenseId(value?: string) {
                if (value !== void 0) {
                    if (this.state().licenseId !== value) {
                        this.state().licenseId = value;
                        this.changed = true;
                    }
                }
                return this.state().licenseId;
            };

		accountId(value?: number) {
                if (value !== void 0) {
                    if (this.state().accountId !== value) {
                        this.state().accountId = value;
                        this.changed = true;
                    }
                }
                return this.state().accountId;
            };

		manager(value?: string) {
                if (value !== void 0) {
                    if (this.state().manager !== value) {
                        this.state().manager = value;
                        this.changed = true;
                    }
                }
                return this.state().manager;
            };

		endDate(value?: Date) {
                if (value !== void 0) {
                    if (this.state().endDate !== value) {
                        this.state().endDate = value;
                        this.changed = true;
                    }
                }
                return this.state().endDate;
            };

		templateId(value?: string) {
                if (value !== void 0) {
                    if (this.state().templateId !== value) {
                        this.state().templateId = value;
                        this.changed = true;
                    }
                }
                return this.state().templateId;
            };

		activationKey(value?: string) {
                if (value !== void 0) {
                    if (this.state().activationKey !== value) {
                        this.state().activationKey = value;
                        this.changed = true;
                    }
                }
                return this.state().activationKey;
            };

		deviceLimit(value?: number) {
                if (value !== void 0) {
                    if (this.state().deviceLimit !== value) {
                        this.state().deviceLimit = value;
                        this.changed = true;
                    }
                }
                return this.state().deviceLimit;
            };

		photoLimit(value?: number) {
                if (value !== void 0) {
                    if (this.state().photoLimit !== value) {
                        this.state().photoLimit = value;
                        this.changed = true;
                    }
                }
                return this.state().photoLimit;
            };

		features(value?: number) {
                if (value !== void 0) {
                    if (this.state().features !== value) {
                        this.state().features = value;
                        this.changed = true;
                    }
                }
                return this.state().features;
            };

		cloudStorageLimit(value?: number) {
                if (value !== void 0) {
                    if (this.state().cloudStorageLimit !== value) {
                        this.state().cloudStorageLimit = value;
                        this.changed = true;
                    }
                }
                return this.state().cloudStorageLimit;
            };

		availableUpgrades(value?: string) {
                if (value !== void 0) {
                    if (this.state().availableUpgrades !== value) {
                        this.state().availableUpgrades = value;
                        this.changed = true;
                    }
                }
                return this.state().availableUpgrades;
            };

		status(value?: number) {
                if (value !== void 0) {
                    if (this.state().status !== value) {
                        this.state().status = value;
                        this.changed = true;
                    }
                }
                return this.state().status;
            };

    differs(original: License) {
        return (
            (this.flags() !== void 0 && this.flags() !== original.flags())
		 || (this.modifiedTime() !== void 0 && this.modifiedTime() !== original.modifiedTime())
		 || (this.createdTime() !== void 0 && this.createdTime() !== original.createdTime())
		 || (this.licenseId() !== void 0 && this.licenseId() !== original.licenseId())
		 || (this.accountId() !== void 0 && this.accountId() !== original.accountId())
		 || (this.manager() !== void 0 && this.manager() !== original.manager())
		 || (this.endDate() !== void 0 && this.endDate() !== original.endDate())
		 || (this.templateId() !== void 0 && this.templateId() !== original.templateId())
		 || (this.activationKey() !== void 0 && this.activationKey() !== original.activationKey())
		 || (this.deviceLimit() !== void 0 && this.deviceLimit() !== original.deviceLimit())
		 || (this.photoLimit() !== void 0 && this.photoLimit() !== original.photoLimit())
		 || (this.features() !== void 0 && this.features() !== original.features())
		 || (this.cloudStorageLimit() !== void 0 && this.cloudStorageLimit() !== original.cloudStorageLimit())
		 || (this.availableUpgrades() !== void 0 && this.availableUpgrades() !== original.availableUpgrades())
		 || (this.status() !== void 0 && this.status() !== original.status())
        );
    }







}



export function sanitizeInput(source: License, amdin: boolean, mode: string) : ILicense;
export function sanitizeInput(source: ILicense, admin: boolean, mode: string) : ILicense;
export function sanitizeInput(source: License | ILicense, admin = false, mode="default"): ILicense {
    let s: ILicense;
    if (source instanceof License)
        s = source.state();
    else
        s = source;        
    let t = {} as ILicense;
    
    
        
            switch(mode) {
                
                
                
            }
            t.flags = s.flags;
		t.licenseId = s.licenseId;
		t.accountId = s.accountId;
		t.manager = s.manager;
		t.endDate = s.endDate;
		t.templateId = s.templateId;
		t.activationKey = s.activationKey;
		t.deviceLimit = s.deviceLimit;
		t.photoLimit = s.photoLimit;
		t.features = s.features;
		t.cloudStorageLimit = s.cloudStorageLimit;
		t.availableUpgrades = s.availableUpgrades;
		t.status = s.status;
        
    return t;
}

export function sanitizeOutput(source: License, amdin: boolean) : ILicense;
export function sanitizeOutput(source: ILicense, admin: boolean) : ILicense;
export function sanitizeOutput(source: License | ILicense, admin = false): ILicense {
    let s: ILicense;
    if (source instanceof License)
        s = source.state();
    else
        s = source;        
    let t = {} as ILicense;
    
    
    
    t.flags = s.flags;	
t.modifiedTime = s.modifiedTime;	
t.createdTime = s.createdTime;	
t.licenseId = s.licenseId;	
t.accountId = s.accountId;	
t.manager = s.manager;	
t.endDate = s.endDate;	
t.templateId = s.templateId;	
t.activationKey = s.activationKey;	
t.deviceLimit = s.deviceLimit;	
t.photoLimit = s.photoLimit;	
t.features = s.features;	
t.cloudStorageLimit = s.cloudStorageLimit;	
t.availableUpgrades = s.availableUpgrades;	
t.status = s.status;
    return t;
}

export function mergeState(dbVersion: ILicense, newVersion: ILicense) {
    let targetState: ILicense = {};
    targetState.flags = newVersion.flags === undefined ? dbVersion.flags : newVersion.flags;
	targetState.modifiedTime = newVersion.modifiedTime === undefined ? dbVersion.modifiedTime : newVersion.modifiedTime;
	targetState.createdTime = newVersion.createdTime === undefined ? dbVersion.createdTime : newVersion.createdTime;
	targetState.licenseId = newVersion.licenseId === undefined ? dbVersion.licenseId : newVersion.licenseId;
	targetState.accountId = newVersion.accountId === undefined ? dbVersion.accountId : newVersion.accountId;
	targetState.manager = newVersion.manager === undefined ? dbVersion.manager : newVersion.manager;
	targetState.endDate = newVersion.endDate === undefined ? dbVersion.endDate : newVersion.endDate;
	targetState.templateId = newVersion.templateId === undefined ? dbVersion.templateId : newVersion.templateId;
	targetState.activationKey = newVersion.activationKey === undefined ? dbVersion.activationKey : newVersion.activationKey;
	targetState.deviceLimit = newVersion.deviceLimit === undefined ? dbVersion.deviceLimit : newVersion.deviceLimit;
	targetState.photoLimit = newVersion.photoLimit === undefined ? dbVersion.photoLimit : newVersion.photoLimit;
	targetState.features = newVersion.features === undefined ? dbVersion.features : newVersion.features;
	targetState.cloudStorageLimit = newVersion.cloudStorageLimit === undefined ? dbVersion.cloudStorageLimit : newVersion.cloudStorageLimit;
	targetState.availableUpgrades = newVersion.availableUpgrades === undefined ? dbVersion.availableUpgrades : newVersion.availableUpgrades;
	targetState.status = newVersion.status === undefined ? dbVersion.status : newVersion.status;
    return targetState;
}

export function merge(dbVersion: License, newVersion: License) {
    return new License(mergeState(dbVersion.state(), newVersion.state()));
}
