{
  // See https://go.microsoft.com/fwlink/?LinkId=733558
  // for the documentation about the tasks.json format
  "version": "2.0.0",
  "command": "npm",
  "tasks": [
    {
      "label": "build",
      "type": "shell",
      "args": ["run", "build"],
      "problemMatcher": [],
      "group": "build"
    },
    {
      "label": "full build",
      "type": "shell",
      "args": ["run", "build:full"],
      "problemMatcher": []
    },
    {
      "type": "npm",
      "script": "build",
      "group": "build",
      "problemMatcher": [],
      "label": "npm: build",
      "detail": "cd ./scripts && bash ./build.sh && cd .."
    },
    {
      "type": "npm",
      "script": "build-fast",
      "group": {
        "kind": "build",
        "isDefault": true
      },
      "problemMatcher": [],
      "label": "npm: build-fast",
      "detail": "cd ./scripts && bash ./build.fast.sh && cd ..",
      "runOptions": {
        "runOn": "default"
      }
    }
  ]
}
