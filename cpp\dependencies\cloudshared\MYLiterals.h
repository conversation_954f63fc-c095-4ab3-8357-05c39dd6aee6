#if !defined(_MYLITERALS_H) || defined(DEFINE_LITERALS)

#if !defined(_MYLITERALS_H)
#define _MYLITERALS_H
#endif

#ifndef DEFINE_LITERALS

#include <vector>

#define DECLARE_LITERAL(name, id) static const int name = id;

struct MYLiterals
{
#endif // DEFINE_LITERALS

#include "MYTypedLiterals.h"

    struct DeviceFile
    {
        DECLARE_LITERAL(deviceId, 0);
        DECLARE_LITERAL(version, 1);
        DECLARE_LITERAL(dataHash, 2);
        DECLARE_LITERAL(visualEditHash, 3);
        DECLARE_LITERAL(basisDataHash, 4);
        DECLARE_LITERAL(inInternalDataStorage, 5);
        DECLARE_LITERAL(isDraft, 6);
        DECLARE_LITERAL(cropZoomFactor, 7);
        DECLARE_LITERAL(alUrl, 8);
        DECLARE_LITERAL(fileNameNoExt, 9);
        DECLARE_LITERAL(parseHash, 10);
        DECLARE_LITERAL(parsability, 11);
        DECLARE_LITERAL(hardWant, 12);
    };

    struct File
    {
        DECLARE_LITERAL(fileType, 0);
        DECLARE_LITERAL(format, 1);
        DECLARE_LITERAL(originalDataHash, 2);
        DECLARE_LITERAL(fileSize, 3);
        DECLARE_LITERAL(fileDateTime, 4);
        DECLARE_LITERAL(orientation, 5);
        DECLARE_LITERAL(width, 6);
        DECLARE_LITERAL(height, 7);
        DECLARE_LITERAL(isGenerated, 8);
        DECLARE_LITERAL(legacy_genVersion, 9);
    };

    struct Files
    {
        DECLARE_LITERAL(media, 1);
        DECLARE_LITERAL(files, 2);
        DECLARE_LITERAL(hashMap, 3);
        DECLARE_LITERAL(stringMap, 4);
        DECLARE_LITERAL(visualEditHash, 5);
        DECLARE_LITERAL(orientation, 6);
    };

    struct LocalFile
    {
        DECLARE_LITERAL(dataHash, 0);
        DECLARE_LITERAL(visualEditHash, 1);
        DECLARE_LITERAL(basisDataHash, 2);
        DECLARE_LITERAL(parseHash, 3);
        DECLARE_LITERAL(flags, 4);
        DECLARE_LITERAL(cropZoomFactor, 5);
        DECLARE_LITERAL(format, 6);
        DECLARE_LITERAL(alUrl, 7);
        DECLARE_LITERAL(byocId, 8);
        DECLARE_LITERAL(fileSize, 9);
        DECLARE_LITERAL(width, 10);
        DECLARE_LITERAL(height, 11);
        DECLARE_LITERAL(genVersion, 12);
    };

    struct BucketLocalFiles
    {
        DECLARE_LITERAL(mediaMap, 7);
        DECLARE_LITERAL(hashMap, 6);
        DECLARE_LITERAL(stringMap, 5);
    };

    struct Needs
    {
        DECLARE_LITERAL(deviceId, 0)
        DECLARE_LITERAL(values, 1);
    };

    struct DeviceWantsPerFormat
    {
        DECLARE_LITERAL(format, 0);
        DECLARE_LITERAL(wantsHasCanGenerate, 1);
    };

    struct NetworkNodeData
    {
        DECLARE_LITERAL(rootFolders, 0);
        DECLARE_LITERAL(volumes, 1);
        DECLARE_LITERAL(internalDataDir, 2);
        DECLARE_LITERAL(mylioDir, 3);
        DECLARE_LITERAL(uniqueHash, 4);
        DECLARE_LITERAL(localName, 5);
        DECLARE_LITERAL(localPath, 6);
        DECLARE_LITERAL(name, 7);
        DECLARE_LITERAL(totalSpace, 8);
        DECLARE_LITERAL(freeSpace, 9);
        DECLARE_LITERAL(alUrl, 10)
    };

    DECLARE_LITERAL(request, 1);
    DECLARE_LITERAL(response, 2);
    DECLARE_LITERAL(devicestatus, 3);

    DECLARE_LITERAL(data, 5);
    DECLARE_LITERAL(have, 6);
    DECLARE_LITERAL(want, 7);

    DECLARE_LITERAL(rev, 9);

    DECLARE_LITERAL(output, 10);
    DECLARE_LITERAL(rows, 11);
    DECLARE_LITERAL(error, 12);
    DECLARE_LITERAL(taskId, 13);
    DECLARE_LITERAL(result, 14);
    DECLARE_LITERAL(success, 15);
    DECLARE_LITERAL(roots, 16);
    DECLARE_LITERAL(root, 17);

    // Search for OBJECT_REPLICATION to get all these
    DECLARE_LITERAL(devicedata, 20);
    DECLARE_LITERAL(devices, 21);
    DECLARE_LITERAL(systemProperties, 22);
    DECLARE_LITERAL(userProperties, 23);
    DECLARE_LITERAL(messages, 24);
    DECLARE_LITERAL(resources, 25);
    DECLARE_LITERAL(accounts, 26);

    DECLARE_LITERAL(jsoncatalog, 30);
    DECLARE_LITERAL(hashCatalog_OBSOLETE, 31);
    DECLARE_LITERAL(filesProcessed, 32);
    DECLARE_LITERAL(filesToProcess, 33);
    DECLARE_LITERAL(isuptodate, 34);
    DECLARE_LITERAL(isatquota, 35);
    DECLARE_LITERAL(isidle, 36);
    DECLARE_LITERAL(catalogProcessed, 37);
    DECLARE_LITERAL(catalogToProcess, 38);
    DECLARE_LITERAL(bulkReplicationMode, 39);

    DECLARE_LITERAL(signature, 40);
    DECLARE_LITERAL(deviceid, 41);
    DECLARE_LITERAL(devicetype, 42);
    DECLARE_LITERAL(challenge, 43);
    DECLARE_LITERAL(protocol_flags, 44);
    DECLARE_LITERAL(authenticated, 45);
    DECLARE_LITERAL(dbgHttpPort, 46);
    DECLARE_LITERAL(canReplicateWithRating, 47);
    DECLARE_LITERAL(zombie, 48);

    DECLARE_LITERAL(mediahash, 50);
    DECLARE_LITERAL(mediafiletype, 51);
    DECLARE_LITERAL(offset, 52);
    DECLARE_LITERAL(datahash, 53);
    DECLARE_LITERAL(priority, 54);
    DECLARE_LITERAL(resumingdatahash, 55);
    DECLARE_LITERAL(files, 56);
    DECLARE_LITERAL(filetype, 58);
    DECLARE_LITERAL(streamoffset, 59);

    DECLARE_LITERAL(cmd, 60);
    DECLARE_LITERAL(sql, 61);
    DECLARE_LITERAL(hashgroups, 62);
    DECLARE_LITERAL(itemType, 63);
    DECLARE_LITERAL(endPoint, 64);
    DECLARE_LITERAL(payload, 65);

    DECLARE_LITERAL(aid, 70);
    DECLARE_LITERAL(key, 71);
    DECLARE_LITERAL(secret, 72);
    DECLARE_LITERAL(token, 73);
    DECLARE_LITERAL(refreshtoken, 74);
    DECLARE_LITERAL(services, 75);
    DECLARE_LITERAL(name, 76);
    DECLARE_LITERAL(uri, 77);
    DECLARE_LITERAL(legacyAccountId, 78);

    DECLARE_LITERAL(encrypt, 80);
    DECLARE_LITERAL(sub, 81);
    DECLARE_LITERAL(flags, 82);
    DECLARE_LITERAL(email, 83);
    DECLARE_LITERAL(password, 84);
    DECLARE_LITERAL(idp, 85);
    DECLARE_LITERAL(role, 86);
    DECLARE_LITERAL(planId, 87);
    DECLARE_LITERAL(rtoken, 88);
    DECLARE_LITERAL(codeVerifier, 89);
    DECLARE_LITERAL(codeChallenge, 90);
    DECLARE_LITERAL(pin, 91);
    DECLARE_LITERAL(accountPrivateKey, 92);
    DECLARE_LITERAL(accountCert, 94);

    DECLARE_LITERAL(value, 89);

    DECLARE_LITERAL(initialoffset, 90);
    DECLARE_LITERAL(filesize, 91);
    DECLARE_LITERAL(width, 92);
    DECLARE_LITERAL(previewformat, 93);
    DECLARE_LITERAL(height, 94);
    DECLARE_LITERAL(localbasisdatahash, 95);
    DECLARE_LITERAL(localvisualedithash, 96);
    DECLARE_LITERAL(localcropzoomfactor, 97);
    DECLARE_LITERAL(isdraft, 98);
    DECLARE_LITERAL(fileresponse, 99);

    DECLARE_LITERAL(slipStreamThumb, 100);
    DECLARE_LITERAL(CICM, 101);

    DECLARE_LITERAL(sync_ResourceSyncEnabled, 102);
    DECLARE_LITERAL(disabledDevices, 103);
    DECLARE_LITERAL(hostDeviceId, 104);

    DECLARE_LITERAL(supportTicket_takeScreenShot, 110);
    DECLARE_LITERAL(supportTicket_includeCatalog, 111);
    DECLARE_LITERAL(supportTicket_additionalFile, 112);
    DECLARE_LITERAL(supportTicket_crashFile, 113);
    DECLARE_LITERAL(supportTicket_logs, 114);
    DECLARE_LITERAL(supportTicket_subject, 115);
    DECLARE_LITERAL(supportTicket_commentsToSend, 116);
    DECLARE_LITERAL(supportTicket_requestId, 117);
    DECLARE_LITERAL(supportTicket_requestLogs, 118);
    DECLARE_LITERAL(supportTicket_isFromWebsite, 119);
    DECLARE_LITERAL(supportTicket_consoleCommands, 120);
    DECLARE_LITERAL(supportTicket_validUntilTime, 121);

    DECLARE_LITERAL(accountCreated, 143);

    DECLARE_LITERAL(trev, 0);
    DECLARE_LITERAL(rid, 1);

    DECLARE_LITERAL(raw, 1);
    DECLARE_LITERAL(nonRaw, 2);
    DECLARE_LITERAL(video, 3);
    DECLARE_LITERAL(displayImage, 4);
    DECLARE_LITERAL(xmp, 5);
    DECLARE_LITERAL(preview, 6);
    DECLARE_LITERAL(thumbnail, 7);

#ifndef DEFINE_LITERALS
};
#endif // DEFINE_LITERALS

#endif // _MYLITERALS_H
