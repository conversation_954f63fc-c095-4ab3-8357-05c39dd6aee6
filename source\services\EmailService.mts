import _ = require("lodash");
import path = require("path");
import moment = require("moment");
import { Account } from "../models/Account.model.mjs";
import { config, getServices } from "../system/Config.mjs";
import { Context } from "../system/Context.mjs";
import { TokenService } from "./TokenService.mjs";
import { error } from "../system/error.mjs";
import { ids } from "../system/Strings.mjs";
import { post, RestClient, RestClientMimeType } from "../system/RestClient.mjs";

export const EmailTemplateId = {
  resetPasswordTmpl: "d-6ac408397cc246fcbde6703a25f7241d",
  resetPasswordIdpTmpl: "d-a59801bab44b4be9b8f0442937244b5c",
  changeEmailTmpl: "d-8c8642b6ada84d28b51d2cadd3aa74d7",
  changePasswordTmpl: "d-3c8cc9515f3b4450bb76dcd31f518d7d",
  deviceAddedTmpl: "d-1f7d93f38c5a438e82580ba22af1acc7",
  magicLinkTmpl: "d-d40cb43e12fa4e03b40233c8158808b4",
  createLinkTmpl: "d-c862a05bffdc478f94801bccdafeb858",
  loginLinkTmpl: "d-48929951b19644b6b8704ab383ba7e63",
  pinTmpl: "d-48929951b19644b6b8704ab383ba7e63",
  ssoToPinLinkTmpl: "d-eac892c068f74d508d4d5ed15513b25c",
  invitationTmpl: "d-afe8dfdfd957412aa396548e501d15ff"
};

export interface AccountPlanChange {
  email: string;
  account_id: number;
  plan_id: string;
  next_plan_id: string;
  days: number;
}

export class EmailService {
  private rest_ = new RestClient(
    "https://api.sendgrid.com/v3/mail",
    config.sendgrid_api_key,
    RestClientMimeType.JSON,
    RestClientMimeType.None
  );
  constructor(private tokenService: TokenService) { }

  public sendPin(context: Context, email: string, link: string, pin: string) {
    this.send(context, email, EmailTemplateId.pinTmpl, { link, pin });
  }

  public sendInvitation(context: Context, email: string, link: string, pin: string) {
    this.send(context, email, EmailTemplateId.invitationTmpl, { link, pin });
  }


  async sendSsoToPinLink(
    context: Context,
    email: string,
    link: string,
    idp: string
  ) {
    this.send(context, email, EmailTemplateId.ssoToPinLinkTmpl, {
      idp,
      email,
      link,
    });
  }

  public async sendEmail(
    context: Context,
    to: string,
    emailTemplateId: string,
    params: any
  ) {
    return this.send(context, to, emailTemplateId, params);
  }

  public sendMagicLink(context: Context, account: Account, link: string) {
    this.send(context, account.email(), EmailTemplateId.magicLinkTmpl, {
      link,
    });
  }

  public sendResetPasswordLink(context: Context, account: Account) {
    if (account == null) {
      return Promise.resolve();
    }
    let link: string = null;
    let serviceName: string = null;
    let templateId = "";
    const idp = account.idp();
    switch (idp) {
      case "mylio":
        let token = this.tokenService.shortLivedToken(
          context,
          account,
          config.reset_password_link_lifespan
        );
        link = `${config.website}/changepassword?token=${token}`;
        templateId = EmailTemplateId.resetPasswordTmpl;
        break;
      case "apple":
        link = "https://appleid.apple.com/";
        serviceName = "Apple";
        templateId = EmailTemplateId.resetPasswordIdpTmpl;
        break;
      case "google":
        link = "https://myaccount.google.com";
        serviceName = "Google";
        templateId = EmailTemplateId.resetPasswordIdpTmpl;
        break;
      case "facebook":
        link = "https://www.facebook.com/settings?tab=security";
        serviceName = "Facebook";
        templateId = EmailTemplateId.resetPasswordIdpTmpl;
        break;
      case "microsoft":
        link = "https://login.microsoftonline.com";
        serviceName = "Microsoft";
        templateId = EmailTemplateId.resetPasswordIdpTmpl;
        break;
      default:
        return Promise.resolve();
    }

    return this.send(context, account, templateId, { link, serviceName });
  }

  public sendChangeEmailAlert(context: Context, account: Account) {
    return this.send(context, account, EmailTemplateId.changeEmailTmpl, {});
  }

  public sendChangePasswordAlert(context: Context, account: Account) {
    return this.send(context, account, EmailTemplateId.changePasswordTmpl, {});
  }

  private sendToMany(
    context: Context,
    emails: string[],
    subject: string,
    content: string
  ) {
    return Promise.all(
      emails.map((email) => this.send(context, email, subject, content))
    );
  }

  private async send(
    context: Context,
    to: string | Account,
    templateId: string,
    substitutions: any
  ) {
    await this.send2(context, to, templateId, substitutions, "mylio.com");
    // await this.send2(context, to, templateId, substitutions, "myliophotos.com");
  }

  private async send2(
    context: Context,
    to: string | Account,
    templateId: string,
    substitutions: any,
    fromDomain: string
  ) {
    let email = "";
    if (typeof to == "string") {
      email = to;
    } else {
      email = to.email() || to.sub();
    }

    // only for development environment
    if (process.env.NODE_ENV === "development") {
      // do not send emails to test mylio accounts
      const testEmail =
        /(^mylotest.*)|(^[abcdef\d]{8}-[abcdef\d]{4}-[abcdef\d]{4}-[abcdef\d]{4}-[abcdef\d]{12})@mylio\.com/;
      if (testEmail.test(email)) {
        return Promise.resolve();
      }
    }

    let body = {
      from: {
        email: `noreply@${fromDomain}`,
        name: "Mylio",
      },
      personalizations: [
        {
          to: [{ email }],
          dynamic_template_data: substitutions,
        },
      ],
      template_id: templateId,
    };


    try {
      await this.rest_.post("send", body);
    } catch (error) {
      context.error(error, ids.SEND_EMAIL_FAILED, { templateId, to });
    }
  }

  public sendCreateAccountLink(context: Context, email: string, link: string) {
    this.send(context, email, EmailTemplateId.createLinkTmpl, { link });
  }

  public sendLoginLink(
    context: Context,
    email: string,
    link: string,
    pin: string
  ) {
    this.send(context, email, EmailTemplateId.loginLinkTmpl, { link, pin });
  }
}
