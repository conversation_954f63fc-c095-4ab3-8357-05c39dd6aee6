
import { makeAutoObservable } from "mobx"
    


    




export interface ILicenseTemplate {
    flags?: number;
	modifiedTime?: Date;
	createdTime?: Date;
	templateId?: string;
	duration?: string;
	uses?: number;
	availableUpgrades?: string;
	displayName?: string;
	public?: boolean;
	weight?: number;
	deviceLimit?: number;
	photoLimit?: number;
	features?: number;
	cloudStorageLimit?: number;
}

export interface IWireLicenseTemplate {
    flags?: number;
	modifiedTime?: string;
	createdTime?: string;
	templateId?: string;
	duration?: string;
	uses?: number;
	availableUpgrades?: string;
	displayName?: string;
	public?: boolean;
	weight?: number;
	deviceLimit?: number;
	photoLimit?: number;
	features?: number;
	cloudStorageLimit?: number;
}

export class LicenseTemplate implements ILicenseTemplate {
    flags?: number;
	modifiedTime?: Date;
	createdTime?: Date;
	templateId?: string;
	duration?: string;
	uses?: number;
	availableUpgrades?: string;
	displayName?: string;
	public?: boolean;
	weight?: number;
	deviceLimit?: number;
	photoLimit?: number;
	features?: number;
	cloudStorageLimit?: number;
    setFlags(value: number) { this.changed = true; this.flags = value; }
	setModifiedTime(value: Date) { this.changed = true; this.modifiedTime = value; }
	setCreatedTime(value: Date) { this.changed = true; this.createdTime = value; }
	setTemplateId(value: string) { this.changed = true; this.templateId = value; }
	setDuration(value: string) { this.changed = true; this.duration = value; }
	setUses(value: number) { this.changed = true; this.uses = value; }
	setAvailableUpgrades(value: string) { this.changed = true; this.availableUpgrades = value; }
	setDisplayName(value: string) { this.changed = true; this.displayName = value; }
	setPublic(value: boolean) { this.changed = true; this.public = value; }
	setWeight(value: number) { this.changed = true; this.weight = value; }
	setDeviceLimit(value: number) { this.changed = true; this.deviceLimit = value; }
	setPhotoLimit(value: number) { this.changed = true; this.photoLimit = value; }
	setFeatures(value: number) { this.changed = true; this.features = value; }
	setCloudStorageLimit(value: number) { this.changed = true; this.cloudStorageLimit = value; }
    changed = false;
    setChanged() {
        this.changed = true;
    }

    clearChanged() {
        this.changed = false;
    }

    constructor(state? : IWireLicenseTemplate | ILicenseTemplate) {
        if (!state)
            throw "An LicenseTemplate must have a valid start state";
        this.flags = state.flags;;
	if (typeof(state.modifiedTime) === "string")
            this.modifiedTime = new Date(state.modifiedTime);
         else
            this.modifiedTime = state.modifiedTime;
	if (typeof(state.createdTime) === "string")
            this.createdTime = new Date(state.createdTime);
         else
            this.createdTime = state.createdTime;
	this.templateId = state.templateId;;
	this.duration = state.duration;;
	this.uses = state.uses;;
	this.availableUpgrades = state.availableUpgrades;;
	this.displayName = state.displayName;;
	this.public = state.public;;
	this.weight = state.weight;;
	this.deviceLimit = state.deviceLimit;;
	this.photoLimit = state.photoLimit;;
	this.features = state.features;;
	this.cloudStorageLimit = state.cloudStorageLimit;
        makeAutoObservable(this, {
            flags: true,
			modifiedTime: true,
			createdTime: true,
			templateId: true,
			duration: true,
			uses: true,
			availableUpgrades: true,
			displayName: true,
			public: true,
			weight: true,
			deviceLimit: true,
			photoLimit: true,
			features: true,
			cloudStorageLimit: true
        });

    }

    state() : ILicenseTemplate {
        return {
            flags : this.flags,
		modifiedTime : this.modifiedTime,
		createdTime : this.createdTime,
		templateId : this.templateId,
		duration : this.duration,
		uses : this.uses,
		availableUpgrades : this.availableUpgrades,
		displayName : this.displayName,
		public : this.public,
		weight : this.weight,
		deviceLimit : this.deviceLimit,
		photoLimit : this.photoLimit,
		features : this.features,
		cloudStorageLimit : this.cloudStorageLimit
        };
    }

    asWire() : IWireLicenseTemplate {
        return {
            flags : this.flags,
		modifiedTime : this.modifiedTime ? this.modifiedTime.toISOString() : undefined,
		createdTime : this.createdTime ? this.createdTime.toISOString() : undefined,
		templateId : this.templateId,
		duration : this.duration,
		uses : this.uses,
		availableUpgrades : this.availableUpgrades,
		displayName : this.displayName,
		public : this.public,
		weight : this.weight,
		deviceLimit : this.deviceLimit,
		photoLimit : this.photoLimit,
		features : this.features,
		cloudStorageLimit : this.cloudStorageLimit
        };
    }

    



    


}


