﻿set client_min_messages = 'debug';
set schema 'd0';

create or replace function etl_load_client_events(_modified_time timestamptz)
returns void
as
$$
    insert into d0.client_event (
        device_long_id,
        time_on_client_clock,
        modified_time,
        event_type,
        data
    )
    select device_long_id, time_on_client_clock, modified_time, event_type, data
    from t0.client_event source
    where source.modified_time >= _modified_time - interval '10 minutes'
    on conflict do nothing

$$
language sql;

create or replace function etl_load_cloud_events(_modified_time timestamptz)
returns void
as
$$
    insert into d0.cloud_event (
        account_id,
        device_id,
        event_type,
        modified_time,
        time_on_cloud_clock,
        data
    )
    select account_id, device_id, event_type, modified_time, time_on_cloud_clock, data
    from t0.cloud_event source
    where source.modified_time >= _modified_time - interval '10 minutes'
    on conflict do nothing
$$
language sql;

create or replace function etl_load_counters(_etl_time timestamptz, _modified_time timestamptz)
returns void
as
$$
    insert into d0.counters (
        account_id,
        device_id,
        modified_time,
        etl_time,
        data
    )
    select account_id, device_id, modified_time, _etl_time, data
    from t0.counters source
    where modified_time > _modified_time - interval '10 minutes'
    on conflict(account_id, device_id) do update
        set etl_time = excluded.etl_time,
      modified_time = excluded.modified_time,
            data = excluded.data
    where (counters.data)::text != (excluded.data)::text;

    insert into d0.counters_history
        select * from d0.counters where etl_time = _etl_time
    on conflict do nothing;
$$
language sql;

create or replace function etl_load_accounts(_etl_time timestamptz, _modified_time timestamptz)
returns void
as
$$
  insert into d0.account (
      account_id,
      plan_id,
      modified_time,
      etl_time,
      next_plan_date,
      next_plan_id
  )
  select account_id, plan_id, modified_time, _etl_time, next_plan_date, next_plan_id
  from a0.account source
  where modified_time > _modified_time - interval '10 minutes' and plan_id is not null
  on conflict(account_id) do update
  set plan_id = excluded.plan_id,
      modified_time = excluded.modified_time,
      etl_time = excluded.etl_time,
      next_plan_date = excluded.next_plan_date,
      next_plan_id = excluded.next_plan_id
  where account.plan_id != excluded.plan_id
  or account.next_plan_date != excluded.next_plan_date
  or account.next_plan_id != excluded.next_plan_id;

  insert into d0.account_history
      select * from d0.account where etl_time = _etl_time
  on conflict do nothing;
$$
language sql;

create or replace function etl_load_devices(_etl_time timestamptz, _modified_time timestamptz)
returns void
as
$$
    insert into d0.device (
        account_id,
        device_id,
        long_id,
        etl_time,
        modified_time,
        device_type,
        deleted,
        os,
        build
    )
    select
        d.account_id,
        d.device_id,
        d.long_id,
        _etl_time,
        greatest(dd.modified_time, d.modified_time),
        d.device_type,
        d.deleted,
        coalesce(dd.os, case d.device_type when 18 then 'AMZ' when 19 then 'GOOG' end),
        coalesce(dd.build, '1')
    from a0.device d
    left join a0.device_data dd on d.account_id = dd.account_id and d.device_id = dd.device_id
    where greatest(d.modified_time, dd.modified_time) > _modified_time - interval '10 minutes'
    and (dd.device_id is not null or d.device_type in (18, 19))
    on conflict(account_id, device_id) do update
        set etl_time = excluded.etl_time,
            modified_time = excluded.modified_time,
            device_type = excluded.device_type,
            deleted = excluded.deleted,
            os = excluded.os,
            build = excluded.build
    where device.device_type != excluded.device_type
    or device.deleted != excluded.deleted
    or device.os != excluded.os
    or device.build != excluded.build;

    insert into d0.device_history
    select * from d0.device where etl_time = _etl_time
    on conflict do nothing;
$$
language sql;

drop function etl_run();

create function etl_run()
returns void
as
$$
declare
    __etl_time timestamptz = now();
begin
    perform * from d0.etl_load_client_events(
        coalesce((select max(modified_time) from d0.client_event), to_timestamp(0))
    );

    perform * from d0.etl_load_cloud_events(
        coalesce((select max(modified_time) from d0.cloud_event), to_timestamp(0))
    );

    perform * from d0.etl_load_counters(
        __etl_time,
        coalesce((select max(modified_time) from d0.counters), to_timestamp(0))
    );

    perform * from d0.etl_load_accounts(
        __etl_time,
        coalesce((select max(modified_time) from d0.account), to_timestamp(0))
    );

    perform * from d0.etl_load_devices(
        __etl_time,
        coalesce((select max(modified_time) from d0.device), to_timestamp(0))
    );
end;
$$
language plpgsql;
