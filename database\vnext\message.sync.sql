

create or replace function a0.message_presync()
returns void
as
$$
    create temp table if not exists __message_sync_data (
        _flags int,
	_modified_time timestamptz,
	_created_time timestamptz,
	_account_id int,
	_deleted boolean,
	_t bytea,
	_d bytea,
	_device_id int,
	_message_id int,
	_seconds_to_display int,
	_displayed int,
	_message text,
	_link text
    );
    truncate table __message_sync_data;

    create temp table if not exists __have (
        _account_id int,
        _t bytea
    );
    truncate table __have;
    
    


$$
language sql;


select * from a0.message_presync();



create or replace function a0.message_merkle(__account_id int)
returns void
as
$$
    update a0.account_metadata
        set message_merkle = agg.merkle
    from (
        select account_id, digest(string_agg(t, null), 'sha1') as merkle
        from (
            select account_id, t from a0.message where account_id = __account_id
            order by t
        ) as x
        group by account_id
    ) as agg
    where a0.account_metadata.account_id = agg.account_id
    and (a0.account_metadata.message_merkle != agg.merkle or a0.account_metadata.message_merkle is null);
$$
language sql;

drop function if exists a0.message_sync_have(int, text[]) cascade;
create or replace function a0.message_sync_have(__account_id int, _have_text text[])
returns table (
    op int,
    "flags" int,
"modifiedTime" timestamptz,
"createdTime" timestamptz,
"accountId" int,
"deleted" boolean,
"t" text,
"d" text,
"deviceId" int,
"messageId" int,
"secondsToDisplay" int,
"displayed" int,
"message" text,
"link" text
)
as
$$
    insert into __have
    select __account_id, decode(t, 'base64') as _t from unnest(_have_text) as t;
    create index if not exists __have_index on __have(_account_id, substring(_t from 1 for 20), _t);

    select 
        3 as op,
        null,
	null,
	null,
	null,
	null,
	encode(_t, 'base64') as t,
	null,
	null,
	null,
	null,
	null,
	null,
	null
    from __have
    left join a0.message cloud 
    on cloud.account_id = __have._account_id
    and substring(t from 1 for 20) = substring(_t from 1 for 20)
    where (t is null or t < _t)

    union all

    select
        2 as op,
        flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		deleted,
		encode(t, 'base64') as "t",
		encode(d, 'base64') as "d",
		device_id as "deviceId",
		message_id as "messageId",
		seconds_to_display as "secondsToDisplay",
		displayed,
		message,
		link
    from a0.message cloud
    left join __have on _account_id = account_id and substring(t from 1 for 20) = substring(_t from 1 for 20)
    where account_id = __account_id
    and (_t < t or _t is null);    

$$
language sql;

drop function if exists a0.message_sync_data(int, json) cascade;
create or replace function a0.message_sync_data(__account_id int, _data_json json)
returns table (
    op int,
    "flags" int,
"modifiedTime" timestamptz,
"createdTime" timestamptz,
"accountId" int,
"deleted" boolean,
"t" text,
"d" text,
"deviceId" int,
"messageId" int,
"secondsToDisplay" int,
"displayed" int,
"message" text,
"link" text
)
as
$$





insert into __message_sync_data 
select
    flags as _flags,
	now(),
	"createdTime" as _created_time,
	__account_id,
	deleted as _deleted,
	decode("t", 'base64') as _t,
	decode("d", 'base64') as _d,
	"deviceId" as _device_id,
	"messageId" as _message_id,
	"secondsToDisplay" as _seconds_to_display,
	displayed as _displayed,
	message as _message,
	link as _link
    from json_to_recordset(_data_json) 
    as t(
        "flags" int,
	"createdTime" timestamptz,
	"accountId" int,
	"deleted" boolean,
	"t" text,
	"d" text,
	"deviceId" int,
	"messageId" int,
	"secondsToDisplay" int,
	"displayed" int,
	"message" text,
	"link" text
    );

create index if not exists __message_sync_data_index on __message_sync_data(_account_id, substring(_t from 1 for 20), _t);


update a0.message cloud
    set flags = _flags,
	modified_time = _modified_time,
	created_time = _created_time,
	deleted = _deleted,
	t = _t,
	d = _d,
	device_id = _device_id,
	seconds_to_display = _seconds_to_display,
	displayed = _displayed,
	message = _message,
	link = _link
from __message_sync_data client
where account_id = _account_id and substring(t from 1 for 20) = substring(_t from 1 for 20)
and _t > t;

update __message_sync_data
    set _t = least(E'\\x000000000000000000000000000000000000000000000000000000000000000000', decode((a0.message_create(
        _flags,
		_modified_time,
		_created_time,
		__account_id,
		_deleted,
		null,
		encode(_d, 'base64'),
		_device_id,
		_message_id,
		_seconds_to_display,
		_displayed,
		_message,
		_link)).t, 'base64'))
where _message_id is null;

insert into a0.message (
    flags,
	modified_time,
	created_time,
	account_id,
	deleted,
	t,
	d,
	device_id,
	message_id,
	seconds_to_display,
	displayed,
	message,
	link
)
select
    _flags,
	_modified_time,
	_created_time,
	_account_id,
	_deleted,
	_t,
	_d,
	_device_id,
	_message_id,
	_seconds_to_display,
	_displayed,
	_message,
	_link
from __message_sync_data
where not exists (select * from a0.message where account_id = _account_id and substring(t from 1 for 20) = substring(_t from 1 for 20));

select from a0.message_merkle(__account_id);





select
    2 as op,
    flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		deleted,
		encode(t, 'base64') as "t",
		encode(d, 'base64') as "d",
		device_id as "deviceId",
		message_id as "messageId",
		seconds_to_display as "secondsToDisplay",
		displayed,
		message,
		link
from a0.message cloud
join __message_sync_data on account_id = _account_id and substring(t from 1 for 20) = substring(_t from 1 for 20)
where (_t < t or _message_id is null);

$$
language sql;

drop function if exists a0.message_sync(int, json, boolean, text[], boolean) cascade;
create or replace function a0.message_sync(__account_id int, _data_json json, _do_data boolean, _have text[], _do_have boolean)
returns table (
    op int,
    "flags" int,
"modifiedTime" timestamptz,
"createdTime" timestamptz,
"accountId" int,
"deleted" boolean,
"t" text,
"d" text,
"deviceId" int,
"messageId" int,
"secondsToDisplay" int,
"displayed" int,
"message" text,
"link" text
)
as
$$
begin
    perform * 
    from a0.account_metadata 
    where account_id = __account_id for update;

    perform a0.message_presync();
    if (_do_data) then
        return query select * from a0.message_sync_data(__account_id, _data_json);
    end if;
    if (_do_have) then
        return query select * from a0.message_sync_have(__account_id, _have);
    end if;
end;
$$
language plpgsql;
