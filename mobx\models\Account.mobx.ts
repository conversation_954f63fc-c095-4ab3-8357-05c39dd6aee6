
import { makeAutoObservable } from "mobx"
    


    




export interface IAccount {
    flags?: number;
	modifiedTime?: Date;
	createdTime?: Date;
	accountId?: number;
	t?: string;
	cipher?: string;
	clientCipher?: string;
	clientCipherVersion?: number;
	minBuild?: number;
	peerToPeerKey?: string;
	clientPeerToPeerKey?: string;
	clientPeerToPeerKeyVersion?: number;
	rsaPrivateKey?: string;
	x509Cert?: string;
	tfa?: boolean;
	idp?: string;
	sub?: string;
	email?: string;
	planId?: string;
	role?: string;
	deviceLimit?: number;
	photoLimit?: number;
	cloudStorageLimit?: number;
	features?: number;
	nextPlanDate?: Date;
	availableUpgrades?: string;
	licenseTemplateId?: string;
	licenseDisplayName?: string;
	licenseManager?: string;
	licenseFlags?: number;
	availableUpgradesFeatures?: number;
	licenseId?: string;
	password?: string;
	affiliateId?: string;
}

export interface IWireAccount {
    flags?: number;
	modifiedTime?: string;
	createdTime?: string;
	accountId?: number;
	t?: string;
	cipher?: string;
	clientCipher?: string;
	clientCipherVersion?: number;
	minBuild?: number;
	peerToPeerKey?: string;
	clientPeerToPeerKey?: string;
	clientPeerToPeerKeyVersion?: number;
	rsaPrivateKey?: string;
	x509Cert?: string;
	tfa?: boolean;
	idp?: string;
	sub?: string;
	email?: string;
	planId?: string;
	role?: string;
	deviceLimit?: number;
	photoLimit?: number;
	cloudStorageLimit?: number;
	features?: number;
	nextPlanDate?: string;
	availableUpgrades?: string;
	licenseTemplateId?: string;
	licenseDisplayName?: string;
	licenseManager?: string;
	licenseFlags?: number;
	availableUpgradesFeatures?: number;
	licenseId?: string;
	password?: string;
	affiliateId?: string;
}

export class Account implements IAccount {
    flags?: number;
	modifiedTime?: Date;
	createdTime?: Date;
	accountId?: number;
	t?: string;
	cipher?: string;
	clientCipher?: string;
	clientCipherVersion?: number;
	minBuild?: number;
	peerToPeerKey?: string;
	clientPeerToPeerKey?: string;
	clientPeerToPeerKeyVersion?: number;
	rsaPrivateKey?: string;
	x509Cert?: string;
	tfa?: boolean;
	idp?: string;
	sub?: string;
	email?: string;
	planId?: string;
	role?: string;
	deviceLimit?: number;
	photoLimit?: number;
	cloudStorageLimit?: number;
	features?: number;
	nextPlanDate?: Date;
	availableUpgrades?: string;
	licenseTemplateId?: string;
	licenseDisplayName?: string;
	licenseManager?: string;
	licenseFlags?: number;
	availableUpgradesFeatures?: number;
	licenseId?: string;
	password?: string;
	affiliateId?: string;
    setFlags(value: number) { this.changed = true; this.flags = value; }
	setModifiedTime(value: Date) { this.changed = true; this.modifiedTime = value; }
	setCreatedTime(value: Date) { this.changed = true; this.createdTime = value; }
	setAccountId(value: number) { this.changed = true; this.accountId = value; }
	setT(value: string) { this.changed = true; this.t = value; }
	setCipher(value: string) { this.changed = true; this.cipher = value; }
	setClientCipher(value: string) { this.changed = true; this.clientCipher = value; }
	setClientCipherVersion(value: number) { this.changed = true; this.clientCipherVersion = value; }
	setMinBuild(value: number) { this.changed = true; this.minBuild = value; }
	setPeerToPeerKey(value: string) { this.changed = true; this.peerToPeerKey = value; }
	setClientPeerToPeerKey(value: string) { this.changed = true; this.clientPeerToPeerKey = value; }
	setClientPeerToPeerKeyVersion(value: number) { this.changed = true; this.clientPeerToPeerKeyVersion = value; }
	setRsaPrivateKey(value: string) { this.changed = true; this.rsaPrivateKey = value; }
	setX509Cert(value: string) { this.changed = true; this.x509Cert = value; }
	setTfa(value: boolean) { this.changed = true; this.tfa = value; }
	setIdp(value: string) { this.changed = true; this.idp = value; }
	setSub(value: string) { this.changed = true; this.sub = value; }
	setEmail(value: string) { this.changed = true; this.email = value; }
	setPlanId(value: string) { this.changed = true; this.planId = value; }
	setRole(value: string) { this.changed = true; this.role = value; }
	setDeviceLimit(value: number) { this.changed = true; this.deviceLimit = value; }
	setPhotoLimit(value: number) { this.changed = true; this.photoLimit = value; }
	setCloudStorageLimit(value: number) { this.changed = true; this.cloudStorageLimit = value; }
	setFeatures(value: number) { this.changed = true; this.features = value; }
	setNextPlanDate(value: Date) { this.changed = true; this.nextPlanDate = value; }
	setAvailableUpgrades(value: string) { this.changed = true; this.availableUpgrades = value; }
	setLicenseTemplateId(value: string) { this.changed = true; this.licenseTemplateId = value; }
	setLicenseDisplayName(value: string) { this.changed = true; this.licenseDisplayName = value; }
	setLicenseManager(value: string) { this.changed = true; this.licenseManager = value; }
	setLicenseFlags(value: number) { this.changed = true; this.licenseFlags = value; }
	setAvailableUpgradesFeatures(value: number) { this.changed = true; this.availableUpgradesFeatures = value; }
	setLicenseId(value: string) { this.changed = true; this.licenseId = value; }
	setPassword(value: string) { this.changed = true; this.password = value; }
	setAffiliateId(value: string) { this.changed = true; this.affiliateId = value; }
    changed = false;
    setChanged() {
        this.changed = true;
    }

    clearChanged() {
        this.changed = false;
    }

    constructor(state? : IWireAccount | IAccount) {
        if (!state)
            throw "An Account must have a valid start state";
        this.flags = state.flags;;
	if (typeof(state.modifiedTime) === "string")
            this.modifiedTime = new Date(state.modifiedTime);
         else
            this.modifiedTime = state.modifiedTime;
	if (typeof(state.createdTime) === "string")
            this.createdTime = new Date(state.createdTime);
         else
            this.createdTime = state.createdTime;
	this.accountId = state.accountId;;
	this.t = state.t;;
	this.cipher = state.cipher;;
	this.clientCipher = state.clientCipher;;
	this.clientCipherVersion = state.clientCipherVersion;;
	this.minBuild = state.minBuild;;
	this.peerToPeerKey = state.peerToPeerKey;;
	this.clientPeerToPeerKey = state.clientPeerToPeerKey;;
	this.clientPeerToPeerKeyVersion = state.clientPeerToPeerKeyVersion;;
	this.rsaPrivateKey = state.rsaPrivateKey;;
	this.x509Cert = state.x509Cert;;
	this.tfa = state.tfa;;
	this.idp = state.idp;;
	this.sub = state.sub;;
	this.email = state.email;;
	this.planId = state.planId;;
	this.role = state.role;;
	this.deviceLimit = state.deviceLimit;;
	this.photoLimit = state.photoLimit;;
	this.cloudStorageLimit = state.cloudStorageLimit;;
	this.features = state.features;;
	if (typeof(state.nextPlanDate) === "string")
            this.nextPlanDate = new Date(state.nextPlanDate);
         else
            this.nextPlanDate = state.nextPlanDate;
	this.availableUpgrades = state.availableUpgrades;;
	this.licenseTemplateId = state.licenseTemplateId;;
	this.licenseDisplayName = state.licenseDisplayName;;
	this.licenseManager = state.licenseManager;;
	this.licenseFlags = state.licenseFlags;;
	this.availableUpgradesFeatures = state.availableUpgradesFeatures;;
	this.licenseId = state.licenseId;;
	this.password = state.password;;
	this.affiliateId = state.affiliateId;
        makeAutoObservable(this, {
            flags: true,
			modifiedTime: true,
			createdTime: true,
			accountId: true,
			t: true,
			cipher: true,
			clientCipher: true,
			clientCipherVersion: true,
			minBuild: true,
			peerToPeerKey: true,
			clientPeerToPeerKey: true,
			clientPeerToPeerKeyVersion: true,
			rsaPrivateKey: true,
			x509Cert: true,
			tfa: true,
			idp: true,
			sub: true,
			email: true,
			planId: true,
			role: true,
			deviceLimit: true,
			photoLimit: true,
			cloudStorageLimit: true,
			features: true,
			nextPlanDate: true,
			availableUpgrades: true,
			licenseTemplateId: true,
			licenseDisplayName: true,
			licenseManager: true,
			licenseFlags: true,
			availableUpgradesFeatures: true,
			licenseId: true,
			password: true,
			affiliateId: true
        });

    }

    state() : IAccount {
        return {
            flags : this.flags,
		modifiedTime : this.modifiedTime,
		createdTime : this.createdTime,
		accountId : this.accountId,
		t : this.t,
		cipher : this.cipher,
		clientCipher : this.clientCipher,
		clientCipherVersion : this.clientCipherVersion,
		minBuild : this.minBuild,
		peerToPeerKey : this.peerToPeerKey,
		clientPeerToPeerKey : this.clientPeerToPeerKey,
		clientPeerToPeerKeyVersion : this.clientPeerToPeerKeyVersion,
		rsaPrivateKey : this.rsaPrivateKey,
		x509Cert : this.x509Cert,
		tfa : this.tfa,
		idp : this.idp,
		sub : this.sub,
		email : this.email,
		planId : this.planId,
		role : this.role,
		deviceLimit : this.deviceLimit,
		photoLimit : this.photoLimit,
		cloudStorageLimit : this.cloudStorageLimit,
		features : this.features,
		nextPlanDate : this.nextPlanDate,
		availableUpgrades : this.availableUpgrades,
		licenseTemplateId : this.licenseTemplateId,
		licenseDisplayName : this.licenseDisplayName,
		licenseManager : this.licenseManager,
		licenseFlags : this.licenseFlags,
		availableUpgradesFeatures : this.availableUpgradesFeatures,
		licenseId : this.licenseId,
		password : this.password,
		affiliateId : this.affiliateId
        };
    }

    asWire() : IWireAccount {
        return {
            flags : this.flags,
		modifiedTime : this.modifiedTime ? this.modifiedTime.toISOString() : undefined,
		createdTime : this.createdTime ? this.createdTime.toISOString() : undefined,
		accountId : this.accountId,
		t : this.t,
		cipher : this.cipher,
		clientCipher : this.clientCipher,
		clientCipherVersion : this.clientCipherVersion,
		minBuild : this.minBuild,
		peerToPeerKey : this.peerToPeerKey,
		clientPeerToPeerKey : this.clientPeerToPeerKey,
		clientPeerToPeerKeyVersion : this.clientPeerToPeerKeyVersion,
		rsaPrivateKey : this.rsaPrivateKey,
		x509Cert : this.x509Cert,
		tfa : this.tfa,
		idp : this.idp,
		sub : this.sub,
		email : this.email,
		planId : this.planId,
		role : this.role,
		deviceLimit : this.deviceLimit,
		photoLimit : this.photoLimit,
		cloudStorageLimit : this.cloudStorageLimit,
		features : this.features,
		nextPlanDate : this.nextPlanDate ? this.nextPlanDate.toISOString() : undefined,
		availableUpgrades : this.availableUpgrades,
		licenseTemplateId : this.licenseTemplateId,
		licenseDisplayName : this.licenseDisplayName,
		licenseManager : this.licenseManager,
		licenseFlags : this.licenseFlags,
		availableUpgradesFeatures : this.availableUpgradesFeatures,
		licenseId : this.licenseId,
		password : this.password,
		affiliateId : this.affiliateId
        };
    }

    



    


}


