create or replace function a0.create_recompute_results_table() 
returns void
as
$$
begin
	drop table if exists recompute_results;
	create temp table if not exists recompute_results(
		account_id int primary key, 
		device_limit int, 
		photo_limit int,
		features int,
		active_aggregate_flags int,
		aggregate_flags int,
		cloud_storage_limit int,
		flags int,
		available_upgrades text,
		license_template_id text,
		license_display_name text,
		manager text,
		available_upgrades_features int,
		license_id text,
		end_date timestamptz
	) on commit preserve rows;
end;
$$
language plpgsql;


select a0.create_recompute_results_table();

CREATE OR REPLACE FUNCTION a0.recompute_licenses(
	)
    RETURNS void
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE PARALLEL UNSAFE
AS $BODY$
declare
	meta record;
	ACTIVE int = 1;
	DELETED int = 2;
	CANCELED int = 3;
	TRIAL int = 4;
begin

/*
	fl_trial int4 = 1;
	fl_addon int4 = 2;
	fl_storage int4 = 4;
	fl_multi_user int4 = 8;
*/


update recompute_results r set 
	device_limit = x.device_limit, 
	photo_limit = x.photo_limit,
	features = x.features,
	active_aggregate_flags = x.active_aggregate_flags,
	aggregate_flags = x.aggregate_flags,
	cloud_storage_limit = x.cloud_storage_limit
from (
	select 
		a.account_id,
		max(coalesce(l.device_limit, t.device_limit, 0)) filter (where l.status != DELETED) device_limit, 
		max(coalesce(l.photo_limit, t.photo_limit, 0)) filter (where l.status != DELETED) photo_limit,
		bit_or(coalesce(l.features, 0) | t.features) filter (where l.status != DELETED) features,
		bit_or(coalesce(l.flags, 0) | t.flags | case when l.manager != 'M' then 32 else 0 end) filter (where l.status != DELETED) active_aggregate_flags,
		bit_or(coalesce(l.flags, 0) | t.flags | case when l.manager != 'M' then 32 else 0 end) aggregate_flags,
		sum(coalesce(l.cloud_storage_limit, t.cloud_storage_limit, 0)) filter (where l.status != DELETED) as cloud_storage_limit
	from recompute_results a
	join a0.license l on a.account_id = l.account_id
	join a0.license_template t on t.template_id = l.template_id
	group by a.account_id
) x
where x.account_id = r.account_id;
 
update recompute_results r
	set 
		license_template_id = x.template_id,
		--paid flag
		flags = x.flags | (case x.manager when 'M' then 0 else 32 end),
		license_display_name = x.display_name,
		manager = x.manager,
		available_upgrades = x.available_upgrades,
		license_id = x.license_id,
		end_date = x.end_date
		
from (
    select
        l.account_id,
        l.template_id,
		l.license_Id,
		coalesce(l.flags, 0) | t.flags flags,
		coalesce(l.available_upgrades, t.available_upgrades) as available_upgrades,
		recompute_results.aggregate_flags,
		t.display_name,
		l.end_date,
        row_number() over (
            partition by l.account_id
            order by weight + case when l.manager != 'M' then 1 else 0 end desc) as rn,
		l.manager
    from  a0.license l
	join recompute_results on recompute_results.account_id = l.account_id
	join a0.license_template t on t.template_id = l.template_id
	where status != DELETED
) x where x.account_id = r.account_id and x.rn = 1;


drop table if exists available_upgrades;
create temp table available_upgrades
(
	account_id int,
	upgrade text,
	features int,
	primary key (account_id, upgrade)
);

insert into available_upgrades(account_id, upgrade, features)
select r.account_id, x.upgrade, t.features
from recompute_results r
join (
		SELECT account_id, unnest(string_to_array(available_upgrades, '|')) AS upgrade
		from recompute_results 
) x on x.account_id = r.account_id
join a0.license_template t on '=' || t.template_id = x.upgrade
where not (r.manager = 'A' and x.upgrade in ('=personal-group', '=business-group'));

update recompute_results r
set 
available_upgrades = x.available_upgrades,
available_upgrades_features = x.available_upgrades_features
from (
	select 
		account_id, 
		string_agg(upgrade, '|') available_upgrades, 
		bit_or(features) available_upgrades_features
	from available_upgrades a
	group by account_id
) x
where r.account_id = x.account_id;	

update a0.account a
	set 
		t = next_trev(t),
		plan_id = case c.license_template_id when 'free' then '1000' else '1015' end,
		next_plan_date = c.end_date,
		photo_limit = c.photo_limit,
		device_limit = c.device_limit,
		features = c.features,
		cloud_storage_limit = c.cloud_storage_limit,
		license_template_id = c.license_template_id,
		available_upgrades = c.available_upgrades,
		license_display_name = c.license_display_name,
		flags = 
			/* can trial plus */
			case when (c.aggregate_flags & 32 = 32) then 0 else 1 end
			|
			/* is currently trialing */
			case when (c.active_aggregate_flags & 1 = 1) then 2 else 0 end
			|
			/* is currently a paid account */
			case when (c.flags & 32 = 32) then 16 else 0 end
			|
			/* can buy a plus subscription */
			case when (not c.flags & 32 = 32) then 32 else 0 end
			|
			/* can buy cloud storage */
			case when (c.flags & 32 = 32) then 128 else 0 end,
		license_flags = c.flags,
		license_manager = c.manager,
		available_upgrades_features = c.available_upgrades_features,
		license_id = c.license_id
from recompute_results c
where a.account_id = c.account_id;

perform a0.account_merkle(account_id)
from recompute_results;
end;
$BODY$;


create or replace function a0.recompute_licenses_for_expired_accounts()
returns void
as
$$
begin
	perform a0.create_recompute_results_table();
	with updated as (
		update a0.license
		set status = DELETED, modified_time = now()
		where end_date < now()
		and status != DELETED
		and coalesce(manager, 'M')  in ('M', 'P', 'S')
		returning account_id
	)
	insert into recompute_results(account_id)
		select account_id from updated;
	perform a0.recompute_licenses();
end;
$$
language plpgsql;


create or replace function a0.recompute_licenses_for_all_accounts()
returns void
as
$$
begin
	perform a0.create_recompute_results_table();
	insert into recompute_results(account_id)
		select account_id from a0.account;
	perform a0.recompute_licenses();
end;
$$
language plpgsql;


create or replace function a0.recompute_licenses_for_one_account(account_id_ int)
returns void
as
$$
begin
	perform a0.create_recompute_results_table();
	insert into recompute_results(account_id) values(account_id_);
	perform a0.recompute_licenses();
end;
$$
language plpgsql;


create or replace function a0.compute_available_upgrades(account_id_ int, feature_ int) 
returns table(sku text)
as
$$
declare
	meta record;
	ACTIVE int = 1;
	DELETED int = 2;
	CANCELED int = 3;
	TRIAL int = 4;
begin
	return query select template_id as sku
	from a0.license_template t
	join a0.account a on a.account_id = account_id_
	join (
		SELECT unnest(string_to_array(available_upgrades, '|')) AS upgrade
		from a0.account a
		where a.account_id = account_id_
	) x on '=' || t.template_id = x.upgrade or '+' || t.template_id = x.upgrade
	where t.features & feature_ = feature_
	and (t.flags & 2 = 0 or a.license_flags & 32 = 32)
	and (t.template_id not in ('personal-group', 'business-group') or (a.license_manager != 'A') );
	
end;
$$
language plpgsql;

select a0.recompute_licenses_for_one_account(768 );


select license_flags, flags from a0.account where account_id = 768;



