



create table a0.system_property(
    flags int NULL,
	modified_time timestamptz NULL,
	created_time timestamptz NULL,
	account_id int NOT NULL,
	deleted boolean NULL,
	t bytea NULL,
	d bytea NULL,
	system_property_id int NOT NULL,
	name text NULL,
	value text NULL
);

alter table a0.system_property
add primary key (account_id,system_property_id);

 

 create index ix_system_property_by_account_id on a0.system_property(account_id);
create index ix_system_property_by_name on a0.system_property(name);

