import express = require("express");
import { microservice } from "../microservices/account.microservice.mjs";
import { safeAny, secure, s, safeNone } from "../system/safe.mjs";
import { Context } from "../system/Context.mjs";
import { makeError } from "../system/error.mjs";
import { ids } from "../system/Strings.mjs";

export function addLockRoutes(router: express.Router) {
  router.get("/accounts/:aid/lock/:key", safeNone, secure, (req, res, next) => {
    let context = req.context as Context;
    if (context.userAgent?.build < 7640) return next(makeError(400, ids.LOCKED));
    return microservice.lockService
      .lock(
        context,
        context.aid,
        req.params.key,
        context.query.timeout,
        s(req.query.ticket)
      )
      .then((ticket) => {
        return res.status(200).json({ ticket });
      })
      .catch(next);
  });

  router.delete(
    "/accounts/:aid/lock/:key",
    safeAny,
    secure,
    (req, res, next) => {
      let context = req.context as Context;
      return microservice.lockService
        .unlock(context, context.aid, req.params.key, s(req.query.ticket))
        .then((ticket) => {
          return res.status(200).json({ ticket });
        })
        .catch(next);
    }
  );

  return router;
}

export default addLockRoutes;
