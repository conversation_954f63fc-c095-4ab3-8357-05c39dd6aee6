"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const { STSClient, GetSessionTokenCommand } = require("@aws-sdk/client-sts");
const { readFileSync, writeFileSync } = require("fs");
const { join } = require("path");
const readline = require("node:readline/promises").createInterface({
  input: process.stdin,
  output: process.stdout,
  terminal: true,
});
async function main() {
  const username = await readline.question("Enter your username: ");
  const mfaToken = await readline.question("Enter your MFA token code: ");
  const stsClient = new STSClient({ region: "us-west-2" });
  const mfaParams = {
    DurationSeconds: 12 * 3600,
    SerialNumber: `arn:aws:iam::988863557391:mfa/${username}`,
    TokenCode: mfaToken.trim(),
  };
  try {
    console.log(mfaParams);
    const { Credentials } = await stsClient.send(
      new GetSessionTokenCommand(mfaParams)
    );
    const credentials = {
      accessKeyId: Credentials.AccessKeyId,
      secretAccessKey: Credentials.SecretAccessKey,
      sessionToken: Credentials.SessionToken,
    };
    const credentialsFilePath = join(process.env.HOME, ".aws", "credentials");
    const credentialsFileContent = readFileSync(credentialsFilePath, {
      encoding: "utf-8",
    });
    let updatedCredentialsFileContent;
    if (credentialsFileContent.includes("[MFA]")) {
      let findExpr =
        /\[MFA\]\naws_access_key_id\s*=\s*[^\n]*\naws_secret_access_key\s*=\s*[^\n]*(\naws_session_token\s*=\s*[^\n]*)?/;
      updatedCredentialsFileContent = credentialsFileContent.replace(
        findExpr,
        `[MFA]\naws_access_key_id = ${credentials.accessKeyId}\naws_secret_access_key = ${credentials.secretAccessKey}\naws_session_token = ${credentials.sessionToken}\n`
      );
    } else {
      updatedCredentialsFileContent =
        credentialsFileContent.trim() +
        `\n\n[MFA]\naws_access_key_id = ${credentials.accessKeyId}\naws_secret_access_key = ${credentials.secretAccessKey}\naws_session_token = ${credentials.sessionToken}\n`;
    }
    writeFileSync(credentialsFilePath, updatedCredentialsFileContent);
    console.log(`Credentials written successfully to ${credentialsFilePath}`);
  } catch (error) {
    console.error(error);
  } finally {
    readline.close();
  }
}
main();
//# sourceMappingURL=data:application/json;base64,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
