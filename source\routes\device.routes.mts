import express = require("express");
import { microservice as g } from "../microservices/account.microservice.mjs";
import { sanitizeOutput } from "../models/Device.model.mjs";
import {
  safeDevice,
  safeNone,
  secure,
  safeAny,
  admin,
} from "../system/safe.mjs";
import { EventType } from "../system/Strings.mjs";
import { Context } from "../system/Context.mjs";
import {
  DeviceData,
  sanitizeOutput as sanitizeDeviceData,
} from "../models/DeviceData.model.mjs";
import { telemetry } from "../system/telemetry.mjs";
import {
  SupportTicket,
  sanitizeInput as sanitizeInputST,
} from "../models/SupportTicket.model.mjs";
import { Mode } from "../models/IModel.mjs";

export function addDeviceRoutes(router: express.Router) {
  router.put(
    "/accounts/:aid/devices/:did",
    safeDevice,
    secure,
    (req, res, next) => {
      let context = req.context;
      return g.deviceService
        .update(context, context.aid, context.device)
        .then((device) => {
          context.dumpLog();
          return res
            .status(200)
            .json(sanitizeOutput(device, context.hasAdminRights()));
        })
        .catch(next);
    }
  );

  router.post(
    "/accounts/:aid/device-reservation",
    safeAny,
    secure,
    (req, res, next) => {
      let context = req.context;
      return g.deviceService
        .reserve(context, context.aid, context.any.dType)
        .then((result) => {
          context.dumpLog();
          return res.status(200).json(result);
        })
        .catch(next);
    }
  );

  router.delete(
    "/accounts/:aid/devices/:did",
    safeNone,
    secure,
    (req, res, next) => {
      let context = req.context;
      return g.deviceService
        .delete(context, context.aid, context.did)
        .then((device) => {
          telemetry(context, context.aid, EventType.DEVICE_DELETED, {
            aid: context.aid,
            did: context.did,
          });
          context.dumpLog();
          return res.sendStatus(200);
        })
        .catch(next);
    }
  );

  router.get(
    "/accounts/:aid/devices/:did",
    safeNone,
    secure,
    (req, res, next) => {
      let context = req.context;
      return g.deviceService
        .read(context, context.aid, context.did)
        .then((device) => {
          context.dumpLog();
          return res
            .status(200)
            .json(sanitizeOutput(device, context.hasAdminRights()));
        })
        .catch(next);
    }
  );

  router.post(
    "/accounts/:aid/devices",
    safeDevice,
    secure,
    (req, res, next) => {
      let context = req.context;
      return g.deviceService
        .create(context, context.aid, context.device)
        .then((device) => {
          context.dumpLog();
          return res
            .status(200)
            .json(sanitizeOutput(device, context.hasAdminRights()));
        })
        .catch(next);
    }
  );

  router.get(
    "/accounts/:aid/devices",
    safeNone,
    secure,
    async (req, res, next) => {
      let context = req.context;
      let raw = await g.deviceService.list(context, context.aid);
      let clean = raw.map((dd) => {
        return {
          device: sanitizeOutput(dd.device, context.hasAdminRights()),
          data: dd.data
            ? sanitizeDeviceData(dd.data, context.hasAdminRights())
            : undefined,
        };
      });
      context.dumpLog();
      res.status(200).json(clean);
    }
  );

  router.post(
    "/accounts/:aid/devices/:did/ticket",
    safeAny,
    secure,
    async (req, res, next) => {
      let context = req.context;
      let ticket = new SupportTicket(
        sanitizeInputST(context.any, context.hasAdminRights(), Mode.Create)
      );
      await g.deviceService.createSupportTicket(
        context,
        context.aid,
        parseInt(req.params.did),
        ticket
      );
      context.dumpLog();
      return res.status(204).send();
    }
  );

  router.post(
    "/accounts/:aid/devices/ticket",
    safeAny,
    secure,
    admin,
    async (req, res, next) => {
      const context: Context = req.context;
      const aid: number = context.aid;
      let ticket = new SupportTicket(
        sanitizeInputST(context.any, context.hasAdminRights(), Mode.Create)
      );
      await g.deviceService.createSupportTicketsForDevices(
        context,
        aid,
        ticket
      );
      context.dumpLog();
      return res.sendStatus(200);
    }
  );

  router.get(
    "/accounts/:aid/device-data",
    safeNone,
    secure,
    (req, res, next) => {
      const context: Context = req.context;
      const aid: number = context.aid;

      return g.deviceService
        .listDeviceDataForAccount(context, aid)
        .then((deviceData) => {
          context.dumpLog();

          return res
            .status(200)
            .json(
              deviceData.map((dd) =>
                sanitizeDeviceData(dd, context.hasAdminRights())
              )
            );
        })
        .catch(next);
    }
  );

  router.get(
    "/accounts/:aid/device-data/:did",
    safeNone,
    secure,
    (req, res, next) => {
      const context: Context = req.context;
      const aid: number = context.aid;
      const did: number = context.did;

      return g.deviceService
        .readDeviceData(context, aid, did)
        .then((deviceData) => {
          context.dumpLog();

          if (!deviceData) {
            return res.status(200).json({});
          }
          return res.status(200).json(deviceData.state());
        })
        .catch(next);
    }
  );
}
