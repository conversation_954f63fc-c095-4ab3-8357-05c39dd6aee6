

    drop view if exists a0."Message" cascade;

    create or replace view a0."Message" as
    select
    flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		deleted,
		encode(t, 'base64') as "t",
		encode(d, 'base64') as "d",
		device_id as "deviceId",
		message_id as "messageId",
		seconds_to_display as "secondsToDisplay",
		displayed,
		message,
		link
    from a0.message;
    

drop function if exists a0.message_create; 
        create function a0.message_create(
            _flags int,
	_modified_time timestamptz,
	_created_time timestamptz,
	_account_id int,
	_deleted boolean,
	__t text,
	__d text,
	_device_id int,
	_message_id int,
	_seconds_to_display int,
	_displayed int,
	_message text,
	_link text
        )
        returns a0."Message"
        as $$
        
    declare
        result a0."Message";
        _t bytea;
		_d bytea;
    begin
        _t := decode(__t, 'base64');
		_d := decode(__d, 'base64');
        


       
        


        
        _message_id := nextval('a0.message_message_id');
        _t := public.new_trev4(_message_id, 33);
        
        _modified_time := coalesce(_modified_time, now());
        _created_time := coalesce(_created_time, now());
        insert into a0.message (
            flags,
	modified_time,
	created_time,
	account_id,
	deleted,
	t,
	d,
	device_id,
	message_id,
	seconds_to_display,
	displayed,
	message,
	link
        )
        values(
            _flags,
			_modified_time,
			_created_time,
			_account_id,
			_deleted,
			_t,
			_d,
			_device_id,
			_message_id,
			_seconds_to_display,
			_displayed,
			_message,
			_link
        )
        returning
            flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		deleted,
		encode(t, 'base64') as "t",
		encode(d, 'base64') as "d",
		device_id as "deviceId",
		message_id as "messageId",
		seconds_to_display as "secondsToDisplay",
		displayed,
		message,
		link
        into result;

        



        perform a0.message_merkle(_account_id);

    
        



        return result;
    end;
        $$
        language plpgsql;
        

drop function if exists a0.message_update; 
        create function a0.message_update(
            _flags int,
	_modified_time timestamptz,
	_created_time timestamptz,
	_account_id int,
	_deleted boolean,
	__t text,
	__d text,
	_device_id int,
	_message_id int,
	_seconds_to_display int,
	_displayed int,
	_message text,
	_link text
        )
        returns a0."Message"
        as $$
        
    declare
        result a0."Message";
        _t bytea;
		_d bytea;
    begin
        _t := decode(__t, 'base64');
		_d := decode(__d, 'base64');
        


       
        


        
        _t := public.next_trev(_t);
        _modified_time := now();
        update a0.message
        set
            flags = _flags,
			modified_time = _modified_time,
			created_time = _created_time,
			deleted = _deleted,
			t = _t,
			d = _d,
			device_id = _device_id,
			seconds_to_display = _seconds_to_display,
			displayed = _displayed,
			message = _message,
			link = _link
        where account_id = _account_id and message_id = _message_id
        returning
            flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		deleted,
		encode(t, 'base64') as "t",
		encode(d, 'base64') as "d",
		device_id as "deviceId",
		message_id as "messageId",
		seconds_to_display as "secondsToDisplay",
		displayed,
		message,
		link
        into result;

        perform a0.message_merkle(_account_id);

    
        



        return result;
    end;
        $$
        language plpgsql;
        

drop function if exists a0.message_read_by_account_id_and_message_id; 
        create function a0.message_read_by_account_id_and_message_id(
            _account_id int,
	_message_id int
        )
        returns a0."Message"
        as $$
        select
        flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		deleted,
		encode(t, 'base64') as "t",
		encode(d, 'base64') as "d",
		device_id as "deviceId",
		message_id as "messageId",
		seconds_to_display as "secondsToDisplay",
		displayed,
		message,
		link
        from a0.message
        where account_id = _account_id and message_id = _message_id;
        $$
        language sql;
        

drop function if exists a0.message_delete_by_account_id_and_message_id; 
        create function a0.message_delete_by_account_id_and_message_id(
            _account_id int,
	_message_id int
        )
        returns void
        as $$
        
    declare
        
        
    begin
        
        


       
        


        

    delete from a0.message
    where account_id = _account_id and message_id = _message_id;

    perform a0.message_merkle(_account_id);
    
        



        
    end;
        $$
        language plpgsql;
        

drop function if exists a0.message_delete_by_account_id; 
        create function a0.message_delete_by_account_id(
            _account_id int
        )
        returns void
        as $$
        
    declare
        
        
    begin
        
        


       
        


        

    delete from a0.message
    where account_id = _account_id;

    perform a0.message_merkle(_account_id);
    
        



        
    end;
        $$
        language plpgsql;
        

drop function if exists a0.message_find_by_account_id; 
        create function a0.message_find_by_account_id(
            _account_id int
        )
        returns setof a0."Message"
        as $$
        select
        flags,
		modified_time as "modifiedTime",
		created_time as "createdTime",
		account_id as "accountId",
		deleted,
		encode(t, 'base64') as "t",
		encode(d, 'base64') as "d",
		device_id as "deviceId",
		message_id as "messageId",
		seconds_to_display as "secondsToDisplay",
		displayed,
		message,
		link
        from a0.message
        where account_id = _account_id;
        $$
        language sql;
        
