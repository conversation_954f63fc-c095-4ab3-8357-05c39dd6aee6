﻿SET client_min_messages=WARNING;

create extension if not exists postgres_fdw;

create schema a0;

CREATE SERVER account_server
        FOREIGN DATA WRAPPER postgres_fdw
        OPTIONS (host :account_host, port '5432', dbname 'account0');

CREATE USER MAPPING FOR :account_user_mapping
        SERVER account_server
        OPTIONS (user :account_user, password :a0_password);

ALTER SERVER account_server OPTIONS (ADD use_remote_estimate 'true');

CREATE USER MAPPING FOR datawarehouse
        SERVER account_server
        OPTIONS (user 'datawarehouse', password :d0_password);

IMPORT FOREIGN SCHEMA a0
    LIMIT TO ( device, account, device_data )
    FROM SERVER account_server
    INTO a0;

create schema t0;

CREATE SERVER telemetry_server
        FOREIGN DATA WRAPPER postgres_fdw
        OPTIONS (host :telemetry_host, port '5432', dbname 'telemetry');

ALTER SERVER telemetry_server OPTIONS (ADD use_remote_estimate 'true');

CREATE USER MAPPING FOR :telemetry_user_mapping
        SERVER telemetry_server
        OPTIONS (user :telemetry_user, password :t0_password);

CREATE USER MAPPING FOR datawarehouse
        SERVER telemetry_server
        OPTIONS (user 'datawarehouse', password :d0_password);

IMPORT FOREIGN SCHEMA t0
    FROM SERVER telemetry_server
    INTO t0;

create extension if not exists pgcrypto;
create extension if not exists dblink;

create schema if not exists d0;

set schema 'd0';

create table cloud_event (
    account_id int,
    time_on_cloud_clock timestamptz not null,
    device_id int,
    event_type int not null,
    data json,
    modified_time timestamptz not null default now(),
    primary key(event_type, time_on_cloud_clock)
);

create table client_event (
    device_long_id bytea,
    time_on_client_clock timestamptz NOT NULL,
    event_type char(2) NOT NULL,
    uptime int8,
    data text,
    session_id int4,
    modified_time timestamptz not null default now(),
    PRIMARY KEY (device_long_id, event_type, time_on_client_clock)
);

create table counters_base (
    account_id int,
    device_id int,
    etl_time timestamptz,
    modified_time timestamptz,
    data json
);

create table counters (
    primary key (account_id, device_id)
) inherits (counters_base);

create table counters_history (
    primary key (account_id, device_id, etl_time)
) inherits (counters_base);

create table account_base (
    account_id int not null primary key,
    etl_time timestamptz,
    modified_time timestamptz,
    plan_id text not null,
    next_plan_date date,
    next_plan_id text
);

create table account (
    primary key(account_id)
) inherits (account_base);

create table account_history (
    primary key (account_id, etl_time)
) inherits (account_base);

create table device_base (
    account_id int not null,
    device_id int not null,
    long_id bytea,
    etl_time timestamptz,
    modified_time timestamptz,
    os text,
    build text,
    ram_in_mb int,
    is_32_bit boolean,
    deleted boolean,
    device_type int
);

create table device (
    primary key(account_id, device_id)
) inherits (device_base);

create table device_history (
    primary key(account_id, device_id, etl_time)
) inherits (device_base);
