

import Validation from "../system/Validation.mjs";
import { IModel } from "./IModel.mjs";







export interface ISupportTicket {
    flags?: number;
	modifiedTime?: Date;
	createdTime?: Date;
	subject?: string;
	comments?: string;
	sendScreenshot?: boolean;
	includeCatalog?: boolean;
	logLevel?: string;
	logLevelFlags?: number;
	requestId?: string;
	consoleCommandString?: string;
	ttl?: number;
}


export class SupportTicket 
implements IModel {
    private _state: ISupportTicket;

    


    
    changed = false;

    constructor(state: ISupportTicket) {
        this.state(state);
    }

    isUndefined() {
        return this._state !== void 0;
    }

    async validate(skipUndefined?: boolean) {
        let v = new Validation(this, skipUndefined);
        


        return v;
    }

    rtt() {
        return "SupportTicket"; 
    }

    state (value?: ISupportTicket) {
        if (value !== undefined) { 
            this._state = value;
            
            


        }
        return this._state;
    }

    flags(value?: number) {
                if (value !== void 0) {
                    if (this.state().flags !== value) {
                        this.state().flags = value;
                        this.changed = true;
                    }
                }
                return this.state().flags;
            };

		modifiedTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().modifiedTime !== value) {
                        this.state().modifiedTime = value;
                        this.changed = true;
                    }
                }
                return this.state().modifiedTime;
            };

		createdTime(value?: Date) {
                if (value !== void 0) {
                    if (this.state().createdTime !== value) {
                        this.state().createdTime = value;
                        this.changed = true;
                    }
                }
                return this.state().createdTime;
            };

		subject(value?: string) {
                if (value !== void 0) {
                    if (this.state().subject !== value) {
                        this.state().subject = value;
                        this.changed = true;
                    }
                }
                return this.state().subject;
            };

		comments(value?: string) {
                if (value !== void 0) {
                    if (this.state().comments !== value) {
                        this.state().comments = value;
                        this.changed = true;
                    }
                }
                return this.state().comments;
            };

		sendScreenshot(value?: boolean) {
                if (value !== void 0) {
                    if (this.state().sendScreenshot !== value) {
                        this.state().sendScreenshot = value;
                        this.changed = true;
                    }
                }
                return this.state().sendScreenshot;
            };

		includeCatalog(value?: boolean) {
                if (value !== void 0) {
                    if (this.state().includeCatalog !== value) {
                        this.state().includeCatalog = value;
                        this.changed = true;
                    }
                }
                return this.state().includeCatalog;
            };

		logLevel(value?: string) {
                if (value !== void 0) {
                    if (this.state().logLevel !== value) {
                        this.state().logLevel = value;
                        this.changed = true;
                    }
                }
                return this.state().logLevel;
            };

		logLevelFlags(value?: number) {
                if (value !== void 0) {
                    if (this.state().logLevelFlags !== value) {
                        this.state().logLevelFlags = value;
                        this.changed = true;
                    }
                }
                return this.state().logLevelFlags;
            };

		requestId(value?: string) {
                if (value !== void 0) {
                    if (this.state().requestId !== value) {
                        this.state().requestId = value;
                        this.changed = true;
                    }
                }
                return this.state().requestId;
            };

		consoleCommandString(value?: string) {
                if (value !== void 0) {
                    if (this.state().consoleCommandString !== value) {
                        this.state().consoleCommandString = value;
                        this.changed = true;
                    }
                }
                return this.state().consoleCommandString;
            };

		ttl(value?: number) {
                if (value !== void 0) {
                    if (this.state().ttl !== value) {
                        this.state().ttl = value;
                        this.changed = true;
                    }
                }
                return this.state().ttl;
            };

    differs(original: SupportTicket) {
        return (
            (this.flags() !== void 0 && this.flags() !== original.flags())
		 || (this.modifiedTime() !== void 0 && this.modifiedTime() !== original.modifiedTime())
		 || (this.createdTime() !== void 0 && this.createdTime() !== original.createdTime())
		 || (this.subject() !== void 0 && this.subject() !== original.subject())
		 || (this.comments() !== void 0 && this.comments() !== original.comments())
		 || (this.sendScreenshot() !== void 0 && this.sendScreenshot() !== original.sendScreenshot())
		 || (this.includeCatalog() !== void 0 && this.includeCatalog() !== original.includeCatalog())
		 || (this.logLevel() !== void 0 && this.logLevel() !== original.logLevel())
		 || (this.logLevelFlags() !== void 0 && this.logLevelFlags() !== original.logLevelFlags())
		 || (this.requestId() !== void 0 && this.requestId() !== original.requestId())
		 || (this.consoleCommandString() !== void 0 && this.consoleCommandString() !== original.consoleCommandString())
		 || (this.ttl() !== void 0 && this.ttl() !== original.ttl())
        );
    }







}



export function sanitizeInput(source: SupportTicket, amdin: boolean, mode: string) : ISupportTicket;
export function sanitizeInput(source: ISupportTicket, admin: boolean, mode: string) : ISupportTicket;
export function sanitizeInput(source: SupportTicket | ISupportTicket, admin = false, mode="default"): ISupportTicket {
    let s: ISupportTicket;
    if (source instanceof SupportTicket)
        s = source.state();
    else
        s = source;        
    let t = {} as ISupportTicket;
    
    
        
            switch(mode) {
                
                
                
            }
            t.flags = s.flags;
		t.subject = s.subject;
		t.comments = s.comments;
		t.sendScreenshot = s.sendScreenshot;
		t.includeCatalog = s.includeCatalog;
		t.logLevel = s.logLevel;
		t.logLevelFlags = s.logLevelFlags;
		t.requestId = s.requestId;
		t.consoleCommandString = s.consoleCommandString;
		t.ttl = s.ttl;
        
    return t;
}

export function sanitizeOutput(source: SupportTicket, amdin: boolean) : ISupportTicket;
export function sanitizeOutput(source: ISupportTicket, admin: boolean) : ISupportTicket;
export function sanitizeOutput(source: SupportTicket | ISupportTicket, admin = false): ISupportTicket {
    let s: ISupportTicket;
    if (source instanceof SupportTicket)
        s = source.state();
    else
        s = source;        
    let t = {} as ISupportTicket;
    
    
    
    t.flags = s.flags;	
t.modifiedTime = s.modifiedTime;	
t.createdTime = s.createdTime;	
t.subject = s.subject;	
t.comments = s.comments;	
t.sendScreenshot = s.sendScreenshot;	
t.includeCatalog = s.includeCatalog;	
t.logLevel = s.logLevel;	
t.logLevelFlags = s.logLevelFlags;	
t.requestId = s.requestId;	
t.consoleCommandString = s.consoleCommandString;	
t.ttl = s.ttl;
    return t;
}

export function mergeState(dbVersion: ISupportTicket, newVersion: ISupportTicket) {
    let targetState: ISupportTicket = {};
    targetState.flags = newVersion.flags === undefined ? dbVersion.flags : newVersion.flags;
	targetState.modifiedTime = newVersion.modifiedTime === undefined ? dbVersion.modifiedTime : newVersion.modifiedTime;
	targetState.createdTime = newVersion.createdTime === undefined ? dbVersion.createdTime : newVersion.createdTime;
	targetState.subject = newVersion.subject === undefined ? dbVersion.subject : newVersion.subject;
	targetState.comments = newVersion.comments === undefined ? dbVersion.comments : newVersion.comments;
	targetState.sendScreenshot = newVersion.sendScreenshot === undefined ? dbVersion.sendScreenshot : newVersion.sendScreenshot;
	targetState.includeCatalog = newVersion.includeCatalog === undefined ? dbVersion.includeCatalog : newVersion.includeCatalog;
	targetState.logLevel = newVersion.logLevel === undefined ? dbVersion.logLevel : newVersion.logLevel;
	targetState.logLevelFlags = newVersion.logLevelFlags === undefined ? dbVersion.logLevelFlags : newVersion.logLevelFlags;
	targetState.requestId = newVersion.requestId === undefined ? dbVersion.requestId : newVersion.requestId;
	targetState.consoleCommandString = newVersion.consoleCommandString === undefined ? dbVersion.consoleCommandString : newVersion.consoleCommandString;
	targetState.ttl = newVersion.ttl === undefined ? dbVersion.ttl : newVersion.ttl;
    return targetState;
}

export function merge(dbVersion: SupportTicket, newVersion: SupportTicket) {
    return new SupportTicket(mergeState(dbVersion.state(), newVersion.state()));
}
