/**
 * Data validated via a receipt with external store
 */
export interface ExternalStoreValidationResult {
  /** Mylio account ID */
  accountId: number;
  /** Product identifier from the store */
  productId: string;
  /** Milliseconds from epoch in string form */
  purchaseDate: string;
  /** Milliseconds from epoch in string form */
  expirationDate: string;
  /** Whether trial is allowed for the store account */
  trialAllowed: boolean;
  /** Whether the receipt transaction is cancelled */
  cancelled: boolean;
}

/**
 * Result of applying a receipt to an account
 */
export interface ExternalPurchaseApplicationResult {
  /**
   * Whether the purchase was applicable to account.
   * Will be false if the account is on a track different
   * from the store that sent the recipt.
   * Note that this will be true even if the recipt resulted in no account change
   */
  handled: boolean;
  /** Time server used to evaluate expiration */
  now: string;
  /** Plan ID before receipt application */
  originalPlanId: string;
  /** Plan ID after receipt application.
   * Might be different or same to the originalPlanId
   * A receipt application might be a no-op
   */
  newPlanId: string;
}
