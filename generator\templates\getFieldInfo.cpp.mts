// @ts-nocheck

import * as url from "url";
import { Entity, Field, Datatype } from "../Entity.mjs";
import { jsname } from "../ScriptHelper.mjs";
import * as changeCase from "change-case";
import fs = require("fs");
import { dirname } from "path";
const __dirname = url.fileURLToPath(new URL(".", import.meta.url));

let schema = JSON.parse(
  fs.readFileSync(`${__dirname}/../entities/Schema.json`, "utf8")
);
let typesBySname = {} as any;
for (let typeName of Object.keys(schema.types)) {
  let t = schema.types[typeName];
  let name = t.cloudname || typeName;
  typesBySname[name] = t;
}

function cname(e: Entity) {
  let name = e.name;
  if (name.endsWith("_property")) name = name.split("_")[0];
  return changeCase.pascalCase(name);
}

function lookupBJsonType(entity: Entity, field: Field) {
  let clientName = cname(entity);
  let type = typesBySname[clientName];
  // @ts-ignore
  let clientField = type.fields.find((cf) => cf.cloudname === jsname(field));
  if (clientField) {
    let clientType = clientField.cloudType || clientField.type;
    let rawType = schema.types[clientType];
    if (rawType.isPersistableobject || rawType.isResource) {
      return "hash";
    } else if (rawType.BJsonType) {
      return rawType.BJsonType;
    }
  }
  throw Error(
    `Unable to resolve BJsonType for type ${clientName} and field ${jsname(
      field
    )}`
  );
}

export function template(entities: Entity[]) {
  let types = Object.keys(typesBySname)
    .map((typeName) => {
      let entity = entities.find((e) => cname(e) === typeName);
      return { t: typesBySname[typeName], e: entity };
    })
    .filter((t) => !!t.e);

  /* tslint:disable */

  let cpp = `
#include "getFieldInfo.h"
#include "MYLiterals.h"


bool getFieldInfo(int sectionSid, int sid, Field& f)  {
    switch (sectionSid) {
    case MYLiterals::resources:
        switch(sid) {
            case MYLiterals::trev:
                f.name = "t";
                f.isDate = false;
                f.type = BJsonType::trev;
                return true;
        }
        break;

    ${types
      .map(
        (t) => `case MYLiterals::${t.e.plural}:
                        switch(sid) {
                            case MYLiterals::trev:
                                f.name = "t";
                                f.isDate = false;
                                f.type = BJsonType::trev;
                                return true;
                            
                        ${t.t.fields
            .filter((tf) => !!tf.cloudname)
            .map((tf) => {
              let ef = t.e.fields.find(
                (f) => jsname(f) === tf.cloudname
              );
              if (ef) {
                return `case MYLiterals::${cname(
                  t.e
                )}::${jsname(ef)}:
                                                f.name = "${jsname(ef)}";
                                                f.type = BJsonType::${lookupBJsonType(
                  t.e,
                  ef
                )};
                                                f.isDate = ${ef.datatype === Datatype.date
                    ? "true"
                    : "false"
                  };
                  f.isInt64 = ${ef.datatype === Datatype.int64 ? "true" : "false"};
                                                return true;`;
              } else return "";
              // tf =>
            })
            .join("\n\t\t\t")
          // t.t.fields
          }

                            //switch sid
                        }
                        break;
                    `
      )
      .join("\n\t\t")
    // types.map
    }
    // switch sectionSid
    }
    return false;

// bool getFieldInfo
}

bool getFieldInfo(int sectionSid, const std::string& name, Field& f) {
    switch (sectionSid) {
    case MYLiterals::resources:
        if (name == "t") {
            f.sid = MYLiterals::trev;
            f.type = BJsonType::trev;
            f.isDate = false;
            return true;
        }
        break;

    ${types
      .map(
        (t) => `case MYLiterals::${t.e.plural}:
        if (name == "t") {
            f.sid = MYLiterals::trev;
            f.type = BJsonType::trev;
            f.isDate = false;
            return true;
        }
    
        ${t.t.fields
            .filter((tf) => !!tf.cloudname)
            .map((tf) => {
              let ef = t.e.fields.find((f) => jsname(f) === tf.cloudname);
              if (ef) {
                return `if (name == "${jsname(ef)}") {
                                f.sid = ${tf.sid};
                                f.isDate = ${ef.datatype === Datatype.date
                    ? "true"
                    : "false"
                  };
                                f.type = BJsonType::${lookupBJsonType(t.e, ef)};
                                return true;
                            }`;
              }
              return "";
              // tf =>
            })
            .join("\n\t\t")
          // t.t.fields
          }
        break;
    `
      )
      .join("\n\t\t")
    // types.map
    }
    // switch sectionSid
    }
    return false;
}`;
  return cpp;
}
