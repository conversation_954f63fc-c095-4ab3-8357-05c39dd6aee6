import { config, getServices } from "../system/Config.mjs";
import { error, makeError } from "../system/error.mjs";
import { RefreshTokenService } from "./RefreshTokenService.mjs";
import { Context } from "../system/Context.mjs";
import { RefreshToken } from "../models/RefreshToken.model.mjs";
import {
  Device,
  DeviceType,
  sanitizeOutput as sanitizeDevice,
} from "../models/Device.model.mjs";
import { DeviceService } from "./DeviceService.mjs";
import { tx } from "../system/Postgres.mjs";
import { LockService } from "./LockService.mjs";
import { ids } from "../system/Strings.mjs";
import { postForm } from "../system/fetch.mjs";

// Setting the tenant to 'consumers' only allows the apis to be used with OneDrive Personal accounts MYLO-47844
// https://docs.microsoft.com/en-us/azure/active-directory/develop/active-directory-v2-protocols#endpoints
let API_TENANT = "consumers";
let API_URL = `https://login.microsoftonline.com/${API_TENANT}`;

export class MicrosoftOAuthService {
  public async getRefreshToken(code: string) {
    let form = {
      client_id: config.microsoft.clientID,
      client_secret: config.microsoft.clientSecret,
      code: code,
      grant_type: "authorization_code",
      redirect_uri: `${config.cloud}/microsoft/authorize`,
    };
    let tokenReponse = await postForm(`${API_URL}/oauth2/v2.0/token`, form);
    let tokenResult = await tokenReponse.json();
    return tokenResult.refresh_token;
  }

  public async getAccessToken(rtoken: string) {
    let form = {
      client_id: config.microsoft.clientID,
      client_secret: config.microsoft.clientSecret,
      grant_type: "refresh_token",
      refresh_token: rtoken,
      redirect_uri: `${config.cloud}/microsoft/authorize`,
    };

    let options = {
      form: form,
      method: "POST",
      uri: `${API_URL}/oauth2/v2.0/token`,
    };

    let accessTokenResponse = await postForm(
      `${API_URL}/oauth2/v2.0/token`,
      form
    );
    let result = await accessTokenResponse.json();
    return result.access_token;
  }

  public getDriveOAuthUrl(
    context: Context,
    aid: number,
    encrypt: boolean,
    isMobile: boolean
  ) {
    let scope = "Files.ReadWrite%20offline_access";
    return this.getOAuthUrl(
      context,
      aid,
      "drive",
      encrypt,
      isMobile,
      `${scope}`
    );
  }

  private getOAuthUrl(
    context: Context,
    aid,
    task: string,
    encrypt: boolean,
    isMobile: boolean,
    scope: string
  ) {
    let base = `${API_URL}/oauth2/v2.0/authorize`;
    let state = Buffer.from(
      JSON.stringify({ aid, encrypt, task, isMobile }),
      "utf8"
    ).toString("base64");
    let uri = `${config.cloud}/microsoft/authorize`;
    let nonce = 12345; // TODO: randomly generate the nonce and verify it when we get the redirect

    // tslint:disable
    return `${base}?scope=${scope}&state=${state}&client_id=${config.microsoft.clientID}&redirect_uri=${uri}&response_type=code&response_mode=query&nonce=${nonce}&prompt=select_account`;
    // tslint:enable
  }
}

export class MicrosoftService {
  private _oauth = new MicrosoftOAuthService();

  constructor(
    private refreshTokenService: RefreshTokenService,
    private deviceService: DeviceService,
    private lockService: LockService
  ) {}

  public getDriveAccessToken(context: Context, aid: number) {
    const idp = "microsoft";
    const task = "drive";

    return this.refreshTokenService
      .read(context, aid, idp, task)
      .then((refreshToken) => {
        if (!refreshToken)
          return error<{ token: string }>(400, ids.NO_REFRESH_TOKEN);

        return this._oauth
          .getAccessToken(refreshToken.token())
          .catch((err) =>
            this.handleGetAccessTokenError(context, err, aid, idp, task)
          );
      });
  }

  public getDriveOAuthUrl(
    context: Context,
    aid: number,
    encrypt: boolean,
    isMobile: boolean
  ) {
    return this._oauth.getDriveOAuthUrl(context, aid, encrypt, isMobile);
  }

  public addRefreshToken(code: string, state: any) {
    let rtokenString: string;
    let context = new Context();
    let task = state.task;
    context.aid = state.aid;
    let aid = context.aid;

    return this._oauth
      .getRefreshToken(code)
      .then((refreshToken) => {
        rtokenString = refreshToken;
        return this.refreshTokenService.tryRead(
          context,
          aid,
          "microsoft",
          task
        );
      })
      .then((refreshToken) => {
        if (!refreshToken)
          return this.refreshTokenService.create(
            context,
            aid,
            new RefreshToken({
              idp: "microsoft",
              accountId: aid,
              task,
              token: rtokenString,
            })
          );

        refreshToken.token(rtokenString);
        return this.refreshTokenService.update(context, aid, refreshToken);
      });
  }

  public revokeToken(context: Context, aid: number, task: string) {
    return this.refreshTokenService.delete(context, aid, "microsoft", task);
  }

  public reserveDrive(context: Context, aid: number) {
    let ticket: string;
    return this.lockService
      .lock(context, aid, "microsoft/drive/reservation", 10)
      .then((result) => {
        ticket = result;
        return this.deviceService.findByType(
          context,
          aid,
          DeviceType.MicrosoftDrive
        );
      })
      .then((result) => {
        if (result && result.length > 0 && !result[0].deleted) {
          return error<any>(400, ids.DUPLICATE_CLOUD_DRIVE);
        }
        return this.deviceService.newId(context, aid);
      })
      .then((result) => {
        return {
          ticket,
          deviceId: result,
          deviceName: "Microsoft Drive",
        };
      });
  }

  public addDrive(context: Context, aid: number, encrypt: boolean) {
    return tx(context, () => {
      return this.deviceService
        .findByType(context, aid, DeviceType.MicrosoftDrive)
        .then((drives) => {
          if (!drives || drives.length === 0)
            return this.deviceService.create(
              context,
              aid,
              new Device({
                deviceType: DeviceType.MicrosoftDrive,
                encrypt: encrypt || false,
                name: "Microsoft Drive",
                nickname: "Microsoft Drive",
                creationTime: new Date(),
              })
            );
          return drives[0];
        });
    }).then((drive) => {
      return sanitizeDevice(drive, context.hasAdminRights());
    });
  }

  private async handleGetAccessTokenError(
    context: Context,
    err: any,
    aid: number,
    idp: string,
    task: string
  ) {
    if (err && err.error) {
      try {
        const parsedError: any = JSON.parse(err.error);

        if (
          parsedError &&
          parsedError.error &&
          parsedError.error === "invalid_grant"
        ) {
          await this.refreshTokenService.delete(context, aid, idp, task);
        }
      } catch (e) {
        throw makeError(404, ids.REFRESHTOKEN_NOT_VALID_FOR_ACCOUNT);
      }
    }

    throw makeError(404, ids.REFRESHTOKEN_NOT_VALID_FOR_ACCOUNT);
  }
}
